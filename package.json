{"name": "logictrue", "version": "3.1.0", "description": "生产智控平台", "author": "logictrue", "license": "MIT", "scripts": {"dev": "vue-cli-service serve", "prod": "vue-cli-service serve  --mode production", "build:prod": "vue-cli-service build", "build:stage": "vue-cli-service build --mode staging", "preview": "node build/index.js --preview", "lint": "eslint --ext .js,.vue src", "serve": "set NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service serve", "build": "set NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service build"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "keywords": ["vue", "admin", "dashboard", "element-ui", "boilerplate", "admin-template", "management-system"], "repository": {"type": "git", "url": "https://gitee.com/y_project/RuoYi-Cloud.git"}, "dependencies": {"@antv/g2plot": "^2.4.33", "@antv/x6": "^2.18.1", "@antv/x6-plugin-clipboard": "^2.1.6", "@antv/x6-plugin-export": "^2.1.6", "@antv/x6-plugin-history": "^2.2.4", "@antv/x6-plugin-selection": "^2.2.2", "@antv/x6-plugin-stencil": "^2.1.5", "@riophae/vue-treeselect": "0.4.0", "@wchbrad/vue-easy-tree": "^1.0.9", "@wecom/jssdk": "^1.4.1", "ace-builds": "^1.19.0", "axios": "^0.21.0", "babel-eslint": "^10.1.0", "clipboard": "2.0.6", "compression-webpack-plugin": "^1.1.12", "dhtmlx-gantt": "^8.0.6", "echarts": "^5.5.0", "element-resize-detector": "^1.2.4", "element-ui": "2.15.6", "exceljs": "^4.4.0", "file-saver": "^2.0.4", "highlight.js": "9.18.5", "html2canvas": "^1.4.1", "intro.js": "^7.2.0", "jquery": "^3.6.0", "js-base64": "^3.7.5", "js-beautify": "^1.14.7", "js-cookie": "2.2.1", "jsencrypt": "3.0.0-rc.1", "json-bigint": "^1.0.0", "lodash": "4.17.15", "lodash-es": "4.17.21", "mavon-editor": "^2.10.4", "moment": "^2.29.4", "monaco-editor": "^0.28.1", "monaco-editor-webpack-plugin": "^4.2.0", "nprogress": "0.2.0", "pinyin-match": "^1.2.2", "qrcode": "^1.5.0", "qrcodejs2": "0.0.2", "qs": "^6.9.6", "quill": "1.3.7", "remote-vue-loader": "1.0.0", "screenfull": "5.0.2", "sign-canvas": "^1.1.4", "sortablejs": "^1.10.2", "sql-formatter": "^2.3.4", "three": "^0.122.0", "vue": "2.6.12", "vue-amap": "^0.5.10", "vue-barcode": "^1.3.0", "vue-code-view": "^0.4.1", "vue-codemirror": "^4.0.6", "vue-cropper": "0.5.5", "vue-draggable-resizable-gorkys": "^2.4.8", "vue-echarts": "^5.0.0-beta.0", "vue-grid-layout": "^2.4.0", "vue-i18n": "^8.26.7", "vue-json-editor": "^1.4.3", "vue-json-excel": "^0.3.0", "vue-json-pretty": "^1.8.2", "vue-json-viewer": "^2.2.22", "vue-pdf": "^4.3.0", "vue-qr": "^4.0.9", "vue-resizable": "^1.3.1", "vue-router": "3.4.9", "vue-runtime-helpers": "^1.1.2", "vue2-editor": "^2.10.3", "vuedraggable": "^2.23.0", "vuex": "3.6.0", "wangeditor": "^4.7.10", "watermark-dom": "^2.3.0", "xlsx": "^0.18.5", "xlsx-js-style": "^1.2.0", "dompurify": "^3.2.5", "markdown-it": "^12.3.2", "prismjs": "^1.30.0", "markdown-it-emoji": "^3.0.0", "markdown-it-katex": "^2.0.3", "markdown-it-mermaid": "^0.2.5"}, "devDependencies": {"@vue/cli-plugin-babel": "4.4.6", "@vue/cli-plugin-eslint": "4.4.6", "@vue/cli-service": "4.4.6", "babel-eslint": "10.1.0", "chalk": "4.1.0", "connect": "3.6.6", "eslint": "7.15.0", "eslint-plugin-vue": "7.2.0", "html-webpack-plugin": "^5.6.0", "lint-staged": "10.5.3", "runjs": "4.4.2", "sass": "1.32.0", "sass-loader": "10.1.0", "script-ext-html-webpack-plugin": "2.1.5", "svg-sprite-loader": "5.1.1", "v-contextmenu": "^2.9.0", "vue-template-compiler": "2.6.12"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions"]}