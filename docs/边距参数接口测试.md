# 边距参数接口测试文档

## 修改内容概述

已成功将边距参数添加到所有相关的打印接口中，确保前端UI中设置的边距值能够正确传递给后端处理。

## 修改的文件和接口

### 1. printDialog.vue - 主要打印对话框组件

#### 修改的方法：

**exportPdf 接口调用 (getPdfPreviewFile方法)**
```javascript
exportPdf({
  customPaperSize: customPaperSize, // 自定义宽高
  customExcel: this.excelJson,
  range: this.range,
  unicode: this.uniCode,
  margin: this.margin, // 添加边距参数
  ...this.printConfig,
})
```

**本地打印服务调用 (printMain方法)**
```javascript
axios.post('http://127.0.0.1:18950/print', {
  print: this.currPrinter,
  url: strURL,
  orientation: this.printConfig.orientation.toString(),
  paperSizeType: this.printConfig.paperSizeType.toString(),
  copies: this.printConfig.copies.toString(),
  printMode: this.printConfig.printMode.toString(),
  margin: this.margin, // 添加边距参数
})
```

**表格打印接口 (table2Pdf方法)**
```javascript
this.excel2PdfParam['reportExcelDto'] = {
  ...this.printConfig,
  unicode: this.uniCode,
  margin: this.margin, // 添加边距参数
};
```

**报表打印接口 (reportPrint方法)**
```javascript
this.excel2PdfParam['reportExcelDto'] = {
  ...this.printConfig,
  unicode: this.uniCode,
  reportUid: this.excel2PdfParam.reportUid,
  margin: this.margin // 添加边距参数
}
```

**多报表合并打印接口 (reportGaeaList方法)**
```javascript
this.reportExcelMoreDto['reportExcelDto'] = {
  ...this.printConfig,
  margin: this.margin, // 添加边距参数
};
```

### 2. printBarcodeTagDialog.vue - 条码打印对话框组件

**条码打印接口 (printMain方法)**
```javascript
// 添加边距参数到URL参数中
const marginString = this.margin.map((value, index) => {
  return `margin${index}=${encodeURIComponent(value)}`;
}).join('&');

axios.get('http://127.0.0.1:18950/printImg?print=' + this.currPrinter + "&" + paramsString + "&" + marginString + "&url="+fileUrl)
```

## 边距参数格式

边距参数使用数组格式存储：
- `margin[0]` - 上边距
- `margin[1]` - 左边距  
- `margin[2]` - 下边距
- `margin[3]` - 右边距

默认值：`[1, 0.75, 1, 0.75]`

## 测试建议

### 前端测试
1. 打开打印对话框
2. 点击"边距"按钮，修改边距值
3. 关闭边距设置弹窗（触发handleChange）
4. 执行打印操作
5. 检查网络请求中是否包含margin参数

### 后端验证
需要后端开发人员确认以下接口是否正确接收和处理margin参数：
- `/interfaces/reportExcel/exportPdf`
- `/interfaces/reportExcel/excel2PdfByTable`
- `/interfaces/reportExcel/excel2PdfByReport`
- `/interfaces/reportExcel/exportListPdf`
- 本地打印服务：`http://127.0.0.1:18950/print`
- 条码打印服务：`http://127.0.0.1:18950/printImg`

## 注意事项

1. 边距参数的单位需要与后端保持一致（通常为英寸或毫米）
2. 条码打印使用URL参数传递，格式为 `margin0=1&margin1=0.75&margin2=1&margin3=0.75`
3. 其他打印接口使用JSON对象传递，格式为 `margin: [1, 0.75, 1, 0.75]`
4. 边距值的变化会触发预览更新（通过handleChange方法）

## 相关文件

- `src/views/tool/excelreport/viewer/printDialog.vue` - 主打印对话框
- `src/views/tool/excelreport/viewer/printForm.vue` - 打印表单组件
- `src/views/tool/excelreport/viewer/printBarcodeTagDialog.vue` - 条码打印对话框
- `src/api/interfaces/interfaces.js` - API接口定义
