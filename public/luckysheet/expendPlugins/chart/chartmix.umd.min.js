(function(t,e){"object"===typeof exports&&"object"===typeof module?module.exports=e(require("echarts"),require("Vuex"),require("Vue")):"function"===typeof define&&define.amd?define(["echarts","Vuex","Vue"],e):"object"===typeof exports?exports["chartmix"]=e(require("echarts"),require("Vuex"),require("Vue")):t["chartmix"]=e(t["echarts"],t["Vuex"],t["Vue"])})("undefined"!==typeof self?self:this,(function(t,e,n){return function(t){var e={};function n(r){if(e[r])return e[r].exports;var i=e[r]={i:r,l:!1,exports:{}};return t[r].call(i.exports,i,i.exports,n),i.l=!0,i.exports}return n.m=t,n.c=e,n.d=function(t,e,r){n.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:r})},n.r=function(t){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},n.t=function(t,e){if(1&e&&(t=n(t)),8&e)return t;if(4&e&&"object"===typeof t&&t&&t.__esModule)return t;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var i in t)n.d(r,i,function(e){return t[e]}.bind(null,i));return r},n.n=function(t){var e=t&&t.__esModule?function(){return t["default"]}:function(){return t};return n.d(e,"a",e),e},n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},n.p="",n(n.s="fb15")}({"00fd":function(t,e,n){var r=n("9e69"),i=Object.prototype,a=i.hasOwnProperty,o=i.toString,s=r?r.toStringTag:void 0;function l(t){var e=a.call(t,s),n=t[s];try{t[s]=void 0;var r=!0}catch(l){}var i=o.call(t);return r&&(e?t[s]=n:delete t[s]),i}t.exports=l},"03dd":function(t,e,n){var r=n("eac5"),i=n("57a5"),a=Object.prototype,o=a.hasOwnProperty;function s(t){if(!r(t))return i(t);var e=[];for(var n in Object(t))o.call(t,n)&&"constructor"!=n&&e.push(n);return e}t.exports=s},"04f8":function(t,e,n){var r=n("2d00"),i=n("d039"),a=n("da84"),o=a.String;t.exports=!!Object.getOwnPropertySymbols&&!i((function(){var t=Symbol();return!o(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&r&&r<41}))},"0644":function(t,e,n){var r=n("3818"),i=1,a=4;function o(t){return r(t,i|a)}t.exports=o},"06cf":function(t,e,n){var r=n("83ab"),i=n("c65b"),a=n("d1e7"),o=n("5c6c"),s=n("fc6a"),l=n("a04b"),c=n("1a2d"),u=n("0cfb"),h=Object.getOwnPropertyDescriptor;e.f=r?h:function(t,e){if(t=s(t),e=l(e),u)try{return h(t,e)}catch(n){}if(c(t,e))return o(!i(a.f,t,e),t[e])}},"07c7":function(t,e){function n(){return!1}t.exports=n},"07fa":function(t,e,n){var r=n("50c4");t.exports=function(t){return r(t.length)}},"087d":function(t,e){function n(t,e){var n=-1,r=e.length,i=t.length;while(++n<r)t[i+n]=e[n];return t}t.exports=n},"0b07":function(t,e,n){var r=n("34ac"),i=n("3698");function a(t,e){var n=i(t,e);return r(n)?n:void 0}t.exports=a},"0cfb":function(t,e,n){var r=n("83ab"),i=n("d039"),a=n("cc12");t.exports=!r&&!i((function(){return 7!=Object.defineProperty(a("div"),"a",{get:function(){return 7}}).a}))},"0d24":function(t,e,n){(function(t){var r=n("2b3e"),i=n("07c7"),a=e&&!e.nodeType&&e,o=a&&"object"==typeof t&&t&&!t.nodeType&&t,s=o&&o.exports===a,l=s?r.Buffer:void 0,c=l?l.isBuffer:void 0,u=c||i;t.exports=u}).call(this,n("62e4")(t))},"0d51":function(t,e){var n=String;t.exports=function(t){try{return n(t)}catch(e){return"Object"}}},"0f0f":function(t,e,n){var r=n("8eeb"),i=n("9934");function a(t,e){return t&&r(e,i(e),t)}t.exports=a},"100e":function(t,e,n){var r=n("cd9d"),i=n("2286"),a=n("c1c9");function o(t,e){return a(i(t,e,r),t+"")}t.exports=o},1041:function(t,e,n){var r=n("8eeb"),i=n("a029");function a(t,e){return r(t,i(t),e)}t.exports=a},1157:function(t,e,n){var r,i;
/*!
 * jQuery JavaScript Library v3.7.0
 * https://jquery.com/
 *
 * Copyright OpenJS Foundation and other contributors
 * Released under the MIT license
 * https://jquery.org/license
 *
 * Date: 2023-05-11T18:29Z
 */(function(e,n){"use strict";"object"===typeof t.exports?t.exports=e.document?n(e,!0):function(t){if(!t.document)throw new Error("jQuery requires a window with a document");return n(t)}:n(e)})("undefined"!==typeof window?window:this,(function(n,a){"use strict";var o=[],s=Object.getPrototypeOf,l=o.slice,c=o.flat?function(t){return o.flat.call(t)}:function(t){return o.concat.apply([],t)},u=o.push,h=o.indexOf,f={},p=f.toString,d=f.hasOwnProperty,g=d.toString,m=g.call(Object),b={},v=function(t){return"function"===typeof t&&"number"!==typeof t.nodeType&&"function"!==typeof t.item},y=function(t){return null!=t&&t===t.window},x=n.document,A={type:!0,src:!0,nonce:!0,noModule:!0};function w(t,e,n){n=n||x;var r,i,a=n.createElement("script");if(a.text=t,e)for(r in A)i=e[r]||e.getAttribute&&e.getAttribute(r),i&&a.setAttribute(r,i);n.head.appendChild(a).parentNode.removeChild(a)}function C(t){return null==t?t+"":"object"===typeof t||"function"===typeof t?f[p.call(t)]||"object":typeof t}var S="3.7.0",k=/HTML$/i,O=function(t,e){return new O.fn.init(t,e)};function T(t){var e=!!t&&"length"in t&&t.length,n=C(t);return!v(t)&&!y(t)&&("array"===n||0===e||"number"===typeof e&&e>0&&e-1 in t)}function I(t,e){return t.nodeName&&t.nodeName.toLowerCase()===e.toLowerCase()}O.fn=O.prototype={jquery:S,constructor:O,length:0,toArray:function(){return l.call(this)},get:function(t){return null==t?l.call(this):t<0?this[t+this.length]:this[t]},pushStack:function(t){var e=O.merge(this.constructor(),t);return e.prevObject=this,e},each:function(t){return O.each(this,t)},map:function(t){return this.pushStack(O.map(this,(function(e,n){return t.call(e,n,e)})))},slice:function(){return this.pushStack(l.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},even:function(){return this.pushStack(O.grep(this,(function(t,e){return(e+1)%2})))},odd:function(){return this.pushStack(O.grep(this,(function(t,e){return e%2})))},eq:function(t){var e=this.length,n=+t+(t<0?e:0);return this.pushStack(n>=0&&n<e?[this[n]]:[])},end:function(){return this.prevObject||this.constructor()},push:u,sort:o.sort,splice:o.splice},O.extend=O.fn.extend=function(){var t,e,n,r,i,a,o=arguments[0]||{},s=1,l=arguments.length,c=!1;for("boolean"===typeof o&&(c=o,o=arguments[s]||{},s++),"object"===typeof o||v(o)||(o={}),s===l&&(o=this,s--);s<l;s++)if(null!=(t=arguments[s]))for(e in t)r=t[e],"__proto__"!==e&&o!==r&&(c&&r&&(O.isPlainObject(r)||(i=Array.isArray(r)))?(n=o[e],a=i&&!Array.isArray(n)?[]:i||O.isPlainObject(n)?n:{},i=!1,o[e]=O.extend(c,a,r)):void 0!==r&&(o[e]=r));return o},O.extend({expando:"jQuery"+(S+Math.random()).replace(/\D/g,""),isReady:!0,error:function(t){throw new Error(t)},noop:function(){},isPlainObject:function(t){var e,n;return!(!t||"[object Object]"!==p.call(t))&&(e=s(t),!e||(n=d.call(e,"constructor")&&e.constructor,"function"===typeof n&&g.call(n)===m))},isEmptyObject:function(t){var e;for(e in t)return!1;return!0},globalEval:function(t,e,n){w(t,{nonce:e&&e.nonce},n)},each:function(t,e){var n,r=0;if(T(t)){for(n=t.length;r<n;r++)if(!1===e.call(t[r],r,t[r]))break}else for(r in t)if(!1===e.call(t[r],r,t[r]))break;return t},text:function(t){var e,n="",r=0,i=t.nodeType;if(i){if(1===i||9===i||11===i)return t.textContent;if(3===i||4===i)return t.nodeValue}else while(e=t[r++])n+=O.text(e);return n},makeArray:function(t,e){var n=e||[];return null!=t&&(T(Object(t))?O.merge(n,"string"===typeof t?[t]:t):u.call(n,t)),n},inArray:function(t,e,n){return null==e?-1:h.call(e,t,n)},isXMLDoc:function(t){var e=t&&t.namespaceURI,n=t&&(t.ownerDocument||t).documentElement;return!k.test(e||n&&n.nodeName||"HTML")},merge:function(t,e){for(var n=+e.length,r=0,i=t.length;r<n;r++)t[i++]=e[r];return t.length=i,t},grep:function(t,e,n){for(var r,i=[],a=0,o=t.length,s=!n;a<o;a++)r=!e(t[a],a),r!==s&&i.push(t[a]);return i},map:function(t,e,n){var r,i,a=0,o=[];if(T(t))for(r=t.length;a<r;a++)i=e(t[a],a,n),null!=i&&o.push(i);else for(a in t)i=e(t[a],a,n),null!=i&&o.push(i);return c(o)},guid:1,support:b}),"function"===typeof Symbol&&(O.fn[Symbol.iterator]=o[Symbol.iterator]),O.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),(function(t,e){f["[object "+e+"]"]=e.toLowerCase()}));var E=o.pop,D=o.sort,L=o.splice,G="[\\x20\\t\\r\\n\\f]",j=new RegExp("^"+G+"+|((?:^|[^\\\\])(?:\\\\.)*)"+G+"+$","g");O.contains=function(t,e){var n=e&&e.parentNode;return t===n||!(!n||1!==n.nodeType||!(t.contains?t.contains(n):t.compareDocumentPosition&&16&t.compareDocumentPosition(n)))};var N=/([\0-\x1f\x7f]|^-?\d)|^-$|[^\x80-\uFFFF\w-]/g;function M(t,e){return e?"\0"===t?"�":t.slice(0,-1)+"\\"+t.charCodeAt(t.length-1).toString(16)+" ":"\\"+t}O.escapeSelector=function(t){return(t+"").replace(N,M)};var R=x,B=u;(function(){var t,e,r,i,a,s,c,u,f,p,g=B,m=O.expando,v=0,y=0,x=et(),A=et(),w=et(),C=et(),S=function(t,e){return t===e&&(a=!0),0},k="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",T="(?:\\\\[\\da-fA-F]{1,6}"+G+"?|\\\\[^\\r\\n\\f]|[\\w-]|[^\0-\\x7f])+",N="\\["+G+"*("+T+")(?:"+G+"*([*^$|!~]?=)"+G+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+T+"))|)"+G+"*\\]",M=":("+T+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+N+")*)|.*)\\)|)",V=new RegExp(G+"+","g"),P=new RegExp("^"+G+"*,"+G+"*"),z=new RegExp("^"+G+"*([>+~]|"+G+")"+G+"*"),Q=new RegExp(G+"|>"),W=new RegExp(M),F=new RegExp("^"+T+"$"),q={ID:new RegExp("^#("+T+")"),CLASS:new RegExp("^\\.("+T+")"),TAG:new RegExp("^("+T+"|[*])"),ATTR:new RegExp("^"+N),PSEUDO:new RegExp("^"+M),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+G+"*(even|odd|(([+-]|)(\\d*)n|)"+G+"*(?:([+-]|)"+G+"*(\\d+)|))"+G+"*\\)|)","i"),bool:new RegExp("^(?:"+k+")$","i"),needsContext:new RegExp("^"+G+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+G+"*((?:-\\d)?\\d*)"+G+"*\\)|)(?=[^-]|$)","i")},X=/^(?:input|select|textarea|button)$/i,H=/^h\d$/i,Z=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,J=/[+~]/,U=new RegExp("\\\\[\\da-fA-F]{1,6}"+G+"?|\\\\([^\\r\\n\\f])","g"),Y=function(t,e){var n="0x"+t.slice(1)-65536;return e||(n<0?String.fromCharCode(n+65536):String.fromCharCode(n>>10|55296,1023&n|56320))},K=function(){ct()},_=pt((function(t){return!0===t.disabled&&I(t,"fieldset")}),{dir:"parentNode",next:"legend"});function $(){try{return s.activeElement}catch(t){}}try{g.apply(o=l.call(R.childNodes),R.childNodes),o[R.childNodes.length].nodeType}catch(wt){g={apply:function(t,e){B.apply(t,l.call(e))},call:function(t){B.apply(t,l.call(arguments,1))}}}function tt(t,e,n,r){var i,a,o,l,c,h,p,d=e&&e.ownerDocument,v=e?e.nodeType:9;if(n=n||[],"string"!==typeof t||!t||1!==v&&9!==v&&11!==v)return n;if(!r&&(ct(e),e=e||s,u)){if(11!==v&&(c=Z.exec(t)))if(i=c[1]){if(9===v){if(!(o=e.getElementById(i)))return n;if(o.id===i)return g.call(n,o),n}else if(d&&(o=d.getElementById(i))&&tt.contains(e,o)&&o.id===i)return g.call(n,o),n}else{if(c[2])return g.apply(n,e.getElementsByTagName(t)),n;if((i=c[3])&&e.getElementsByClassName)return g.apply(n,e.getElementsByClassName(i)),n}if(!C[t+" "]&&(!f||!f.test(t))){if(p=t,d=e,1===v&&(Q.test(t)||z.test(t))){d=J.test(t)&&lt(e.parentNode)||e,d==e&&b.scope||((l=e.getAttribute("id"))?l=O.escapeSelector(l):e.setAttribute("id",l=m)),h=ht(t),a=h.length;while(a--)h[a]=(l?"#"+l:":scope")+" "+ft(h[a]);p=h.join(",")}try{return g.apply(n,d.querySelectorAll(p)),n}catch(y){C(t,!0)}finally{l===m&&e.removeAttribute("id")}}}return At(t.replace(j,"$1"),e,n,r)}function et(){var t=[];function n(r,i){return t.push(r+" ")>e.cacheLength&&delete n[t.shift()],n[r+" "]=i}return n}function nt(t){return t[m]=!0,t}function rt(t){var e=s.createElement("fieldset");try{return!!t(e)}catch(wt){return!1}finally{e.parentNode&&e.parentNode.removeChild(e),e=null}}function it(t){return function(e){return I(e,"input")&&e.type===t}}function at(t){return function(e){return(I(e,"input")||I(e,"button"))&&e.type===t}}function ot(t){return function(e){return"form"in e?e.parentNode&&!1===e.disabled?"label"in e?"label"in e.parentNode?e.parentNode.disabled===t:e.disabled===t:e.isDisabled===t||e.isDisabled!==!t&&_(e)===t:e.disabled===t:"label"in e&&e.disabled===t}}function st(t){return nt((function(e){return e=+e,nt((function(n,r){var i,a=t([],n.length,e),o=a.length;while(o--)n[i=a[o]]&&(n[i]=!(r[i]=n[i]))}))}))}function lt(t){return t&&"undefined"!==typeof t.getElementsByTagName&&t}function ct(t){var n,r=t?t.ownerDocument||t:R;return r!=s&&9===r.nodeType&&r.documentElement?(s=r,c=s.documentElement,u=!O.isXMLDoc(s),p=c.matches||c.webkitMatchesSelector||c.msMatchesSelector,R!=s&&(n=s.defaultView)&&n.top!==n&&n.addEventListener("unload",K),b.getById=rt((function(t){return c.appendChild(t).id=O.expando,!s.getElementsByName||!s.getElementsByName(O.expando).length})),b.disconnectedMatch=rt((function(t){return p.call(t,"*")})),b.scope=rt((function(){return s.querySelectorAll(":scope")})),b.cssHas=rt((function(){try{return s.querySelector(":has(*,:jqfake)"),!1}catch(wt){return!0}})),b.getById?(e.filter.ID=function(t){var e=t.replace(U,Y);return function(t){return t.getAttribute("id")===e}},e.find.ID=function(t,e){if("undefined"!==typeof e.getElementById&&u){var n=e.getElementById(t);return n?[n]:[]}}):(e.filter.ID=function(t){var e=t.replace(U,Y);return function(t){var n="undefined"!==typeof t.getAttributeNode&&t.getAttributeNode("id");return n&&n.value===e}},e.find.ID=function(t,e){if("undefined"!==typeof e.getElementById&&u){var n,r,i,a=e.getElementById(t);if(a){if(n=a.getAttributeNode("id"),n&&n.value===t)return[a];i=e.getElementsByName(t),r=0;while(a=i[r++])if(n=a.getAttributeNode("id"),n&&n.value===t)return[a]}return[]}}),e.find.TAG=function(t,e){return"undefined"!==typeof e.getElementsByTagName?e.getElementsByTagName(t):e.querySelectorAll(t)},e.find.CLASS=function(t,e){if("undefined"!==typeof e.getElementsByClassName&&u)return e.getElementsByClassName(t)},f=[],rt((function(t){var e;c.appendChild(t).innerHTML="<a id='"+m+"' href='' disabled='disabled'></a><select id='"+m+"-\r\\' disabled='disabled'><option selected=''></option></select>",t.querySelectorAll("[selected]").length||f.push("\\["+G+"*(?:value|"+k+")"),t.querySelectorAll("[id~="+m+"-]").length||f.push("~="),t.querySelectorAll("a#"+m+"+*").length||f.push(".#.+[+~]"),t.querySelectorAll(":checked").length||f.push(":checked"),e=s.createElement("input"),e.setAttribute("type","hidden"),t.appendChild(e).setAttribute("name","D"),c.appendChild(t).disabled=!0,2!==t.querySelectorAll(":disabled").length&&f.push(":enabled",":disabled"),e=s.createElement("input"),e.setAttribute("name",""),t.appendChild(e),t.querySelectorAll("[name='']").length||f.push("\\["+G+"*name"+G+"*="+G+"*(?:''|\"\")")})),b.cssHas||f.push(":has"),f=f.length&&new RegExp(f.join("|")),S=function(t,e){if(t===e)return a=!0,0;var n=!t.compareDocumentPosition-!e.compareDocumentPosition;return n||(n=(t.ownerDocument||t)==(e.ownerDocument||e)?t.compareDocumentPosition(e):1,1&n||!b.sortDetached&&e.compareDocumentPosition(t)===n?t===s||t.ownerDocument==R&&tt.contains(R,t)?-1:e===s||e.ownerDocument==R&&tt.contains(R,e)?1:i?h.call(i,t)-h.call(i,e):0:4&n?-1:1)},s):s}for(t in tt.matches=function(t,e){return tt(t,null,null,e)},tt.matchesSelector=function(t,e){if(ct(t),u&&!C[e+" "]&&(!f||!f.test(e)))try{var n=p.call(t,e);if(n||b.disconnectedMatch||t.document&&11!==t.document.nodeType)return n}catch(wt){C(e,!0)}return tt(e,s,null,[t]).length>0},tt.contains=function(t,e){return(t.ownerDocument||t)!=s&&ct(t),O.contains(t,e)},tt.attr=function(t,n){(t.ownerDocument||t)!=s&&ct(t);var r=e.attrHandle[n.toLowerCase()],i=r&&d.call(e.attrHandle,n.toLowerCase())?r(t,n,!u):void 0;return void 0!==i?i:t.getAttribute(n)},tt.error=function(t){throw new Error("Syntax error, unrecognized expression: "+t)},O.uniqueSort=function(t){var e,n=[],r=0,o=0;if(a=!b.sortStable,i=!b.sortStable&&l.call(t,0),D.call(t,S),a){while(e=t[o++])e===t[o]&&(r=n.push(o));while(r--)L.call(t,n[r],1)}return i=null,t},O.fn.uniqueSort=function(){return this.pushStack(O.uniqueSort(l.apply(this)))},e=O.expr={cacheLength:50,createPseudo:nt,match:q,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(t){return t[1]=t[1].replace(U,Y),t[3]=(t[3]||t[4]||t[5]||"").replace(U,Y),"~="===t[2]&&(t[3]=" "+t[3]+" "),t.slice(0,4)},CHILD:function(t){return t[1]=t[1].toLowerCase(),"nth"===t[1].slice(0,3)?(t[3]||tt.error(t[0]),t[4]=+(t[4]?t[5]+(t[6]||1):2*("even"===t[3]||"odd"===t[3])),t[5]=+(t[7]+t[8]||"odd"===t[3])):t[3]&&tt.error(t[0]),t},PSEUDO:function(t){var e,n=!t[6]&&t[2];return q.CHILD.test(t[0])?null:(t[3]?t[2]=t[4]||t[5]||"":n&&W.test(n)&&(e=ht(n,!0))&&(e=n.indexOf(")",n.length-e)-n.length)&&(t[0]=t[0].slice(0,e),t[2]=n.slice(0,e)),t.slice(0,3))}},filter:{TAG:function(t){var e=t.replace(U,Y).toLowerCase();return"*"===t?function(){return!0}:function(t){return I(t,e)}},CLASS:function(t){var e=x[t+" "];return e||(e=new RegExp("(^|"+G+")"+t+"("+G+"|$)"))&&x(t,(function(t){return e.test("string"===typeof t.className&&t.className||"undefined"!==typeof t.getAttribute&&t.getAttribute("class")||"")}))},ATTR:function(t,e,n){return function(r){var i=tt.attr(r,t);return null==i?"!="===e:!e||(i+="","="===e?i===n:"!="===e?i!==n:"^="===e?n&&0===i.indexOf(n):"*="===e?n&&i.indexOf(n)>-1:"$="===e?n&&i.slice(-n.length)===n:"~="===e?(" "+i.replace(V," ")+" ").indexOf(n)>-1:"|="===e&&(i===n||i.slice(0,n.length+1)===n+"-"))}},CHILD:function(t,e,n,r,i){var a="nth"!==t.slice(0,3),o="last"!==t.slice(-4),s="of-type"===e;return 1===r&&0===i?function(t){return!!t.parentNode}:function(e,n,l){var c,u,h,f,p,d=a!==o?"nextSibling":"previousSibling",g=e.parentNode,b=s&&e.nodeName.toLowerCase(),y=!l&&!s,x=!1;if(g){if(a){while(d){h=e;while(h=h[d])if(s?I(h,b):1===h.nodeType)return!1;p=d="only"===t&&!p&&"nextSibling"}return!0}if(p=[o?g.firstChild:g.lastChild],o&&y){u=g[m]||(g[m]={}),c=u[t]||[],f=c[0]===v&&c[1],x=f&&c[2],h=f&&g.childNodes[f];while(h=++f&&h&&h[d]||(x=f=0)||p.pop())if(1===h.nodeType&&++x&&h===e){u[t]=[v,f,x];break}}else if(y&&(u=e[m]||(e[m]={}),c=u[t]||[],f=c[0]===v&&c[1],x=f),!1===x)while(h=++f&&h&&h[d]||(x=f=0)||p.pop())if((s?I(h,b):1===h.nodeType)&&++x&&(y&&(u=h[m]||(h[m]={}),u[t]=[v,x]),h===e))break;return x-=i,x===r||x%r===0&&x/r>=0}}},PSEUDO:function(t,n){var r,i=e.pseudos[t]||e.setFilters[t.toLowerCase()]||tt.error("unsupported pseudo: "+t);return i[m]?i(n):i.length>1?(r=[t,t,"",n],e.setFilters.hasOwnProperty(t.toLowerCase())?nt((function(t,e){var r,a=i(t,n),o=a.length;while(o--)r=h.call(t,a[o]),t[r]=!(e[r]=a[o])})):function(t){return i(t,0,r)}):i}},pseudos:{not:nt((function(t){var e=[],n=[],r=xt(t.replace(j,"$1"));return r[m]?nt((function(t,e,n,i){var a,o=r(t,null,i,[]),s=t.length;while(s--)(a=o[s])&&(t[s]=!(e[s]=a))})):function(t,i,a){return e[0]=t,r(e,null,a,n),e[0]=null,!n.pop()}})),has:nt((function(t){return function(e){return tt(t,e).length>0}})),contains:nt((function(t){return t=t.replace(U,Y),function(e){return(e.textContent||O.text(e)).indexOf(t)>-1}})),lang:nt((function(t){return F.test(t||"")||tt.error("unsupported lang: "+t),t=t.replace(U,Y).toLowerCase(),function(e){var n;do{if(n=u?e.lang:e.getAttribute("xml:lang")||e.getAttribute("lang"))return n=n.toLowerCase(),n===t||0===n.indexOf(t+"-")}while((e=e.parentNode)&&1===e.nodeType);return!1}})),target:function(t){var e=n.location&&n.location.hash;return e&&e.slice(1)===t.id},root:function(t){return t===c},focus:function(t){return t===$()&&s.hasFocus()&&!!(t.type||t.href||~t.tabIndex)},enabled:ot(!1),disabled:ot(!0),checked:function(t){return I(t,"input")&&!!t.checked||I(t,"option")&&!!t.selected},selected:function(t){return t.parentNode&&t.parentNode.selectedIndex,!0===t.selected},empty:function(t){for(t=t.firstChild;t;t=t.nextSibling)if(t.nodeType<6)return!1;return!0},parent:function(t){return!e.pseudos.empty(t)},header:function(t){return H.test(t.nodeName)},input:function(t){return X.test(t.nodeName)},button:function(t){return I(t,"input")&&"button"===t.type||I(t,"button")},text:function(t){var e;return I(t,"input")&&"text"===t.type&&(null==(e=t.getAttribute("type"))||"text"===e.toLowerCase())},first:st((function(){return[0]})),last:st((function(t,e){return[e-1]})),eq:st((function(t,e,n){return[n<0?n+e:n]})),even:st((function(t,e){for(var n=0;n<e;n+=2)t.push(n);return t})),odd:st((function(t,e){for(var n=1;n<e;n+=2)t.push(n);return t})),lt:st((function(t,e,n){var r;for(r=n<0?n+e:n>e?e:n;--r>=0;)t.push(r);return t})),gt:st((function(t,e,n){for(var r=n<0?n+e:n;++r<e;)t.push(r);return t}))}},e.pseudos.nth=e.pseudos.eq,{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})e.pseudos[t]=it(t);for(t in{submit:!0,reset:!0})e.pseudos[t]=at(t);function ut(){}function ht(t,n){var r,i,a,o,s,l,c,u=A[t+" "];if(u)return n?0:u.slice(0);s=t,l=[],c=e.preFilter;while(s){for(o in r&&!(i=P.exec(s))||(i&&(s=s.slice(i[0].length)||s),l.push(a=[])),r=!1,(i=z.exec(s))&&(r=i.shift(),a.push({value:r,type:i[0].replace(j," ")}),s=s.slice(r.length)),e.filter)!(i=q[o].exec(s))||c[o]&&!(i=c[o](i))||(r=i.shift(),a.push({value:r,type:o,matches:i}),s=s.slice(r.length));if(!r)break}return n?s.length:s?tt.error(t):A(t,l).slice(0)}function ft(t){for(var e=0,n=t.length,r="";e<n;e++)r+=t[e].value;return r}function pt(t,e,n){var r=e.dir,i=e.next,a=i||r,o=n&&"parentNode"===a,s=y++;return e.first?function(e,n,i){while(e=e[r])if(1===e.nodeType||o)return t(e,n,i);return!1}:function(e,n,l){var c,u,h=[v,s];if(l){while(e=e[r])if((1===e.nodeType||o)&&t(e,n,l))return!0}else while(e=e[r])if(1===e.nodeType||o)if(u=e[m]||(e[m]={}),i&&I(e,i))e=e[r]||e;else{if((c=u[a])&&c[0]===v&&c[1]===s)return h[2]=c[2];if(u[a]=h,h[2]=t(e,n,l))return!0}return!1}}function dt(t){return t.length>1?function(e,n,r){var i=t.length;while(i--)if(!t[i](e,n,r))return!1;return!0}:t[0]}function gt(t,e,n){for(var r=0,i=e.length;r<i;r++)tt(t,e[r],n);return n}function mt(t,e,n,r,i){for(var a,o=[],s=0,l=t.length,c=null!=e;s<l;s++)(a=t[s])&&(n&&!n(a,r,i)||(o.push(a),c&&e.push(s)));return o}function bt(t,e,n,r,i,a){return r&&!r[m]&&(r=bt(r)),i&&!i[m]&&(i=bt(i,a)),nt((function(a,o,s,l){var c,u,f,p,d=[],m=[],b=o.length,v=a||gt(e||"*",s.nodeType?[s]:s,[]),y=!t||!a&&e?v:mt(v,d,t,s,l);if(n?(p=i||(a?t:b||r)?[]:o,n(y,p,s,l)):p=y,r){c=mt(p,m),r(c,[],s,l),u=c.length;while(u--)(f=c[u])&&(p[m[u]]=!(y[m[u]]=f))}if(a){if(i||t){if(i){c=[],u=p.length;while(u--)(f=p[u])&&c.push(y[u]=f);i(null,p=[],c,l)}u=p.length;while(u--)(f=p[u])&&(c=i?h.call(a,f):d[u])>-1&&(a[c]=!(o[c]=f))}}else p=mt(p===o?p.splice(b,p.length):p),i?i(null,o,p,l):g.apply(o,p)}))}function vt(t){for(var n,i,a,o=t.length,s=e.relative[t[0].type],l=s||e.relative[" "],c=s?1:0,u=pt((function(t){return t===n}),l,!0),f=pt((function(t){return h.call(n,t)>-1}),l,!0),p=[function(t,e,i){var a=!s&&(i||e!=r)||((n=e).nodeType?u(t,e,i):f(t,e,i));return n=null,a}];c<o;c++)if(i=e.relative[t[c].type])p=[pt(dt(p),i)];else{if(i=e.filter[t[c].type].apply(null,t[c].matches),i[m]){for(a=++c;a<o;a++)if(e.relative[t[a].type])break;return bt(c>1&&dt(p),c>1&&ft(t.slice(0,c-1).concat({value:" "===t[c-2].type?"*":""})).replace(j,"$1"),i,c<a&&vt(t.slice(c,a)),a<o&&vt(t=t.slice(a)),a<o&&ft(t))}p.push(i)}return dt(p)}function yt(t,n){var i=n.length>0,a=t.length>0,o=function(o,l,c,h,f){var p,d,m,b=0,y="0",x=o&&[],A=[],w=r,C=o||a&&e.find.TAG("*",f),S=v+=null==w?1:Math.random()||.1,k=C.length;for(f&&(r=l==s||l||f);y!==k&&null!=(p=C[y]);y++){if(a&&p){d=0,l||p.ownerDocument==s||(ct(p),c=!u);while(m=t[d++])if(m(p,l||s,c)){g.call(h,p);break}f&&(v=S)}i&&((p=!m&&p)&&b--,o&&x.push(p))}if(b+=y,i&&y!==b){d=0;while(m=n[d++])m(x,A,l,c);if(o){if(b>0)while(y--)x[y]||A[y]||(A[y]=E.call(h));A=mt(A)}g.apply(h,A),f&&!o&&A.length>0&&b+n.length>1&&O.uniqueSort(h)}return f&&(v=S,r=w),x};return i?nt(o):o}function xt(t,e){var n,r=[],i=[],a=w[t+" "];if(!a){e||(e=ht(t)),n=e.length;while(n--)a=vt(e[n]),a[m]?r.push(a):i.push(a);a=w(t,yt(i,r)),a.selector=t}return a}function At(t,n,r,i){var a,o,s,l,c,h="function"===typeof t&&t,f=!i&&ht(t=h.selector||t);if(r=r||[],1===f.length){if(o=f[0]=f[0].slice(0),o.length>2&&"ID"===(s=o[0]).type&&9===n.nodeType&&u&&e.relative[o[1].type]){if(n=(e.find.ID(s.matches[0].replace(U,Y),n)||[])[0],!n)return r;h&&(n=n.parentNode),t=t.slice(o.shift().value.length)}a=q.needsContext.test(t)?0:o.length;while(a--){if(s=o[a],e.relative[l=s.type])break;if((c=e.find[l])&&(i=c(s.matches[0].replace(U,Y),J.test(o[0].type)&&lt(n.parentNode)||n))){if(o.splice(a,1),t=i.length&&ft(o),!t)return g.apply(r,i),r;break}}}return(h||xt(t,f))(i,n,!u,r,!n||J.test(t)&&lt(n.parentNode)||n),r}ut.prototype=e.filters=e.pseudos,e.setFilters=new ut,b.sortStable=m.split("").sort(S).join("")===m,ct(),b.sortDetached=rt((function(t){return 1&t.compareDocumentPosition(s.createElement("fieldset"))})),O.find=tt,O.expr[":"]=O.expr.pseudos,O.unique=O.uniqueSort,tt.compile=xt,tt.select=At,tt.setDocument=ct,tt.escape=O.escapeSelector,tt.getText=O.text,tt.isXML=O.isXMLDoc,tt.selectors=O.expr,tt.support=O.support,tt.uniqueSort=O.uniqueSort})();var V=function(t,e,n){var r=[],i=void 0!==n;while((t=t[e])&&9!==t.nodeType)if(1===t.nodeType){if(i&&O(t).is(n))break;r.push(t)}return r},P=function(t,e){for(var n=[];t;t=t.nextSibling)1===t.nodeType&&t!==e&&n.push(t);return n},z=O.expr.match.needsContext,Q=/^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i;function W(t,e,n){return v(e)?O.grep(t,(function(t,r){return!!e.call(t,r,t)!==n})):e.nodeType?O.grep(t,(function(t){return t===e!==n})):"string"!==typeof e?O.grep(t,(function(t){return h.call(e,t)>-1!==n})):O.filter(e,t,n)}O.filter=function(t,e,n){var r=e[0];return n&&(t=":not("+t+")"),1===e.length&&1===r.nodeType?O.find.matchesSelector(r,t)?[r]:[]:O.find.matches(t,O.grep(e,(function(t){return 1===t.nodeType})))},O.fn.extend({find:function(t){var e,n,r=this.length,i=this;if("string"!==typeof t)return this.pushStack(O(t).filter((function(){for(e=0;e<r;e++)if(O.contains(i[e],this))return!0})));for(n=this.pushStack([]),e=0;e<r;e++)O.find(t,i[e],n);return r>1?O.uniqueSort(n):n},filter:function(t){return this.pushStack(W(this,t||[],!1))},not:function(t){return this.pushStack(W(this,t||[],!0))},is:function(t){return!!W(this,"string"===typeof t&&z.test(t)?O(t):t||[],!1).length}});var F,q=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/,X=O.fn.init=function(t,e,n){var r,i;if(!t)return this;if(n=n||F,"string"===typeof t){if(r="<"===t[0]&&">"===t[t.length-1]&&t.length>=3?[null,t,null]:q.exec(t),!r||!r[1]&&e)return!e||e.jquery?(e||n).find(t):this.constructor(e).find(t);if(r[1]){if(e=e instanceof O?e[0]:e,O.merge(this,O.parseHTML(r[1],e&&e.nodeType?e.ownerDocument||e:x,!0)),Q.test(r[1])&&O.isPlainObject(e))for(r in e)v(this[r])?this[r](e[r]):this.attr(r,e[r]);return this}return i=x.getElementById(r[2]),i&&(this[0]=i,this.length=1),this}return t.nodeType?(this[0]=t,this.length=1,this):v(t)?void 0!==n.ready?n.ready(t):t(O):O.makeArray(t,this)};X.prototype=O.fn,F=O(x);var H=/^(?:parents|prev(?:Until|All))/,Z={children:!0,contents:!0,next:!0,prev:!0};function J(t,e){while((t=t[e])&&1!==t.nodeType);return t}O.fn.extend({has:function(t){var e=O(t,this),n=e.length;return this.filter((function(){for(var t=0;t<n;t++)if(O.contains(this,e[t]))return!0}))},closest:function(t,e){var n,r=0,i=this.length,a=[],o="string"!==typeof t&&O(t);if(!z.test(t))for(;r<i;r++)for(n=this[r];n&&n!==e;n=n.parentNode)if(n.nodeType<11&&(o?o.index(n)>-1:1===n.nodeType&&O.find.matchesSelector(n,t))){a.push(n);break}return this.pushStack(a.length>1?O.uniqueSort(a):a)},index:function(t){return t?"string"===typeof t?h.call(O(t),this[0]):h.call(this,t.jquery?t[0]:t):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(t,e){return this.pushStack(O.uniqueSort(O.merge(this.get(),O(t,e))))},addBack:function(t){return this.add(null==t?this.prevObject:this.prevObject.filter(t))}}),O.each({parent:function(t){var e=t.parentNode;return e&&11!==e.nodeType?e:null},parents:function(t){return V(t,"parentNode")},parentsUntil:function(t,e,n){return V(t,"parentNode",n)},next:function(t){return J(t,"nextSibling")},prev:function(t){return J(t,"previousSibling")},nextAll:function(t){return V(t,"nextSibling")},prevAll:function(t){return V(t,"previousSibling")},nextUntil:function(t,e,n){return V(t,"nextSibling",n)},prevUntil:function(t,e,n){return V(t,"previousSibling",n)},siblings:function(t){return P((t.parentNode||{}).firstChild,t)},children:function(t){return P(t.firstChild)},contents:function(t){return null!=t.contentDocument&&s(t.contentDocument)?t.contentDocument:(I(t,"template")&&(t=t.content||t),O.merge([],t.childNodes))}},(function(t,e){O.fn[t]=function(n,r){var i=O.map(this,e,n);return"Until"!==t.slice(-5)&&(r=n),r&&"string"===typeof r&&(i=O.filter(r,i)),this.length>1&&(Z[t]||O.uniqueSort(i),H.test(t)&&i.reverse()),this.pushStack(i)}}));var U=/[^\x20\t\r\n\f]+/g;function Y(t){var e={};return O.each(t.match(U)||[],(function(t,n){e[n]=!0})),e}function K(t){return t}function _(t){throw t}function $(t,e,n,r){var i;try{t&&v(i=t.promise)?i.call(t).done(e).fail(n):t&&v(i=t.then)?i.call(t,e,n):e.apply(void 0,[t].slice(r))}catch(t){n.apply(void 0,[t])}}O.Callbacks=function(t){t="string"===typeof t?Y(t):O.extend({},t);var e,n,r,i,a=[],o=[],s=-1,l=function(){for(i=i||t.once,r=e=!0;o.length;s=-1){n=o.shift();while(++s<a.length)!1===a[s].apply(n[0],n[1])&&t.stopOnFalse&&(s=a.length,n=!1)}t.memory||(n=!1),e=!1,i&&(a=n?[]:"")},c={add:function(){return a&&(n&&!e&&(s=a.length-1,o.push(n)),function e(n){O.each(n,(function(n,r){v(r)?t.unique&&c.has(r)||a.push(r):r&&r.length&&"string"!==C(r)&&e(r)}))}(arguments),n&&!e&&l()),this},remove:function(){return O.each(arguments,(function(t,e){var n;while((n=O.inArray(e,a,n))>-1)a.splice(n,1),n<=s&&s--})),this},has:function(t){return t?O.inArray(t,a)>-1:a.length>0},empty:function(){return a&&(a=[]),this},disable:function(){return i=o=[],a=n="",this},disabled:function(){return!a},lock:function(){return i=o=[],n||e||(a=n=""),this},locked:function(){return!!i},fireWith:function(t,n){return i||(n=n||[],n=[t,n.slice?n.slice():n],o.push(n),e||l()),this},fire:function(){return c.fireWith(this,arguments),this},fired:function(){return!!r}};return c},O.extend({Deferred:function(t){var e=[["notify","progress",O.Callbacks("memory"),O.Callbacks("memory"),2],["resolve","done",O.Callbacks("once memory"),O.Callbacks("once memory"),0,"resolved"],["reject","fail",O.Callbacks("once memory"),O.Callbacks("once memory"),1,"rejected"]],r="pending",i={state:function(){return r},always:function(){return a.done(arguments).fail(arguments),this},catch:function(t){return i.then(null,t)},pipe:function(){var t=arguments;return O.Deferred((function(n){O.each(e,(function(e,r){var i=v(t[r[4]])&&t[r[4]];a[r[1]]((function(){var t=i&&i.apply(this,arguments);t&&v(t.promise)?t.promise().progress(n.notify).done(n.resolve).fail(n.reject):n[r[0]+"With"](this,i?[t]:arguments)}))})),t=null})).promise()},then:function(t,r,i){var a=0;function o(t,e,r,i){return function(){var s=this,l=arguments,c=function(){var n,c;if(!(t<a)){if(n=r.apply(s,l),n===e.promise())throw new TypeError("Thenable self-resolution");c=n&&("object"===typeof n||"function"===typeof n)&&n.then,v(c)?i?c.call(n,o(a,e,K,i),o(a,e,_,i)):(a++,c.call(n,o(a,e,K,i),o(a,e,_,i),o(a,e,K,e.notifyWith))):(r!==K&&(s=void 0,l=[n]),(i||e.resolveWith)(s,l))}},u=i?c:function(){try{c()}catch(n){O.Deferred.exceptionHook&&O.Deferred.exceptionHook(n,u.error),t+1>=a&&(r!==_&&(s=void 0,l=[n]),e.rejectWith(s,l))}};t?u():(O.Deferred.getErrorHook?u.error=O.Deferred.getErrorHook():O.Deferred.getStackHook&&(u.error=O.Deferred.getStackHook()),n.setTimeout(u))}}return O.Deferred((function(n){e[0][3].add(o(0,n,v(i)?i:K,n.notifyWith)),e[1][3].add(o(0,n,v(t)?t:K)),e[2][3].add(o(0,n,v(r)?r:_))})).promise()},promise:function(t){return null!=t?O.extend(t,i):i}},a={};return O.each(e,(function(t,n){var o=n[2],s=n[5];i[n[1]]=o.add,s&&o.add((function(){r=s}),e[3-t][2].disable,e[3-t][3].disable,e[0][2].lock,e[0][3].lock),o.add(n[3].fire),a[n[0]]=function(){return a[n[0]+"With"](this===a?void 0:this,arguments),this},a[n[0]+"With"]=o.fireWith})),i.promise(a),t&&t.call(a,a),a},when:function(t){var e=arguments.length,n=e,r=Array(n),i=l.call(arguments),a=O.Deferred(),o=function(t){return function(n){r[t]=this,i[t]=arguments.length>1?l.call(arguments):n,--e||a.resolveWith(r,i)}};if(e<=1&&($(t,a.done(o(n)).resolve,a.reject,!e),"pending"===a.state()||v(i[n]&&i[n].then)))return a.then();while(n--)$(i[n],o(n),a.reject);return a.promise()}});var tt=/^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/;O.Deferred.exceptionHook=function(t,e){n.console&&n.console.warn&&t&&tt.test(t.name)&&n.console.warn("jQuery.Deferred exception: "+t.message,t.stack,e)},O.readyException=function(t){n.setTimeout((function(){throw t}))};var et=O.Deferred();function nt(){x.removeEventListener("DOMContentLoaded",nt),n.removeEventListener("load",nt),O.ready()}O.fn.ready=function(t){return et.then(t).catch((function(t){O.readyException(t)})),this},O.extend({isReady:!1,readyWait:1,ready:function(t){(!0===t?--O.readyWait:O.isReady)||(O.isReady=!0,!0!==t&&--O.readyWait>0||et.resolveWith(x,[O]))}}),O.ready.then=et.then,"complete"===x.readyState||"loading"!==x.readyState&&!x.documentElement.doScroll?n.setTimeout(O.ready):(x.addEventListener("DOMContentLoaded",nt),n.addEventListener("load",nt));var rt=function(t,e,n,r,i,a,o){var s=0,l=t.length,c=null==n;if("object"===C(n))for(s in i=!0,n)rt(t,e,s,n[s],!0,a,o);else if(void 0!==r&&(i=!0,v(r)||(o=!0),c&&(o?(e.call(t,r),e=null):(c=e,e=function(t,e,n){return c.call(O(t),n)})),e))for(;s<l;s++)e(t[s],n,o?r:r.call(t[s],s,e(t[s],n)));return i?t:c?e.call(t):l?e(t[0],n):a},it=/^-ms-/,at=/-([a-z])/g;function ot(t,e){return e.toUpperCase()}function st(t){return t.replace(it,"ms-").replace(at,ot)}var lt=function(t){return 1===t.nodeType||9===t.nodeType||!+t.nodeType};function ct(){this.expando=O.expando+ct.uid++}ct.uid=1,ct.prototype={cache:function(t){var e=t[this.expando];return e||(e={},lt(t)&&(t.nodeType?t[this.expando]=e:Object.defineProperty(t,this.expando,{value:e,configurable:!0}))),e},set:function(t,e,n){var r,i=this.cache(t);if("string"===typeof e)i[st(e)]=n;else for(r in e)i[st(r)]=e[r];return i},get:function(t,e){return void 0===e?this.cache(t):t[this.expando]&&t[this.expando][st(e)]},access:function(t,e,n){return void 0===e||e&&"string"===typeof e&&void 0===n?this.get(t,e):(this.set(t,e,n),void 0!==n?n:e)},remove:function(t,e){var n,r=t[this.expando];if(void 0!==r){if(void 0!==e){Array.isArray(e)?e=e.map(st):(e=st(e),e=e in r?[e]:e.match(U)||[]),n=e.length;while(n--)delete r[e[n]]}(void 0===e||O.isEmptyObject(r))&&(t.nodeType?t[this.expando]=void 0:delete t[this.expando])}},hasData:function(t){var e=t[this.expando];return void 0!==e&&!O.isEmptyObject(e)}};var ut=new ct,ht=new ct,ft=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,pt=/[A-Z]/g;function dt(t){return"true"===t||"false"!==t&&("null"===t?null:t===+t+""?+t:ft.test(t)?JSON.parse(t):t)}function gt(t,e,n){var r;if(void 0===n&&1===t.nodeType)if(r="data-"+e.replace(pt,"-$&").toLowerCase(),n=t.getAttribute(r),"string"===typeof n){try{n=dt(n)}catch(i){}ht.set(t,e,n)}else n=void 0;return n}O.extend({hasData:function(t){return ht.hasData(t)||ut.hasData(t)},data:function(t,e,n){return ht.access(t,e,n)},removeData:function(t,e){ht.remove(t,e)},_data:function(t,e,n){return ut.access(t,e,n)},_removeData:function(t,e){ut.remove(t,e)}}),O.fn.extend({data:function(t,e){var n,r,i,a=this[0],o=a&&a.attributes;if(void 0===t){if(this.length&&(i=ht.get(a),1===a.nodeType&&!ut.get(a,"hasDataAttrs"))){n=o.length;while(n--)o[n]&&(r=o[n].name,0===r.indexOf("data-")&&(r=st(r.slice(5)),gt(a,r,i[r])));ut.set(a,"hasDataAttrs",!0)}return i}return"object"===typeof t?this.each((function(){ht.set(this,t)})):rt(this,(function(e){var n;if(a&&void 0===e)return n=ht.get(a,t),void 0!==n?n:(n=gt(a,t),void 0!==n?n:void 0);this.each((function(){ht.set(this,t,e)}))}),null,e,arguments.length>1,null,!0)},removeData:function(t){return this.each((function(){ht.remove(this,t)}))}}),O.extend({queue:function(t,e,n){var r;if(t)return e=(e||"fx")+"queue",r=ut.get(t,e),n&&(!r||Array.isArray(n)?r=ut.access(t,e,O.makeArray(n)):r.push(n)),r||[]},dequeue:function(t,e){e=e||"fx";var n=O.queue(t,e),r=n.length,i=n.shift(),a=O._queueHooks(t,e),o=function(){O.dequeue(t,e)};"inprogress"===i&&(i=n.shift(),r--),i&&("fx"===e&&n.unshift("inprogress"),delete a.stop,i.call(t,o,a)),!r&&a&&a.empty.fire()},_queueHooks:function(t,e){var n=e+"queueHooks";return ut.get(t,n)||ut.access(t,n,{empty:O.Callbacks("once memory").add((function(){ut.remove(t,[e+"queue",n])}))})}}),O.fn.extend({queue:function(t,e){var n=2;return"string"!==typeof t&&(e=t,t="fx",n--),arguments.length<n?O.queue(this[0],t):void 0===e?this:this.each((function(){var n=O.queue(this,t,e);O._queueHooks(this,t),"fx"===t&&"inprogress"!==n[0]&&O.dequeue(this,t)}))},dequeue:function(t){return this.each((function(){O.dequeue(this,t)}))},clearQueue:function(t){return this.queue(t||"fx",[])},promise:function(t,e){var n,r=1,i=O.Deferred(),a=this,o=this.length,s=function(){--r||i.resolveWith(a,[a])};"string"!==typeof t&&(e=t,t=void 0),t=t||"fx";while(o--)n=ut.get(a[o],t+"queueHooks"),n&&n.empty&&(r++,n.empty.add(s));return s(),i.promise(e)}});var mt=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,bt=new RegExp("^(?:([+-])=|)("+mt+")([a-z%]*)$","i"),vt=["Top","Right","Bottom","Left"],yt=x.documentElement,xt=function(t){return O.contains(t.ownerDocument,t)},At={composed:!0};yt.getRootNode&&(xt=function(t){return O.contains(t.ownerDocument,t)||t.getRootNode(At)===t.ownerDocument});var wt=function(t,e){return t=e||t,"none"===t.style.display||""===t.style.display&&xt(t)&&"none"===O.css(t,"display")};function Ct(t,e,n,r){var i,a,o=20,s=r?function(){return r.cur()}:function(){return O.css(t,e,"")},l=s(),c=n&&n[3]||(O.cssNumber[e]?"":"px"),u=t.nodeType&&(O.cssNumber[e]||"px"!==c&&+l)&&bt.exec(O.css(t,e));if(u&&u[3]!==c){l/=2,c=c||u[3],u=+l||1;while(o--)O.style(t,e,u+c),(1-a)*(1-(a=s()/l||.5))<=0&&(o=0),u/=a;u*=2,O.style(t,e,u+c),n=n||[]}return n&&(u=+u||+l||0,i=n[1]?u+(n[1]+1)*n[2]:+n[2],r&&(r.unit=c,r.start=u,r.end=i)),i}var St={};function kt(t){var e,n=t.ownerDocument,r=t.nodeName,i=St[r];return i||(e=n.body.appendChild(n.createElement(r)),i=O.css(e,"display"),e.parentNode.removeChild(e),"none"===i&&(i="block"),St[r]=i,i)}function Ot(t,e){for(var n,r,i=[],a=0,o=t.length;a<o;a++)r=t[a],r.style&&(n=r.style.display,e?("none"===n&&(i[a]=ut.get(r,"display")||null,i[a]||(r.style.display="")),""===r.style.display&&wt(r)&&(i[a]=kt(r))):"none"!==n&&(i[a]="none",ut.set(r,"display",n)));for(a=0;a<o;a++)null!=i[a]&&(t[a].style.display=i[a]);return t}O.fn.extend({show:function(){return Ot(this,!0)},hide:function(){return Ot(this)},toggle:function(t){return"boolean"===typeof t?t?this.show():this.hide():this.each((function(){wt(this)?O(this).show():O(this).hide()}))}});var Tt=/^(?:checkbox|radio)$/i,It=/<([a-z][^\/\0>\x20\t\r\n\f]*)/i,Et=/^$|^module$|\/(?:java|ecma)script/i;(function(){var t=x.createDocumentFragment(),e=t.appendChild(x.createElement("div")),n=x.createElement("input");n.setAttribute("type","radio"),n.setAttribute("checked","checked"),n.setAttribute("name","t"),e.appendChild(n),b.checkClone=e.cloneNode(!0).cloneNode(!0).lastChild.checked,e.innerHTML="<textarea>x</textarea>",b.noCloneChecked=!!e.cloneNode(!0).lastChild.defaultValue,e.innerHTML="<option></option>",b.option=!!e.lastChild})();var Dt={thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};function Lt(t,e){var n;return n="undefined"!==typeof t.getElementsByTagName?t.getElementsByTagName(e||"*"):"undefined"!==typeof t.querySelectorAll?t.querySelectorAll(e||"*"):[],void 0===e||e&&I(t,e)?O.merge([t],n):n}function Gt(t,e){for(var n=0,r=t.length;n<r;n++)ut.set(t[n],"globalEval",!e||ut.get(e[n],"globalEval"))}Dt.tbody=Dt.tfoot=Dt.colgroup=Dt.caption=Dt.thead,Dt.th=Dt.td,b.option||(Dt.optgroup=Dt.option=[1,"<select multiple='multiple'>","</select>"]);var jt=/<|&#?\w+;/;function Nt(t,e,n,r,i){for(var a,o,s,l,c,u,h=e.createDocumentFragment(),f=[],p=0,d=t.length;p<d;p++)if(a=t[p],a||0===a)if("object"===C(a))O.merge(f,a.nodeType?[a]:a);else if(jt.test(a)){o=o||h.appendChild(e.createElement("div")),s=(It.exec(a)||["",""])[1].toLowerCase(),l=Dt[s]||Dt._default,o.innerHTML=l[1]+O.htmlPrefilter(a)+l[2],u=l[0];while(u--)o=o.lastChild;O.merge(f,o.childNodes),o=h.firstChild,o.textContent=""}else f.push(e.createTextNode(a));h.textContent="",p=0;while(a=f[p++])if(r&&O.inArray(a,r)>-1)i&&i.push(a);else if(c=xt(a),o=Lt(h.appendChild(a),"script"),c&&Gt(o),n){u=0;while(a=o[u++])Et.test(a.type||"")&&n.push(a)}return h}var Mt=/^([^.]*)(?:\.(.+)|)/;function Rt(){return!0}function Bt(){return!1}function Vt(t,e,n,r,i,a){var o,s;if("object"===typeof e){for(s in"string"!==typeof n&&(r=r||n,n=void 0),e)Vt(t,s,n,r,e[s],a);return t}if(null==r&&null==i?(i=n,r=n=void 0):null==i&&("string"===typeof n?(i=r,r=void 0):(i=r,r=n,n=void 0)),!1===i)i=Bt;else if(!i)return t;return 1===a&&(o=i,i=function(t){return O().off(t),o.apply(this,arguments)},i.guid=o.guid||(o.guid=O.guid++)),t.each((function(){O.event.add(this,e,i,r,n)}))}function Pt(t,e,n){n?(ut.set(t,e,!1),O.event.add(t,e,{namespace:!1,handler:function(t){var n,r=ut.get(this,e);if(1&t.isTrigger&&this[e]){if(r)(O.event.special[e]||{}).delegateType&&t.stopPropagation();else if(r=l.call(arguments),ut.set(this,e,r),this[e](),n=ut.get(this,e),ut.set(this,e,!1),r!==n)return t.stopImmediatePropagation(),t.preventDefault(),n}else r&&(ut.set(this,e,O.event.trigger(r[0],r.slice(1),this)),t.stopPropagation(),t.isImmediatePropagationStopped=Rt)}})):void 0===ut.get(t,e)&&O.event.add(t,e,Rt)}O.event={global:{},add:function(t,e,n,r,i){var a,o,s,l,c,u,h,f,p,d,g,m=ut.get(t);if(lt(t)){n.handler&&(a=n,n=a.handler,i=a.selector),i&&O.find.matchesSelector(yt,i),n.guid||(n.guid=O.guid++),(l=m.events)||(l=m.events=Object.create(null)),(o=m.handle)||(o=m.handle=function(e){return"undefined"!==typeof O&&O.event.triggered!==e.type?O.event.dispatch.apply(t,arguments):void 0}),e=(e||"").match(U)||[""],c=e.length;while(c--)s=Mt.exec(e[c])||[],p=g=s[1],d=(s[2]||"").split(".").sort(),p&&(h=O.event.special[p]||{},p=(i?h.delegateType:h.bindType)||p,h=O.event.special[p]||{},u=O.extend({type:p,origType:g,data:r,handler:n,guid:n.guid,selector:i,needsContext:i&&O.expr.match.needsContext.test(i),namespace:d.join(".")},a),(f=l[p])||(f=l[p]=[],f.delegateCount=0,h.setup&&!1!==h.setup.call(t,r,d,o)||t.addEventListener&&t.addEventListener(p,o)),h.add&&(h.add.call(t,u),u.handler.guid||(u.handler.guid=n.guid)),i?f.splice(f.delegateCount++,0,u):f.push(u),O.event.global[p]=!0)}},remove:function(t,e,n,r,i){var a,o,s,l,c,u,h,f,p,d,g,m=ut.hasData(t)&&ut.get(t);if(m&&(l=m.events)){e=(e||"").match(U)||[""],c=e.length;while(c--)if(s=Mt.exec(e[c])||[],p=g=s[1],d=(s[2]||"").split(".").sort(),p){h=O.event.special[p]||{},p=(r?h.delegateType:h.bindType)||p,f=l[p]||[],s=s[2]&&new RegExp("(^|\\.)"+d.join("\\.(?:.*\\.|)")+"(\\.|$)"),o=a=f.length;while(a--)u=f[a],!i&&g!==u.origType||n&&n.guid!==u.guid||s&&!s.test(u.namespace)||r&&r!==u.selector&&("**"!==r||!u.selector)||(f.splice(a,1),u.selector&&f.delegateCount--,h.remove&&h.remove.call(t,u));o&&!f.length&&(h.teardown&&!1!==h.teardown.call(t,d,m.handle)||O.removeEvent(t,p,m.handle),delete l[p])}else for(p in l)O.event.remove(t,p+e[c],n,r,!0);O.isEmptyObject(l)&&ut.remove(t,"handle events")}},dispatch:function(t){var e,n,r,i,a,o,s=new Array(arguments.length),l=O.event.fix(t),c=(ut.get(this,"events")||Object.create(null))[l.type]||[],u=O.event.special[l.type]||{};for(s[0]=l,e=1;e<arguments.length;e++)s[e]=arguments[e];if(l.delegateTarget=this,!u.preDispatch||!1!==u.preDispatch.call(this,l)){o=O.event.handlers.call(this,l,c),e=0;while((i=o[e++])&&!l.isPropagationStopped()){l.currentTarget=i.elem,n=0;while((a=i.handlers[n++])&&!l.isImmediatePropagationStopped())l.rnamespace&&!1!==a.namespace&&!l.rnamespace.test(a.namespace)||(l.handleObj=a,l.data=a.data,r=((O.event.special[a.origType]||{}).handle||a.handler).apply(i.elem,s),void 0!==r&&!1===(l.result=r)&&(l.preventDefault(),l.stopPropagation()))}return u.postDispatch&&u.postDispatch.call(this,l),l.result}},handlers:function(t,e){var n,r,i,a,o,s=[],l=e.delegateCount,c=t.target;if(l&&c.nodeType&&!("click"===t.type&&t.button>=1))for(;c!==this;c=c.parentNode||this)if(1===c.nodeType&&("click"!==t.type||!0!==c.disabled)){for(a=[],o={},n=0;n<l;n++)r=e[n],i=r.selector+" ",void 0===o[i]&&(o[i]=r.needsContext?O(i,this).index(c)>-1:O.find(i,this,null,[c]).length),o[i]&&a.push(r);a.length&&s.push({elem:c,handlers:a})}return c=this,l<e.length&&s.push({elem:c,handlers:e.slice(l)}),s},addProp:function(t,e){Object.defineProperty(O.Event.prototype,t,{enumerable:!0,configurable:!0,get:v(e)?function(){if(this.originalEvent)return e(this.originalEvent)}:function(){if(this.originalEvent)return this.originalEvent[t]},set:function(e){Object.defineProperty(this,t,{enumerable:!0,configurable:!0,writable:!0,value:e})}})},fix:function(t){return t[O.expando]?t:new O.Event(t)},special:{load:{noBubble:!0},click:{setup:function(t){var e=this||t;return Tt.test(e.type)&&e.click&&I(e,"input")&&Pt(e,"click",!0),!1},trigger:function(t){var e=this||t;return Tt.test(e.type)&&e.click&&I(e,"input")&&Pt(e,"click"),!0},_default:function(t){var e=t.target;return Tt.test(e.type)&&e.click&&I(e,"input")&&ut.get(e,"click")||I(e,"a")}},beforeunload:{postDispatch:function(t){void 0!==t.result&&t.originalEvent&&(t.originalEvent.returnValue=t.result)}}}},O.removeEvent=function(t,e,n){t.removeEventListener&&t.removeEventListener(e,n)},O.Event=function(t,e){if(!(this instanceof O.Event))return new O.Event(t,e);t&&t.type?(this.originalEvent=t,this.type=t.type,this.isDefaultPrevented=t.defaultPrevented||void 0===t.defaultPrevented&&!1===t.returnValue?Rt:Bt,this.target=t.target&&3===t.target.nodeType?t.target.parentNode:t.target,this.currentTarget=t.currentTarget,this.relatedTarget=t.relatedTarget):this.type=t,e&&O.extend(this,e),this.timeStamp=t&&t.timeStamp||Date.now(),this[O.expando]=!0},O.Event.prototype={constructor:O.Event,isDefaultPrevented:Bt,isPropagationStopped:Bt,isImmediatePropagationStopped:Bt,isSimulated:!1,preventDefault:function(){var t=this.originalEvent;this.isDefaultPrevented=Rt,t&&!this.isSimulated&&t.preventDefault()},stopPropagation:function(){var t=this.originalEvent;this.isPropagationStopped=Rt,t&&!this.isSimulated&&t.stopPropagation()},stopImmediatePropagation:function(){var t=this.originalEvent;this.isImmediatePropagationStopped=Rt,t&&!this.isSimulated&&t.stopImmediatePropagation(),this.stopPropagation()}},O.each({altKey:!0,bubbles:!0,cancelable:!0,changedTouches:!0,ctrlKey:!0,detail:!0,eventPhase:!0,metaKey:!0,pageX:!0,pageY:!0,shiftKey:!0,view:!0,char:!0,code:!0,charCode:!0,key:!0,keyCode:!0,button:!0,buttons:!0,clientX:!0,clientY:!0,offsetX:!0,offsetY:!0,pointerId:!0,pointerType:!0,screenX:!0,screenY:!0,targetTouches:!0,toElement:!0,touches:!0,which:!0},O.event.addProp),O.each({focus:"focusin",blur:"focusout"},(function(t,e){function n(t){if(x.documentMode){var n=ut.get(this,"handle"),r=O.event.fix(t);r.type="focusin"===t.type?"focus":"blur",r.isSimulated=!0,n(t),r.target===r.currentTarget&&n(r)}else O.event.simulate(e,t.target,O.event.fix(t))}O.event.special[t]={setup:function(){var r;if(Pt(this,t,!0),!x.documentMode)return!1;r=ut.get(this,e),r||this.addEventListener(e,n),ut.set(this,e,(r||0)+1)},trigger:function(){return Pt(this,t),!0},teardown:function(){var t;if(!x.documentMode)return!1;t=ut.get(this,e)-1,t?ut.set(this,e,t):(this.removeEventListener(e,n),ut.remove(this,e))},_default:function(e){return ut.get(e.target,t)},delegateType:e},O.event.special[e]={setup:function(){var r=this.ownerDocument||this.document||this,i=x.documentMode?this:r,a=ut.get(i,e);a||(x.documentMode?this.addEventListener(e,n):r.addEventListener(t,n,!0)),ut.set(i,e,(a||0)+1)},teardown:function(){var r=this.ownerDocument||this.document||this,i=x.documentMode?this:r,a=ut.get(i,e)-1;a?ut.set(i,e,a):(x.documentMode?this.removeEventListener(e,n):r.removeEventListener(t,n,!0),ut.remove(i,e))}}})),O.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},(function(t,e){O.event.special[t]={delegateType:e,bindType:e,handle:function(t){var n,r=this,i=t.relatedTarget,a=t.handleObj;return i&&(i===r||O.contains(r,i))||(t.type=a.origType,n=a.handler.apply(this,arguments),t.type=e),n}}})),O.fn.extend({on:function(t,e,n,r){return Vt(this,t,e,n,r)},one:function(t,e,n,r){return Vt(this,t,e,n,r,1)},off:function(t,e,n){var r,i;if(t&&t.preventDefault&&t.handleObj)return r=t.handleObj,O(t.delegateTarget).off(r.namespace?r.origType+"."+r.namespace:r.origType,r.selector,r.handler),this;if("object"===typeof t){for(i in t)this.off(i,e,t[i]);return this}return!1!==e&&"function"!==typeof e||(n=e,e=void 0),!1===n&&(n=Bt),this.each((function(){O.event.remove(this,t,n,e)}))}});var zt=/<script|<style|<link/i,Qt=/checked\s*(?:[^=]|=\s*.checked.)/i,Wt=/^\s*<!\[CDATA\[|\]\]>\s*$/g;function Ft(t,e){return I(t,"table")&&I(11!==e.nodeType?e:e.firstChild,"tr")&&O(t).children("tbody")[0]||t}function qt(t){return t.type=(null!==t.getAttribute("type"))+"/"+t.type,t}function Xt(t){return"true/"===(t.type||"").slice(0,5)?t.type=t.type.slice(5):t.removeAttribute("type"),t}function Ht(t,e){var n,r,i,a,o,s,l;if(1===e.nodeType){if(ut.hasData(t)&&(a=ut.get(t),l=a.events,l))for(i in ut.remove(e,"handle events"),l)for(n=0,r=l[i].length;n<r;n++)O.event.add(e,i,l[i][n]);ht.hasData(t)&&(o=ht.access(t),s=O.extend({},o),ht.set(e,s))}}function Zt(t,e){var n=e.nodeName.toLowerCase();"input"===n&&Tt.test(t.type)?e.checked=t.checked:"input"!==n&&"textarea"!==n||(e.defaultValue=t.defaultValue)}function Jt(t,e,n,r){e=c(e);var i,a,o,s,l,u,h=0,f=t.length,p=f-1,d=e[0],g=v(d);if(g||f>1&&"string"===typeof d&&!b.checkClone&&Qt.test(d))return t.each((function(i){var a=t.eq(i);g&&(e[0]=d.call(this,i,a.html())),Jt(a,e,n,r)}));if(f&&(i=Nt(e,t[0].ownerDocument,!1,t,r),a=i.firstChild,1===i.childNodes.length&&(i=a),a||r)){for(o=O.map(Lt(i,"script"),qt),s=o.length;h<f;h++)l=i,h!==p&&(l=O.clone(l,!0,!0),s&&O.merge(o,Lt(l,"script"))),n.call(t[h],l,h);if(s)for(u=o[o.length-1].ownerDocument,O.map(o,Xt),h=0;h<s;h++)l=o[h],Et.test(l.type||"")&&!ut.access(l,"globalEval")&&O.contains(u,l)&&(l.src&&"module"!==(l.type||"").toLowerCase()?O._evalUrl&&!l.noModule&&O._evalUrl(l.src,{nonce:l.nonce||l.getAttribute("nonce")},u):w(l.textContent.replace(Wt,""),l,u))}return t}function Ut(t,e,n){for(var r,i=e?O.filter(e,t):t,a=0;null!=(r=i[a]);a++)n||1!==r.nodeType||O.cleanData(Lt(r)),r.parentNode&&(n&&xt(r)&&Gt(Lt(r,"script")),r.parentNode.removeChild(r));return t}O.extend({htmlPrefilter:function(t){return t},clone:function(t,e,n){var r,i,a,o,s=t.cloneNode(!0),l=xt(t);if(!b.noCloneChecked&&(1===t.nodeType||11===t.nodeType)&&!O.isXMLDoc(t))for(o=Lt(s),a=Lt(t),r=0,i=a.length;r<i;r++)Zt(a[r],o[r]);if(e)if(n)for(a=a||Lt(t),o=o||Lt(s),r=0,i=a.length;r<i;r++)Ht(a[r],o[r]);else Ht(t,s);return o=Lt(s,"script"),o.length>0&&Gt(o,!l&&Lt(t,"script")),s},cleanData:function(t){for(var e,n,r,i=O.event.special,a=0;void 0!==(n=t[a]);a++)if(lt(n)){if(e=n[ut.expando]){if(e.events)for(r in e.events)i[r]?O.event.remove(n,r):O.removeEvent(n,r,e.handle);n[ut.expando]=void 0}n[ht.expando]&&(n[ht.expando]=void 0)}}}),O.fn.extend({detach:function(t){return Ut(this,t,!0)},remove:function(t){return Ut(this,t)},text:function(t){return rt(this,(function(t){return void 0===t?O.text(this):this.empty().each((function(){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(this.textContent=t)}))}),null,t,arguments.length)},append:function(){return Jt(this,arguments,(function(t){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var e=Ft(this,t);e.appendChild(t)}}))},prepend:function(){return Jt(this,arguments,(function(t){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var e=Ft(this,t);e.insertBefore(t,e.firstChild)}}))},before:function(){return Jt(this,arguments,(function(t){this.parentNode&&this.parentNode.insertBefore(t,this)}))},after:function(){return Jt(this,arguments,(function(t){this.parentNode&&this.parentNode.insertBefore(t,this.nextSibling)}))},empty:function(){for(var t,e=0;null!=(t=this[e]);e++)1===t.nodeType&&(O.cleanData(Lt(t,!1)),t.textContent="");return this},clone:function(t,e){return t=null!=t&&t,e=null==e?t:e,this.map((function(){return O.clone(this,t,e)}))},html:function(t){return rt(this,(function(t){var e=this[0]||{},n=0,r=this.length;if(void 0===t&&1===e.nodeType)return e.innerHTML;if("string"===typeof t&&!zt.test(t)&&!Dt[(It.exec(t)||["",""])[1].toLowerCase()]){t=O.htmlPrefilter(t);try{for(;n<r;n++)e=this[n]||{},1===e.nodeType&&(O.cleanData(Lt(e,!1)),e.innerHTML=t);e=0}catch(i){}}e&&this.empty().append(t)}),null,t,arguments.length)},replaceWith:function(){var t=[];return Jt(this,arguments,(function(e){var n=this.parentNode;O.inArray(this,t)<0&&(O.cleanData(Lt(this)),n&&n.replaceChild(e,this))}),t)}}),O.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},(function(t,e){O.fn[t]=function(t){for(var n,r=[],i=O(t),a=i.length-1,o=0;o<=a;o++)n=o===a?this:this.clone(!0),O(i[o])[e](n),u.apply(r,n.get());return this.pushStack(r)}}));var Yt=new RegExp("^("+mt+")(?!px)[a-z%]+$","i"),Kt=/^--/,_t=function(t){var e=t.ownerDocument.defaultView;return e&&e.opener||(e=n),e.getComputedStyle(t)},$t=function(t,e,n){var r,i,a={};for(i in e)a[i]=t.style[i],t.style[i]=e[i];for(i in r=n.call(t),e)t.style[i]=a[i];return r},te=new RegExp(vt.join("|"),"i");function ee(t,e,n){var r,i,a,o,s=Kt.test(e),l=t.style;return n=n||_t(t),n&&(o=n.getPropertyValue(e)||n[e],s&&o&&(o=o.replace(j,"$1")||void 0),""!==o||xt(t)||(o=O.style(t,e)),!b.pixelBoxStyles()&&Yt.test(o)&&te.test(e)&&(r=l.width,i=l.minWidth,a=l.maxWidth,l.minWidth=l.maxWidth=l.width=o,o=n.width,l.width=r,l.minWidth=i,l.maxWidth=a)),void 0!==o?o+"":o}function ne(t,e){return{get:function(){if(!t())return(this.get=e).apply(this,arguments);delete this.get}}}(function(){function t(){if(u){c.style.cssText="position:absolute;left:-11111px;width:60px;margin-top:1px;padding:0;border:0",u.style.cssText="position:relative;display:block;box-sizing:border-box;overflow:scroll;margin:auto;border:1px;padding:1px;width:60%;top:1%",yt.appendChild(c).appendChild(u);var t=n.getComputedStyle(u);r="1%"!==t.top,l=12===e(t.marginLeft),u.style.right="60%",o=36===e(t.right),i=36===e(t.width),u.style.position="absolute",a=12===e(u.offsetWidth/3),yt.removeChild(c),u=null}}function e(t){return Math.round(parseFloat(t))}var r,i,a,o,s,l,c=x.createElement("div"),u=x.createElement("div");u.style&&(u.style.backgroundClip="content-box",u.cloneNode(!0).style.backgroundClip="",b.clearCloneStyle="content-box"===u.style.backgroundClip,O.extend(b,{boxSizingReliable:function(){return t(),i},pixelBoxStyles:function(){return t(),o},pixelPosition:function(){return t(),r},reliableMarginLeft:function(){return t(),l},scrollboxSize:function(){return t(),a},reliableTrDimensions:function(){var t,e,r,i;return null==s&&(t=x.createElement("table"),e=x.createElement("tr"),r=x.createElement("div"),t.style.cssText="position:absolute;left:-11111px;border-collapse:separate",e.style.cssText="border:1px solid",e.style.height="1px",r.style.height="9px",r.style.display="block",yt.appendChild(t).appendChild(e).appendChild(r),i=n.getComputedStyle(e),s=parseInt(i.height,10)+parseInt(i.borderTopWidth,10)+parseInt(i.borderBottomWidth,10)===e.offsetHeight,yt.removeChild(t)),s}}))})();var re=["Webkit","Moz","ms"],ie=x.createElement("div").style,ae={};function oe(t){var e=t[0].toUpperCase()+t.slice(1),n=re.length;while(n--)if(t=re[n]+e,t in ie)return t}function se(t){var e=O.cssProps[t]||ae[t];return e||(t in ie?t:ae[t]=oe(t)||t)}var le=/^(none|table(?!-c[ea]).+)/,ce={position:"absolute",visibility:"hidden",display:"block"},ue={letterSpacing:"0",fontWeight:"400"};function he(t,e,n){var r=bt.exec(e);return r?Math.max(0,r[2]-(n||0))+(r[3]||"px"):e}function fe(t,e,n,r,i,a){var o="width"===e?1:0,s=0,l=0,c=0;if(n===(r?"border":"content"))return 0;for(;o<4;o+=2)"margin"===n&&(c+=O.css(t,n+vt[o],!0,i)),r?("content"===n&&(l-=O.css(t,"padding"+vt[o],!0,i)),"margin"!==n&&(l-=O.css(t,"border"+vt[o]+"Width",!0,i))):(l+=O.css(t,"padding"+vt[o],!0,i),"padding"!==n?l+=O.css(t,"border"+vt[o]+"Width",!0,i):s+=O.css(t,"border"+vt[o]+"Width",!0,i));return!r&&a>=0&&(l+=Math.max(0,Math.ceil(t["offset"+e[0].toUpperCase()+e.slice(1)]-a-l-s-.5))||0),l+c}function pe(t,e,n){var r=_t(t),i=!b.boxSizingReliable()||n,a=i&&"border-box"===O.css(t,"boxSizing",!1,r),o=a,s=ee(t,e,r),l="offset"+e[0].toUpperCase()+e.slice(1);if(Yt.test(s)){if(!n)return s;s="auto"}return(!b.boxSizingReliable()&&a||!b.reliableTrDimensions()&&I(t,"tr")||"auto"===s||!parseFloat(s)&&"inline"===O.css(t,"display",!1,r))&&t.getClientRects().length&&(a="border-box"===O.css(t,"boxSizing",!1,r),o=l in t,o&&(s=t[l])),s=parseFloat(s)||0,s+fe(t,e,n||(a?"border":"content"),o,r,s)+"px"}function de(t,e,n,r,i){return new de.prototype.init(t,e,n,r,i)}O.extend({cssHooks:{opacity:{get:function(t,e){if(e){var n=ee(t,"opacity");return""===n?"1":n}}}},cssNumber:{animationIterationCount:!0,aspectRatio:!0,borderImageSlice:!0,columnCount:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,gridArea:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnStart:!0,gridRow:!0,gridRowEnd:!0,gridRowStart:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,scale:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeMiterlimit:!0,strokeOpacity:!0},cssProps:{},style:function(t,e,n,r){if(t&&3!==t.nodeType&&8!==t.nodeType&&t.style){var i,a,o,s=st(e),l=Kt.test(e),c=t.style;if(l||(e=se(s)),o=O.cssHooks[e]||O.cssHooks[s],void 0===n)return o&&"get"in o&&void 0!==(i=o.get(t,!1,r))?i:c[e];a=typeof n,"string"===a&&(i=bt.exec(n))&&i[1]&&(n=Ct(t,e,i),a="number"),null!=n&&n===n&&("number"!==a||l||(n+=i&&i[3]||(O.cssNumber[s]?"":"px")),b.clearCloneStyle||""!==n||0!==e.indexOf("background")||(c[e]="inherit"),o&&"set"in o&&void 0===(n=o.set(t,n,r))||(l?c.setProperty(e,n):c[e]=n))}},css:function(t,e,n,r){var i,a,o,s=st(e),l=Kt.test(e);return l||(e=se(s)),o=O.cssHooks[e]||O.cssHooks[s],o&&"get"in o&&(i=o.get(t,!0,n)),void 0===i&&(i=ee(t,e,r)),"normal"===i&&e in ue&&(i=ue[e]),""===n||n?(a=parseFloat(i),!0===n||isFinite(a)?a||0:i):i}}),O.each(["height","width"],(function(t,e){O.cssHooks[e]={get:function(t,n,r){if(n)return!le.test(O.css(t,"display"))||t.getClientRects().length&&t.getBoundingClientRect().width?pe(t,e,r):$t(t,ce,(function(){return pe(t,e,r)}))},set:function(t,n,r){var i,a=_t(t),o=!b.scrollboxSize()&&"absolute"===a.position,s=o||r,l=s&&"border-box"===O.css(t,"boxSizing",!1,a),c=r?fe(t,e,r,l,a):0;return l&&o&&(c-=Math.ceil(t["offset"+e[0].toUpperCase()+e.slice(1)]-parseFloat(a[e])-fe(t,e,"border",!1,a)-.5)),c&&(i=bt.exec(n))&&"px"!==(i[3]||"px")&&(t.style[e]=n,n=O.css(t,e)),he(t,n,c)}}})),O.cssHooks.marginLeft=ne(b.reliableMarginLeft,(function(t,e){if(e)return(parseFloat(ee(t,"marginLeft"))||t.getBoundingClientRect().left-$t(t,{marginLeft:0},(function(){return t.getBoundingClientRect().left})))+"px"})),O.each({margin:"",padding:"",border:"Width"},(function(t,e){O.cssHooks[t+e]={expand:function(n){for(var r=0,i={},a="string"===typeof n?n.split(" "):[n];r<4;r++)i[t+vt[r]+e]=a[r]||a[r-2]||a[0];return i}},"margin"!==t&&(O.cssHooks[t+e].set=he)})),O.fn.extend({css:function(t,e){return rt(this,(function(t,e,n){var r,i,a={},o=0;if(Array.isArray(e)){for(r=_t(t),i=e.length;o<i;o++)a[e[o]]=O.css(t,e[o],!1,r);return a}return void 0!==n?O.style(t,e,n):O.css(t,e)}),t,e,arguments.length>1)}}),O.Tween=de,de.prototype={constructor:de,init:function(t,e,n,r,i,a){this.elem=t,this.prop=n,this.easing=i||O.easing._default,this.options=e,this.start=this.now=this.cur(),this.end=r,this.unit=a||(O.cssNumber[n]?"":"px")},cur:function(){var t=de.propHooks[this.prop];return t&&t.get?t.get(this):de.propHooks._default.get(this)},run:function(t){var e,n=de.propHooks[this.prop];return this.options.duration?this.pos=e=O.easing[this.easing](t,this.options.duration*t,0,1,this.options.duration):this.pos=e=t,this.now=(this.end-this.start)*e+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),n&&n.set?n.set(this):de.propHooks._default.set(this),this}},de.prototype.init.prototype=de.prototype,de.propHooks={_default:{get:function(t){var e;return 1!==t.elem.nodeType||null!=t.elem[t.prop]&&null==t.elem.style[t.prop]?t.elem[t.prop]:(e=O.css(t.elem,t.prop,""),e&&"auto"!==e?e:0)},set:function(t){O.fx.step[t.prop]?O.fx.step[t.prop](t):1!==t.elem.nodeType||!O.cssHooks[t.prop]&&null==t.elem.style[se(t.prop)]?t.elem[t.prop]=t.now:O.style(t.elem,t.prop,t.now+t.unit)}}},de.propHooks.scrollTop=de.propHooks.scrollLeft={set:function(t){t.elem.nodeType&&t.elem.parentNode&&(t.elem[t.prop]=t.now)}},O.easing={linear:function(t){return t},swing:function(t){return.5-Math.cos(t*Math.PI)/2},_default:"swing"},O.fx=de.prototype.init,O.fx.step={};var ge,me,be=/^(?:toggle|show|hide)$/,ve=/queueHooks$/;function ye(){me&&(!1===x.hidden&&n.requestAnimationFrame?n.requestAnimationFrame(ye):n.setTimeout(ye,O.fx.interval),O.fx.tick())}function xe(){return n.setTimeout((function(){ge=void 0})),ge=Date.now()}function Ae(t,e){var n,r=0,i={height:t};for(e=e?1:0;r<4;r+=2-e)n=vt[r],i["margin"+n]=i["padding"+n]=t;return e&&(i.opacity=i.width=t),i}function we(t,e,n){for(var r,i=(ke.tweeners[e]||[]).concat(ke.tweeners["*"]),a=0,o=i.length;a<o;a++)if(r=i[a].call(n,e,t))return r}function Ce(t,e,n){var r,i,a,o,s,l,c,u,h="width"in e||"height"in e,f=this,p={},d=t.style,g=t.nodeType&&wt(t),m=ut.get(t,"fxshow");for(r in n.queue||(o=O._queueHooks(t,"fx"),null==o.unqueued&&(o.unqueued=0,s=o.empty.fire,o.empty.fire=function(){o.unqueued||s()}),o.unqueued++,f.always((function(){f.always((function(){o.unqueued--,O.queue(t,"fx").length||o.empty.fire()}))}))),e)if(i=e[r],be.test(i)){if(delete e[r],a=a||"toggle"===i,i===(g?"hide":"show")){if("show"!==i||!m||void 0===m[r])continue;g=!0}p[r]=m&&m[r]||O.style(t,r)}if(l=!O.isEmptyObject(e),l||!O.isEmptyObject(p))for(r in h&&1===t.nodeType&&(n.overflow=[d.overflow,d.overflowX,d.overflowY],c=m&&m.display,null==c&&(c=ut.get(t,"display")),u=O.css(t,"display"),"none"===u&&(c?u=c:(Ot([t],!0),c=t.style.display||c,u=O.css(t,"display"),Ot([t]))),("inline"===u||"inline-block"===u&&null!=c)&&"none"===O.css(t,"float")&&(l||(f.done((function(){d.display=c})),null==c&&(u=d.display,c="none"===u?"":u)),d.display="inline-block")),n.overflow&&(d.overflow="hidden",f.always((function(){d.overflow=n.overflow[0],d.overflowX=n.overflow[1],d.overflowY=n.overflow[2]}))),l=!1,p)l||(m?"hidden"in m&&(g=m.hidden):m=ut.access(t,"fxshow",{display:c}),a&&(m.hidden=!g),g&&Ot([t],!0),f.done((function(){for(r in g||Ot([t]),ut.remove(t,"fxshow"),p)O.style(t,r,p[r])}))),l=we(g?m[r]:0,r,f),r in m||(m[r]=l.start,g&&(l.end=l.start,l.start=0))}function Se(t,e){var n,r,i,a,o;for(n in t)if(r=st(n),i=e[r],a=t[n],Array.isArray(a)&&(i=a[1],a=t[n]=a[0]),n!==r&&(t[r]=a,delete t[n]),o=O.cssHooks[r],o&&"expand"in o)for(n in a=o.expand(a),delete t[r],a)n in t||(t[n]=a[n],e[n]=i);else e[r]=i}function ke(t,e,n){var r,i,a=0,o=ke.prefilters.length,s=O.Deferred().always((function(){delete l.elem})),l=function(){if(i)return!1;for(var e=ge||xe(),n=Math.max(0,c.startTime+c.duration-e),r=n/c.duration||0,a=1-r,o=0,l=c.tweens.length;o<l;o++)c.tweens[o].run(a);return s.notifyWith(t,[c,a,n]),a<1&&l?n:(l||s.notifyWith(t,[c,1,0]),s.resolveWith(t,[c]),!1)},c=s.promise({elem:t,props:O.extend({},e),opts:O.extend(!0,{specialEasing:{},easing:O.easing._default},n),originalProperties:e,originalOptions:n,startTime:ge||xe(),duration:n.duration,tweens:[],createTween:function(e,n){var r=O.Tween(t,c.opts,e,n,c.opts.specialEasing[e]||c.opts.easing);return c.tweens.push(r),r},stop:function(e){var n=0,r=e?c.tweens.length:0;if(i)return this;for(i=!0;n<r;n++)c.tweens[n].run(1);return e?(s.notifyWith(t,[c,1,0]),s.resolveWith(t,[c,e])):s.rejectWith(t,[c,e]),this}}),u=c.props;for(Se(u,c.opts.specialEasing);a<o;a++)if(r=ke.prefilters[a].call(c,t,u,c.opts),r)return v(r.stop)&&(O._queueHooks(c.elem,c.opts.queue).stop=r.stop.bind(r)),r;return O.map(u,we,c),v(c.opts.start)&&c.opts.start.call(t,c),c.progress(c.opts.progress).done(c.opts.done,c.opts.complete).fail(c.opts.fail).always(c.opts.always),O.fx.timer(O.extend(l,{elem:t,anim:c,queue:c.opts.queue})),c}O.Animation=O.extend(ke,{tweeners:{"*":[function(t,e){var n=this.createTween(t,e);return Ct(n.elem,t,bt.exec(e),n),n}]},tweener:function(t,e){v(t)?(e=t,t=["*"]):t=t.match(U);for(var n,r=0,i=t.length;r<i;r++)n=t[r],ke.tweeners[n]=ke.tweeners[n]||[],ke.tweeners[n].unshift(e)},prefilters:[Ce],prefilter:function(t,e){e?ke.prefilters.unshift(t):ke.prefilters.push(t)}}),O.speed=function(t,e,n){var r=t&&"object"===typeof t?O.extend({},t):{complete:n||!n&&e||v(t)&&t,duration:t,easing:n&&e||e&&!v(e)&&e};return O.fx.off?r.duration=0:"number"!==typeof r.duration&&(r.duration in O.fx.speeds?r.duration=O.fx.speeds[r.duration]:r.duration=O.fx.speeds._default),null!=r.queue&&!0!==r.queue||(r.queue="fx"),r.old=r.complete,r.complete=function(){v(r.old)&&r.old.call(this),r.queue&&O.dequeue(this,r.queue)},r},O.fn.extend({fadeTo:function(t,e,n,r){return this.filter(wt).css("opacity",0).show().end().animate({opacity:e},t,n,r)},animate:function(t,e,n,r){var i=O.isEmptyObject(t),a=O.speed(e,n,r),o=function(){var e=ke(this,O.extend({},t),a);(i||ut.get(this,"finish"))&&e.stop(!0)};return o.finish=o,i||!1===a.queue?this.each(o):this.queue(a.queue,o)},stop:function(t,e,n){var r=function(t){var e=t.stop;delete t.stop,e(n)};return"string"!==typeof t&&(n=e,e=t,t=void 0),e&&this.queue(t||"fx",[]),this.each((function(){var e=!0,i=null!=t&&t+"queueHooks",a=O.timers,o=ut.get(this);if(i)o[i]&&o[i].stop&&r(o[i]);else for(i in o)o[i]&&o[i].stop&&ve.test(i)&&r(o[i]);for(i=a.length;i--;)a[i].elem!==this||null!=t&&a[i].queue!==t||(a[i].anim.stop(n),e=!1,a.splice(i,1));!e&&n||O.dequeue(this,t)}))},finish:function(t){return!1!==t&&(t=t||"fx"),this.each((function(){var e,n=ut.get(this),r=n[t+"queue"],i=n[t+"queueHooks"],a=O.timers,o=r?r.length:0;for(n.finish=!0,O.queue(this,t,[]),i&&i.stop&&i.stop.call(this,!0),e=a.length;e--;)a[e].elem===this&&a[e].queue===t&&(a[e].anim.stop(!0),a.splice(e,1));for(e=0;e<o;e++)r[e]&&r[e].finish&&r[e].finish.call(this);delete n.finish}))}}),O.each(["toggle","show","hide"],(function(t,e){var n=O.fn[e];O.fn[e]=function(t,r,i){return null==t||"boolean"===typeof t?n.apply(this,arguments):this.animate(Ae(e,!0),t,r,i)}})),O.each({slideDown:Ae("show"),slideUp:Ae("hide"),slideToggle:Ae("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},(function(t,e){O.fn[t]=function(t,n,r){return this.animate(e,t,n,r)}})),O.timers=[],O.fx.tick=function(){var t,e=0,n=O.timers;for(ge=Date.now();e<n.length;e++)t=n[e],t()||n[e]!==t||n.splice(e--,1);n.length||O.fx.stop(),ge=void 0},O.fx.timer=function(t){O.timers.push(t),O.fx.start()},O.fx.interval=13,O.fx.start=function(){me||(me=!0,ye())},O.fx.stop=function(){me=null},O.fx.speeds={slow:600,fast:200,_default:400},O.fn.delay=function(t,e){return t=O.fx&&O.fx.speeds[t]||t,e=e||"fx",this.queue(e,(function(e,r){var i=n.setTimeout(e,t);r.stop=function(){n.clearTimeout(i)}}))},function(){var t=x.createElement("input"),e=x.createElement("select"),n=e.appendChild(x.createElement("option"));t.type="checkbox",b.checkOn=""!==t.value,b.optSelected=n.selected,t=x.createElement("input"),t.value="t",t.type="radio",b.radioValue="t"===t.value}();var Oe,Te=O.expr.attrHandle;O.fn.extend({attr:function(t,e){return rt(this,O.attr,t,e,arguments.length>1)},removeAttr:function(t){return this.each((function(){O.removeAttr(this,t)}))}}),O.extend({attr:function(t,e,n){var r,i,a=t.nodeType;if(3!==a&&8!==a&&2!==a)return"undefined"===typeof t.getAttribute?O.prop(t,e,n):(1===a&&O.isXMLDoc(t)||(i=O.attrHooks[e.toLowerCase()]||(O.expr.match.bool.test(e)?Oe:void 0)),void 0!==n?null===n?void O.removeAttr(t,e):i&&"set"in i&&void 0!==(r=i.set(t,n,e))?r:(t.setAttribute(e,n+""),n):i&&"get"in i&&null!==(r=i.get(t,e))?r:(r=O.find.attr(t,e),null==r?void 0:r))},attrHooks:{type:{set:function(t,e){if(!b.radioValue&&"radio"===e&&I(t,"input")){var n=t.value;return t.setAttribute("type",e),n&&(t.value=n),e}}}},removeAttr:function(t,e){var n,r=0,i=e&&e.match(U);if(i&&1===t.nodeType)while(n=i[r++])t.removeAttribute(n)}}),Oe={set:function(t,e,n){return!1===e?O.removeAttr(t,n):t.setAttribute(n,n),n}},O.each(O.expr.match.bool.source.match(/\w+/g),(function(t,e){var n=Te[e]||O.find.attr;Te[e]=function(t,e,r){var i,a,o=e.toLowerCase();return r||(a=Te[o],Te[o]=i,i=null!=n(t,e,r)?o:null,Te[o]=a),i}}));var Ie=/^(?:input|select|textarea|button)$/i,Ee=/^(?:a|area)$/i;function De(t){var e=t.match(U)||[];return e.join(" ")}function Le(t){return t.getAttribute&&t.getAttribute("class")||""}function Ge(t){return Array.isArray(t)?t:"string"===typeof t&&t.match(U)||[]}O.fn.extend({prop:function(t,e){return rt(this,O.prop,t,e,arguments.length>1)},removeProp:function(t){return this.each((function(){delete this[O.propFix[t]||t]}))}}),O.extend({prop:function(t,e,n){var r,i,a=t.nodeType;if(3!==a&&8!==a&&2!==a)return 1===a&&O.isXMLDoc(t)||(e=O.propFix[e]||e,i=O.propHooks[e]),void 0!==n?i&&"set"in i&&void 0!==(r=i.set(t,n,e))?r:t[e]=n:i&&"get"in i&&null!==(r=i.get(t,e))?r:t[e]},propHooks:{tabIndex:{get:function(t){var e=O.find.attr(t,"tabindex");return e?parseInt(e,10):Ie.test(t.nodeName)||Ee.test(t.nodeName)&&t.href?0:-1}}},propFix:{for:"htmlFor",class:"className"}}),b.optSelected||(O.propHooks.selected={get:function(t){var e=t.parentNode;return e&&e.parentNode&&e.parentNode.selectedIndex,null},set:function(t){var e=t.parentNode;e&&(e.selectedIndex,e.parentNode&&e.parentNode.selectedIndex)}}),O.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],(function(){O.propFix[this.toLowerCase()]=this})),O.fn.extend({addClass:function(t){var e,n,r,i,a,o;return v(t)?this.each((function(e){O(this).addClass(t.call(this,e,Le(this)))})):(e=Ge(t),e.length?this.each((function(){if(r=Le(this),n=1===this.nodeType&&" "+De(r)+" ",n){for(a=0;a<e.length;a++)i=e[a],n.indexOf(" "+i+" ")<0&&(n+=i+" ");o=De(n),r!==o&&this.setAttribute("class",o)}})):this)},removeClass:function(t){var e,n,r,i,a,o;return v(t)?this.each((function(e){O(this).removeClass(t.call(this,e,Le(this)))})):arguments.length?(e=Ge(t),e.length?this.each((function(){if(r=Le(this),n=1===this.nodeType&&" "+De(r)+" ",n){for(a=0;a<e.length;a++){i=e[a];while(n.indexOf(" "+i+" ")>-1)n=n.replace(" "+i+" "," ")}o=De(n),r!==o&&this.setAttribute("class",o)}})):this):this.attr("class","")},toggleClass:function(t,e){var n,r,i,a,o=typeof t,s="string"===o||Array.isArray(t);return v(t)?this.each((function(n){O(this).toggleClass(t.call(this,n,Le(this),e),e)})):"boolean"===typeof e&&s?e?this.addClass(t):this.removeClass(t):(n=Ge(t),this.each((function(){if(s)for(a=O(this),i=0;i<n.length;i++)r=n[i],a.hasClass(r)?a.removeClass(r):a.addClass(r);else void 0!==t&&"boolean"!==o||(r=Le(this),r&&ut.set(this,"__className__",r),this.setAttribute&&this.setAttribute("class",r||!1===t?"":ut.get(this,"__className__")||""))})))},hasClass:function(t){var e,n,r=0;e=" "+t+" ";while(n=this[r++])if(1===n.nodeType&&(" "+De(Le(n))+" ").indexOf(e)>-1)return!0;return!1}});var je=/\r/g;O.fn.extend({val:function(t){var e,n,r,i=this[0];return arguments.length?(r=v(t),this.each((function(n){var i;1===this.nodeType&&(i=r?t.call(this,n,O(this).val()):t,null==i?i="":"number"===typeof i?i+="":Array.isArray(i)&&(i=O.map(i,(function(t){return null==t?"":t+""}))),e=O.valHooks[this.type]||O.valHooks[this.nodeName.toLowerCase()],e&&"set"in e&&void 0!==e.set(this,i,"value")||(this.value=i))}))):i?(e=O.valHooks[i.type]||O.valHooks[i.nodeName.toLowerCase()],e&&"get"in e&&void 0!==(n=e.get(i,"value"))?n:(n=i.value,"string"===typeof n?n.replace(je,""):null==n?"":n)):void 0}}),O.extend({valHooks:{option:{get:function(t){var e=O.find.attr(t,"value");return null!=e?e:De(O.text(t))}},select:{get:function(t){var e,n,r,i=t.options,a=t.selectedIndex,o="select-one"===t.type,s=o?null:[],l=o?a+1:i.length;for(r=a<0?l:o?a:0;r<l;r++)if(n=i[r],(n.selected||r===a)&&!n.disabled&&(!n.parentNode.disabled||!I(n.parentNode,"optgroup"))){if(e=O(n).val(),o)return e;s.push(e)}return s},set:function(t,e){var n,r,i=t.options,a=O.makeArray(e),o=i.length;while(o--)r=i[o],(r.selected=O.inArray(O.valHooks.option.get(r),a)>-1)&&(n=!0);return n||(t.selectedIndex=-1),a}}}}),O.each(["radio","checkbox"],(function(){O.valHooks[this]={set:function(t,e){if(Array.isArray(e))return t.checked=O.inArray(O(t).val(),e)>-1}},b.checkOn||(O.valHooks[this].get=function(t){return null===t.getAttribute("value")?"on":t.value})}));var Ne=n.location,Me={guid:Date.now()},Re=/\?/;O.parseXML=function(t){var e,r;if(!t||"string"!==typeof t)return null;try{e=(new n.DOMParser).parseFromString(t,"text/xml")}catch(i){}return r=e&&e.getElementsByTagName("parsererror")[0],e&&!r||O.error("Invalid XML: "+(r?O.map(r.childNodes,(function(t){return t.textContent})).join("\n"):t)),e};var Be=/^(?:focusinfocus|focusoutblur)$/,Ve=function(t){t.stopPropagation()};O.extend(O.event,{trigger:function(t,e,r,i){var a,o,s,l,c,u,h,f,p=[r||x],g=d.call(t,"type")?t.type:t,m=d.call(t,"namespace")?t.namespace.split("."):[];if(o=f=s=r=r||x,3!==r.nodeType&&8!==r.nodeType&&!Be.test(g+O.event.triggered)&&(g.indexOf(".")>-1&&(m=g.split("."),g=m.shift(),m.sort()),c=g.indexOf(":")<0&&"on"+g,t=t[O.expando]?t:new O.Event(g,"object"===typeof t&&t),t.isTrigger=i?2:3,t.namespace=m.join("."),t.rnamespace=t.namespace?new RegExp("(^|\\.)"+m.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,t.result=void 0,t.target||(t.target=r),e=null==e?[t]:O.makeArray(e,[t]),h=O.event.special[g]||{},i||!h.trigger||!1!==h.trigger.apply(r,e))){if(!i&&!h.noBubble&&!y(r)){for(l=h.delegateType||g,Be.test(l+g)||(o=o.parentNode);o;o=o.parentNode)p.push(o),s=o;s===(r.ownerDocument||x)&&p.push(s.defaultView||s.parentWindow||n)}a=0;while((o=p[a++])&&!t.isPropagationStopped())f=o,t.type=a>1?l:h.bindType||g,u=(ut.get(o,"events")||Object.create(null))[t.type]&&ut.get(o,"handle"),u&&u.apply(o,e),u=c&&o[c],u&&u.apply&&lt(o)&&(t.result=u.apply(o,e),!1===t.result&&t.preventDefault());return t.type=g,i||t.isDefaultPrevented()||h._default&&!1!==h._default.apply(p.pop(),e)||!lt(r)||c&&v(r[g])&&!y(r)&&(s=r[c],s&&(r[c]=null),O.event.triggered=g,t.isPropagationStopped()&&f.addEventListener(g,Ve),r[g](),t.isPropagationStopped()&&f.removeEventListener(g,Ve),O.event.triggered=void 0,s&&(r[c]=s)),t.result}},simulate:function(t,e,n){var r=O.extend(new O.Event,n,{type:t,isSimulated:!0});O.event.trigger(r,null,e)}}),O.fn.extend({trigger:function(t,e){return this.each((function(){O.event.trigger(t,e,this)}))},triggerHandler:function(t,e){var n=this[0];if(n)return O.event.trigger(t,e,n,!0)}});var Pe=/\[\]$/,ze=/\r?\n/g,Qe=/^(?:submit|button|image|reset|file)$/i,We=/^(?:input|select|textarea|keygen)/i;function Fe(t,e,n,r){var i;if(Array.isArray(e))O.each(e,(function(e,i){n||Pe.test(t)?r(t,i):Fe(t+"["+("object"===typeof i&&null!=i?e:"")+"]",i,n,r)}));else if(n||"object"!==C(e))r(t,e);else for(i in e)Fe(t+"["+i+"]",e[i],n,r)}O.param=function(t,e){var n,r=[],i=function(t,e){var n=v(e)?e():e;r[r.length]=encodeURIComponent(t)+"="+encodeURIComponent(null==n?"":n)};if(null==t)return"";if(Array.isArray(t)||t.jquery&&!O.isPlainObject(t))O.each(t,(function(){i(this.name,this.value)}));else for(n in t)Fe(n,t[n],e,i);return r.join("&")},O.fn.extend({serialize:function(){return O.param(this.serializeArray())},serializeArray:function(){return this.map((function(){var t=O.prop(this,"elements");return t?O.makeArray(t):this})).filter((function(){var t=this.type;return this.name&&!O(this).is(":disabled")&&We.test(this.nodeName)&&!Qe.test(t)&&(this.checked||!Tt.test(t))})).map((function(t,e){var n=O(this).val();return null==n?null:Array.isArray(n)?O.map(n,(function(t){return{name:e.name,value:t.replace(ze,"\r\n")}})):{name:e.name,value:n.replace(ze,"\r\n")}})).get()}});var qe=/%20/g,Xe=/#.*$/,He=/([?&])_=[^&]*/,Ze=/^(.*?):[ \t]*([^\r\n]*)$/gm,Je=/^(?:about|app|app-storage|.+-extension|file|res|widget):$/,Ue=/^(?:GET|HEAD)$/,Ye=/^\/\//,Ke={},_e={},$e="*/".concat("*"),tn=x.createElement("a");function en(t){return function(e,n){"string"!==typeof e&&(n=e,e="*");var r,i=0,a=e.toLowerCase().match(U)||[];if(v(n))while(r=a[i++])"+"===r[0]?(r=r.slice(1)||"*",(t[r]=t[r]||[]).unshift(n)):(t[r]=t[r]||[]).push(n)}}function nn(t,e,n,r){var i={},a=t===_e;function o(s){var l;return i[s]=!0,O.each(t[s]||[],(function(t,s){var c=s(e,n,r);return"string"!==typeof c||a||i[c]?a?!(l=c):void 0:(e.dataTypes.unshift(c),o(c),!1)})),l}return o(e.dataTypes[0])||!i["*"]&&o("*")}function rn(t,e){var n,r,i=O.ajaxSettings.flatOptions||{};for(n in e)void 0!==e[n]&&((i[n]?t:r||(r={}))[n]=e[n]);return r&&O.extend(!0,t,r),t}function an(t,e,n){var r,i,a,o,s=t.contents,l=t.dataTypes;while("*"===l[0])l.shift(),void 0===r&&(r=t.mimeType||e.getResponseHeader("Content-Type"));if(r)for(i in s)if(s[i]&&s[i].test(r)){l.unshift(i);break}if(l[0]in n)a=l[0];else{for(i in n){if(!l[0]||t.converters[i+" "+l[0]]){a=i;break}o||(o=i)}a=a||o}if(a)return a!==l[0]&&l.unshift(a),n[a]}function on(t,e,n,r){var i,a,o,s,l,c={},u=t.dataTypes.slice();if(u[1])for(o in t.converters)c[o.toLowerCase()]=t.converters[o];a=u.shift();while(a)if(t.responseFields[a]&&(n[t.responseFields[a]]=e),!l&&r&&t.dataFilter&&(e=t.dataFilter(e,t.dataType)),l=a,a=u.shift(),a)if("*"===a)a=l;else if("*"!==l&&l!==a){if(o=c[l+" "+a]||c["* "+a],!o)for(i in c)if(s=i.split(" "),s[1]===a&&(o=c[l+" "+s[0]]||c["* "+s[0]],o)){!0===o?o=c[i]:!0!==c[i]&&(a=s[0],u.unshift(s[1]));break}if(!0!==o)if(o&&t.throws)e=o(e);else try{e=o(e)}catch(h){return{state:"parsererror",error:o?h:"No conversion from "+l+" to "+a}}}return{state:"success",data:e}}tn.href=Ne.href,O.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:Ne.href,type:"GET",isLocal:Je.test(Ne.protocol),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":$e,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":JSON.parse,"text xml":O.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(t,e){return e?rn(rn(t,O.ajaxSettings),e):rn(O.ajaxSettings,t)},ajaxPrefilter:en(Ke),ajaxTransport:en(_e),ajax:function(t,e){"object"===typeof t&&(e=t,t=void 0),e=e||{};var r,i,a,o,s,l,c,u,h,f,p=O.ajaxSetup({},e),d=p.context||p,g=p.context&&(d.nodeType||d.jquery)?O(d):O.event,m=O.Deferred(),b=O.Callbacks("once memory"),v=p.statusCode||{},y={},A={},w="canceled",C={readyState:0,getResponseHeader:function(t){var e;if(c){if(!o){o={};while(e=Ze.exec(a))o[e[1].toLowerCase()+" "]=(o[e[1].toLowerCase()+" "]||[]).concat(e[2])}e=o[t.toLowerCase()+" "]}return null==e?null:e.join(", ")},getAllResponseHeaders:function(){return c?a:null},setRequestHeader:function(t,e){return null==c&&(t=A[t.toLowerCase()]=A[t.toLowerCase()]||t,y[t]=e),this},overrideMimeType:function(t){return null==c&&(p.mimeType=t),this},statusCode:function(t){var e;if(t)if(c)C.always(t[C.status]);else for(e in t)v[e]=[v[e],t[e]];return this},abort:function(t){var e=t||w;return r&&r.abort(e),S(0,e),this}};if(m.promise(C),p.url=((t||p.url||Ne.href)+"").replace(Ye,Ne.protocol+"//"),p.type=e.method||e.type||p.method||p.type,p.dataTypes=(p.dataType||"*").toLowerCase().match(U)||[""],null==p.crossDomain){l=x.createElement("a");try{l.href=p.url,l.href=l.href,p.crossDomain=tn.protocol+"//"+tn.host!==l.protocol+"//"+l.host}catch(k){p.crossDomain=!0}}if(p.data&&p.processData&&"string"!==typeof p.data&&(p.data=O.param(p.data,p.traditional)),nn(Ke,p,e,C),c)return C;for(h in u=O.event&&p.global,u&&0===O.active++&&O.event.trigger("ajaxStart"),p.type=p.type.toUpperCase(),p.hasContent=!Ue.test(p.type),i=p.url.replace(Xe,""),p.hasContent?p.data&&p.processData&&0===(p.contentType||"").indexOf("application/x-www-form-urlencoded")&&(p.data=p.data.replace(qe,"+")):(f=p.url.slice(i.length),p.data&&(p.processData||"string"===typeof p.data)&&(i+=(Re.test(i)?"&":"?")+p.data,delete p.data),!1===p.cache&&(i=i.replace(He,"$1"),f=(Re.test(i)?"&":"?")+"_="+Me.guid+++f),p.url=i+f),p.ifModified&&(O.lastModified[i]&&C.setRequestHeader("If-Modified-Since",O.lastModified[i]),O.etag[i]&&C.setRequestHeader("If-None-Match",O.etag[i])),(p.data&&p.hasContent&&!1!==p.contentType||e.contentType)&&C.setRequestHeader("Content-Type",p.contentType),C.setRequestHeader("Accept",p.dataTypes[0]&&p.accepts[p.dataTypes[0]]?p.accepts[p.dataTypes[0]]+("*"!==p.dataTypes[0]?", "+$e+"; q=0.01":""):p.accepts["*"]),p.headers)C.setRequestHeader(h,p.headers[h]);if(p.beforeSend&&(!1===p.beforeSend.call(d,C,p)||c))return C.abort();if(w="abort",b.add(p.complete),C.done(p.success),C.fail(p.error),r=nn(_e,p,e,C),r){if(C.readyState=1,u&&g.trigger("ajaxSend",[C,p]),c)return C;p.async&&p.timeout>0&&(s=n.setTimeout((function(){C.abort("timeout")}),p.timeout));try{c=!1,r.send(y,S)}catch(k){if(c)throw k;S(-1,k)}}else S(-1,"No Transport");function S(t,e,o,l){var h,f,y,x,A,w=e;c||(c=!0,s&&n.clearTimeout(s),r=void 0,a=l||"",C.readyState=t>0?4:0,h=t>=200&&t<300||304===t,o&&(x=an(p,C,o)),!h&&O.inArray("script",p.dataTypes)>-1&&O.inArray("json",p.dataTypes)<0&&(p.converters["text script"]=function(){}),x=on(p,x,C,h),h?(p.ifModified&&(A=C.getResponseHeader("Last-Modified"),A&&(O.lastModified[i]=A),A=C.getResponseHeader("etag"),A&&(O.etag[i]=A)),204===t||"HEAD"===p.type?w="nocontent":304===t?w="notmodified":(w=x.state,f=x.data,y=x.error,h=!y)):(y=w,!t&&w||(w="error",t<0&&(t=0))),C.status=t,C.statusText=(e||w)+"",h?m.resolveWith(d,[f,w,C]):m.rejectWith(d,[C,w,y]),C.statusCode(v),v=void 0,u&&g.trigger(h?"ajaxSuccess":"ajaxError",[C,p,h?f:y]),b.fireWith(d,[C,w]),u&&(g.trigger("ajaxComplete",[C,p]),--O.active||O.event.trigger("ajaxStop")))}return C},getJSON:function(t,e,n){return O.get(t,e,n,"json")},getScript:function(t,e){return O.get(t,void 0,e,"script")}}),O.each(["get","post"],(function(t,e){O[e]=function(t,n,r,i){return v(n)&&(i=i||r,r=n,n=void 0),O.ajax(O.extend({url:t,type:e,dataType:i,data:n,success:r},O.isPlainObject(t)&&t))}})),O.ajaxPrefilter((function(t){var e;for(e in t.headers)"content-type"===e.toLowerCase()&&(t.contentType=t.headers[e]||"")})),O._evalUrl=function(t,e,n){return O.ajax({url:t,type:"GET",dataType:"script",cache:!0,async:!1,global:!1,converters:{"text script":function(){}},dataFilter:function(t){O.globalEval(t,e,n)}})},O.fn.extend({wrapAll:function(t){var e;return this[0]&&(v(t)&&(t=t.call(this[0])),e=O(t,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&e.insertBefore(this[0]),e.map((function(){var t=this;while(t.firstElementChild)t=t.firstElementChild;return t})).append(this)),this},wrapInner:function(t){return v(t)?this.each((function(e){O(this).wrapInner(t.call(this,e))})):this.each((function(){var e=O(this),n=e.contents();n.length?n.wrapAll(t):e.append(t)}))},wrap:function(t){var e=v(t);return this.each((function(n){O(this).wrapAll(e?t.call(this,n):t)}))},unwrap:function(t){return this.parent(t).not("body").each((function(){O(this).replaceWith(this.childNodes)})),this}}),O.expr.pseudos.hidden=function(t){return!O.expr.pseudos.visible(t)},O.expr.pseudos.visible=function(t){return!!(t.offsetWidth||t.offsetHeight||t.getClientRects().length)},O.ajaxSettings.xhr=function(){try{return new n.XMLHttpRequest}catch(t){}};var sn={0:200,1223:204},ln=O.ajaxSettings.xhr();b.cors=!!ln&&"withCredentials"in ln,b.ajax=ln=!!ln,O.ajaxTransport((function(t){var e,r;if(b.cors||ln&&!t.crossDomain)return{send:function(i,a){var o,s=t.xhr();if(s.open(t.type,t.url,t.async,t.username,t.password),t.xhrFields)for(o in t.xhrFields)s[o]=t.xhrFields[o];for(o in t.mimeType&&s.overrideMimeType&&s.overrideMimeType(t.mimeType),t.crossDomain||i["X-Requested-With"]||(i["X-Requested-With"]="XMLHttpRequest"),i)s.setRequestHeader(o,i[o]);e=function(t){return function(){e&&(e=r=s.onload=s.onerror=s.onabort=s.ontimeout=s.onreadystatechange=null,"abort"===t?s.abort():"error"===t?"number"!==typeof s.status?a(0,"error"):a(s.status,s.statusText):a(sn[s.status]||s.status,s.statusText,"text"!==(s.responseType||"text")||"string"!==typeof s.responseText?{binary:s.response}:{text:s.responseText},s.getAllResponseHeaders()))}},s.onload=e(),r=s.onerror=s.ontimeout=e("error"),void 0!==s.onabort?s.onabort=r:s.onreadystatechange=function(){4===s.readyState&&n.setTimeout((function(){e&&r()}))},e=e("abort");try{s.send(t.hasContent&&t.data||null)}catch(l){if(e)throw l}},abort:function(){e&&e()}}})),O.ajaxPrefilter((function(t){t.crossDomain&&(t.contents.script=!1)})),O.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(t){return O.globalEval(t),t}}}),O.ajaxPrefilter("script",(function(t){void 0===t.cache&&(t.cache=!1),t.crossDomain&&(t.type="GET")})),O.ajaxTransport("script",(function(t){var e,n;if(t.crossDomain||t.scriptAttrs)return{send:function(r,i){e=O("<script>").attr(t.scriptAttrs||{}).prop({charset:t.scriptCharset,src:t.url}).on("load error",n=function(t){e.remove(),n=null,t&&i("error"===t.type?404:200,t.type)}),x.head.appendChild(e[0])},abort:function(){n&&n()}}}));var cn=[],un=/(=)\?(?=&|$)|\?\?/;O.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var t=cn.pop()||O.expando+"_"+Me.guid++;return this[t]=!0,t}}),O.ajaxPrefilter("json jsonp",(function(t,e,r){var i,a,o,s=!1!==t.jsonp&&(un.test(t.url)?"url":"string"===typeof t.data&&0===(t.contentType||"").indexOf("application/x-www-form-urlencoded")&&un.test(t.data)&&"data");if(s||"jsonp"===t.dataTypes[0])return i=t.jsonpCallback=v(t.jsonpCallback)?t.jsonpCallback():t.jsonpCallback,s?t[s]=t[s].replace(un,"$1"+i):!1!==t.jsonp&&(t.url+=(Re.test(t.url)?"&":"?")+t.jsonp+"="+i),t.converters["script json"]=function(){return o||O.error(i+" was not called"),o[0]},t.dataTypes[0]="json",a=n[i],n[i]=function(){o=arguments},r.always((function(){void 0===a?O(n).removeProp(i):n[i]=a,t[i]&&(t.jsonpCallback=e.jsonpCallback,cn.push(i)),o&&v(a)&&a(o[0]),o=a=void 0})),"script"})),b.createHTMLDocument=function(){var t=x.implementation.createHTMLDocument("").body;return t.innerHTML="<form></form><form></form>",2===t.childNodes.length}(),O.parseHTML=function(t,e,n){return"string"!==typeof t?[]:("boolean"===typeof e&&(n=e,e=!1),e||(b.createHTMLDocument?(e=x.implementation.createHTMLDocument(""),r=e.createElement("base"),r.href=x.location.href,e.head.appendChild(r)):e=x),i=Q.exec(t),a=!n&&[],i?[e.createElement(i[1])]:(i=Nt([t],e,a),a&&a.length&&O(a).remove(),O.merge([],i.childNodes)));var r,i,a},O.fn.load=function(t,e,n){var r,i,a,o=this,s=t.indexOf(" ");return s>-1&&(r=De(t.slice(s)),t=t.slice(0,s)),v(e)?(n=e,e=void 0):e&&"object"===typeof e&&(i="POST"),o.length>0&&O.ajax({url:t,type:i||"GET",dataType:"html",data:e}).done((function(t){a=arguments,o.html(r?O("<div>").append(O.parseHTML(t)).find(r):t)})).always(n&&function(t,e){o.each((function(){n.apply(this,a||[t.responseText,e,t])}))}),this},O.expr.pseudos.animated=function(t){return O.grep(O.timers,(function(e){return t===e.elem})).length},O.offset={setOffset:function(t,e,n){var r,i,a,o,s,l,c,u=O.css(t,"position"),h=O(t),f={};"static"===u&&(t.style.position="relative"),s=h.offset(),a=O.css(t,"top"),l=O.css(t,"left"),c=("absolute"===u||"fixed"===u)&&(a+l).indexOf("auto")>-1,c?(r=h.position(),o=r.top,i=r.left):(o=parseFloat(a)||0,i=parseFloat(l)||0),v(e)&&(e=e.call(t,n,O.extend({},s))),null!=e.top&&(f.top=e.top-s.top+o),null!=e.left&&(f.left=e.left-s.left+i),"using"in e?e.using.call(t,f):h.css(f)}},O.fn.extend({offset:function(t){if(arguments.length)return void 0===t?this:this.each((function(e){O.offset.setOffset(this,t,e)}));var e,n,r=this[0];return r?r.getClientRects().length?(e=r.getBoundingClientRect(),n=r.ownerDocument.defaultView,{top:e.top+n.pageYOffset,left:e.left+n.pageXOffset}):{top:0,left:0}:void 0},position:function(){if(this[0]){var t,e,n,r=this[0],i={top:0,left:0};if("fixed"===O.css(r,"position"))e=r.getBoundingClientRect();else{e=this.offset(),n=r.ownerDocument,t=r.offsetParent||n.documentElement;while(t&&(t===n.body||t===n.documentElement)&&"static"===O.css(t,"position"))t=t.parentNode;t&&t!==r&&1===t.nodeType&&(i=O(t).offset(),i.top+=O.css(t,"borderTopWidth",!0),i.left+=O.css(t,"borderLeftWidth",!0))}return{top:e.top-i.top-O.css(r,"marginTop",!0),left:e.left-i.left-O.css(r,"marginLeft",!0)}}},offsetParent:function(){return this.map((function(){var t=this.offsetParent;while(t&&"static"===O.css(t,"position"))t=t.offsetParent;return t||yt}))}}),O.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},(function(t,e){var n="pageYOffset"===e;O.fn[t]=function(r){return rt(this,(function(t,r,i){var a;if(y(t)?a=t:9===t.nodeType&&(a=t.defaultView),void 0===i)return a?a[e]:t[r];a?a.scrollTo(n?a.pageXOffset:i,n?i:a.pageYOffset):t[r]=i}),t,r,arguments.length)}})),O.each(["top","left"],(function(t,e){O.cssHooks[e]=ne(b.pixelPosition,(function(t,n){if(n)return n=ee(t,e),Yt.test(n)?O(t).position()[e]+"px":n}))})),O.each({Height:"height",Width:"width"},(function(t,e){O.each({padding:"inner"+t,content:e,"":"outer"+t},(function(n,r){O.fn[r]=function(i,a){var o=arguments.length&&(n||"boolean"!==typeof i),s=n||(!0===i||!0===a?"margin":"border");return rt(this,(function(e,n,i){var a;return y(e)?0===r.indexOf("outer")?e["inner"+t]:e.document.documentElement["client"+t]:9===e.nodeType?(a=e.documentElement,Math.max(e.body["scroll"+t],a["scroll"+t],e.body["offset"+t],a["offset"+t],a["client"+t])):void 0===i?O.css(e,n,s):O.style(e,n,i,s)}),e,o?i:void 0,o)}}))})),O.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],(function(t,e){O.fn[e]=function(t){return this.on(e,t)}})),O.fn.extend({bind:function(t,e,n){return this.on(t,null,e,n)},unbind:function(t,e){return this.off(t,null,e)},delegate:function(t,e,n,r){return this.on(e,t,n,r)},undelegate:function(t,e,n){return 1===arguments.length?this.off(t,"**"):this.off(e,t||"**",n)},hover:function(t,e){return this.mouseenter(t).mouseleave(e||t)}}),O.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "),(function(t,e){O.fn[e]=function(t,n){return arguments.length>0?this.on(e,null,t,n):this.trigger(e)}}));var hn=/^[\s\uFEFF\xA0]+|([^\s\uFEFF\xA0])[\s\uFEFF\xA0]+$/g;O.proxy=function(t,e){var n,r,i;if("string"===typeof e&&(n=t[e],e=t,t=n),v(t))return r=l.call(arguments,2),i=function(){return t.apply(e||this,r.concat(l.call(arguments)))},i.guid=t.guid=t.guid||O.guid++,i},O.holdReady=function(t){t?O.readyWait++:O.ready(!0)},O.isArray=Array.isArray,O.parseJSON=JSON.parse,O.nodeName=I,O.isFunction=v,O.isWindow=y,O.camelCase=st,O.type=C,O.now=Date.now,O.isNumeric=function(t){var e=O.type(t);return("number"===e||"string"===e)&&!isNaN(t-parseFloat(t))},O.trim=function(t){return null==t?"":(t+"").replace(hn,"$1")},r=[],i=function(){return O}.apply(e,r),void 0===i||(t.exports=i);var fn=n.jQuery,pn=n.$;return O.noConflict=function(t){return n.$===O&&(n.$=pn),t&&n.jQuery===O&&(n.jQuery=fn),O},"undefined"===typeof a&&(n.jQuery=n.$=O),O}))},1290:function(t,e){function n(t){var e=typeof t;return"string"==e||"number"==e||"symbol"==e||"boolean"==e?"__proto__"!==t:null===t}t.exports=n},1310:function(t,e){function n(t){return null!=t&&"object"==typeof t}t.exports=n},1368:function(t,e,n){var r=n("da03"),i=function(){var t=/[^.]+$/.exec(r&&r.keys&&r.keys.IE_PROTO||"");return t?"Symbol(src)_1."+t:""}();function a(t){return!!i&&i in t}t.exports=a},"13d2":function(t,e,n){var r=n("e330"),i=n("d039"),a=n("1626"),o=n("1a2d"),s=n("83ab"),l=n("5e77").CONFIGURABLE,c=n("8925"),u=n("69f3"),h=u.enforce,f=u.get,p=String,d=Object.defineProperty,g=r("".slice),m=r("".replace),b=r([].join),v=s&&!i((function(){return 8!==d((function(){}),"length",{value:8}).length})),y=String(String).split("String"),x=t.exports=function(t,e,n){"Symbol("===g(p(e),0,7)&&(e="["+m(p(e),/^Symbol\(([^)]*)\)/,"$1")+"]"),n&&n.getter&&(e="get "+e),n&&n.setter&&(e="set "+e),(!o(t,"name")||l&&t.name!==e)&&(s?d(t,"name",{value:e,configurable:!0}):t.name=e),v&&n&&o(n,"arity")&&t.length!==n.arity&&d(t,"length",{value:n.arity});try{n&&o(n,"constructor")&&n.constructor?s&&d(t,"prototype",{writable:!1}):t.prototype&&(t.prototype=void 0)}catch(i){}var r=h(t);return o(r,"source")||(r.source=b(y,"string"==typeof e?e:"")),t};Function.prototype.toString=x((function(){return a(this)&&f(this).source||c(this)}),"toString")},"14d9":function(t,e,n){"use strict";var r=n("23e7"),i=n("7b0b"),a=n("07fa"),o=n("3a34"),s=n("3511"),l=n("d039"),c=l((function(){return 4294967297!==[].push.call({length:4294967296},1)})),u=function(){try{Object.defineProperty([],"length",{writable:!1}).push()}catch(t){return t instanceof TypeError}},h=c||!u();r({target:"Array",proto:!0,arity:1,forced:h},{push:function(t){var e=i(this),n=a(e),r=arguments.length;s(n+r);for(var l=0;l<r;l++)e[n]=arguments[l],n++;return o(e,n),n}})},1626:function(t,e,n){var r=n("8ea1"),i=r.all;t.exports=r.IS_HTMLDDA?function(t){return"function"==typeof t||t===i}:function(t){return"function"==typeof t}},"164e":function(e,n){e.exports=t},"1a2d":function(t,e,n){var r=n("e330"),i=n("7b0b"),a=r({}.hasOwnProperty);t.exports=Object.hasOwn||function(t,e){return a(i(t),e)}},"1a2d0":function(t,e,n){var r=n("42a2"),i=n("1310"),a="[object Map]";function o(t){return i(t)&&r(t)==a}t.exports=o},"1a8c":function(t,e){function n(t){var e=typeof t;return null!=t&&("object"==e||"function"==e)}t.exports=n},"1bac":function(t,e,n){var r=n("7d1f"),i=n("a029"),a=n("9934");function o(t){return r(t,a,i)}t.exports=o},"1c3c":function(t,e,n){var r=n("9e69"),i=n("2474"),a=n("9638"),o=n("a2be"),s=n("edfa"),l=n("ac41"),c=1,u=2,h="[object Boolean]",f="[object Date]",p="[object Error]",d="[object Map]",g="[object Number]",m="[object RegExp]",b="[object Set]",v="[object String]",y="[object Symbol]",x="[object ArrayBuffer]",A="[object DataView]",w=r?r.prototype:void 0,C=w?w.valueOf:void 0;function S(t,e,n,r,w,S,k){switch(n){case A:if(t.byteLength!=e.byteLength||t.byteOffset!=e.byteOffset)return!1;t=t.buffer,e=e.buffer;case x:return!(t.byteLength!=e.byteLength||!S(new i(t),new i(e)));case h:case f:case g:return a(+t,+e);case p:return t.name==e.name&&t.message==e.message;case m:case v:return t==e+"";case d:var O=s;case b:var T=r&c;if(O||(O=l),t.size!=e.size&&!T)return!1;var I=k.get(t);if(I)return I==e;r|=u,k.set(t,e);var E=o(O(t),O(e),r,w,S,k);return k["delete"](t),E;case y:if(C)return C.call(t)==C.call(e)}return!1}t.exports=S},"1cec":function(t,e,n){var r=n("0b07"),i=n("2b3e"),a=r(i,"Promise");t.exports=a},"1d80":function(t,e,n){var r=n("7234"),i=TypeError;t.exports=function(t){if(r(t))throw i("Can't call method on "+t);return t}},"1efc":function(t,e){function n(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=e?1:0,e}t.exports=n},"1fc8":function(t,e,n){var r=n("4245");function i(t,e){var n=r(this,t),i=n.size;return n.set(t,e),this.size+=n.size==i?0:1,this}t.exports=i},2286:function(t,e,n){var r=n("85e3"),i=Math.max;function a(t,e,n){return e=i(void 0===e?t.length-1:e,0),function(){var a=arguments,o=-1,s=i(a.length-e,0),l=Array(s);while(++o<s)l[o]=a[e+o];o=-1;var c=Array(e+1);while(++o<e)c[o]=a[o];return c[e]=n(l),r(t,this,c)}}t.exports=a},"23cb":function(t,e,n){var r=n("5926"),i=Math.max,a=Math.min;t.exports=function(t,e){var n=r(t);return n<0?i(n+e,0):a(n,e)}},"23e7":function(t,e,n){var r=n("da84"),i=n("06cf").f,a=n("9112"),o=n("cb2d"),s=n("6374"),l=n("e893"),c=n("94ca");t.exports=function(t,e){var n,u,h,f,p,d,g=t.target,m=t.global,b=t.stat;if(u=m?r:b?r[g]||s(g,{}):(r[g]||{}).prototype,u)for(h in e){if(p=e[h],t.dontCallGetSet?(d=i(u,h),f=d&&d.value):f=u[h],n=c(m?h:g+(b?".":"#")+h,t.forced),!n&&void 0!==f){if(typeof p==typeof f)continue;l(p,f)}(t.sham||f&&f.sham)&&a(p,"sham",!0),o(u,h,p,t)}}},"241c":function(t,e,n){var r=n("ca84"),i=n("7839"),a=i.concat("length","prototype");e.f=Object.getOwnPropertyNames||function(t){return r(t,a)}},2474:function(t,e,n){var r=n("2b3e"),i=r.Uint8Array;t.exports=i},2478:function(t,e,n){var r=n("4245");function i(t){return r(this,t).get(t)}t.exports=i},2524:function(t,e,n){var r=n("6044"),i="__lodash_hash_undefined__";function a(t,e){var n=this.__data__;return this.size+=this.has(t)?0:1,n[t]=r&&void 0===e?i:e,this}t.exports=a},"253c":function(t,e,n){var r=n("3729"),i=n("1310"),a="[object Arguments]";function o(t){return i(t)&&r(t)==a}t.exports=o},"28c9":function(t,e){function n(){this.__data__=[],this.size=0}t.exports=n},"29f3":function(t,e){var n=Object.prototype,r=n.toString;function i(t){return r.call(t)}t.exports=i},"2b3e":function(t,e,n){var r=n("585a"),i="object"==typeof self&&self&&self.Object===Object&&self,a=r||i||Function("return this")();t.exports=a},"2d00":function(t,e,n){var r,i,a=n("da84"),o=n("342f"),s=a.process,l=a.Deno,c=s&&s.versions||l&&l.version,u=c&&c.v8;u&&(r=u.split("."),i=r[0]>0&&r[0]<4?1:+(r[0]+r[1])),!i&&o&&(r=o.match(/Edge\/(\d+)/),(!r||r[1]>=74)&&(r=o.match(/Chrome\/(\d+)/),r&&(i=+r[1]))),t.exports=i},"2d7c":function(t,e){function n(t,e){var n=-1,r=null==t?0:t.length,i=0,a=[];while(++n<r){var o=t[n];e(o,n,t)&&(a[i++]=o)}return a}t.exports=n},"2dcb":function(t,e,n){var r=n("91e9"),i=r(Object.getPrototypeOf,Object);t.exports=i},"2ec1":function(t,e,n){var r=n("100e"),i=n("9aff");function a(t){return r((function(e,n){var r=-1,a=n.length,o=a>1?n[a-1]:void 0,s=a>2?n[2]:void 0;o=t.length>3&&"function"==typeof o?(a--,o):void 0,s&&i(n[0],n[1],s)&&(o=a<3?void 0:o,a=1),e=Object(e);while(++r<a){var l=n[r];l&&t(e,l,r,o)}return e}))}t.exports=a},"2fcc":function(t,e){function n(t){var e=this.__data__,n=e["delete"](t);return this.size=e.size,n}t.exports=n},"30c9":function(t,e,n){var r=n("9520"),i=n("b218");function a(t){return null!=t&&i(t.length)&&!r(t)}t.exports=a},"32b3":function(t,e,n){var r=n("872a"),i=n("9638"),a=Object.prototype,o=a.hasOwnProperty;function s(t,e,n){var a=t[e];o.call(t,e)&&i(a,n)&&(void 0!==n||e in t)||r(t,e,n)}t.exports=s},"32f4":function(t,e,n){var r=n("2d7c"),i=n("d327"),a=Object.prototype,o=a.propertyIsEnumerable,s=Object.getOwnPropertySymbols,l=s?function(t){return null==t?[]:(t=Object(t),r(s(t),(function(e){return o.call(t,e)})))}:i;t.exports=l},"342f":function(t,e){t.exports="undefined"!=typeof navigator&&String(navigator.userAgent)||""},"34ac":function(t,e,n){var r=n("9520"),i=n("1368"),a=n("1a8c"),o=n("dc57"),s=/[\\^$.*+?()[\]{}|]/g,l=/^\[object .+?Constructor\]$/,c=Function.prototype,u=Object.prototype,h=c.toString,f=u.hasOwnProperty,p=RegExp("^"+h.call(f).replace(s,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function d(t){if(!a(t)||i(t))return!1;var e=r(t)?p:l;return e.test(o(t))}t.exports=d},3511:function(t,e){var n=TypeError,r=9007199254740991;t.exports=function(t){if(t>r)throw n("Maximum allowed index exceeded");return t}},3698:function(t,e){function n(t,e){return null==t?void 0:t[e]}t.exports=n},3729:function(t,e,n){var r=n("9e69"),i=n("00fd"),a=n("29f3"),o="[object Null]",s="[object Undefined]",l=r?r.toStringTag:void 0;function c(t){return null==t?void 0===t?s:o:l&&l in Object(t)?i(t):a(t)}t.exports=c},3818:function(t,e,n){var r=n("7e64"),i=n("8057"),a=n("32b3"),o=n("5b01"),s=n("0f0f"),l=n("e538"),c=n("4359"),u=n("54eb"),h=n("1041"),f=n("a994"),p=n("1bac"),d=n("42a2"),g=n("c87c"),m=n("c2b6"),b=n("fa21"),v=n("6747"),y=n("0d24"),x=n("cc45"),A=n("1a8c"),w=n("d7ee"),C=n("ec69"),S=n("9934"),k=1,O=2,T=4,I="[object Arguments]",E="[object Array]",D="[object Boolean]",L="[object Date]",G="[object Error]",j="[object Function]",N="[object GeneratorFunction]",M="[object Map]",R="[object Number]",B="[object Object]",V="[object RegExp]",P="[object Set]",z="[object String]",Q="[object Symbol]",W="[object WeakMap]",F="[object ArrayBuffer]",q="[object DataView]",X="[object Float32Array]",H="[object Float64Array]",Z="[object Int8Array]",J="[object Int16Array]",U="[object Int32Array]",Y="[object Uint8Array]",K="[object Uint8ClampedArray]",_="[object Uint16Array]",$="[object Uint32Array]",tt={};function et(t,e,n,E,D,L){var G,M=e&k,R=e&O,V=e&T;if(n&&(G=D?n(t,E,D,L):n(t)),void 0!==G)return G;if(!A(t))return t;var P=v(t);if(P){if(G=g(t),!M)return c(t,G)}else{var z=d(t),Q=z==j||z==N;if(y(t))return l(t,M);if(z==B||z==I||Q&&!D){if(G=R||Q?{}:b(t),!M)return R?h(t,s(G,t)):u(t,o(G,t))}else{if(!tt[z])return D?t:{};G=m(t,z,M)}}L||(L=new r);var W=L.get(t);if(W)return W;L.set(t,G),w(t)?t.forEach((function(r){G.add(et(r,e,n,r,t,L))})):x(t)&&t.forEach((function(r,i){G.set(i,et(r,e,n,i,t,L))}));var F=V?R?p:f:R?S:C,q=P?void 0:F(t);return i(q||t,(function(r,i){q&&(i=r,r=t[i]),a(G,i,et(r,e,n,i,t,L))})),G}tt[I]=tt[E]=tt[F]=tt[q]=tt[D]=tt[L]=tt[X]=tt[H]=tt[Z]=tt[J]=tt[U]=tt[M]=tt[R]=tt[B]=tt[V]=tt[P]=tt[z]=tt[Q]=tt[Y]=tt[K]=tt[_]=tt[$]=!0,tt[G]=tt[j]=tt[W]=!1,t.exports=et},"39ff":function(t,e,n){var r=n("0b07"),i=n("2b3e"),a=r(i,"WeakMap");t.exports=a},"3a34":function(t,e,n){"use strict";var r=n("83ab"),i=n("e8b5"),a=TypeError,o=Object.getOwnPropertyDescriptor,s=r&&!function(){if(void 0!==this)return!0;try{Object.defineProperty([],"length",{writable:!1}).length=1}catch(t){return t instanceof TypeError}}();t.exports=s?function(t,e){if(i(t)&&!o(t,"length").writable)throw a("Cannot set read only .length");return t.length=e}:function(t,e){return t.length=e}},"3a9b":function(t,e,n){var r=n("e330");t.exports=r({}.isPrototypeOf)},"3b4a":function(t,e,n){var r=n("0b07"),i=function(){try{var t=r(Object,"defineProperty");return t({},"",{}),t}catch(e){}}();t.exports=i},"40d5":function(t,e,n){var r=n("d039");t.exports=!r((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")}))},"41c3":function(t,e,n){var r=n("1a8c"),i=n("eac5"),a=n("ec8c"),o=Object.prototype,s=o.hasOwnProperty;function l(t){if(!r(t))return a(t);var e=i(t),n=[];for(var o in t)("constructor"!=o||!e&&s.call(t,o))&&n.push(o);return n}t.exports=l},4245:function(t,e,n){var r=n("1290");function i(t,e){var n=t.__data__;return r(e)?n["string"==typeof e?"string":"hash"]:n.map}t.exports=i},42454:function(t,e,n){var r=n("f909"),i=n("2ec1"),a=i((function(t,e,n){r(t,e,n)}));t.exports=a},4284:function(t,e){function n(t,e){var n=-1,r=null==t?0:t.length;while(++n<r)if(e(t[n],n,t))return!0;return!1}t.exports=n},"42a2":function(t,e,n){var r=n("b5a7"),i=n("79bc"),a=n("1cec"),o=n("c869"),s=n("39ff"),l=n("3729"),c=n("dc57"),u="[object Map]",h="[object Object]",f="[object Promise]",p="[object Set]",d="[object WeakMap]",g="[object DataView]",m=c(r),b=c(i),v=c(a),y=c(o),x=c(s),A=l;(r&&A(new r(new ArrayBuffer(1)))!=g||i&&A(new i)!=u||a&&A(a.resolve())!=f||o&&A(new o)!=p||s&&A(new s)!=d)&&(A=function(t){var e=l(t),n=e==h?t.constructor:void 0,r=n?c(n):"";if(r)switch(r){case m:return g;case b:return u;case v:return f;case y:return p;case x:return d}return e}),t.exports=A},4359:function(t,e){function n(t,e){var n=-1,r=t.length;e||(e=Array(r));while(++n<r)e[n]=t[n];return e}t.exports=n},"44ad":function(t,e,n){var r=n("e330"),i=n("d039"),a=n("c6b6"),o=Object,s=r("".split);t.exports=i((function(){return!o("z").propertyIsEnumerable(0)}))?function(t){return"String"==a(t)?s(t,""):o(t)}:o},"485a":function(t,e,n){var r=n("c65b"),i=n("1626"),a=n("861d"),o=TypeError;t.exports=function(t,e){var n,s;if("string"===e&&i(n=t.toString)&&!a(s=r(n,t)))return s;if(i(n=t.valueOf)&&!a(s=r(n,t)))return s;if("string"!==e&&i(n=t.toString)&&!a(s=r(n,t)))return s;throw o("Can't convert object to primitive value")}},"49f4":function(t,e,n){var r=n("6044");function i(){this.__data__=r?r(null):{},this.size=0}t.exports=i},"4d64":function(t,e,n){var r=n("fc6a"),i=n("23cb"),a=n("07fa"),o=function(t){return function(e,n,o){var s,l=r(e),c=a(l),u=i(o,c);if(t&&n!=n){while(c>u)if(s=l[u++],s!=s)return!0}else for(;c>u;u++)if((t||u in l)&&l[u]===n)return t||u||0;return!t&&-1}};t.exports={includes:o(!0),indexOf:o(!1)}},"4f4d":function(t,e,n){"use strict";t.exports={aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],grey:[128,128,128],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],rebeccapurple:[102,51,153],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]}},"4f50":function(t,e,n){var r=n("b760"),i=n("e538"),a=n("c8fe"),o=n("4359"),s=n("fa21"),l=n("d370"),c=n("6747"),u=n("dcbe"),h=n("0d24"),f=n("9520"),p=n("1a8c"),d=n("60ed"),g=n("73ac"),m=n("8adb"),b=n("8de2");function v(t,e,n,v,y,x,A){var w=m(t,n),C=m(e,n),S=A.get(C);if(S)r(t,n,S);else{var k=x?x(w,C,n+"",t,e,A):void 0,O=void 0===k;if(O){var T=c(C),I=!T&&h(C),E=!T&&!I&&g(C);k=C,T||I||E?c(w)?k=w:u(w)?k=o(w):I?(O=!1,k=i(C,!0)):E?(O=!1,k=a(C,!0)):k=[]:d(C)||l(C)?(k=w,l(w)?k=b(w):p(w)&&!f(w)||(k=s(C))):O=!1}O&&(A.set(C,k),y(k,C,v,x,A),A["delete"](C)),r(t,n,k)}}t.exports=v},"50c4":function(t,e,n){var r=n("5926"),i=Math.min;t.exports=function(t){return t>0?i(r(t),9007199254740991):0}},"50d8":function(t,e){function n(t,e){var n=-1,r=Array(t);while(++n<t)r[n]=e(n);return r}t.exports=n},"54eb":function(t,e,n){var r=n("8eeb"),i=n("32f4");function a(t,e){return r(t,i(t),e)}t.exports=a},"55a3":function(t,e){function n(t){return this.__data__.has(t)}t.exports=n},5692:function(t,e,n){var r=n("c430"),i=n("c6cd");(t.exports=function(t,e){return i[t]||(i[t]=void 0!==e?e:{})})("versions",[]).push({version:"3.30.2",mode:r?"pure":"global",copyright:"© 2014-2023 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.30.2/LICENSE",source:"https://github.com/zloirock/core-js"})},"56ef":function(t,e,n){var r=n("d066"),i=n("e330"),a=n("241c"),o=n("7418"),s=n("825a"),l=i([].concat);t.exports=r("Reflect","ownKeys")||function(t){var e=a.f(s(t)),n=o.f;return n?l(e,n(t)):e}},"57a5":function(t,e,n){var r=n("91e9"),i=r(Object.keys,Object);t.exports=i},"585a":function(t,e,n){(function(e){var n="object"==typeof e&&e&&e.Object===Object&&e;t.exports=n}).call(this,n("c8ba"))},5880:function(t,n){t.exports=e},5926:function(t,e,n){var r=n("b42e");t.exports=function(t){var e=+t;return e!==e||0===e?0:r(e)}},"59ed":function(t,e,n){var r=n("1626"),i=n("0d51"),a=TypeError;t.exports=function(t){if(r(t))return t;throw a(i(t)+" is not a function")}},"5b01":function(t,e,n){var r=n("8eeb"),i=n("ec69");function a(t,e){return t&&r(e,i(e),t)}t.exports=a},"5c6c":function(t,e){t.exports=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}}},"5d89":function(t,e,n){var r=n("f8af");function i(t,e){var n=e?r(t.buffer):t.buffer;return new t.constructor(n,t.byteOffset,t.byteLength)}t.exports=i},"5e2e":function(t,e,n){var r=n("28c9"),i=n("69d5"),a=n("b4c0"),o=n("fba5"),s=n("67ca");function l(t){var e=-1,n=null==t?0:t.length;this.clear();while(++e<n){var r=t[e];this.set(r[0],r[1])}}l.prototype.clear=r,l.prototype["delete"]=i,l.prototype.get=a,l.prototype.has=o,l.prototype.set=s,t.exports=l},"5e77":function(t,e,n){var r=n("83ab"),i=n("1a2d"),a=Function.prototype,o=r&&Object.getOwnPropertyDescriptor,s=i(a,"name"),l=s&&"something"===function(){}.name,c=s&&(!r||r&&o(a,"name").configurable);t.exports={EXISTS:s,PROPER:l,CONFIGURABLE:c}},6044:function(t,e,n){var r=n("0b07"),i=r(Object,"create");t.exports=i},"60ed":function(t,e,n){var r=n("3729"),i=n("2dcb"),a=n("1310"),o="[object Object]",s=Function.prototype,l=Object.prototype,c=s.toString,u=l.hasOwnProperty,h=c.call(Object);function f(t){if(!a(t)||r(t)!=o)return!1;var e=i(t);if(null===e)return!0;var n=u.call(e,"constructor")&&e.constructor;return"function"==typeof n&&n instanceof n&&c.call(n)==h}t.exports=f},"62e4":function(t,e){t.exports=function(t){return t.webpackPolyfill||(t.deprecate=function(){},t.paths=[],t.children||(t.children=[]),Object.defineProperty(t,"loaded",{enumerable:!0,get:function(){return t.l}}),Object.defineProperty(t,"id",{enumerable:!0,get:function(){return t.i}}),t.webpackPolyfill=1),t}},6374:function(t,e,n){var r=n("da84"),i=Object.defineProperty;t.exports=function(t,e){try{i(r,t,{value:e,configurable:!0,writable:!0})}catch(n){r[t]=e}return e}},"63ea":function(t,e,n){var r=n("c05f");function i(t,e){return r(t,e)}t.exports=i},6747:function(t,e){var n=Array.isArray;t.exports=n},"67ca":function(t,e,n){var r=n("cb5a");function i(t,e){var n=this.__data__,i=r(n,t);return i<0?(++this.size,n.push([t,e])):n[i][1]=e,this}t.exports=i},"69d5":function(t,e,n){var r=n("cb5a"),i=Array.prototype,a=i.splice;function o(t){var e=this.__data__,n=r(e,t);if(n<0)return!1;var i=e.length-1;return n==i?e.pop():a.call(e,n,1),--this.size,!0}t.exports=o},"69f3":function(t,e,n){var r,i,a,o=n("cdce"),s=n("da84"),l=n("861d"),c=n("9112"),u=n("1a2d"),h=n("c6cd"),f=n("f772"),p=n("d012"),d="Object already initialized",g=s.TypeError,m=s.WeakMap,b=function(t){return a(t)?i(t):r(t,{})},v=function(t){return function(e){var n;if(!l(e)||(n=i(e)).type!==t)throw g("Incompatible receiver, "+t+" required");return n}};if(o||h.state){var y=h.state||(h.state=new m);y.get=y.get,y.has=y.has,y.set=y.set,r=function(t,e){if(y.has(t))throw g(d);return e.facade=t,y.set(t,e),e},i=function(t){return y.get(t)||{}},a=function(t){return y.has(t)}}else{var x=f("state");p[x]=!0,r=function(t,e){if(u(t,x))throw g(d);return e.facade=t,c(t,x,e),e},i=function(t){return u(t,x)?t[x]:{}},a=function(t){return u(t,x)}}t.exports={set:r,get:i,has:a,enforce:b,getterFor:v}},"6f6c":function(t,e){var n=/\w*$/;function r(t){var e=new t.constructor(t.source,n.exec(t));return e.lastIndex=t.lastIndex,e}t.exports=r},"6fcd":function(t,e,n){var r=n("50d8"),i=n("d370"),a=n("6747"),o=n("0d24"),s=n("c098"),l=n("73ac"),c=Object.prototype,u=c.hasOwnProperty;function h(t,e){var n=a(t),c=!n&&i(t),h=!n&&!c&&o(t),f=!n&&!c&&!h&&l(t),p=n||c||h||f,d=p?r(t.length,String):[],g=d.length;for(var m in t)!e&&!u.call(t,m)||p&&("length"==m||h&&("offset"==m||"parent"==m)||f&&("buffer"==m||"byteLength"==m||"byteOffset"==m)||s(m,g))||d.push(m);return d}t.exports=h},7234:function(t,e){t.exports=function(t){return null===t||void 0===t}},"72af":function(t,e,n){var r=n("99cd"),i=r();t.exports=i},"72f0":function(t,e){function n(t){return function(){return t}}t.exports=n},"73ac":function(t,e,n){var r=n("743f"),i=n("b047"),a=n("99d3"),o=a&&a.isTypedArray,s=o?i(o):r;t.exports=s},7418:function(t,e){e.f=Object.getOwnPropertySymbols},"743f":function(t,e,n){var r=n("3729"),i=n("b218"),a=n("1310"),o="[object Arguments]",s="[object Array]",l="[object Boolean]",c="[object Date]",u="[object Error]",h="[object Function]",f="[object Map]",p="[object Number]",d="[object Object]",g="[object RegExp]",m="[object Set]",b="[object String]",v="[object WeakMap]",y="[object ArrayBuffer]",x="[object DataView]",A="[object Float32Array]",w="[object Float64Array]",C="[object Int8Array]",S="[object Int16Array]",k="[object Int32Array]",O="[object Uint8Array]",T="[object Uint8ClampedArray]",I="[object Uint16Array]",E="[object Uint32Array]",D={};function L(t){return a(t)&&i(t.length)&&!!D[r(t)]}D[A]=D[w]=D[C]=D[S]=D[k]=D[O]=D[T]=D[I]=D[E]=!0,D[o]=D[s]=D[y]=D[l]=D[x]=D[c]=D[u]=D[h]=D[f]=D[p]=D[d]=D[g]=D[m]=D[b]=D[v]=!1,t.exports=L},7530:function(t,e,n){var r=n("1a8c"),i=Object.create,a=function(){function t(){}return function(e){if(!r(e))return{};if(i)return i(e);t.prototype=e;var n=new t;return t.prototype=void 0,n}}();t.exports=a},"77ad":function(t,e,n){"use strict";n("a520")},7839:function(t,e){t.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},"79bc":function(t,e,n){var r=n("0b07"),i=n("2b3e"),a=r(i,"Map");t.exports=a},"7a48":function(t,e,n){var r=n("6044"),i=Object.prototype,a=i.hasOwnProperty;function o(t){var e=this.__data__;return r?void 0!==e[t]:a.call(e,t)}t.exports=o},"7b0b":function(t,e,n){var r=n("1d80"),i=Object;t.exports=function(t){return i(r(t))}},"7b83":function(t,e,n){var r=n("7c64"),i=n("93ed"),a=n("2478"),o=n("a524"),s=n("1fc8");function l(t){var e=-1,n=null==t?0:t.length;this.clear();while(++e<n){var r=t[e];this.set(r[0],r[1])}}l.prototype.clear=r,l.prototype["delete"]=i,l.prototype.get=a,l.prototype.has=o,l.prototype.set=s,t.exports=l},"7b97":function(t,e,n){var r=n("7e64"),i=n("a2be"),a=n("1c3c"),o=n("b1e5"),s=n("42a2"),l=n("6747"),c=n("0d24"),u=n("73ac"),h=1,f="[object Arguments]",p="[object Array]",d="[object Object]",g=Object.prototype,m=g.hasOwnProperty;function b(t,e,n,g,b,v){var y=l(t),x=l(e),A=y?p:s(t),w=x?p:s(e);A=A==f?d:A,w=w==f?d:w;var C=A==d,S=w==d,k=A==w;if(k&&c(t)){if(!c(e))return!1;y=!0,C=!1}if(k&&!C)return v||(v=new r),y||u(t)?i(t,e,n,g,b,v):a(t,e,A,n,g,b,v);if(!(n&h)){var O=C&&m.call(t,"__wrapped__"),T=S&&m.call(e,"__wrapped__");if(O||T){var I=O?t.value():t,E=T?e.value():e;return v||(v=new r),b(I,E,n,g,v)}}return!!k&&(v||(v=new r),o(t,e,n,g,b,v))}t.exports=b},"7c64":function(t,e,n){var r=n("e24b"),i=n("5e2e"),a=n("79bc");function o(){this.size=0,this.__data__={hash:new r,map:new(a||i),string:new r}}t.exports=o},"7d1f":function(t,e,n){var r=n("087d"),i=n("6747");function a(t,e,n){var a=e(t);return i(t)?a:r(a,n(t))}t.exports=a},"7e64":function(t,e,n){var r=n("5e2e"),i=n("efb6"),a=n("2fcc"),o=n("802a"),s=n("55a3"),l=n("d02c");function c(t){var e=this.__data__=new r(t);this.size=e.size}c.prototype.clear=i,c.prototype["delete"]=a,c.prototype.get=o,c.prototype.has=s,c.prototype.set=l,t.exports=c},"7ed2":function(t,e){var n="__lodash_hash_undefined__";function r(t){return this.__data__.set(t,n),this}t.exports=r},"802a":function(t,e){function n(t){return this.__data__.get(t)}t.exports=n},8057:function(t,e){function n(t,e){var n=-1,r=null==t?0:t.length;while(++n<r)if(!1===e(t[n],n,t))break;return t}t.exports=n},"825a":function(t,e,n){var r=n("861d"),i=String,a=TypeError;t.exports=function(t){if(r(t))return t;throw a(i(t)+" is not an object")}},"83ab":function(t,e,n){var r=n("d039");t.exports=!r((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]}))},"85e3":function(t,e){function n(t,e,n){switch(n.length){case 0:return t.call(e);case 1:return t.call(e,n[0]);case 2:return t.call(e,n[0],n[1]);case 3:return t.call(e,n[0],n[1],n[2])}return t.apply(e,n)}t.exports=n},"861d":function(t,e,n){var r=n("1626"),i=n("8ea1"),a=i.all;t.exports=i.IS_HTMLDDA?function(t){return"object"==typeof t?null!==t:r(t)||t===a}:function(t){return"object"==typeof t?null!==t:r(t)}},"872a":function(t,e,n){var r=n("3b4a");function i(t,e,n){"__proto__"==e&&r?r(t,e,{configurable:!0,enumerable:!0,value:n,writable:!0}):t[e]=n}t.exports=i},8925:function(t,e,n){var r=n("e330"),i=n("1626"),a=n("c6cd"),o=r(Function.toString);i(a.inspectSource)||(a.inspectSource=function(t){return o(t)}),t.exports=a.inspectSource},"8adb":function(t,e){function n(t,e){if(("constructor"!==e||"function"!==typeof t[e])&&"__proto__"!=e)return t[e]}t.exports=n},"8bbf":function(t,e){t.exports=n},"8ce1":function(t,e,n){"use strict";n("de44")},"8de2":function(t,e,n){var r=n("8eeb"),i=n("9934");function a(t){return r(t,i(t))}t.exports=a},"8ea1":function(t,e){var n="object"==typeof document&&document.all,r="undefined"==typeof n&&void 0!==n;t.exports={all:n,IS_HTMLDDA:r}},"8eeb":function(t,e,n){var r=n("32b3"),i=n("872a");function a(t,e,n,a){var o=!n;n||(n={});var s=-1,l=e.length;while(++s<l){var c=e[s],u=a?a(n[c],t[c],c,n,t):void 0;void 0===u&&(u=t[c]),o?i(n,c,u):r(n,c,u)}return n}t.exports=a},"90e3":function(t,e,n){var r=n("e330"),i=0,a=Math.random(),o=r(1..toString);t.exports=function(t){return"Symbol("+(void 0===t?"":t)+")_"+o(++i+a,36)}},9112:function(t,e,n){var r=n("83ab"),i=n("9bf2"),a=n("5c6c");t.exports=r?function(t,e,n){return i.f(t,e,a(1,n))}:function(t,e,n){return t[e]=n,t}},"91e9":function(t,e){function n(t,e){return function(n){return t(e(n))}}t.exports=n},"93ed":function(t,e,n){var r=n("4245");function i(t){var e=r(this,t)["delete"](t);return this.size-=e?1:0,e}t.exports=i},"94ca":function(t,e,n){var r=n("d039"),i=n("1626"),a=/#|\.prototype\./,o=function(t,e){var n=l[s(t)];return n==u||n!=c&&(i(e)?r(e):!!e)},s=o.normalize=function(t){return String(t).replace(a,".").toLowerCase()},l=o.data={},c=o.NATIVE="N",u=o.POLYFILL="P";t.exports=o},9520:function(t,e,n){var r=n("3729"),i=n("1a8c"),a="[object AsyncFunction]",o="[object Function]",s="[object GeneratorFunction]",l="[object Proxy]";function c(t){if(!i(t))return!1;var e=r(t);return e==o||e==s||e==a||e==l}t.exports=c},9638:function(t,e){function n(t,e){return t===e||t!==t&&e!==e}t.exports=n},9934:function(t,e,n){var r=n("6fcd"),i=n("41c3"),a=n("30c9");function o(t){return a(t)?r(t,!0):i(t)}t.exports=o},"99cd":function(t,e){function n(t){return function(e,n,r){var i=-1,a=Object(e),o=r(e),s=o.length;while(s--){var l=o[t?s:++i];if(!1===n(a[l],l,a))break}return e}}t.exports=n},"99d3":function(t,e,n){(function(t){var r=n("585a"),i=e&&!e.nodeType&&e,a=i&&"object"==typeof t&&t&&!t.nodeType&&t,o=a&&a.exports===i,s=o&&r.process,l=function(){try{var t=a&&a.require&&a.require("util").types;return t||s&&s.binding&&s.binding("util")}catch(e){}}();t.exports=l}).call(this,n("62e4")(t))},"9aff":function(t,e,n){var r=n("9638"),i=n("30c9"),a=n("c098"),o=n("1a8c");function s(t,e,n){if(!o(n))return!1;var s=typeof e;return!!("number"==s?i(n)&&a(e,n.length):"string"==s&&e in n)&&r(n[e],t)}t.exports=s},"9bf2":function(t,e,n){var r=n("83ab"),i=n("0cfb"),a=n("aed9"),o=n("825a"),s=n("a04b"),l=TypeError,c=Object.defineProperty,u=Object.getOwnPropertyDescriptor,h="enumerable",f="configurable",p="writable";e.f=r?a?function(t,e,n){if(o(t),e=s(e),o(n),"function"===typeof t&&"prototype"===e&&"value"in n&&p in n&&!n[p]){var r=u(t,e);r&&r[p]&&(t[e]=n.value,n={configurable:f in n?n[f]:r[f],enumerable:h in n?n[h]:r[h],writable:!1})}return c(t,e,n)}:c:function(t,e,n){if(o(t),e=s(e),o(n),i)try{return c(t,e,n)}catch(r){}if("get"in n||"set"in n)throw l("Accessors not supported");return"value"in n&&(t[e]=n.value),t}},"9e69":function(t,e,n){var r=n("2b3e"),i=r.Symbol;t.exports=i},a029:function(t,e,n){var r=n("087d"),i=n("2dcb"),a=n("32f4"),o=n("d327"),s=Object.getOwnPropertySymbols,l=s?function(t){var e=[];while(t)r(e,a(t)),t=i(t);return e}:o;t.exports=l},a04b:function(t,e,n){var r=n("c04e"),i=n("d9b5");t.exports=function(t){var e=r(t,"string");return i(e)?e:e+""}},a2be:function(t,e,n){var r=n("d612"),i=n("4284"),a=n("c584"),o=1,s=2;function l(t,e,n,l,c,u){var h=n&o,f=t.length,p=e.length;if(f!=p&&!(h&&p>f))return!1;var d=u.get(t),g=u.get(e);if(d&&g)return d==e&&g==t;var m=-1,b=!0,v=n&s?new r:void 0;u.set(t,e),u.set(e,t);while(++m<f){var y=t[m],x=e[m];if(l)var A=h?l(x,y,m,e,t,u):l(y,x,m,t,e,u);if(void 0!==A){if(A)continue;b=!1;break}if(v){if(!i(e,(function(t,e){if(!a(v,e)&&(y===t||c(y,t,n,l,u)))return v.push(e)}))){b=!1;break}}else if(y!==x&&!c(y,x,n,l,u)){b=!1;break}}return u["delete"](t),u["delete"](e),b}t.exports=l},a2db:function(t,e,n){var r=n("9e69"),i=r?r.prototype:void 0,a=i?i.valueOf:void 0;function o(t){return a?Object(a.call(t)):{}}t.exports=o},a454:function(t,e,n){var r=n("72f0"),i=n("3b4a"),a=n("cd9d"),o=i?function(t,e){return i(t,"toString",{configurable:!0,enumerable:!1,value:r(e),writable:!0})}:a;t.exports=o},a520:function(t,e,n){},a524:function(t,e,n){var r=n("4245");function i(t){return r(this,t).has(t)}t.exports=i},a994:function(t,e,n){var r=n("7d1f"),i=n("32f4"),a=n("ec69");function o(t){return r(t,a,i)}t.exports=o},ac41:function(t,e){function n(t){var e=-1,n=Array(t.size);return t.forEach((function(t){n[++e]=t})),n}t.exports=n},aed9:function(t,e,n){var r=n("83ab"),i=n("d039");t.exports=r&&i((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype}))},b047:function(t,e){function n(t){return function(e){return t(e)}}t.exports=n},b1e5:function(t,e,n){var r=n("a994"),i=1,a=Object.prototype,o=a.hasOwnProperty;function s(t,e,n,a,s,l){var c=n&i,u=r(t),h=u.length,f=r(e),p=f.length;if(h!=p&&!c)return!1;var d=h;while(d--){var g=u[d];if(!(c?g in e:o.call(e,g)))return!1}var m=l.get(t),b=l.get(e);if(m&&b)return m==e&&b==t;var v=!0;l.set(t,e),l.set(e,t);var y=c;while(++d<h){g=u[d];var x=t[g],A=e[g];if(a)var w=c?a(A,x,g,e,t,l):a(x,A,g,t,e,l);if(!(void 0===w?x===A||s(x,A,n,a,l):w)){v=!1;break}y||(y="constructor"==g)}if(v&&!y){var C=t.constructor,S=e.constructor;C==S||!("constructor"in t)||!("constructor"in e)||"function"==typeof C&&C instanceof C&&"function"==typeof S&&S instanceof S||(v=!1)}return l["delete"](t),l["delete"](e),v}t.exports=s},b218:function(t,e){var n=9007199254740991;function r(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=n}t.exports=r},b42e:function(t,e){var n=Math.ceil,r=Math.floor;t.exports=Math.trunc||function(t){var e=+t;return(e>0?r:n)(e)}},b4c0:function(t,e,n){var r=n("cb5a");function i(t){var e=this.__data__,n=r(e,t);return n<0?void 0:e[n][1]}t.exports=i},b5a7:function(t,e,n){var r=n("0b07"),i=n("2b3e"),a=r(i,"DataView");t.exports=a},b622:function(t,e,n){var r=n("da84"),i=n("5692"),a=n("1a2d"),o=n("90e3"),s=n("04f8"),l=n("fdbf"),c=r.Symbol,u=i("wks"),h=l?c["for"]||c:c&&c.withoutSetter||o;t.exports=function(t){return a(u,t)||(u[t]=s&&a(c,t)?c[t]:h("Symbol."+t)),u[t]}},b760:function(t,e,n){var r=n("872a"),i=n("9638");function a(t,e,n){(void 0!==n&&!i(t[e],n)||void 0===n&&!(e in t))&&r(t,e,n)}t.exports=a},bbc0:function(t,e,n){var r=n("6044"),i="__lodash_hash_undefined__",a=Object.prototype,o=a.hasOwnProperty;function s(t){var e=this.__data__;if(r){var n=e[t];return n===i?void 0:n}return o.call(e,t)?e[t]:void 0}t.exports=s},c04e:function(t,e,n){var r=n("c65b"),i=n("861d"),a=n("d9b5"),o=n("dc4a"),s=n("485a"),l=n("b622"),c=TypeError,u=l("toPrimitive");t.exports=function(t,e){if(!i(t)||a(t))return t;var n,l=o(t,u);if(l){if(void 0===e&&(e="default"),n=r(l,t,e),!i(n)||a(n))return n;throw c("Can't convert object to primitive value")}return void 0===e&&(e="number"),s(t,e)}},c05f:function(t,e,n){var r=n("7b97"),i=n("1310");function a(t,e,n,o,s){return t===e||(null==t||null==e||!i(t)&&!i(e)?t!==t&&e!==e:r(t,e,n,o,a,s))}t.exports=a},c098:function(t,e){var n=9007199254740991,r=/^(?:0|[1-9]\d*)$/;function i(t,e){var i=typeof t;return e=null==e?n:e,!!e&&("number"==i||"symbol"!=i&&r.test(t))&&t>-1&&t%1==0&&t<e}t.exports=i},c1c9:function(t,e,n){var r=n("a454"),i=n("f3c1"),a=i(r);t.exports=a},c2b6:function(t,e,n){var r=n("f8af"),i=n("5d89"),a=n("6f6c"),o=n("a2db"),s=n("c8fe"),l="[object Boolean]",c="[object Date]",u="[object Map]",h="[object Number]",f="[object RegExp]",p="[object Set]",d="[object String]",g="[object Symbol]",m="[object ArrayBuffer]",b="[object DataView]",v="[object Float32Array]",y="[object Float64Array]",x="[object Int8Array]",A="[object Int16Array]",w="[object Int32Array]",C="[object Uint8Array]",S="[object Uint8ClampedArray]",k="[object Uint16Array]",O="[object Uint32Array]";function T(t,e,n){var T=t.constructor;switch(e){case m:return r(t);case l:case c:return new T(+t);case b:return i(t,n);case v:case y:case x:case A:case w:case C:case S:case k:case O:return s(t,n);case u:return new T;case h:case d:return new T(t);case f:return a(t);case p:return new T;case g:return o(t)}}t.exports=T},c3fc:function(t,e,n){var r=n("42a2"),i=n("1310"),a="[object Set]";function o(t){return i(t)&&r(t)==a}t.exports=o},c430:function(t,e){t.exports=!1},c584:function(t,e){function n(t,e){return t.has(e)}t.exports=n},c65b:function(t,e,n){var r=n("40d5"),i=Function.prototype.call;t.exports=r?i.bind(i):function(){return i.apply(i,arguments)}},c6b6:function(t,e,n){var r=n("e330"),i=r({}.toString),a=r("".slice);t.exports=function(t){return a(i(t),8,-1)}},c6cd:function(t,e,n){var r=n("da84"),i=n("6374"),a="__core-js_shared__",o=r[a]||i(a,{});t.exports=o},c869:function(t,e,n){var r=n("0b07"),i=n("2b3e"),a=r(i,"Set");t.exports=a},c87c:function(t,e){var n=Object.prototype,r=n.hasOwnProperty;function i(t){var e=t.length,n=new t.constructor(e);return e&&"string"==typeof t[0]&&r.call(t,"index")&&(n.index=t.index,n.input=t.input),n}t.exports=i},c8ba:function(t,e){var n;n=function(){return this}();try{n=n||new Function("return this")()}catch(r){"object"===typeof window&&(n=window)}t.exports=n},c8fe:function(t,e,n){var r=n("f8af");function i(t,e){var n=e?r(t.buffer):t.buffer;return new t.constructor(n,t.byteOffset,t.length)}t.exports=i},ca84:function(t,e,n){var r=n("e330"),i=n("1a2d"),a=n("fc6a"),o=n("4d64").indexOf,s=n("d012"),l=r([].push);t.exports=function(t,e){var n,r=a(t),c=0,u=[];for(n in r)!i(s,n)&&i(r,n)&&l(u,n);while(e.length>c)i(r,n=e[c++])&&(~o(u,n)||l(u,n));return u}},cb2d:function(t,e,n){var r=n("1626"),i=n("9bf2"),a=n("13d2"),o=n("6374");t.exports=function(t,e,n,s){s||(s={});var l=s.enumerable,c=void 0!==s.name?s.name:e;if(r(n)&&a(n,c,s),s.global)l?t[e]=n:o(e,n);else{try{s.unsafe?t[e]&&(l=!0):delete t[e]}catch(u){}l?t[e]=n:i.f(t,e,{value:n,enumerable:!1,configurable:!s.nonConfigurable,writable:!s.nonWritable})}return t}},cb5a:function(t,e,n){var r=n("9638");function i(t,e){var n=t.length;while(n--)if(r(t[n][0],e))return n;return-1}t.exports=i},cc12:function(t,e,n){var r=n("da84"),i=n("861d"),a=r.document,o=i(a)&&i(a.createElement);t.exports=function(t){return o?a.createElement(t):{}}},cc45:function(t,e,n){var r=n("1a2d0"),i=n("b047"),a=n("99d3"),o=a&&a.isMap,s=o?i(o):r;t.exports=s},cd9d:function(t,e){function n(t){return t}t.exports=n},cdce:function(t,e,n){var r=n("da84"),i=n("1626"),a=r.WeakMap;t.exports=i(a)&&/native code/.test(String(a))},d012:function(t,e){t.exports={}},d02c:function(t,e,n){var r=n("5e2e"),i=n("79bc"),a=n("7b83"),o=200;function s(t,e){var n=this.__data__;if(n instanceof r){var s=n.__data__;if(!i||s.length<o-1)return s.push([t,e]),this.size=++n.size,this;n=this.__data__=new a(s)}return n.set(t,e),this.size=n.size,this}t.exports=s},d039:function(t,e){t.exports=function(t){try{return!!t()}catch(e){return!0}}},d066:function(t,e,n){var r=n("da84"),i=n("1626"),a=function(t){return i(t)?t:void 0};t.exports=function(t,e){return arguments.length<2?a(r[t]):r[t]&&r[t][e]}},d1e7:function(t,e,n){"use strict";var r={}.propertyIsEnumerable,i=Object.getOwnPropertyDescriptor,a=i&&!r.call({1:2},1);e.f=a?function(t){var e=i(this,t);return!!e&&e.enumerable}:r},d327:function(t,e){function n(){return[]}t.exports=n},d370:function(t,e,n){var r=n("253c"),i=n("1310"),a=Object.prototype,o=a.hasOwnProperty,s=a.propertyIsEnumerable,l=r(function(){return arguments}())?r:function(t){return i(t)&&o.call(t,"callee")&&!s.call(t,"callee")};t.exports=l},d42f:function(t,e,n){},d612:function(t,e,n){var r=n("7b83"),i=n("7ed2"),a=n("dc0f");function o(t){var e=-1,n=null==t?0:t.length;this.__data__=new r;while(++e<n)this.add(t[e])}o.prototype.add=o.prototype.push=i,o.prototype.has=a,t.exports=o},d7ee:function(t,e,n){var r=n("c3fc"),i=n("b047"),a=n("99d3"),o=a&&a.isSet,s=o?i(o):r;t.exports=s},d81a:function(t,e,n){"use strict";n("d42f")},d9b5:function(t,e,n){var r=n("d066"),i=n("1626"),a=n("3a9b"),o=n("fdbf"),s=Object;t.exports=o?function(t){return"symbol"==typeof t}:function(t){var e=r("Symbol");return i(e)&&a(e.prototype,s(t))}},da03:function(t,e,n){var r=n("2b3e"),i=r["__core-js_shared__"];t.exports=i},da84:function(t,e,n){(function(e){var n=function(t){return t&&t.Math==Math&&t};t.exports=n("object"==typeof globalThis&&globalThis)||n("object"==typeof window&&window)||n("object"==typeof self&&self)||n("object"==typeof e&&e)||function(){return this}()||this||Function("return this")()}).call(this,n("c8ba"))},dc0f:function(t,e){function n(t){return this.__data__.has(t)}t.exports=n},dc4a:function(t,e,n){var r=n("59ed"),i=n("7234");t.exports=function(t,e){var n=t[e];return i(n)?void 0:r(n)}},dc57:function(t,e){var n=Function.prototype,r=n.toString;function i(t){if(null!=t){try{return r.call(t)}catch(e){}try{return t+""}catch(e){}}return""}t.exports=i},dcbe:function(t,e,n){var r=n("30c9"),i=n("1310");function a(t){return i(t)&&r(t)}t.exports=a},de44:function(t,e,n){},e24b:function(t,e,n){var r=n("49f4"),i=n("1efc"),a=n("bbc0"),o=n("7a48"),s=n("2524");function l(t){var e=-1,n=null==t?0:t.length;this.clear();while(++e<n){var r=t[e];this.set(r[0],r[1])}}l.prototype.clear=r,l.prototype["delete"]=i,l.prototype.get=a,l.prototype.has=o,l.prototype.set=s,t.exports=l},e330:function(t,e,n){var r=n("40d5"),i=Function.prototype,a=i.call,o=r&&i.bind.bind(a,a);t.exports=r?o:function(t){return function(){return a.apply(t,arguments)}}},e538:function(t,e,n){(function(t){var r=n("2b3e"),i=e&&!e.nodeType&&e,a=i&&"object"==typeof t&&t&&!t.nodeType&&t,o=a&&a.exports===i,s=o?r.Buffer:void 0,l=s?s.allocUnsafe:void 0;function c(t,e){if(e)return t.slice();var n=t.length,r=l?l(n):new t.constructor(n);return t.copy(r),r}t.exports=c}).call(this,n("62e4")(t))},e893:function(t,e,n){var r=n("1a2d"),i=n("56ef"),a=n("06cf"),o=n("9bf2");t.exports=function(t,e,n){for(var s=i(e),l=o.f,c=a.f,u=0;u<s.length;u++){var h=s[u];r(t,h)||n&&r(n,h)||l(t,h,c(e,h))}}},e8b5:function(t,e,n){var r=n("c6b6");t.exports=Array.isArray||function(t){return"Array"==r(t)}},eac5:function(t,e){var n=Object.prototype;function r(t){var e=t&&t.constructor,r="function"==typeof e&&e.prototype||n;return t===r}t.exports=r},ec69:function(t,e,n){var r=n("6fcd"),i=n("03dd"),a=n("30c9");function o(t){return a(t)?r(t):i(t)}t.exports=o},ec8c:function(t,e){function n(t){var e=[];if(null!=t)for(var n in Object(t))e.push(n);return e}t.exports=n},edfa:function(t,e){function n(t){var e=-1,n=Array(t.size);return t.forEach((function(t,r){n[++e]=[r,t]})),n}t.exports=n},efb6:function(t,e,n){var r=n("5e2e");function i(){this.__data__=new r,this.size=0}t.exports=i},f3c1:function(t,e){var n=800,r=16,i=Date.now;function a(t){var e=0,a=0;return function(){var o=i(),s=r-(o-a);if(a=o,s>0){if(++e>=n)return arguments[0]}else e=0;return t.apply(void 0,arguments)}}t.exports=a},f772:function(t,e,n){var r=n("5692"),i=n("90e3"),a=r("keys");t.exports=function(t){return a[t]||(a[t]=i(t))}},f8af:function(t,e,n){var r=n("2474");function i(t){var e=new t.constructor(t.byteLength);return new r(e).set(new r(t)),e}t.exports=i},f909:function(t,e,n){var r=n("7e64"),i=n("b760"),a=n("72af"),o=n("4f50"),s=n("1a8c"),l=n("9934"),c=n("8adb");function u(t,e,n,h,f){t!==e&&a(e,(function(a,l){if(f||(f=new r),s(a))o(t,e,l,n,u,h,f);else{var p=h?h(c(t,l),a,l+"",t,e,f):void 0;void 0===p&&(p=a),i(t,l,p)}}),l)}t.exports=u},fa21:function(t,e,n){var r=n("7530"),i=n("2dcb"),a=n("eac5");function o(t){return"function"!=typeof t.constructor||a(t)?{}:r(i(t))}t.exports=o},fb15:function(t,e,n){"use strict";n.r(e);var r={};if(n.r(r),n.d(r,"ChartBaseLabel",(function(){return Fe})),n.d(r,"ChartBaseSwitch",(function(){return Ue})),n.d(r,"ChartBaseInput",(function(){return en})),n.d(r,"ChartBaseSelect",(function(){return ln})),n.d(r,"ChartBaseSlider",(function(){return dn})),n.d(r,"ChartBaseBox",(function(){return xn})),n.d(r,"deepCopy",(function(){return L})),n.d(r,"isEqual",(function(){return Me.a})),n.d(r,"importComp",(function(){return An})),n.d(r,"mapActions",(function(){return f["mapActions"]})),"undefined"!==typeof window){var i=window.document.currentScript,a=i&&i.src.match(/(.+\/)[^/]+\.js(\?.*)?$/);a&&(n.p=a[1])}var o=function(){var t=this,e=t._self._c;return e("div",{staticClass:"chartSetting"},[e("div",{staticStyle:{overflow:"hidden",height:"100%"}},[e("chart-list",{attrs:{chartAllType:t.currentChartType,showList:t.showList,lang:t.lang},on:{closeChartShowList:function(e){t.showList=!1}}}),e("div",[e("el-tabs",{attrs:{type:"card"},on:{"tab-click":t.handleClick},model:{value:t.activeName,callback:function(e){t.activeName=e},expression:"activeName"}},[e("el-tab-pane",{attrs:{name:"data"}},[e("span",{attrs:{slot:"label"},slot:"label"},[e("i",{staticClass:"el-icon-date"}),t._v(" "+t._s(t.setItem.data)+" ")]),e("el-row",[e("el-col",{attrs:{span:2}},[e("div",[t._v(" ")])]),e("el-col",{attrs:{span:22}},[e("div",{staticStyle:{"margin-top":"1px"}},[t._v(t._s(t.setItem.chartType))]),e("div",{staticStyle:{"margin-top":"10px"}},[e("el-button",{staticStyle:{width:"100%"},attrs:{size:"small"},on:{click:function(e){t.showList=!t.showList}}},[e("i",{staticClass:"iconfont",class:t.chartTypeTxt[0],staticStyle:{float:"left"}}),t._v(" "+t._s(t.chartTypeTxt[1])+" "),e("i",{staticClass:"iconfont icon-jiantou",staticStyle:{float:"right"}})])],1),e("div",{staticStyle:{"margin-top":"25px"}}),t.chartXYSeriesList?e("div",t._l(t.chartXYSeriesList.fix,(function(n){return e("div",{key:n.title,staticStyle:{"margin-top":"10px"}},[e("el-row",{attrs:{gutter:10}},[e("el-col",{staticStyle:{"line-height":"28px","text-align":"right"},attrs:{span:4}},[t._v(t._s(n.title)+":")]),e("el-col",{attrs:{span:20}},[e("el-tag",{staticStyle:{width:"100%","text-align":"center"},attrs:{size:"medium"}},[e("i",{staticClass:"iconfont",class:n.type,staticStyle:{float:"left"}}),t._v(" "+t._s(n.field)+" ")])],1)],1)],1)})),0):t._e(),e("div",{staticStyle:{"margin-top":"25px"}}),t.chartXYSeriesList?e("div",t._l(t.chartXYSeriesList.change,(function(n,r){return e("div",{key:r,staticStyle:{"margin-top":"10px"}},[e("el-row",{attrs:{gutter:10}},[e("el-col",{staticStyle:{"line-height":"28px","text-align":"right"},attrs:{span:4}},[t._v(t._s(n.title)+":")]),e("el-col",{attrs:{span:20}},[e("el-dropdown",{staticStyle:{width:"100%"},attrs:{size:"medium",trigger:"click"},on:{command:t.handleSeriseCommand}},[e("el-button",{staticStyle:{width:"100%"},attrs:{size:"mini"}},[e("i",{staticClass:"iconfont",class:n.type,staticStyle:{float:"left","font-size":"16px"}}),t._v(" "+t._s(n.field)+" "),e("i",{staticClass:"iconfont icon-jiantou",staticStyle:{float:"right"}})]),e("el-dropdown-menu",{staticStyle:{"min-width":"306px"},attrs:{slot:"dropdown"},slot:"dropdown"},t._l(t.chartXYSeriesList.option,(function(r,i){return e("el-dropdown-item",{key:"A-"+i,attrs:{command:{series:n,option:r}}},[t._v(" "+t._s(r.field)+" "),n.id==r.id?e("i",{staticClass:"iconfont icon-dagou",staticStyle:{float:"right"}}):t._e()])})),1)],1)],1)],1)],1)})),0):t._e(),e("div",{staticStyle:{"margin-top":"25px"}}),e("el-row",[e("div",{staticStyle:{margin:"25px 0"}}),e("el-checkbox",{on:{change:t.checkBoxChange},model:{value:t.currentRangeConfigCheck,callback:function(e){t.currentRangeConfigCheck=e},expression:"currentRangeConfigCheck"}},[t._v(t._s(t.setItem.transpose))]),e("div",{staticStyle:{margin:"15px 0"}}),e("el-checkbox",{attrs:{disabled:t.checkRowDisabled},on:{change:t.checkBoxChange},model:{value:t.currentRangeRowCheck.exits,callback:function(e){t.$set(t.currentRangeRowCheck,"exits",e)},expression:"currentRangeRowCheck.exits"}},[t._v(t._s(t.setItem.row1)+" "+t._s(t.getColRowCheckTxt(!0))+" "+t._s(t.setItem.row2))]),e("div",{staticStyle:{margin:"15px 0"}}),e("el-checkbox",{attrs:{disabled:t.checkColDisabled},on:{change:t.checkBoxChange},model:{value:t.currentRangeColCheck.exits,callback:function(e){t.$set(t.currentRangeColCheck,"exits",e)},expression:"currentRangeColCheck.exits"}},[t._v(t._s(t.setItem.column1)+" "+t._s(t.getColRowCheckTxt())+" "+t._s(t.setItem.column2))])],1)],1)],1)],1),e("el-tab-pane",[e("span",{attrs:{slot:"label"},slot:"label"},[e("i",{staticClass:"el-icon-s-data"}),t._v(" "+t._s(t.setItem.style)+" ")]),e("el-row",[e("el-col",{attrs:{span:1}},[e("div",[t._v(" ")])]),e("el-col",{attrs:{span:22}},[e("el-collapse",[e("chart-title",{attrs:{router:"title",chartAllType:t.currentChartType,titleOption:t.titleOption,lang:t.lang}}),e("chart-sub-title",{attrs:{router:"subtitle",chartAllType:t.currentChartType,subTitleOption:t.subTitleOption,lang:t.lang}}),e("chart-cursor",{attrs:{router:"tooltip",chartAllType:t.currentChartType,cursorOption:t.cursorOption,lang:t.lang}}),e("chart-legend",{attrs:{router:"legend",chartAllType:t.currentChartType,legendOption:t.legendOption,lang:t.lang}}),"pie"!=t.currentChartType.split("|")[1]?e("chart-axis",{attrs:{router:"axis",axisOption:t.axisOption,chartAllType:t.currentChartType,lang:t.lang}}):t._e()],1)],1),e("el-col",{attrs:{span:1}},[e("div",[t._v(" ")])])],1)],1)],1)],1)],1)])},s=[],l=(n("14d9"),function(){var t=this,e=t._self._c;return t.showList?e("div",{staticClass:"luckysheet-datavisual-quick-m",style:{position:"absolute",zIndex:t.zindex,bottom:"0px",left:"0px",right:"0px",background:"#fff"}},[e("el-button",{staticStyle:{width:"100%",margin:"2px 4px 8px 4px"},attrs:{plain:"",round:"",size:"small",type:"danger"},on:{click:function(e){return t.$emit("closeChartShowList")}}},[e("i",{staticClass:"iconfont icon-guanbi",staticStyle:{float:"left"}}),t._v(" "+t._s(t.close)+" ")]),e("el-radio-group",{staticStyle:{display:"block","text-align":"center"},attrs:{size:"mini"},model:{value:t.currentPro,callback:function(e){t.currentPro=e},expression:"currentPro"}},t._l(t.config,(function(n,r){return e("el-radio-button",{key:r,attrs:{label:n.type}},[t._v(t._s(n.name))])})),1),e("div",{staticClass:"luckysheet-datavisual-quick-menu",attrs:{id:"luckysheet-datavisual-quick-menu"}},t._l(t.currentConfig.data,(function(n,r){return e("div",{key:r,attrs:{"data-type":n.type,id:"luckysheet-datavisual-chart-menu-"+n.type},on:{click:t.quickMenu}},[e("i",{staticClass:"iconfont",class:n.icon,attrs:{"aria-hidden":"true"}}),e("span",[t._v(t._s(n.name))])])})),0),e("div",{staticClass:"luckysheet-datavisual-quick-list luckysheet-scrollbars",attrs:{id:"luckysheet-datavisual-quick-list"},on:{scroll:t.quickListScroll}},[t._l(t.currentConfig.data,(function(n,r){return[e("div",{key:r,staticClass:"luckysheet-datavisual-quick-list-title"},[e("a",{attrs:{"data-type":n.type,id:"luckysheet-datavisual-chart-listtitle-"+n.type}},[e("i",{staticClass:"iconfont",class:n.icon,attrs:{"aria-hidden":"true"}}),t._v(" "+t._s(n.name)+" ")])]),e("div",{staticClass:"luckysheet-datavisual-quick-list-ul"},t._l(n.data,(function(n,r){return e("el-tooltip",{key:r,attrs:{content:n.name,"open-delay":500,effect:"dark",placement:"bottom"}},[e("div",{staticClass:"luckysheet-datavisual-quick-list-item",class:n.type==t.currentChartType?"luckysheet-datavisual-quick-list-item-active":"",attrs:{chartAllType:n.type,"data-style":n.type.split("-")[2],"data-type":n.type.split("-")[1]},on:{click:function(e){return t.changeChartType(n.type)}}},[e("img",{attrs:{src:0==n.img.length?"data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiIHN0YW5kYWxvbmU9InllcyI/PjxzdmcgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjQyIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDI0MiAyMDAiIHByZXNlcnZlQXNwZWN0UmF0aW89Im5vbmUiPjxkZWZzLz48cmVjdCB3aWR0aD0iMjQyIiBoZWlnaHQ9IjIwMCIgZmlsbD0iI0VFRUVFRSIvPjxnPjx0ZXh0IHg9IjkzIiB5PSIxMDAiIHN0eWxlPSJmaWxsOiNBQUFBQUE7Zm9udC13ZWlnaHQ6Ym9sZDtmb250LWZhbWlseTpBcmlhbCwgSGVsdmV0aWNhLCBPcGVuIFNhbnMsIHNhbnMtc2VyaWYsIG1vbm9zcGFjZTtmb250LXNpemU6MTFwdDtkb21pbmFudC1iYXNlbGluZTpjZW50cmFsIj4yNDJ4MjAwPC90ZXh0PjwvZz48L3N2Zz4=":n.img,alt:"图片"}})])])})),1)]}))],2)],1):t._e()}),c=[],u=n("1157"),h=n.n(u),f=n("5880"),p=n.n(f);const d={label:{fontSize:12,color:"#333",fontFamily:"sans-serif",fontGroup:[],cusFontSize:12},formatter:{prefix:"",suffix:"",ratio:1,digit:"auto"},item:{color:null,borderColor:"#000",borderType:"solid",borderWidth:1},lineStyle:{color:null,width:1,type:"solid"}},g={title:{show:!1,text:"默认标题",label:L(d.label),position:{value:"left-top",offsetX:40,offsetY:50}},subtitle:{show:!1,text:"",label:L(d.label),distance:{value:"auto",cusGap:40}},config:{color:"transparent",fontFamily:"Sans-serif",grid:{value:"normal",top:5,left:10,right:20,bottom:10}},legend:{show:!0,selectMode:"multiple",selected:[{seriesName:"衣服",isShow:!0},{seriesName:"食材",isShow:!0},{seriesName:"图书",isShow:!0}],label:L(d.label),position:{value:"left-top",offsetX:40,offsetY:50,direction:"horizontal"},width:{value:"auto",cusSize:25},height:{value:"auto",cusSize:14},distance:{value:"auto",cusGap:10},itemGap:10},tooltip:{show:!0,label:L(d.label),backgroundColor:"rgba(50,50,50,0.7)",triggerOn:"mousemove",triggerType:"item",axisPointer:{type:"line",style:{color:"#555",width:"normal",type:"solid"}},format:[{seriesName:"衣服",prefix:"",suffix:"",ratio:1,digit:"auto"},{seriesName:"食材",prefix:"",suffix:"",ratio:1,digit:"auto"},{seriesName:"图书",prefix:"",suffix:"",ratio:1,digit:"auto"}],position:"auto"},axis:{axisType:"xAxisDown",xAxisUp:{show:!1,title:{showTitle:!1,text:"",nameGap:15,rotate:0,label:L(d.label),fzPosition:"end"},name:"显示X轴",inverse:!1,tickLine:{show:!0,width:1,color:"auto"},tick:{show:!0,position:"outside",length:5,width:1,color:"auto"},tickLabel:{show:!0,label:L(d.label),rotate:0,prefix:"",suffix:"",optimize:0,distance:0,min:"auto",max:"auto",ratio:1,digit:"auto"},netLine:{show:!1,width:1,type:"solid",color:"auto",interval:{value:"auto",cusNumber:0}},netArea:{show:!1,interval:{value:"auto",cusNumber:0},colorOne:"auto",colorTwo:"auto"},axisLine:{onZero:!1}},xAxisDown:{show:!0,title:{showTitle:!1,text:"",nameGap:15,rotate:0,label:L(d.label),fzPosition:"end"},name:"显示X轴",inverse:!1,tickLine:{show:!0,width:1,color:"auto"},tick:{show:!0,position:"outside",length:5,width:1,color:"auto"},tickLabel:{show:!0,label:L(d.label),rotate:0,prefix:"",suffix:"",optimize:0,distance:0,min:null,max:null,ratio:1,digit:"auto"},netLine:{show:!1,width:1,type:"solid",color:"auto",interval:{value:"auto",cusNumber:0}},netArea:{show:!1,interval:{value:"auto",cusNumber:0},colorOne:"auto",colorTwo:"auto"}},yAxisLeft:{show:!0,title:{showTitle:!1,text:"",nameGap:15,rotate:0,label:L(d.label),fzPosition:"end"},name:"显示Y轴",inverse:!1,tickLine:{show:!0,width:1,color:"auto"},tick:{show:!0,position:"outside",length:5,width:1,color:"auto"},tickLabel:{show:!0,label:L(d.label),rotate:0,formatter:L(d.formatter),split:5,min:null,max:null,prefix:"",suffix:"",ratio:1,digit:"auto",distance:0},netLine:{show:!1,width:1,type:"solid",color:"auto",interval:{value:"auto",cusNumber:0}},netArea:{show:!1,interval:{value:"auto",cusNumber:0},colorOne:"auto",colorTwo:"auto"}},yAxisRight:{show:!1,title:{showTitle:!1,text:"",nameGap:15,rotate:0,label:L(d.label),fzPosition:"end"},name:"显示Y轴",inverse:!1,tickLine:{show:!0,width:1,color:"auto"},tick:{show:!0,position:"outside",length:5,width:1,color:"auto"},tickLabel:{show:!0,label:L(d.label),rotate:0,formatter:L(d.formatter),split:5,min:null,max:null,prefix:"",suffix:"",ratio:1,digit:"auto",distance:0},netLine:{show:!1,width:1,type:"solid",color:"auto",interval:{value:"auto",cusNumber:0}},netArea:{show:!1,interval:{value:"auto",cusNumber:0},colorOne:"auto",colorTwo:"auto"}}}},m=[{value:"left-top",label:"左上"},{value:"left-middle",label:"左中"},{value:"left-bottom",label:"左下"},{value:"right-top",label:"右上"},{value:"right-middle",label:"右中"},{value:"right-bottom",label:"右下"},{value:"center-top",label:"中上"},{value:"center-middle",label:"居中"},{value:"center-bottom",label:"中下"},{value:"custom",label:"自定义"}],b=[{value:"auto",label:"默认"},{value:"far",label:"远"},{value:"normal",label:"一般"},{value:"close",label:"近"},{value:"custom",label:"自定义"}],v=[{value:6,label:"6px"},{value:8,label:"8px"},{value:10,label:"10px"},{value:12,label:"12px"},{value:14,label:"14px"},{value:16,label:"16px"},{value:18,label:"18px"},{value:20,label:"20px"},{value:22,label:"22px"},{value:24,label:"24px"},{value:30,label:"30x"},{value:36,label:"36px"},{value:"custom",label:"自定义"}],y=[{value:"solid",label:"实线"},{value:"dashed",label:"虚线"},{value:"dotted",label:"点线"}],x=[{value:"normal",label:"正常"},{value:"bold",label:"粗"},{value:"bolder",label:"加粗"}],A=[{value:"auto",label:"默认"},{value:"inside",label:"中心位置"},{value:"top",label:"上侧"},{value:"left",label:"左侧"},{value:"right",label:"右侧"},{value:"bottom",label:"底侧"}],w=[{value:100,label:"乘以100"},{value:10,label:"乘以10"},{value:1,label:"默认"},{value:.1,label:"除以10"},{value:.01,label:"除以100"},{value:.001,label:"除以1000"},{value:1e-4,label:"除以一万"},{value:1e-5,label:"除以10万"},{value:1e-6,label:"除以一百万"},{value:1e-7,label:"除以一千万"},{value:1e-8,label:"除以一亿"},{value:1e-9,label:"除以十亿"}],C=[{value:"auto",label:"自动显示"},{value:0,label:"整数"},{value:1,label:"1位小数"},{value:2,label:"2位小数"},{value:3,label:"3位小数"},{value:4,label:"4位小数"},{value:5,label:"5位小数"},{value:6,label:"6位小数"},{value:7,label:"7位小数"},{value:8,label:"8位小数"}],S=[{value:"auto",label:"默认"},{value:"big",label:"大"},{value:"medium",label:"中"},{value:"small",label:"小"},{value:"custom",label:"自定义"}],k=[{value:"auto",label:"默认"},{value:0,label:"每个刻度"},{value:1,label:"间隔1个"},{value:2,label:"间隔2个"},{value:3,label:"间隔3个"},{value:"custom",label:"自定义"}],O=[{label:"默认",value:"auto"},{label:"6px",value:6},{label:"8px",value:8},{label:"10px",value:10},{label:"12px",value:12},{label:"14px",value:14},{label:"16px",value:16},{label:"18px",value:18},{label:"24px",value:24},{label:"28px",value:28},{label:"36px",value:36},{label:"自定义",value:"custom"}],T={bold:{des:"加粗",text:"B"},italic:{des:"斜体",text:"I"},vertical:{des:"文字方向",text:"垂直"}},I={bold:{des:"加粗",text:"B"},italic:{des:"斜体",text:"I"}},E=[["地区","衣服","食材","图书"],["上海",134,345,51],["北京",345,421,234],["广州",453,224,156],["杭州",321,634,213],["南京",654,542,231]],D={chartAllType:"echarts|line|default",defaultOption:L(g),chartData:L(E)};function L(t){if(!G(t)&&!j(t))return t;let e;if(j(t)){e=new Map;for(let n of t.keys()){let r=t.get(n);if(j(r)||G(r)||Array.isArray(t)){let t=L(r);e.set(n,t)}else e.set(n,r)}}else if("function"===typeof t)e=t;else if(e=Array.isArray(t)?[]:{},t instanceof HTMLElement)e=t.cloneNode(!0);else for(let n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=j(t[n])||G(t[n])?L(t[n]):t[n]);return e}function G(t){return!j(t)&&("object"===typeof t||"function"===typeof t)&&null!==t}function j(t){return t instanceof Map}function N(t){null==t&&(t="chart");for(var e=window.navigator.userAgent.replace(/[^a-zA-Z0-9]/g,"").split(""),n="",r=0;r<12;r++)n+=e[Math.round(Math.random()*(e.length-1))];var i=(new Date).getTime();return t+"_"+n+"_"+i}function M(t){return!(null==t||t.toString().length<5)&&!!e(t);function e(t){var e=/^(\d{4})-(\d{1,2})-(\d{1,2})(\s(\d{1,2}):(\d{1,2})(:(\d{1,2}))?)?$/,n=/^(\d{4})\/(\d{1,2})\/(\d{1,2})(\s(\d{1,2}):(\d{1,2})(:(\d{1,2}))?)?$/;if(!e.test(t)&&!n.test(t))return!1;var r=RegExp.$1,i=RegExp.$2,a=RegExp.$3;if(r<1900)return!1;if(i>12)return!1;if(a>31)return!1;if(2==i){if(29==new Date(r,1,29).getDate()&&a>29)return!1;if(29!=new Date(r,1,29).getDate()&&a>28)return!1}return!0}}function R(t){return""!==t&&null!=t&&!isNaN(t)}function B(t){var e=/[\u4E00-\u9FA5]|[\uFE30-\uFFA0]/gi;return!!e.exec(t)}function V(t){var e="string";return M(t)?e="date":isNaN(parseFloat(t))||B(t)||(e="num"),e}function P(t){for(var e=[],n=0;n<t[0].length;n++){for(var r=[],i=0;i<t.length;i++){var a="";null!=t[i]&&null!=t[i][n]&&(a=t[i][n]),r.push(a)}e.push(r)}return e}function z(t,e){if(0==t.length||t.length!=e.length)return t;for(var n=[],r=0;r<t.length;r++)n[e[r]]=t[r];return n}function Q(t,e){for(var n=[],r=0;r<t.length;r++){var i=t[r];n.push(z(i,e))}return n}function W(t){var e=Object.prototype.toString,n={"[object Boolean]":"boolean","[object Number]":"number","[object String]":"string","[object Function]":"function","[object Array]":"array","[object Date]":"date","[object RegExp]":"regExp","[object Undefined]":"undefined","[object Null]":"null","[object Object]":"object"};return n[e.call(t)]}function F(t){var e,n,r=t.length-1,i=t[0].length-1;while(r>=0&&i>=0){var a=t[r][i];if(!(null===a||R(a)||"object"==W(a)&&R(a.v)||"object"==W(a)&&"undefined"==W(a.v)||""===a||""===a.v)){r==t.length-1&&i==t[0].length-1?(e=r,n=i):(e=r+1,n=i+1);break}if(a&&a.ct&&"yyyy-MM-dd"==a.ct.fa){e=r+1,n=i+1;break}e=r--,n=i--}var o={exits:!1,range:[0,0]};if(e>0)for(var s=e;s>=0;s--){a=t[s][n];if(!(null===a||R(a)||"object"==W(a)&&R(a.v)||"object"==W(a)&&"undefined"==W(a.v)||""===a||""===a.v)){o.exits=!0,o.range=[0,s];break}}var l={exits:!1,range:[0,0]};if(n>0)for(s=n;s>=0;s--){a=t[e][s];if(!(null===a||R(a)||"object"==W(a)&&R(a.v)||"object"==W(a)&&"undefined"==W(a.v)||""===a||""===a.v)){l.exits=!0,l.range=[0,s];break}if(a&&a.ct&&"yyyy-MM-dd"==a.ct.fa){l.exits=!0,l.range=[0,s];break}}return o.range[1]+1==t.length&&(o={exits:!1,range:[0,0]}),l.range[1]+1==t[0].length&&(l={exits:!1,range:[0,0]}),[o,l]}function q(t,e,n,r){var i={};return e.length>1&&(i={title:{row:[0,0],column:[0,0]},rowtitle:{row:[0,0],column:[1,t[0].length-1]},coltitle:{row:[1,t.length-1],column:[0,0]},content:{row:[1,t.length-1],column:[1,t[0].length-1]},type:"multi",range:e}),i=n.exits&&r.exits?{title:{row:r.range,column:n.range},rowtitle:{row:r.range,column:[n.range[1]+1,t[0].length-1]},coltitle:{row:[r.range[1]+1,t.length-1],column:n.range},content:{row:[r.range[1]+1,t.length-1],column:[n.range[1]+1,t[0].length-1]},type:"normal",range:e[0]}:n.exits?{title:null,rowtitle:null,coltitle:{row:[0,t.length-1],column:n.range},content:{row:[0,t.length-1],column:[n.range[1]+1,t[0].length-1]},type:"leftright",range:e[0]}:r.exits?{title:null,rowtitle:{row:r.range,column:[0,t[0].length-1]},coltitle:null,content:{row:[r.range[1]+1,t.length-1],column:[0,t[0].length-1]},type:"topbottom",range:e[0]}:{title:null,rowtitle:null,coltitle:null,content:{row:[0,t.length-1],column:[0,t[0].length-1]},type:"contentonly",range:e[0]},i}function X(t,e,n){var r=null,i=n[t][e];return null!=i&&(r=null!=i.v?i.v:i),void 0==r&&(r=""),r}function H(t,e,n,r,i,a){var o={};if("line"==r||"column"==r||"area"==r||"scatter"==r||"bar"==r||"pie"==r||"radar"==r||"funnel"==r||"gauge"==r||"map"==r)if(a){if("normal"==e.type){var s=e,l=s.rowtitle,c=[];if(null!=l){o.title={text:X(s.title.row[0],s.title.column[0],t)};for(var u=l.column[0];u<=l.column[1];u++){for(var h="",f=l.row[0];f<=l.row[1];f++)h+="\n"+X(f,u,t);h=h.substr(1,h.length),"highcharts"==n&&(h=h.replace(/\n/g,"<br/>")),c.push(h)}o.xAxis=c}var p=s.coltitle,d=[];if(null!=p){for(f=p.row[0];f<=p.row[1];f++){for(h="",u=p.column[0];u<=p.column[1];u++)h+=" "+X(f,u,t);d.push(h.substr(1,h.length))}o.label=d}var g=s.content,m=[];if(null!=g){var b={};for(u=g.column[0];u<=g.column[1];u++){var v=[],y=0;for(f=g.row[0];f<=g.row[1];f++){h=X(f,u,t);v.push(h),u==g.column[0]&&(b[y++]=V(h))}m.push(v)}o.series=m,o.series_tpye=b}}else if("leftright"==e.type){s=e,p=s.coltitle,d=[];if(null!=p){for(f=p.row[0];f<=p.row[1];f++){for(h="",u=p.column[0];u<=p.column[1];u++)h+=" "+X(f,u,t);d.push(h.substr(1,h.length))}o.label=d}g=s.content,m=[];if(null!=g){for(b={},u=g.column[0];u<=g.column[1];u++){for(v=[],y=0,f=g.row[0];f<=g.row[1];f++){h=X(f,u,t);v.push(h),u==g.column[0]&&(b[y++]=V(h))}m.push(v)}o.series=m,o.series_tpye=b}l=s.rowtitle,c=[];if(null==l){o.title={text:"图表标题"};for(u=0;u<=g.column[1]-g.column[0];u++)c.push(u+1);o.xAxis=c}}else if("topbottom"==e.type){s=e,l=s.rowtitle,c=[];if(null!=l){o.title={text:"图表标题"};for(u=l.column[0];u<=l.column[1];u++){for(h="",f=l.row[0];f<=l.row[1];f++)h+="\n"+X(f,u,t);h=h.substr(1,h.length),"highcharts"==n&&(h=h.replace(/\n/g,"<br/>")),c.push(h)}o.xAxis=c}g=s.content,m=[];if(null!=g){for(b={},u=g.column[0];u<=g.column[1];u++){for(v=[],y=0,f=g.row[0];f<=g.row[1];f++){h=X(f,u,t);v.push(h),u==g.column[0]&&(b[y++]=V(h))}m.push(v)}o.series=m,o.series_tpye=b}p=s.coltitle,d=[];if(null==p){for(f=0;f<=g.row[1]-g.row[0];f++)d.push("系列"+(f+1));o.label=d}}else if("contentonly"==e.type){s=e,g=s.content,m=[];if(null!=g){for(b={},u=g.column[0];u<=g.column[1];u++){for(v=[],y=0,f=g.row[0];f<=g.row[1];f++){h=X(f,u,t);v.push(h),u==g.column[0]&&(b[y++]=V(h))}m.push(v)}o.series=m,o.series_tpye=b}l=s.rowtitle,c=[];if(null==l){o.title={text:"图表标题"};for(u=0;u<=g.column[1]-g.column[0];u++)c.push(u+1);o.xAxis=c}p=s.coltitle,d=[];if(null==p){for(f=0;f<=g.row[1]-g.row[0];f++)d.push("系列"+(f+1));o.label=d}}}else if("normal"==e.type){s=e,l=s.rowtitle,c=[];if(null!=l){for(u=l.column[0];u<=l.column[1];u++){for(h="",f=l.row[0];f<=l.row[1];f++)h+=" "+X(f,u,t);c.push(h.substr(1,h.length))}o.label=c}p=s.coltitle,d=[];if(null!=p){for(f=p.row[0];f<=p.row[1];f++){for(h="",u=p.column[0];u<=p.column[1];u++)h+="\n"+X(f,u,t);h=h.substr(1,h.length),"highcharts"==n&&(h=h.replace(/\n/g,"<br/>")),d.push(h)}o.xAxis=d}g=s.content,m=[];if(null!=g){for(b={},f=g.row[0];f<=g.row[1];f++){for(v=[],y=0,u=g.column[0];u<=g.column[1];u++){h=X(f,u,t);v.push(h),f==g.row[0]&&(b[y++]=V(h))}m.push(v)}o.series=m,o.series_tpye=b}}else if("leftright"==e.type){s=e,p=s.coltitle,d=[];if(null!=p){for(f=p.row[0];f<=p.row[1];f++){for(h="",u=p.column[0];u<=p.column[1];u++)h+="\n"+X(f,u,t);h=h.substr(1,h.length),"highcharts"==n&&(h=h.replace(/\n/g,"<br/>")),d.push(h)}o.xAxis=d}g=s.content,m=[];if(null!=g){for(b={},f=g.row[0];f<=g.row[1];f++){for(v=[],y=0,u=g.column[0];u<=g.column[1];u++){h=X(f,u,t);v.push(h),f==g.row[0]&&(b[y++]=V(h))}m.push(v)}o.series=m,o.series_tpye=b}l=s.rowtitle,c=[];if(null==l){o.title={text:"图表标题"};for(u=0;u<=g.column[1]-g.column[0];u++)c.push("系列"+(u+1));o.label=c}}else if("topbottom"==e.type){s=e,l=s.rowtitle,c=[];if(null!=l){o.title={text:"图表标题"};for(u=l.column[0];u<=l.column[1];u++){for(h="",f=l.row[0];f<=l.row[1];f++)h+=" "+X(f,u,t);c.push(h.substr(1,h.length))}o.label=c}g=s.content,m=[];if(null!=g){for(b={},f=g.row[0];f<=g.row[1];f++){for(v=[],y=0,u=g.column[0];u<=g.column[1];u++){h=X(f,u,t);v.push(h),f==g.row[0]&&(b[y++]=V(h))}m.push(v)}o.series=m,o.series_tpye=b}p=s.coltitle,d=[];if(null==p){for(f=0;f<=g.row[1]-g.row[0];f++)d.push(f+1);o.xAxis=d}}else if("contentonly"==e.type){s=e,g=s.content,m=[];if(null!=g){for(b={},f=g.row[0];f<=g.row[1];f++){for(v=[],y=0,u=g.column[0];u<=g.column[1];u++){h=X(f,u,t);v.push(h),f==g.row[0]&&(b[y++]=V(h))}m.push(v)}o.series=m,o.series_tpye=b}l=s.rowtitle,c=[];if(null==l){o.title={text:"图表标题"};for(u=0;u<=g.column[1]-g.column[0];u++)c.push("系列"+(u+1));o.label=c}p=s.coltitle,d=[];if(null==p){for(f=0;f<=g.row[1]-g.row[0];f++)d.push(f+1);o.xAxis=d}}return o}function Z(t){var e={};e.length=t;for(var n=0;n<t;n++)e[n]=n;return e}function J(t,e,n,r,i,a,o){if(e.xAxis&&"bar"!=i){if(null==t.axis.xAxisDown.data||0==t.axis.xAxisDown.data.length||t.axis.xAxisDown.data.length!=e.xAxis.length)t.axis.xAxisDown.data=e.xAxis;else for(var s=0;s<t.axis.xAxisDown.data.length;s++){var l=t.axis.xAxisDown.data[s];l instanceof Object?l.value=e.xAxis[s]:t.axis.xAxisDown.data[s]=e.xAxis[s]}t.axis.xAxisDown.type="category",t.axis.yAxisLeft.type="value"}if("echarts"==r&&"bar"==i){if(null==t.axis.yAxisLeft.data||0==t.axis.yAxisLeft.data.length||t.axis.yAxisLeft.data.length!=e.xAxis.length)t.axis.yAxisLeft.data=e.xAxis;else for(s=0;s<t.axis.yAxisLeft.data.length;s++){l=t.axis.yAxisLeft.data[s];l instanceof Object?l.value=e.xAxis[s]:t.axis.yAxisLeft.data[s]=e.xAxis[s]}t.axis.yAxisLeft.type="category",t.axis.xAxisDown.type="value"}if(e.series){var c=P(Q(e.series,n)),u=z(e.label,n);t.legend.data=u,t.seriesData=c,"pie"==i?ot(t,e,c,u,r,i,a):"line"!=i&&"area"!=i&&"bar"!=i&&"column"!=i||U(t,c,u,r,i,a)}return t}function U(t,e,n,r,i,a){t.series.length!=e.length&&(t.series=[]);for(var o=0;o<e.length;o++)null==t.series[o]?t.series[o]=Y(t.series[o],e[o],n[o],r,i,a):t.series[o]=et(t.series[o],e[o],n[o],r,i,a)}function Y(t,e,n,r,i,a){t={itemStyle:L(d.item),lineStyle:L(d.lineStyle),data:e,type:i,name:n,markPoint:{data:[]},markLine:{data:[]},markArea:{data:[]}};let o=new Map([["line",K],["area",_],["bar",tt],["column",$]]);return o.get(i)(t,e,n,r,i,a)}function K(t,e,n,r,i,a){return"smooth"==a&&(t.smooth=!0),"label"==a&&(t.label={show:!0,formatter:"{c}",fontSize:10,distance:1}),t}function _(t,e,n,r,i,a){return t.type="line",t.areaStyle={normal:{}},"stack"==a&&(t.stack="示例"),t}function $(t,e,n,r,i,a){return t.type="bar","stack"==a&&(t.stack="示例"),t}function tt(t,e,n,r,i,a){return"stack"==a&&(t.stack="示例"),t}function et(t,e,n,r,i,a){if(null==t.data||0==t.data.length||t.data.length!=e.length)t.data=e,t.name=n,t.type=i;else{for(var o=0;o<t.data.length;o++){var s=t.data[o];s instanceof Object?s.value=e[o]:t.data[o]=e[o]}t.name=n,t.type=i}let l=new Map([["line",nt],["area",rt],["bar",it],["column",at]]);return l.get(i)(t,e,n,r,i,a)}function nt(t,e,n,r,i,a){return t}function rt(t,e,n,r,i,a){return t.type="line",t}function it(t,e,n,r,i,a){return t}function at(t,e,n,r,i,a){return t.type="bar",t}function ot(t,e,n,r,i,a,o){t.legend.data=[];for(var s=0;s<e.xAxis.length;s++)t.legend.data.push({name:e.xAxis[s],textStyle:{color:null},value:n[0][s]});for(s=0;s<n.length;s++){if(s>0)return;null==t.series[s]?t.series[s]=st(t.series[s],e,n[s],r[s],i,a,o):t.series[s]=lt(t.series[s],e,n[s],r[s],i,a,o)}}function st(t,e,n,r,i,a,o){let s={name:r,type:"pie",radius:["0%","75%"],data:[],dataLabels:{},seLabel:{},seLine:{}};for(let c=0;c<n.length;c++){let t,r;n[c]>0?(t=n[c],r=e.xAxis[c]):n[c]<=0&&(t="",r=""),s.data.push({value:t,name:r,label:{},labelLine:{lineStyle:{}},itemStyle:{}})}if(t=s,t.roseType=!1,"split"==o)for(var l=0;l<t.data.length;l++)t.data[l].selected="true",t.data[l].selectedOffset=5;return"ring"==o&&(t.radius=["50%","85%"],t.avoidLabelOverlap=!1,t.label={normal:{show:!0,position:"outside"},emphasis:{show:!0,textStyle:{fontSize:"16",fontWeight:"bold"}}}),t}function lt(t,e,n,r,i,a,o){t.name=r;for(let s=0;s<n.length;s++){let r,i;if(n[s]>0?(r=+n[s],i=e.xAxis[s]):n[s]<=0&&(r="",i=""),t.data[s].name=i,t.data[s].value=r,t.data[s].y=r,t.data.length<n.length)for(let e=t.data.length;e<n.length;e++)t.data.push({value:r,name:i,y:r});if(t.data.length>n.length)for(let e=n.length;e<t.data.length;e++)t.data[e].value="",t.data[e].y="",t.data[e].name=""}return t}const ct=function(t,e,n){t[0],t[1],t[2];let r={show:!0,text:"",left:"auto",top:"auto",textStyle:{fontSize:12,color:"#333",fontFamily:"sans-serif",fontStyle:"normal",fontWeight:"normal"},subtextStyle:{fontSize:12,color:"#aaa",fontFamily:"sans-serif",fontStyle:"normal",fontWeight:"normal"},subtext:"",itemGap:10};r.show=e.show,r.text=e.text,r.subtext=n.text,ge(e,r,"textStyle","text"),ge(n,r,"subtextStyle","subtext"),"custom"===e.position.value?(r.left=e.position.offsetX+"%",r.top=e.position.offsetY+"%"):(r.left=e.position.value.split("-")[0],r.top=e.position.value.split("-")[1]);let i=new Map([["auto",10],["far",30],["close",5],["normal",20],["custom",n.distance.cusGap]]);return r.itemGap=i.get(n.distance.value),r};var ut=ct;const ht=function(t,e){let n={show:!0,textStyle:{color:"#333",fontStyle:"normal",fontWeight:"normal",fontSize:12},left:"auto",top:"auto",orient:"horizontal",itemWidth:25,itemGap:10};n.show=e.show,ge(e,n,"textStyle"),"custom"===e.position.value?(n.left=e.position.offsetX,n.top=e.position.offsetY):(n.left=e.position.value.split("-")[0],n.top=e.position.value.split("-")[1]),n.orient=e.position.direction;let r=new Map([["auto",25],["big",45],["medium",18],["small",10],["custom",e.width.cusSize]]),i=new Map([["auto",14],["big",30],["medium",20],["small",10],["custom",e.height.cusSize]]);n.itemWidth=r.get(e.width.value),n.itemHeight=i.get(e.height.value);let a=new Map([["auto",10],["far",20],["near",5],["general",15],["custom",e.distance.cusGap]]);return n.itemGap=a.get(e.distance.value),n};var ft=ht;const pt=function(t,e){const n={show:!0,trigger:"item",textStyle:{color:"#fff",fontStyle:"normal",fontWeight:"normal",fontSize:14},backgroundColor:"rgba(50,50,50,0.7)",triggerOn:"mousemove|click",axisPointer:{type:"line",lineStyle:{type:"solid",width:1,color:"#555"}},position:"right"};n.show=e.show,n.trigger=e.triggerType,n.triggerOn=e.triggerOn,ge(e,n,"textStyle"),n.backgroundColor=e.backgroundColor,n.axisPointer.lineStyle=e.axisPointer.style,n.axisPointer.type=e.axisPointer.type,n.position="auto"==e.position?null:e.position;e.format;return n};var dt=pt;const gt=function(t,e){let n=t[1],r={show:!0,name:"",nameTextStyle:{color:"#333",fontStyle:"normal",fontWeight:"normal",fontSize:12},nameLocation:"end",inverse:!1,interval:null,nameGap:15,nameRotate:null,axisLine:{show:!0,lineStyle:{color:"#333",width:1}},axisTick:{show:!0,inside:!1,length:5,lineStyle:{width:1,type:"solid",color:null}},axisLabel:{show:!0,rotate:0,formatter:null},min:null,max:null,splitLine:{show:!0,lineStyle:{color:"#ccc",width:1,type:"solid"},interval:"auto"},splitArea:{show:!1,areaStyle:{color:["rgba(250,250,250,0.3)","rgba(200,200,200,0.3)"]}}},i=function(t,r){let i=L(e[r]);return t=h.a.extend(t,i),t.show=i.show,t.name=i.title.text,ge(i.title,t,"nameTextStyle"),t.nameLocation=i.title.fzPosition,t.inverse=i.inverse,"value"!=t.type&&(t.interval=i.tickLabel.optimize),t.nameGap=i.title.rotate,t.axisLine.show=i.tickLine.show,t.axisLine.lineStyle=be(i.tickLine.width,i.tickLine.color),t.axisTick.show=i.tick.show,t.axisTick.lineStyle=be(i.tick.width,i.tick.color),t.axisTick.inside="inside"==i.tick.position,t.axisTick.length=i.tick.length,t.axisLabel.show=i.tickLabel.show,t.axisLabel.rotate=i.tickLabel.rotate,"bar"==n&&"x"==r.slice(0,1)||"bar"!=n&&"y"==r.slice(0,1)?(t.min=i.tickLabel.min,t.max=i.tickLabel.max,t.axisLabel.formatter=function(t){return"auto"==i.tickLabel.digit?i.tickLabel.prefix+me.multiply(+t,i.tickLabel.ratio)+i.tickLabel.suffix:i.tickLabel.prefix+me.multiply(+t,i.tickLabel.ratio).toFixed(i.tickLabel.digit)+i.tickLabel.suffix}):t.axisLabel.formatter=function(t){return i.tickLabel.prefix+t+i.tickLabel.suffix},t.splitLine.show=i.netLine.show,t.splitLine.lineStyle=be(i.netLine.width,i.netLine.color,i.netLine.type),t.splitLine.interval=ve(i.netLine.interval.value,i.netLine.interval.cusNumber),t.splitArea.show=i.netArea.show,t.splitArea.interval=ve(i.netArea.interval.value,i.netArea.interval.cusNumber),t.splitArea.areaStyle.color=["auto"==i.netArea.colorOne?"rgba(250,250,250,0.3)":i.netArea.colorOne,"auto"==i.netArea.colorTwo?"rgba(200,200,200,0.3)":i.netArea.colorTwo],t};return{xAxisUp:i(L(r),"xAxisUp"),xAxisDown:i(L(r),"xAxisDown"),yAxisLeft:i(L(r),"yAxisLeft"),yAxisRight:i(L(r),"yAxisRight")}};var mt=gt;const bt=function(t){const e=t.chartAllType.split("|"),n=(e[0],e[1]),r=(e[2],ut(e,t.defaultOption.title,t.defaultOption.subtitle)),i=ft(e,t.defaultOption.legend),a=dt(e,t.defaultOption.tooltip),o=mt(e,t.defaultOption.axis);o.xAxisDown.data=t.defaultOption.axis.xAxisDown.data;const s={title:{...r},tooltip:{...a},legend:{...i},xAxis:[{...o.xAxisDown},{...o.xAxisUp}],yAxis:[o.yAxisLeft,o.yAxisRight],series:t.defaultOption.series?t.defaultOption.series:[{name:"销量",type:"bar",data:[5,20,36,10,10,20]}]};return"pie"==n&&(delete s.xAxis,delete s.yAxis),s};var vt=bt,yt=n("8bbf"),xt=n.n(yt);const At="ENABLE_ACTIVE",wt="DISABLE_ACTIVE",Ct="ENABLE_DRAGGABLE",St="DISABLE_DRAGGABLE",kt="ENABLE_RESIZABLE",Ot="DISABLE_RESIZABLE",Tt="ENABLE_PARENT_LIMITATION",It="DISABLE_PARENT_LIMITATION",Et="ENABLE_SNAP_TO_GRID",Dt="DISABLE_SNAP_TO_GRID",Lt="ENABLE_ASPECT",Gt="DISABLE_ASPECT",jt="ENABLE_X_AXIS",Nt="ENABLE_Y_AXIS",Mt="ENABLE_BOTH_AXIS",Rt="ENABLE_NONE_AXIS",Bt="CHANGE_ZINDEX",Vt="CHANGE_MINW",Pt="CHANGE_MINH",zt="CHANGE_WIDTH",Qt="CHANGE_HEIGHT",Wt="CHANGE_TOP",Ft="CHANGE_LEFT";var qt={ENABLE_ACTIVE:At,DISABLE_ACTIVE:wt,ENABLE_DRAGGABLE:Ct,DISABLE_DRAGGABLE:St,ENABLE_RESIZABLE:kt,DISABLE_RESIZABLE:Ot,ENABLE_PARENT_LIMITATION:Tt,DISABLE_PARENT_LIMITATION:It,ENABLE_SNAP_TO_GRID:Et,DISABLE_SNAP_TO_GRID:Dt,ENABLE_ASPECT:Lt,DISABLE_ASPECT:Gt,ENABLE_X_AXIS:jt,ENABLE_Y_AXIS:Nt,ENABLE_NONE_AXIS:Rt,ENABLE_BOTH_AXIS:Mt,CHANGE_ZINDEX:Bt,CHANGE_MINW:Vt,CHANGE_MINH:Pt,CHANGE_WIDTH:zt,CHANGE_HEIGHT:Qt,CHANGE_TOP:Wt,CHANGE_LEFT:Ft},Xt={setActive({commit:t,state:e},{id:n}){for(let r=0,i=e.rects.length;r<i;r++)t(r!==n?qt.DISABLE_ACTIVE:qt.ENABLE_ACTIVE,r)},unsetActive({commit:t},{id:e}){t(qt.DISABLE_ACTIVE,e)},toggleDraggable({commit:t,state:e},{id:n}){e.rects[n].draggable?t(qt.DISABLE_DRAGGABLE,n):t(qt.ENABLE_DRAGGABLE,n)},toggleResizable({commit:t,state:e},{id:n}){e.rects[n].resizable?t(qt.DISABLE_RESIZABLE,n):t(qt.ENABLE_RESIZABLE,n)},toggleParentLimitation({commit:t,state:e},{id:n}){e.rects[n].parentLim?t(qt.DISABLE_PARENT_LIMITATION,n):t(qt.ENABLE_PARENT_LIMITATION,n)},toggleSnapToGrid({commit:t,state:e},{id:n}){e.rects[n].snapToGrid?t(qt.DISABLE_SNAP_TO_GRID,n):t(qt.ENABLE_SNAP_TO_GRID,n)},setAspect({commit:t},{id:e}){t(qt.ENABLE_ASPECT,e)},unsetAspect({commit:t},{id:e}){t(qt.DISABLE_ASPECT,e)},setWidth({commit:t},{id:e,width:n}){t(qt.CHANGE_WIDTH,{id:e,width:n})},setHeight({commit:t},{id:e,height:n}){t(qt.CHANGE_HEIGHT,{id:e,height:n})},setTop({commit:t},{id:e,top:n}){t(qt.CHANGE_TOP,{id:e,top:n})},setLeft({commit:t},{id:e,left:n}){t(qt.CHANGE_LEFT,{id:e,left:n})},changeXLock({commit:t,state:e},{id:n}){switch(e.rects[n].axis){case"both":t(qt.ENABLE_Y_AXIS,n);break;case"x":t(qt.ENABLE_NONE_AXIS,n);break;case"y":t(qt.ENABLE_BOTH_AXIS,n);break;case"none":t(qt.ENABLE_X_AXIS,n);break}},changeYLock({commit:t,state:e},{id:n}){switch(e.rects[n].axis){case"both":t(qt.ENABLE_X_AXIS,n);break;case"x":t(qt.ENABLE_BOTH_AXIS,n);break;case"y":t(qt.ENABLE_NONE_AXIS,n);break;case"none":t(qt.ENABLE_Y_AXIS,n);break}},changeZToBottom({commit:t,state:e},{id:n}){if(1!==e.rects[n].zIndex){t(qt.CHANGE_ZINDEX,{id:n,zIndex:1});for(let r=0,i=e.rects.length;r<i;r++)if(r!==n){if(e.rects[r].zIndex===e.rects.length)continue;t(qt.CHANGE_ZINDEX,{id:r,zIndex:e.rects[r].zIndex+1})}}},changeZToTop({commit:t,state:e},{id:n}){if(e.rects[n].zIndex!==e.rects.length){t(qt.CHANGE_ZINDEX,{id:n,zIndex:e.rects.length});for(let r=0,i=e.rects.length;r<i;r++)if(r!==n){if(1===e.rects[r].zIndex)continue;t(qt.CHANGE_ZINDEX,{id:r,zIndex:e.rects[r].zIndex-1})}}},setMinWidth({commit:t},{id:e,width:n}){t(qt.CHANGE_MINW,{id:e,minw:n})},setMinHeight({commit:t},{id:e,height:n}){t(qt.CHANGE_MINH,{id:e,minh:n})}},Ht={getActive:t=>{for(let e=0,n=t.rects.length;e<n;e++){let n=t.rects[e];if(n.active)return e}return null}},Zt={[At](t,e){t.rects[e].active=!0},[wt](t,e){t.rects[e].active=!1},[Lt](t,e){t.rects[e].aspectRatio=!0},[Gt](t,e){t.rects[e].aspectRatio=!1},[Ct](t,e){t.rects[e].draggable=!0},[St](t,e){t.rects[e].draggable=!1},[kt](t,e){t.rects[e].resizable=!0},[Ot](t,e){t.rects[e].resizable=!1},[Et](t,e){t.rects[e].snapToGrid=!0},[Dt](t,e){t.rects[e].snapToGrid=!1},[Mt](t,e){t.rects[e].axis="both"},[Rt](t,e){t.rects[e].axis="none"},[jt](t,e){t.rects[e].axis="x"},[Nt](t,e){t.rects[e].axis="y"},[Tt](t,e){t.rects[e].parentLim=!0},[It](t,e){t.rects[e].parentLim=!1},[Bt](t,e){t.rects[e.id].zIndex=e.zIndex},[Qt](t,e){t.rects[e.id].height=e.height},[zt](t,e){t.rects[e.id].width=e.width},[Wt](t,e){t.rects[e.id].top=e.top},[Ft](t,e){t.rects[e.id].left=e.left},[Pt](t,e){t.rects[e.id].minh=e.minh},[Vt](t,e){t.rects[e.id].minw=e.minw}},Jt={rects:[{width:200,height:150,top:10,left:10,draggable:!0,resizable:!0,minw:10,minh:10,axis:"both",parentLim:!0,snapToGrid:!1,aspectRatio:!1,zIndex:1,color:"#EF9A9A",active:!1,chart_id:"chart_5erpeWc1eWal_1596092336315"},{width:200,height:150,top:10,left:220,draggable:!0,resizable:!0,minw:10,minh:10,axis:"both",parentLim:!0,snapToGrid:!1,aspectRatio:!1,zIndex:2,color:"#AED581",active:!1,chart_id:"chart_5erpeWc1eWal_15960973336319"},{width:200,height:150,top:170,left:10,draggable:!0,resizable:!0,minw:10,minh:10,axis:"both",parentLim:!0,snapToGrid:!1,aspectRatio:!1,zIndex:3,color:"#81D4FA",active:!1,chart_id:"chart_5erpeWc1eWal_1596093236310"}]},Ut={namespaced:!0,actions:Xt,getters:Ht,mutations:Zt,state:Jt};const Yt={state:()=>({isShow:!0}),getters:{},mutations:{},actions:{}};var Kt=Yt;const _t="ENABLE_ACTIVE",$t="DISABLE_ACTIVE",te="UPDATE_CHART_ITEM",ee="UPDATE_CHART_ITEM_CHARTLIST",ne="UPDATE_CHART_ITEM_ONE",re="UPDATE_CHART_ITEM_CHARTLIST_ONE";var ie={ENABLE_ACTIVE:_t,DISABLE_ACTIVE:$t,UPDATE_CHART_ITEM:te,UPDATE_CHART_ITEM_CHARTLIST:ee,UPDATE_CHART_ITEM_ONE:ne,UPDATE_CHART_ITEM_CHARTLIST_ONE:re},ae={setActive({commit:t,state:e},{id:n}){for(let r=0,i=e.chartLists.length;r<i;r++)t(r!==n?ie.DISABLE_ACTIVE:ie.ENABLE_ACTIVE,r)},unsetActive({commit:t},{id:e}){t(ie.DISABLE_ACTIVE,e)},updateChartItem({commit:t},e){t(ie.UPDATE_CHART_ITEM,e)},updateChartItemChartlist({commit:t},e){t(ie.UPDATE_CHART_ITEM_CHARTLIST,e)},updateChartItemOne({commit:t},e){t(ie.UPDATE_CHART_ITEM_ONE,e)},updateChartItemChartlistOne({commit:t},e){t(ie.UPDATE_CHART_ITEM_CHARTLIST_ONE,e)}},oe={getActive:t=>{for(let e=0,n=t.chartLists.length;e<n;e++){let n=t.chartLists[e];if(n.active)return e}return null}},se={[_t](t,e){t.chartLists[e].active=!0,t.currentChartIndex=e},[$t](t,e){t.chartLists[e].active=!1},[te](t,e){const{router:n,updateObj:r}=e,i=t.chartLists[t.currentChartIndex].chartOptions;pe(i,n,r)},[ee](t,e){let n=t.chartLists.findIndex(t=>t.chart_id==e.chart_id);t.chartLists[n].chartOptions=h.a.extend(t.chartLists[n].chartOptions,e)},[re](t,e){let n=t.chartLists.findIndex(t=>t.chart_id==e.chart_id);t.chartLists[n].chartOptions[e.key]=e.value},[ne](t,e){t[e.key]=e.value}};n("42454"),n("0644");var le={chartLists:[{chart_id:"chart_5erpeWc1eWal_1596092336315",active:!0,chartOptions:{chart_id:"chart_5erpeWc1eWal_1596092336315",chartAllType:"echarts|line|default",chartPro:"echarts",chartType:"line",chartStyle:"default",chartData:[[{v:"德国人的",ct:{fa:"General",t:"g"},m:"德国人的",bg:"rgba(255,255,255)",bl:0,it:0,ff:1,fs:11,fc:"rgb(0, 0, 0)",ht:1,vt:0},{v:"尔尔",ct:{fa:"General",t:"g"},m:"尔尔",bg:"rgba(255,255,255)",bl:0,it:0,ff:1,fs:11,fc:"rgb(0, 0, 0)",ht:1,vt:0},{v:"地方当然",ct:{fa:"General",t:"g"},m:"地方当然",bg:"rgba(255,255,255)",bl:0,it:0,ff:1,fs:11,fc:"rgb(0, 0, 0)",ht:1,vt:0}],[{v:"尔尔",ct:{fa:"General",t:"g"},m:"尔尔",bg:"rgba(255,255,255)",bl:0,it:0,ff:1,fs:11,fc:"rgb(0, 0, 0)",ht:1,vt:0},{v:1,ct:{fa:"General",t:"n"},m:"1",bg:"rgba(255,255,255)",bl:0,it:0,ff:1,fs:11,fc:"rgb(0, 0, 0)",ht:1,vt:0},{v:2,ct:{fa:"General",t:"n"},m:"2",bg:"rgba(255,255,255)",bl:0,it:0,ff:1,fs:11,fc:"rgb(0, 0, 0)",ht:1,vt:0}],[{v:"树人",ct:{fa:"General",t:"g"},m:"树人",bg:"rgba(255,255,255)",bl:0,it:0,ff:1,fs:11,fc:"rgb(0, 0, 0)",ht:1,vt:0},{v:3,ct:{fa:"General",t:"n"},m:"3",bg:"rgba(255,255,255)",bl:0,it:0,ff:1,fs:11,fc:"rgb(0, 0, 0)",ht:1,vt:0},{v:4,ct:{fa:"General",t:"n"},m:"4",bg:"rgba(255,255,255)",bl:0,it:0,ff:1,fs:11,fc:"rgb(0, 0, 0)",ht:1,vt:0}],[{v:"多个",ct:{fa:"General",t:"g"},m:"多个",bg:"rgba(255,255,255)",bl:0,it:0,ff:1,fs:11,fc:"rgb(0, 0, 0)",ht:1,vt:0},{v:5,ct:{fa:"General",t:"n"},m:"5",bg:"rgba(255,255,255)",bl:0,it:0,ff:1,fs:11,fc:"rgb(0, 0, 0)",ht:1,vt:0},{v:6,ct:{fa:"General",t:"n"},m:"6",bg:"rgba(255,255,255)",bl:0,it:0,ff:1,fs:11,fc:"rgb(0, 0, 0)",ht:1,vt:0}]],rangeArray:[{row:[17,20],column:[9,11],row_focus:17,column_focus:9,left:1077,width:140,top:357,height:31,left_move:1077,width_move:359,top_move:357,height_move:94}],rangeTxt:"J18:L21",rangeColCheck:{exits:!0,range:[0,0]},rangeRowCheck:{exits:!0,range:[0,0]},rangeConfigCheck:!1,rangeSplitArray:{title:{row:[0,0],column:[0,0]},rowtitle:{row:[0,0],column:[1,2]},coltitle:{row:[1,3],column:[0,0]},content:{row:[1,3],column:[1,2]},type:"normal",range:{row:[17,20],column:[9,11],row_focus:17,column_focus:9,left:1077,width:140,top:357,height:31,left_move:1077,width_move:359,top_move:357,height_move:94}},chartDataCache:{label:["尔尔","地方当然"],xAxis:["尔尔","树人","多个"],series:[[1,2],[3,4],[5,6]],series_tpye:{0:"num",1:"num"}},chartDataSeriesOrder:{0:0,1:1,length:2},defaultOption:{title:{show:!1,text:"默认标题",label:{fontSize:12,color:"#333",fontFamily:"sans-serif",fontGroup:[],cusFontSize:12},position:{value:"left-top",offsetX:40,offsetY:50}},subtitle:{show:!1,text:"",label:{fontSize:12,color:"#333",fontFamily:"sans-serif",fontGroup:[],cusFontSize:12},distance:{value:"auto",cusGap:40}},config:{color:"transparent",fontFamily:"Sans-serif",grid:{value:"normal",top:5,left:10,right:20,bottom:10}},legend:{show:!0,selectMode:"multiple",selected:[{seriesName:"衣服",isShow:!0},{seriesName:"食材",isShow:!0},{seriesName:"图书",isShow:!0}],label:{fontSize:12,color:"#333",fontFamily:"sans-serif",fontGroup:[],cusFontSize:12},position:{value:"left-top",offsetX:40,offsetY:50,direction:"horizontal"},width:{value:"auto",cusSize:25},height:{value:"auto",cusSize:14},distance:{value:"auto",cusGap:10},itemGap:10,data:["尔尔","地方当然"]},tooltip:{show:!0,label:{fontSize:12,color:"#333",fontFamily:"sans-serif",fontGroup:[],cusFontSize:12},backgroundColor:"rgba(50,50,50,0.7)",triggerOn:"mousemove",triggerType:"item",axisPointer:{type:"line",style:{color:"#555",width:"normal",type:"solid"}},format:[{seriesName:"衣服",prefix:"",suffix:"",ratio:1,digit:"auto"},{seriesName:"食材",prefix:"",suffix:"",ratio:1,digit:"auto"},{seriesName:"图书",prefix:"",suffix:"",ratio:1,digit:"auto"}],position:"auto"},axis:{axisType:"xAxisDown",xAxisUp:{show:!1,title:{showTitle:!1,text:"",nameGap:15,rotate:0,label:{fontSize:12,color:"#333",fontFamily:"sans-serif",fontGroup:[],cusFontSize:12},fzPosition:"end"},name:"显示X轴",inverse:!1,tickLine:{show:!0,width:1,color:"auto"},tick:{show:!0,position:"outside",length:5,width:1,color:"auto"},tickLabel:{show:!0,label:{fontSize:12,color:"#333",fontFamily:"sans-serif",fontGroup:[],cusFontSize:12},rotate:0,prefix:"",suffix:"",optimize:0,distance:0,min:"auto",max:"auto",ratio:1,digit:"auto"},netLine:{show:!1,width:1,type:"solid",color:"auto",interval:{value:"auto",cusNumber:0}},netArea:{show:!1,interval:{value:"auto",cusNumber:0},colorOne:"auto",colorTwo:"auto"},axisLine:{onZero:!1}},xAxisDown:{show:!0,title:{showTitle:!1,text:"",nameGap:15,rotate:0,label:{fontSize:12,color:"#333",fontFamily:"sans-serif",fontGroup:[],cusFontSize:12},fzPosition:"end"},name:"显示X轴",inverse:!1,tickLine:{show:!0,width:1,color:"auto"},tick:{show:!0,position:"outside",length:5,width:1,color:"auto"},tickLabel:{show:!0,label:{fontSize:12,color:"#333",fontFamily:"sans-serif",fontGroup:[],cusFontSize:12},rotate:0,prefix:"",suffix:"",optimize:0,distance:0,min:null,max:null,ratio:1,digit:"auto"},netLine:{show:!1,width:1,type:"solid",color:"auto",interval:{value:"auto",cusNumber:0}},netArea:{show:!1,interval:{value:"auto",cusNumber:0},colorOne:"auto",colorTwo:"auto"},data:["尔尔","树人","多个"],type:"category"},yAxisLeft:{show:!0,title:{showTitle:!1,text:"",nameGap:15,rotate:0,label:{fontSize:12,color:"#333",fontFamily:"sans-serif",fontGroup:[],cusFontSize:12},fzPosition:"end"},name:"显示Y轴",inverse:!1,tickLine:{show:!0,width:1,color:"auto"},tick:{show:!0,position:"outside",length:5,width:1,color:"auto"},tickLabel:{show:!0,label:{fontSize:12,color:"#333",fontFamily:"sans-serif",fontGroup:[],cusFontSize:12},rotate:0,formatter:{prefix:"",suffix:"",ratio:1,digit:"auto"},split:5,min:null,max:null,prefix:"",suffix:"",ratio:1,digit:"auto",distance:0},netLine:{show:!1,width:1,type:"solid",color:"auto",interval:{value:"auto",cusNumber:0}},netArea:{show:!1,interval:{value:"auto",cusNumber:0},colorOne:"auto",colorTwo:"auto"},type:"value"},yAxisRight:{show:!1,title:{showTitle:!1,text:"",nameGap:15,rotate:0,label:{fontSize:12,color:"#333",fontFamily:"sans-serif",fontGroup:[],cusFontSize:12},fzPosition:"end"},name:"显示Y轴",inverse:!1,tickLine:{show:!0,width:1,color:"auto"},tick:{show:!0,position:"outside",length:5,width:1,color:"auto"},tickLabel:{show:!0,label:{fontSize:12,color:"#333",fontFamily:"sans-serif",fontGroup:[],cusFontSize:12},rotate:0,formatter:{prefix:"",suffix:"",ratio:1,digit:"auto"},split:5,min:null,max:null,prefix:"",suffix:"",ratio:1,digit:"auto",distance:0},netLine:{show:!1,width:1,type:"solid",color:"auto",interval:{value:"auto",cusNumber:0}},netArea:{show:!1,interval:{value:"auto",cusNumber:0},colorOne:"auto",colorTwo:"auto"}}},series:[{itemStyle:{color:null,borderColor:"#000",borderType:"solid",borderWidth:1},lineStyle:{color:null,width:1,type:"solid"},data:[1,3,5],type:"line",name:"尔尔",markPoint:{data:[]},markLine:{data:[]},markArea:{data:[]}},{itemStyle:{color:null,borderColor:"#000",borderType:"solid",borderWidth:1},lineStyle:{color:null,width:1,type:"solid"},data:[2,4,6],type:"line",name:"地方当然",markPoint:{data:[]},markLine:{data:[]},markArea:{data:[]}}],seriesData:[[1,3,5],[2,4,6]]}}},{chart_id:"chart_5erpeWc1eWal_1596093236310",active:!0,chartOptions:{chart_id:"chart_5erpeWc1eWal_1596093236310",chartAllType:"echarts|line|default",chartPro:"echarts",chartType:"line",chartStyle:"default",chartData:[[{v:"德国人的",ct:{fa:"General",t:"g"},m:"德国人的",bg:"rgba(255,255,255)",bl:0,it:0,ff:1,fs:11,fc:"rgb(0, 0, 0)",ht:1,vt:0},{v:"尔尔",ct:{fa:"General",t:"g"},m:"尔尔",bg:"rgba(255,255,255)",bl:0,it:0,ff:1,fs:11,fc:"rgb(0, 0, 0)",ht:1,vt:0},{v:"地方当然",ct:{fa:"General",t:"g"},m:"地方当然",bg:"rgba(255,255,255)",bl:0,it:0,ff:1,fs:11,fc:"rgb(0, 0, 0)",ht:1,vt:0}],[{v:"尔尔",ct:{fa:"General",t:"g"},m:"尔尔",bg:"rgba(255,255,255)",bl:0,it:0,ff:1,fs:11,fc:"rgb(0, 0, 0)",ht:1,vt:0},{v:1,ct:{fa:"General",t:"n"},m:"1",bg:"rgba(255,255,255)",bl:0,it:0,ff:1,fs:11,fc:"rgb(0, 0, 0)",ht:1,vt:0},{v:2,ct:{fa:"General",t:"n"},m:"2",bg:"rgba(255,255,255)",bl:0,it:0,ff:1,fs:11,fc:"rgb(0, 0, 0)",ht:1,vt:0}],[{v:"树人",ct:{fa:"General",t:"g"},m:"树人",bg:"rgba(255,255,255)",bl:0,it:0,ff:1,fs:11,fc:"rgb(0, 0, 0)",ht:1,vt:0},{v:3,ct:{fa:"General",t:"n"},m:"3",bg:"rgba(255,255,255)",bl:0,it:0,ff:1,fs:11,fc:"rgb(0, 0, 0)",ht:1,vt:0},{v:4,ct:{fa:"General",t:"n"},m:"4",bg:"rgba(255,255,255)",bl:0,it:0,ff:1,fs:11,fc:"rgb(0, 0, 0)",ht:1,vt:0}],[{v:"多个",ct:{fa:"General",t:"g"},m:"多个",bg:"rgba(255,255,255)",bl:0,it:0,ff:1,fs:11,fc:"rgb(0, 0, 0)",ht:1,vt:0},{v:5,ct:{fa:"General",t:"n"},m:"5",bg:"rgba(255,255,255)",bl:0,it:0,ff:1,fs:11,fc:"rgb(0, 0, 0)",ht:1,vt:0},{v:6,ct:{fa:"General",t:"n"},m:"6",bg:"rgba(255,255,255)",bl:0,it:0,ff:1,fs:11,fc:"rgb(0, 0, 0)",ht:1,vt:0}]],rangeArray:[{row:[17,20],column:[9,11],row_focus:17,column_focus:9,left:1077,width:140,top:357,height:31,left_move:1077,width_move:359,top_move:357,height_move:94}],rangeTxt:"J18:L21",rangeColCheck:{exits:!0,range:[0,0]},rangeRowCheck:{exits:!0,range:[0,0]},rangeConfigCheck:!1,rangeSplitArray:{title:{row:[0,0],column:[0,0]},rowtitle:{row:[0,0],column:[1,2]},coltitle:{row:[1,3],column:[0,0]},content:{row:[1,3],column:[1,2]},type:"normal",range:{row:[17,20],column:[9,11],row_focus:17,column_focus:9,left:1077,width:140,top:357,height:31,left_move:1077,width_move:359,top_move:357,height_move:94}},chartDataCache:{label:["尔尔","地方当然"],xAxis:["尔尔","树人","多个"],series:[[1,2],[3,4],[5,6]],series_tpye:{0:"num",1:"num"}},chartDataSeriesOrder:{0:0,1:1,length:2},defaultOption:{title:{show:!1,text:"默认标题",label:{fontSize:12,color:"#333",fontFamily:"sans-serif",fontGroup:[],cusFontSize:12},position:{value:"left-top",offsetX:40,offsetY:50}},subtitle:{show:!1,text:"",label:{fontSize:12,color:"#333",fontFamily:"sans-serif",fontGroup:[],cusFontSize:12},distance:{value:"auto",cusGap:40}},config:{color:"transparent",fontFamily:"Sans-serif",grid:{value:"normal",top:5,left:10,right:20,bottom:10}},legend:{show:!0,selectMode:"multiple",selected:[{seriesName:"衣服",isShow:!0},{seriesName:"食材",isShow:!0},{seriesName:"图书",isShow:!0}],label:{fontSize:12,color:"#333",fontFamily:"sans-serif",fontGroup:[],cusFontSize:12},position:{value:"left-top",offsetX:40,offsetY:50,direction:"horizontal"},width:{value:"auto",cusSize:25},height:{value:"auto",cusSize:14},distance:{value:"auto",cusGap:10},itemGap:10,data:["尔尔","地方当然"]},tooltip:{show:!0,label:{fontSize:12,color:"#333",fontFamily:"sans-serif",fontGroup:[],cusFontSize:12},backgroundColor:"rgba(50,50,50,0.7)",triggerOn:"mousemove",triggerType:"item",axisPointer:{type:"line",style:{color:"#555",width:"normal",type:"solid"}},format:[{seriesName:"衣服",prefix:"",suffix:"",ratio:1,digit:"auto"},{seriesName:"食材",prefix:"",suffix:"",ratio:1,digit:"auto"},{seriesName:"图书",prefix:"",suffix:"",ratio:1,digit:"auto"}],position:"auto"},axis:{axisType:"xAxisDown",xAxisUp:{show:!1,title:{showTitle:!1,text:"",nameGap:15,rotate:0,label:{fontSize:12,color:"#333",fontFamily:"sans-serif",fontGroup:[],cusFontSize:12},fzPosition:"end"},name:"显示X轴",inverse:!1,tickLine:{show:!0,width:1,color:"auto"},tick:{show:!0,position:"outside",length:5,width:1,color:"auto"},tickLabel:{show:!0,label:{fontSize:12,color:"#333",fontFamily:"sans-serif",fontGroup:[],cusFontSize:12},rotate:0,prefix:"",suffix:"",optimize:0,distance:0,min:"auto",max:"auto",ratio:1,digit:"auto"},netLine:{show:!1,width:1,type:"solid",color:"auto",interval:{value:"auto",cusNumber:0}},netArea:{show:!1,interval:{value:"auto",cusNumber:0},colorOne:"auto",colorTwo:"auto"},axisLine:{onZero:!1}},xAxisDown:{show:!0,title:{showTitle:!1,text:"",nameGap:15,rotate:0,label:{fontSize:12,color:"#333",fontFamily:"sans-serif",fontGroup:[],cusFontSize:12},fzPosition:"end"},name:"显示X轴",inverse:!1,tickLine:{show:!0,width:1,color:"auto"},tick:{show:!0,position:"outside",length:5,width:1,color:"auto"},tickLabel:{show:!0,label:{fontSize:12,color:"#333",fontFamily:"sans-serif",fontGroup:[],cusFontSize:12},rotate:0,prefix:"",suffix:"",optimize:0,distance:0,min:null,max:null,ratio:1,digit:"auto"},netLine:{show:!1,width:1,type:"solid",color:"auto",interval:{value:"auto",cusNumber:0}},netArea:{show:!1,interval:{value:"auto",cusNumber:0},colorOne:"auto",colorTwo:"auto"},data:["尔尔","树人","多个"],type:"category"},yAxisLeft:{show:!0,title:{showTitle:!1,text:"",nameGap:15,rotate:0,label:{fontSize:12,color:"#333",fontFamily:"sans-serif",fontGroup:[],cusFontSize:12},fzPosition:"end"},name:"显示Y轴",inverse:!1,tickLine:{show:!0,width:1,color:"auto"},tick:{show:!0,position:"outside",length:5,width:1,color:"auto"},tickLabel:{show:!0,label:{fontSize:12,color:"#333",fontFamily:"sans-serif",fontGroup:[],cusFontSize:12},rotate:0,formatter:{prefix:"",suffix:"",ratio:1,digit:"auto"},split:5,min:null,max:null,prefix:"",suffix:"",ratio:1,digit:"auto",distance:0},netLine:{show:!1,width:1,type:"solid",color:"auto",interval:{value:"auto",cusNumber:0}},netArea:{show:!1,interval:{value:"auto",cusNumber:0},colorOne:"auto",colorTwo:"auto"},type:"value"},yAxisRight:{show:!1,title:{showTitle:!1,text:"",nameGap:15,rotate:0,label:{fontSize:12,color:"#333",fontFamily:"sans-serif",fontGroup:[],cusFontSize:12},fzPosition:"end"},name:"显示Y轴",inverse:!1,tickLine:{show:!0,width:1,color:"auto"},tick:{show:!0,position:"outside",length:5,width:1,color:"auto"},tickLabel:{show:!0,label:{fontSize:12,color:"#333",fontFamily:"sans-serif",fontGroup:[],cusFontSize:12},rotate:0,formatter:{prefix:"",suffix:"",ratio:1,digit:"auto"},split:5,min:null,max:null,prefix:"",suffix:"",ratio:1,digit:"auto",distance:0},netLine:{show:!1,width:1,type:"solid",color:"auto",interval:{value:"auto",cusNumber:0}},netArea:{show:!1,interval:{value:"auto",cusNumber:0},colorOne:"auto",colorTwo:"auto"}}},series:[{itemStyle:{color:null,borderColor:"#000",borderType:"solid",borderWidth:1},lineStyle:{color:null,width:1,type:"solid"},data:[1,3,5],type:"line",name:"尔尔",markPoint:{data:[]},markLine:{data:[]},markArea:{data:[]}},{itemStyle:{color:null,borderColor:"#000",borderType:"solid",borderWidth:1},lineStyle:{color:null,width:1,type:"solid"},data:[2,4,6],type:"line",name:"地方当然",markPoint:{data:[]},markLine:{data:[]},markArea:{data:[]}}],seriesData:[[1,3,5],[2,4,6]]}}},{chart_id:"chart_5erpeWc1eWal_15960973336319",active:!0,chartOptions:{chart_id:"chart_5erpeWc1eWal_15960973336319",chartAllType:"echarts|line|default",chartPro:"echarts",chartType:"line",chartStyle:"default",chartData:[[{v:"德国人的",ct:{fa:"General",t:"g"},m:"德国人的",bg:"rgba(255,255,255)",bl:0,it:0,ff:1,fs:11,fc:"rgb(0, 0, 0)",ht:1,vt:0},{v:"尔尔",ct:{fa:"General",t:"g"},m:"尔尔",bg:"rgba(255,255,255)",bl:0,it:0,ff:1,fs:11,fc:"rgb(0, 0, 0)",ht:1,vt:0},{v:"地方当然",ct:{fa:"General",t:"g"},m:"地方当然",bg:"rgba(255,255,255)",bl:0,it:0,ff:1,fs:11,fc:"rgb(0, 0, 0)",ht:1,vt:0}],[{v:"尔尔",ct:{fa:"General",t:"g"},m:"尔尔",bg:"rgba(255,255,255)",bl:0,it:0,ff:1,fs:11,fc:"rgb(0, 0, 0)",ht:1,vt:0},{v:1,ct:{fa:"General",t:"n"},m:"1",bg:"rgba(255,255,255)",bl:0,it:0,ff:1,fs:11,fc:"rgb(0, 0, 0)",ht:1,vt:0},{v:2,ct:{fa:"General",t:"n"},m:"2",bg:"rgba(255,255,255)",bl:0,it:0,ff:1,fs:11,fc:"rgb(0, 0, 0)",ht:1,vt:0}],[{v:"树人",ct:{fa:"General",t:"g"},m:"树人",bg:"rgba(255,255,255)",bl:0,it:0,ff:1,fs:11,fc:"rgb(0, 0, 0)",ht:1,vt:0},{v:3,ct:{fa:"General",t:"n"},m:"3",bg:"rgba(255,255,255)",bl:0,it:0,ff:1,fs:11,fc:"rgb(0, 0, 0)",ht:1,vt:0},{v:4,ct:{fa:"General",t:"n"},m:"4",bg:"rgba(255,255,255)",bl:0,it:0,ff:1,fs:11,fc:"rgb(0, 0, 0)",ht:1,vt:0}],[{v:"多个",ct:{fa:"General",t:"g"},m:"多个",bg:"rgba(255,255,255)",bl:0,it:0,ff:1,fs:11,fc:"rgb(0, 0, 0)",ht:1,vt:0},{v:5,ct:{fa:"General",t:"n"},m:"5",bg:"rgba(255,255,255)",bl:0,it:0,ff:1,fs:11,fc:"rgb(0, 0, 0)",ht:1,vt:0},{v:6,ct:{fa:"General",t:"n"},m:"6",bg:"rgba(255,255,255)",bl:0,it:0,ff:1,fs:11,fc:"rgb(0, 0, 0)",ht:1,vt:0}]],rangeArray:[{row:[17,20],column:[9,11],row_focus:17,column_focus:9,left:1077,width:140,top:357,height:31,left_move:1077,width_move:359,top_move:357,height_move:94}],rangeTxt:"J18:L21",rangeColCheck:{exits:!0,range:[0,0]},rangeRowCheck:{exits:!0,range:[0,0]},rangeConfigCheck:!1,rangeSplitArray:{title:{row:[0,0],column:[0,0]},rowtitle:{row:[0,0],column:[1,2]},coltitle:{row:[1,3],column:[0,0]},content:{row:[1,3],column:[1,2]},type:"normal",range:{row:[17,20],column:[9,11],row_focus:17,column_focus:9,left:1077,width:140,top:357,height:31,left_move:1077,width_move:359,top_move:357,height_move:94}},chartDataCache:{label:["尔尔","地方当然"],xAxis:["尔尔","树人","多个"],series:[[1,2],[3,4],[5,6]],series_tpye:{0:"num",1:"num"}},chartDataSeriesOrder:{0:0,1:1,length:2},defaultOption:{title:{show:!1,text:"默认标题",label:{fontSize:12,color:"#333",fontFamily:"sans-serif",fontGroup:[],cusFontSize:12},position:{value:"left-top",offsetX:40,offsetY:50}},subtitle:{show:!1,text:"",label:{fontSize:12,color:"#333",fontFamily:"sans-serif",fontGroup:[],cusFontSize:12},distance:{value:"auto",cusGap:40}},config:{color:"transparent",fontFamily:"Sans-serif",grid:{value:"normal",top:5,left:10,right:20,bottom:10}},legend:{show:!0,selectMode:"multiple",selected:[{seriesName:"衣服",isShow:!0},{seriesName:"食材",isShow:!0},{seriesName:"图书",isShow:!0}],label:{fontSize:12,color:"#333",fontFamily:"sans-serif",fontGroup:[],cusFontSize:12},position:{value:"left-top",offsetX:40,offsetY:50,direction:"horizontal"},width:{value:"auto",cusSize:25},height:{value:"auto",cusSize:14},distance:{value:"auto",cusGap:10},itemGap:10,data:["尔尔","地方当然"]},tooltip:{show:!0,label:{fontSize:12,color:"#333",fontFamily:"sans-serif",fontGroup:[],cusFontSize:12},backgroundColor:"rgba(50,50,50,0.7)",triggerOn:"mousemove",triggerType:"item",axisPointer:{type:"line",style:{color:"#555",width:"normal",type:"solid"}},format:[{seriesName:"衣服",prefix:"",suffix:"",ratio:1,digit:"auto"},{seriesName:"食材",prefix:"",suffix:"",ratio:1,digit:"auto"},{seriesName:"图书",prefix:"",suffix:"",ratio:1,digit:"auto"}],position:"auto"},axis:{axisType:"xAxisDown",xAxisUp:{show:!1,title:{showTitle:!1,text:"",nameGap:15,rotate:0,label:{fontSize:12,color:"#333",fontFamily:"sans-serif",fontGroup:[],cusFontSize:12},fzPosition:"end"},name:"显示X轴",inverse:!1,tickLine:{show:!0,width:1,color:"auto"},tick:{show:!0,position:"outside",length:5,width:1,color:"auto"},tickLabel:{show:!0,label:{fontSize:12,color:"#333",fontFamily:"sans-serif",fontGroup:[],cusFontSize:12},rotate:0,prefix:"",suffix:"",optimize:0,distance:0,min:"auto",max:"auto",ratio:1,digit:"auto"},netLine:{show:!1,width:1,type:"solid",color:"auto",interval:{value:"auto",cusNumber:0}},netArea:{show:!1,interval:{value:"auto",cusNumber:0},colorOne:"auto",colorTwo:"auto"},axisLine:{onZero:!1}},xAxisDown:{show:!0,title:{showTitle:!1,text:"",nameGap:15,rotate:0,label:{fontSize:12,color:"#333",fontFamily:"sans-serif",fontGroup:[],cusFontSize:12},fzPosition:"end"},name:"显示X轴",inverse:!1,tickLine:{show:!0,width:1,color:"auto"},tick:{show:!0,position:"outside",length:5,width:1,color:"auto"},tickLabel:{show:!0,label:{fontSize:12,color:"#333",fontFamily:"sans-serif",fontGroup:[],cusFontSize:12},rotate:0,prefix:"",suffix:"",optimize:0,distance:0,min:null,max:null,ratio:1,digit:"auto"},netLine:{show:!1,width:1,type:"solid",color:"auto",interval:{value:"auto",cusNumber:0}},netArea:{show:!1,interval:{value:"auto",cusNumber:0},colorOne:"auto",colorTwo:"auto"},data:["尔尔","树人","多个"],type:"category"},yAxisLeft:{show:!0,title:{showTitle:!1,text:"",nameGap:15,rotate:0,label:{fontSize:12,color:"#333",fontFamily:"sans-serif",fontGroup:[],cusFontSize:12},fzPosition:"end"},name:"显示Y轴",inverse:!1,tickLine:{show:!0,width:1,color:"auto"},tick:{show:!0,position:"outside",length:5,width:1,color:"auto"},tickLabel:{show:!0,label:{fontSize:12,color:"#333",fontFamily:"sans-serif",fontGroup:[],cusFontSize:12},rotate:0,formatter:{prefix:"",suffix:"",ratio:1,digit:"auto"},split:5,min:null,max:null,prefix:"",suffix:"",ratio:1,digit:"auto",distance:0},netLine:{show:!1,width:1,type:"solid",color:"auto",interval:{value:"auto",cusNumber:0}},netArea:{show:!1,interval:{value:"auto",cusNumber:0},colorOne:"auto",colorTwo:"auto"},type:"value"},yAxisRight:{show:!1,title:{showTitle:!1,text:"",nameGap:15,rotate:0,label:{fontSize:12,color:"#333",fontFamily:"sans-serif",fontGroup:[],cusFontSize:12},fzPosition:"end"},name:"显示Y轴",inverse:!1,tickLine:{show:!0,width:1,color:"auto"},tick:{show:!0,position:"outside",length:5,width:1,color:"auto"},tickLabel:{show:!0,label:{fontSize:12,color:"#333",fontFamily:"sans-serif",fontGroup:[],cusFontSize:12},rotate:0,formatter:{prefix:"",suffix:"",ratio:1,digit:"auto"},split:5,min:null,max:null,prefix:"",suffix:"",ratio:1,digit:"auto",distance:0},netLine:{show:!1,width:1,type:"solid",color:"auto",interval:{value:"auto",cusNumber:0}},netArea:{show:!1,interval:{value:"auto",cusNumber:0},colorOne:"auto",colorTwo:"auto"}}},series:[{itemStyle:{color:null,borderColor:"#000",borderType:"solid",borderWidth:1},lineStyle:{color:null,width:1,type:"solid"},data:[1,3,5],type:"line",name:"尔尔",markPoint:{data:[]},markLine:{data:[]},markArea:{data:[]}},{itemStyle:{color:null,borderColor:"#000",borderType:"solid",borderWidth:1},lineStyle:{color:null,width:1,type:"solid"},data:[2,4,6],type:"line",name:"地方当然",markPoint:{data:[]},markLine:{data:[]},markArea:{data:[]}}],seriesData:[[1,3,5],[2,4,6]]}}}],currentChartIndex:null},ce={namespaced:!0,actions:ae,getters:oe,mutations:se,state:le};xt.a.use(p.a);new p.a.Store;const ue=!1;var he=new p.a.Store({modules:{rect:Ut,chartRender:Kt,chartSetting:ce},strict:ue});const fe=n("164e"),pe=function(t,e,n){if(void 0==t||void 0==e)return t;let r=e.split("/");const i=t.defaultOption;function a(t){return 0!=r.length?a(t[r.shift()]):(Object.assign(t,n),t)}return a(i),de({chartOptions:t}),t},de=function(t,e){let n=t.chartOptions,r=n.chart_id;const i=n.chartAllType.split("|"),a=i[0],o=document.getElementById(r);if(o&&"echarts"===a){const t=vt(n);let e=fe.getInstanceByDom(o);null==e&&(e=fe.init(o)),e.setOption(t,!0),setTimeout(()=>{fe.getInstanceById(o.getAttribute("_echarts_instance_")).resize()},0)}},ge=function(t,e,n,r){let i=["bold","vertical","italic"];t.label.fontGroup.forEach(a=>{if(i.includes(a))switch(a){case"bold":e[n].fontWeight=a;break;case"vertical":e[r]=t.text.replace(/\B/g,"\n");break;case"italic":e[n].fontStyle=a;break}}),e[n].color=t.label.color,e[n].fontSize=ve(t.label.fontSize,t.label.cusFontSize)},me=function(){function t(t){return Math.floor(t)===t}function e(e){var n={times:1,num:0};if(t(e))return n.num=e,n;var r=e+"",i=r.indexOf("."),a=r.substr(i+1).length,o=Math.pow(10,a),s=parseInt(e*o+.5,10);return n.times=o,n.num=s,n}function n(t,r,i){var a=e(t),o=e(r),s=a.num,l=o.num,c=a.times,u=o.times,h=c>u?c:u,f=null;switch(i){case"add":return f=c===u?s+l:c>u?s+l*(c/u):s*(u/c)+l,f/h;case"subtract":return f=c===u?s-l:c>u?s-l*(c/u):s*(u/c)-l,f/h;case"multiply":return f=s*l/(c*u),f;case"divide":return function(){var t=s/l,e=u/c;return n(t,e,"multiply")}()}}function r(t,e){return n(t,e,"add")}function i(t,e){return n(t,e,"subtract")}function a(t,e){return n(t,e,"multiply")}function o(t,e){return n(t,e,"divide")}return{add:r,subtract:i,multiply:a,divide:o}}(),be=function(t,e,n="solid"){return{width:t,color:e,type:n}},ve=function(t,e){return"custom"!=t?t:e},ye=function(t,e){var n=t.chart_id,r=n,i={},a=e.split("|"),o=a[0],s=a[1],l=a[2];i.chart_id=r,i.chartAllType=e;var c=t.defaultOption;c.series=[];var u=H(t.chartData,t.rangeSplitArray,o,s,l);i.chartDataCache=u;var h=Z(u.series[0].length);i.chartDataSeriesOrder=h;var f=J(c,u,h,o,s,l,t.chartData);i.defaultOption=f,he.dispatch("chartSetting/updateChartItemChartlist",i),de({chartOptions:i,chart_id:r})},xe=function(t,e,n,r){var i=he.state,a=L(i.chartSetting.chartLists[i.chartSetting.currentChartIndex].chartOptions);a.chart_id=t,a.rangeRowCheck=e,a.rangeColCheck=n,a.rangeConfigCheck=r,a.chartData=a.chartData||[],a.rangeSplitArray=q(a.chartData,a.rangeArray,n,e);var o=a.chartAllType.split("|"),s=o[0],l=o[1],c=o[2];a.chartDataCache=H(a.chartData,a.rangeSplitArray,s,l,c,r),a.chartDataSeriesOrder=Z(a.chartDataCache.series[0].length),a.defaultOption=J(a.defaultOption,a.chartDataCache,a.chartDataSeriesOrder,s,l,c),he.dispatch("chartSetting/updateChartItemChartlist",a),de({chartOptions:a,chart_id:t})},Ae=function(t,e){if(null!=t){var n=t.chart_id,r=t.chartAllType.split("|"),i=r[0],a=r[1],o=r[2];t.defaultOption=J(t.defaultOption,t.chartDataCache,e,i,a,o),he.dispatch("chartSetting/updateChartItemChartlist",t),de({chartOptions:t,chart_id:n})}};function we(t,e,n,r){let i=he.state.chartSetting.chartLists.findIndex(e=>e.chart_id==t);he.state.chartSetting.currentChartIndex=i;var a=he.state.chartSetting.chartLists[i].chartOptions,o=a.chartAllType,s=o.split("|"),l=s[0],c=s[1],u=s[2];a.rangeArray=n,a.chartData=e,a.rangeTxt=r;var h=F(e),f=h[0],p=h[1],d=!1;a.rangeColCheck=p,a.rangeRowCheck=f,a.rangeConfigCheck=d;var g=q(e,n,p,f);a.rangeSplitArray=g;var m=H(e,g,l,c,u);a.chartDataCache=m;var b=Z(m.series[0].length);a.chartDataSeriesOrder=b;var v=a.defaultOption,y=J(v,m,b,l,c,u,!0,e);a.defaultOption=y,he.dispatch("chartSetting/updateChartItemChartlist",a),de({chartOptions:a,chart_id:t})}function Ce(t,e){let n=he.state.chartSetting.chartLists.findIndex(e=>e.chart_id==t);he.state.chartSetting.currentChartIndex=n;var r=he.state.chartSetting.chartLists[n].chartOptions,i=r.chartAllType,a=i.split("|"),o=a[0],s=a[1],l=a[2];r.chartData=e;var c=r.rangeRowCheck,u=r.rangeColCheck,h=q(e,r.rangeArray,u,c);r.rangeSplitArray=h;var f=H(e,h,o,s,l);r.chartDataCache=f;var p=Z(f.series[0].length);r.chartDataSeriesOrder=p;var d=r.defaultOption,g=J(d,f,p,o,s,l,!0,e);r.defaultOption=g,he.dispatch("chartSetting/updateChartItemChartlist",r),de({chartOptions:r,chart_id:t})}var Se=n("4f4d");let ke={chartSetting:{data:"数据",chartType:"图表类型",transpose:"转置(切换行/列)",row1:"设选中项第",row2:"行为标题",column:"设选中项第",column:"列为标题",style:"样式",echarts:{line:{default:"默认折线图",smooth:"平滑折线图",label:"带标签的折线图"},area:{default:"默认面积图",stack:"堆叠面积图"},column:{default:"默认柱状图",stack:"堆叠柱状图"},bar:{default:"默认条形图",stack:"堆叠条形图"},pie:{default:"默认饼图",split:"分离型饼图",ring:"环形饼图"}}},chartTitle:{modalName:"标题设置",show:"显示主标题",text:"标题内容",placeholder:"请输入标题内容",label:"文本样式",position:"标题位置",offsetL:"滑动修改左边距偏移量",offsetT:"滑动修改上边距偏移量"},chartSubTitle:{modalName:"副标题设置",placeholder:"请输入标题内容",text:"副标题内容",label:"文本样式",gap:"主副标题间距",content:"滑动修改间距"},chartAxis:{modalName:"XY轴设置",select:"选择坐标轴",text:"标题内容",placeholder:"请输入标题内容",label:"文本样式",align:"文本对齐方式",reverse:"反向坐标轴",content:"滑动修改坐标轴间隔个数",intenval:"坐标轴间隔个数",content1:"滑动修改标题与轴线距离",gap:"标题与轴线距离",content2:"滑动修改倾斜角度",title:"倾斜轴标题",showLine:"显示刻度线",content3:"滑动修改刻度线宽度",lineWidth:"刻度线宽度",lineColor:"刻度线颜色",showTick:"显示刻度",position:"刻度位置",content4:"滑动修改刻度长度",tickLength:"刻度长度",content5:"滑动修改刻度宽度",tickWidth:"刻度宽度",tickColor:"刻度颜色",showLabel:"显示刻度标签",content6:"滑动修改标签倾斜角度",rotate:"倾斜标签",content7:"请输入刻度最小值",min:"刻度最小值",content8:"请输入刻度最大值且最大值不能小于最小值",max:"刻度最大值",ratio:"数值缩放比例",digit:"小数位数",content9:"请输入标签前缀",prefix:"标签前缀",content10:"请输入标签后缀",suffix:"标签后缀",showNet:"显示网格线",content11:"滑动修改网格线宽度",netWidth:"网格线宽度",netType:"网格线类型",netColor:"网格线颜色",netInterval:"网格线分割间隔数",content12:"滑动修改间隔数",showArea:"显示网格区域",areaInterval:"网格区域分割间隔数",area1:"网格区域第一颜色",area2:"网格区域第二颜色"},chartLegend:{modalName:"图例设置",show:"显示图例",label:"图例样式",position:"图例位置",contentH:"滑动修改水平偏移量",contentV:"滑动修改垂直偏移量",direction:"图例朝向",width:"图例宽度",height:"图例高度",contentWidth:"滑动修改图例宽度",contentHeight:"滑动修改图例高度",gap:"图例间距",contentGap:"滑动修改图例间距"},chartCursor:{modalName:"鼠标提示",show:"显示提示框",label:"鼠标提示样式",background:"背景颜色",trigger:"提示触发条件",type:"提示触发类型",lineType:"指示器线类型",lineWidth:"指示器线宽",color:"线条颜色",axisType:"指示器类型",position:"提示框浮层位置",suffix:"鼠标提示后缀",placeholder:"后缀",ratio:"数值比例",digit:"小数位数"}};var Oe=ke;let Te={chartSetting:{data:"data",chartType:"chartType",transpose:"transpose(switch row/column)",row1:"set number",row2:"row as title",column1:"set number",column2:"column as title",style:"style",echarts:{line:{default:"Basic Line",smooth:"Smoothed Line",label:"Line With Label"},area:{default:"Basic Area",stack:"Stacked Area"},column:{default:"Basic Column",stack:"Stacked Column"},bar:{default:"Basic Bar",stack:"Stacked Bar"},pie:{default:"Basic Pie",split:"Split Pie",ring:"Doughnut Pie"}}},chartTitle:{modalName:"Title Setting",show:"show title",text:"content",placeholder:"enter the title",label:"label style",position:"position",offsetL:"slide to change offsetLeft",offsetT:"slide to change offsetTop"},chartSubTitle:{modalName:"SubTitle Setting",placeholder:"enter the title",text:"content",label:"label style",gap:"The gap between the main title and subtitle",content:"silde to change gap"},chartAxis:{modalName:"XY-Axis Setting",select:"select axis",text:"title content",placeholder:"enter title content",label:"label style",align:"text align",reverse:"invert the axis",content:"slide to change interval",intenval:"interval for axis",content1:"slide to change gap",gap:"Gap between name and line",content2:"slide to change rotate",title:"Rotation of axis name",showLine:"show axisLine",content3:"slide to change lineWidth",lineWidth:"lineWidth",lineColor:"lineColor",showTick:"showTick",position:"tick position",content4:"slide to change tickLength",tickLength:"tickLength",content5:"slide to change tickWidth",tickWidth:"tickWidth",tickColor:"tickColor",showLabel:"show axisLabel",content6:"slide to change rotate",rotate:"Rotation of axisLabel",content7:"enter minValue",min:"minValue",content8:"enter maxValue",max:"maxValue",ratio:"scale value",digit:"decimal digits",content9:"enter prefix",prefix:"prefix",content10:"enter suffix",suffix:"suffix",showNet:"show splitLine",content11:"slide to change lineWidth",netWidth:"splitLine line width",netType:"splitLine type",netColor:"splitLine color",netInterval:"Interval of Axis splitLine",content12:"slide to change interval",showArea:"show splitArea",areaInterval:"Interval of Axis splitArea",area1:"areaOne",area2:"areaTwo"},chartLegend:{modalName:"Legend Setting",show:"show legend",label:"label style",position:"position",contentH:"slide to change ox",contentV:"slide to change oy",direction:"direction",width:"itenWidth",height:"itemHeight",contentWidth:"slide to change height",contentHeight:"slide to change width",gap:"The distance between each legend",contentGap:"slide to change thes gap"},chartCursor:{modalName:"Tooltip Setting",show:"show tooltip",label:"label style",background:"backgroundColor",trigger:"Conditions to trigger",type:"Type of triggering",lineType:"line type",lineWidth:"line width",color:"line color",axisType:"Indicator type",position:"position",suffix:"suffix of tooltip",placeholder:"suffix",ratio:"scale value",digit:"decimal digits"}};var Ie=Te,Ee={name:"ChartList",props:{showList:{type:Boolean,default:!1},zindex:{type:Number,default:10},currentChartType:{type:String,default:"echarts|line|default"},lang:{type:String,default:"cn"}},data:function(){return{config:[{type:"echarts",name:"echarts",data:[]}],currentConfig:[],currentPro:null,chartAllType:"",list_scroll_direction:-1e3,echartsCN:[{type:"line",name:"折线图",icon:"icon-tubiaozhexiantu",data:[{type:"echarts|line|default",name:"折线图",img:"data:image/png;base64,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"},{type:"echarts|line|smooth",name:"平滑折线图",img:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABQCAYAAADvCdDvAAAQnklEQVR4Xu1cfVxUZfb/njsDiEhCiqalYmpmCDMDM5YvzKCCu1quCpSxWq1ptlu7lZ82K3tRy9q2N922F1Pz57ZGb+Bb7a9aUJwBTWPGmUFNLVOzF1MhQVDEmblnP3dwaD4s0J03GFvuX8Plec75Pud7n3Oe5zznXkLnFVYWoLBC0wkGnYSE2UPQSUgnIWFmgTCD4/MMMaeldT3L52cw6CvDrootYTYev+AMnzClH4nKKQKwzV5caPVLSJA6+URIiS7psuiEvjsSJkzsXmMxK501NRtHbPrXzCBh6RAxKeOyUxMSemy5JXtSZP7Gj52x3WIeLVzx4ksdAgbwLahv049a1m/23Hv73XqbG++OrHG1qDyVMtJmO9JRAwhU76gptxxZuuiBASPUyag5XYvMm+9oMH/0bpdA5frbv8UZYjabuSWBjvWFuPyqoWgiZPJEKP54H4SePf3V36H9nl2Rj29+OIFli+fDQ0hW3p3Q61TIm5wZMmxarbZVz+Szy+qS0HdnwtjxV9TYbILzdPXGaz/4eGrIkIdQcEpWzjIC3csi1yb0vFQ5M/t6yt/wEZ8/fx7Vp2ujAV5jLyqcFUIILYr2iRBJgjuok2gGYRgYN+rN1oL2Bh2ovuGZuRkKQokkx8UYC8HxlUKMmApwmZMEVjJvBaE7g/9WUVR4X6D6fOnvMyGScJNW/SyIHiDgmfRy68O+KAyHtqrM3BIQMlozeFLWjWoPKRJhe4oLtrYXbr8IMepU0wnCO8xcZDDbJrQX2GDocRsbbAWjhpzORNvWDdUtyU3Oyv6dAOH/AD5CDpemtXbBwOQtwz9C1OohFEFfMHO1wWyLDzaoUMprih0y3FFKVu5WAgzMWFxRXLAolLg8sv0ipNFtac6A0DXCwQMvpmWvKivnMECJTpBmb9H7traMrM6YmsgRysNgriane5aEfHkfACHqEhBlgJGrN1sL2+PpCVTHT+6Kv7YXFybKkec1o/5RUVT4Ozl9AmnjNyFGnep5gnA/RP6L3mJbEAiI9uqbkpm7iAgLGSzbuOqMqXGsVB6RVl3kcA4M9Szxm5BSrSaPCfnM/G+D2far9jJqIHpUWblSnkotijxt9+bCDXJltecs8Z+QEaqrmIUDF0tgdz/pEcpTEgn2ogKfxt0slkizpMWVmVyC22rnE7Dmgkw69Y8AxQvsunqMueJAMAD5IsOUlpKqvKT7a1If16naB10ROKN00ndjrNbvm8tJHp8zVRBoPQPGiqKCDF/0SG1TsnLWEOi2UK+4AiLEqFO/S6CbIOIPeot1ua+DDKS9UTe8X1TPPruHLl7SXZKz/7EFcPz44wWR/DkYC/Rm20aPjia34+cS9qfdPR+xFxUODAS7zzOkteRic0FOkxHn314LRaoWUXfcGSqMLcp1bClG39690f/WxoXP0dVv4Ou33wKfqQM5ne57irHjEHVTnvv3g8++hspTNVh4z+3o36eXX1jn//VVVFWfxt0zs5GadJVfMqROQUsuNkewPS35aqeg3AfwKX257VK/EfrYcW9SUmRldMTWqF69Rqbmv+fuvWvm9LqGH37Qx7DywFlyPQIi98qPmRffFT9kg2d3bi8uiPNRXVPzlMzc+4iw1JdVmq+6AnJZkjKjTnOMgMvYRdcYdu3a5ysAX9szoDBp1ZuIaJIyLr7adfZMFwK7ugxIXKDNf6/pYMmUpp4DgVZK8l/v2mftnsiuM5mxsaK4wO/stPfCgBzO+FAE94AJMWnV+SDKg4i79BarO8CG8jKmqR4lQXiSgSoBzhHp5bsPtabPqFXdSSQsfyb2Cv5OEUUixFm7i9atCQRfSmbuBiJMYca8iuKCZYHIaqlvwISU6tRzGfQ6g98zlNumBxugtzxjWnIyBOUu6R6Jrmv1lgr377au9Trdk4viBjzq9t0N1alvmIoDOjP3rNYA2OxFBZqf0+/r/wMmxHgh0RjqOFKSmNhF0TPOBqKhEMUn9Bb7QjmDVWVOmwVSrL7c1YAHT39TojfbMgkQ5fRtrY0qM7c6VDv3gAnxjiNwIklvtX4eyGBbdT86zf0EPM/gL3uedQxP2rv3vBw9HhczpqHm3PT6yi4Q+Xm9xfaAnL6ttfHakwTdbQWFEJNOvRagGQS+O73c9mogg22p7/aRV0Q7HAlHidCTXOLk9F32D+Xo8A7CExuqZ0+sr1pBgIIh5hjK7evkyGipTSjdVlAIMWo1s4mwCswFerPtRn8H2lq/0jTNPSzgbwyYDeVWnVz5nmUquDG7a9Sp7yXQMgbOkegaLScGtbfbCgohn6rViY4IOsyMGr3ZGk9Ai1Urcg3ZvJ1Jq/kahP4CY9IYs/UjuXI8Zx/eR7UmrXo1iGYxozJCpJGjdu06KFeedzuPKwzGys1bblAIcccRreZzIgwTRDF9jMVe5s8gW+pj0mkMALaCcTTdbE2US7Z3IYP3YdSFfcxHRJTF4G8jHUj354DNc8Qb6N6m+ZiDRogpTf0cBPqzD4UPwvDx08bv2by+qC3yjFrNKiLMBvMjerPtablEewIvwHZ7UaHau589JSWmJlKxHYQUiRRREEcKTrpGb7EXySW8KQPsR/a4rTEEjZBSrTqDiUrAqNCbraq2lE69fd6Ew99++9HEjNHHTDstsXV1Zx9nQbApHA679+5XeppLdepTAMUyHP0N5Xu+kUOIt7Facyk7R1zdo0GM3iKRooiOFuPH6E9WFn3Sq9ugQVmp7xRslqNHlZVjA0jl6/lKuxDS6Ao0VUTo7hJc/cburPi2NcXX/WZm5bqVL/bo0ysBHxRtxVMvrcK5hgYp8VTNoDWC07lYIsaUpr4eAkkrKou+3KqVYySpTdPsuBDMW+u349rBl7i69jky+MGH43v9aiLqv/sW1t/efGK0aVtvObq8TiCDVr/lUynpz4FsWLkcrl0WROXNgELf8pFD9elaPLMiH0Vvr3CL++rIN7hrwRIcr/SkzoGe8d1x9y056L35Y7i2lSJi8hRETLrh59S7/7//0FE8tzLf/fv2G6/H6NTkNvu5XnsZwx99HF0TB0qJSOy4YSIiFjwG6vLz5b1Hj53A4pdWu/H+df4fZOGTGoUs29scgTFN/VsS6C1mLjGYbeOa/7+xyEBcf2l8XOKQxP48Y9oken1tYd2eL76YUVG0bpM7ULKwTNoFS7Pl5nMnI0Y31MaQE+p0q9UuZ8SeIriWYkeLi4Y09dTYpOH/7Df3zm7frX0TdQf2w1Vba1TWO7JH7d3701PSinLPrl1OFYsc/EGLIZKysqFDY8XY6EomUnSh+t7Xfra/ygOisRpQlCpV4iRjwSW+Hd01ZkD9ufqN9uLCTzztpM2cqFSukRJ40r3pZ07WLNhulJUy/6m4rbFEVG7FYZlWM5Gjoyfz2TPHWKBZBBrI4O+IOVtvtn/WliGDvWsPKiEScJNWvQFEUyDyHXqLbZV0z5sMuWcJt+h/vbkiqpt7ljFjWUVxwby2DNNMh98+XXqoXLFd3yXCRAacxOJ8vdm+tDXdgR4NN5cbfEJ0qpmA8E8AH+vLrRP9IcNNrE5j3BEZq3+rq+d0j9eQwzWv+RlEyvjsXAC9iOgpz+xrvsyV4yqatzFp1fOZ6Gl3qoWxSVF7duaYAwdqm7cLpHiiJVxBJ8TbbZmE2GEFsQmfSYaSOzMkkNI+oTpKUSP9XnhJ4g2nSPGOO64ANhdjnscVjb9pTsXQQYn9SaDYPfsPCtU1tW3W6/pKjFGnHgVgHYF6M/gwXML1LR3CeZa/vrjJ1rAEnZBGt6WRDDh9UeyAr6oUykFSzCCHK0PuCVupLmUyQ7FJEqUvtxq8q9HdA2FsFSEezrhOO+OlJx+OlG79/qEnxW2Wigf3FBc876vh22pv0mgSoGDJDUvk1LPIcwwWW+My7sIVaAGFt6yQEFKq02RuiYorWhfdQzKez0+tSad+FSBpHblQX259QgLcPNhL98aM0ODlJY1Fk3c/8nRN2U7LnIrN64L+voq0xyrTaZYw8FCj8Xhln1N1fxpy8GCD9Fcw40hICFFl5mhA5D7NSztfO3e18RP32bbcy6TVfA9CHxJd16VbKnZ695NyVAJwHwhxl3a/JG3YkIFChDKC9x44eGjze6tS5Orwp520GnMBb0ubX2bsVvD5G8ZY9h5tiiPM1fbiwoDeBgg6IRfASW8nqQ0N1cg5W7XaYLbOlmuAbTq12gWyys0cXwjqCMXMaAlzWVpSfxdFfkiEZAmjAsiTMtCqzJwjIBoQ6H4k6IR40gkCeO9fqg8PiWaRI+odfeVsstzxR6d5DMAT7XFGL/chad7uy8GDo76P6/YSEc11b++B5+6Ju/IyJuHWQIsfgkqId1JPWnG8WnNwBkBzpNoog9km64UXo079GYF0AM/Sl9sCqhDx1+By+13ITEh7regtkbGHdkfEXtmte2xdfOXx25ZtK/HrRDKohDTtWi+U+5dpU4aKEPYx6HQMCwO0Fot7Kdva5c7AcnSl9NRFCecSvHf6co3U3u2MqanDoBA3rkpUDUlKHw3V8GFYm18g9tq/O3dp2Zb1vuIJGiFNB0KNqyq15z0Ko1azkQi/YeanDGabuxyntcukVd8HoqUMlBrKrXpfB9NR7f88auw9564duezvLz7ltuf/bynFBy8v/3z5+vwkXzEFLdsrvYR/4PBRTB43GlOz0ptw8LFjqH/icbBSiZhnngNiurWMkRn1jz8CrjyJyOl5UGb8V27S17G1W/vaEyfxyCtvYtuHjduTJUuXg7/ch0mzb20RQ8izvU1VGK3sOYw69SsEugvMG/VmW4ulnMY01QQShE/ALLpY0XusxVLZbhYNgqJ7p9xUUO5UZKsGJ4p1x0/UDzq85/JFO3ee9lV0UFxW05tJrZRqliYnx3MXxVfSuyRg5OnN1neaAzXpNGZp2wLmDXqzbZqvAwmH9vePGjfgPDD279u3+L0YCZgQ79jRVmW5Jx3CzGcATjeY7U0lnaVa1e1MwhuSURk82lBu2x4OBu4IDAET0vRVBBkvwhh16sbviwBVkbGX7BS6Ric4a+tKnGfq5lJjAnK9odyW3RGGCBedAREi96sInsG6z9116veVMd2m9cm9EX2n5+HQC8+jqtQIbmg4CW5Q6S37joWLcToCR0CEeO07ZB8I7Ro6tK+YPGxP2vvr3TmfhhPHYb1tZkNDVWV6RrmtvCOMEE46/SbEe1fu6/vbn45NP3Tl/IcG9vr1JBxa9iKO/+vD/FHFJTPCyTAdhcVvQgJ5vUs6Y1DGx+6I6pHgctScOqysqrlBa7E4OsoI4aTXb0I8dbOBFIltT0q6VG7SMZyMFkosfhEyLDP7ukgSPvVUlYcS4P+abJ8JmXzbn1aeqT+bd7b+XIxSqVhXtv7NnP81o4VyvD4RkpyZM1c/IvXFl59aEPP9Dycw6da7ff5MRSgH80uQ7VNy8Z0PN2PqpAkYrWssJl/4wisYdHlvJA0J2YcNfgk2/q8xBC25mJyVM0lzzdCCx+f9Pnr3vi/xwuv/qCzb8GbCL9JqHTQon1yWhFGVlT2tR3z8A+fOO6pO11bP2bN50/EOwv6LVOszIb9IK4TRoDoJCSMyJCidhHQSEmYWCDM4nTOkk5Aws0CYwemcIZ2EhJkFwgxO5wzpJCTMLBBmcDpnyMVAiNzPxIbZWC4aOEHL9l40I76IgXa6rDAjr5OQMCPkP2b1bLpy9/7qAAAAAElFTkSuQmCC"},{type:"echarts|line|label",name:"带标签的折线图",img:"data:image/png;base64,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"}]},{type:"area",name:"面积图",icon:"icon-fsux_tubiao_duijimianjitu",data:[{type:"echarts|area|default",name:"面积图",img:"data:image/png;base64,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"},{type:"echarts|area|stack",name:"堆叠面积图",img:"data:image/png;base64,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"}]},{type:"column",name:"柱状图",icon:"icon-chart",data:[{type:"echarts|column|default",name:"柱状图",img:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABQCAYAAADvCdDvAAADHklEQVR4Xu2cvW4TQRSF71b8NSkjpSBSqCiCIuyWBvECKA+ABAiESEUDBVRByRtQ8AhgoMcNtFmECEqLSFoo6IhA8iJhOdjLembueMae3f1cX1/PnnPPzJ2zM86ET1IIZEmNhsEIhCRWBBACIYkhkNhwUAiEJIZAYsNBIXUgJM/zIrFxLnQ4xzvbMjg6NI7h7LPnzmPsdDpThYBCHGB8393IReSyKfTK3scgWAZJ4vBMtQ6BkMTogxAISQyBiMNZv3p905S+yLIfn/u9/ngMColIyKVrm8YOshD5sP/2ZQdCIpIwnhpC5gS0689AiCtSc4qDkDkB7foztSQktnXy63VPiu/fjBieun3XFWNV3M1Hu8b48yvL8uT+jYmYxlsn77obe5nIRCdTRimUFVHOW0uFqErOIxhCpoO2EC8LQiDkBAGmrIpiQCEoBIWY1noUgkJQCArx2A+ILOYoKVOWcsqKbZ0c7z6VweFXYwlpTnFoahHrhLZXUy9MWWW0WvnGkDVEuYaoNOYRDCEQwj5k1n3I1tLaC6P4iuLLp37voVagmIueXdbW0pqFjyLf7/e6EKJFAEJUiCX7ggqFqHicLdily4KQ2TBWfRtClG1vCl6WTSGrK8vyuHRc5+fOthSWm062vL7HgDR5k7tBFUshQfJ6Hra2EVJlyVTppFGLOoSoVo5/wUGAK/7fhwTJi0KqWbVOARDiKYc5bgxRiCdHQYBDIZ7oo5C/CNBlTakf69rEos6iPo4A+5BSPfi+U/dRnvPGcGSd/H7zSga2m0637qgXF5djQLYHrLJOQuRN2jqJdWGeLktpLo7CIWSIhAsONkUH6bJcBuJzFxCFoJATBHwq2aUwffI6L+pMWfr/OoGQNlonLlJlDRnOJygEhVR3BigEhQzd0zYpZGSduPzpysG9B0br5NyZ03LxwupETAiLA+vE186OVcmx8qZsv7t0WdbuIhZwsfJCCO9DnN+HoJAamotMWYm1vRACIe3bh2jcXhSCQlCIaSuOQlBI+xQS+waV+txQw76Q3A2qhuEb9HEWcnIx6BM0LBmEJEboHyG8cJwchLkkAAAAAElFTkSuQmCC"},{type:"echarts|column|stack",name:"堆叠柱状图",img:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABQCAYAAADvCdDvAAADWUlEQVR4Xu1dPW8TQRCdCwXhHwQFxemoCIqwW9LEvyBpkSgQNPwAEB8FBIUfkAJBE4kW/4K4Ca0vikg6qhgB4h8QCZFFIpzkwxvr+Xafszm/tJ4b7b03b2Zn5+6SWY3+ltrreWZ2K+SWnHP5QbfTGvTB8utbZxay+NSuZQHH8itCgAiSQgCQUBNWJLP8SiEAs1IIABJqwopkll8pBGBWCgFAQk1YkczyCyskz3OHgpCS3Yutbet/+xG0pMX5OXv28G7JR2y/zWbzzHZDfch/9CllBcVz+WJWamH5hVNWRIwm6ooFHMtvMoTcbK+/MWfXw9hynz91Ow8mceZUe0KWVtd6WZY1QwhxZnsHOx9KPljAsfwmoxARcnYonssuS4SIEDg7TuW2VwqRQqSQUQhIIVKIFCKFlBHwbRbUhwA60S4LAMlnok69InC+y1TUxyzq7AHVy61tOwocJDXm5+w5eZBUwHa8uWEnX/pBIZktNOzK46d/fSQ3oLpoCvnYWs4t9IlIs3ylt196IlJFHYhxX1EXIQhwEzx+FyEiBEAgoolqyJi7rIjYe12JEBECx1jSRf3G6tr9zLKr8N14DJ2574fdzrvBn6SQigphDfdFiAiBRZ50ypJCTnlMpg8RISIETy3T2KlLIVKIFDIKASkkEYUUA6rYbw4V5P/c3DAXOPCZWWjY7L+BT+GXtd4YA6qZxqLNPnoSNqBiKWS3tdzLzIKefjezvdu9/Yk8/V77ba8ISaxTFyEiBN+9eb4GpJSFwacaguFUtvId1illKWXBsTSVp70shdxbab//ZZeuweh7DC/b769vd3fuDP6kGoIhOlRDWESLEBHiR+CidepSCBDJk9xliRARAiBwauJiPGytlJXI8XtBuwgRIXAK8B2/17aGFAOqGIOZwTeHCrSPX7+yk/7ROOAP2foGVDS/Ed6gijKgYjVErEhm+WXh4IvIkV8DYi2EBRzLLwsHEYIlyaEjGRFSETgpBADO1xCxgGP5lUIAoqdq21vgwYoMViSz/LJwUFGvqDwRUhE4KQQATkU94mkvS6qsSGb5ZeGgGgIo2rd7EyEVgZNCAOBUQ1RDor/moJQFKG+qOnX2J/4wvOtrldwn/uoLdfidncu/qwhfdn09iJDEuP0DnXmvq+qIgDYAAAAASUVORK5CYII="}]},{type:"bar",name:"条形图",icon:"icon-fsux_tubiao_duijizhuzhuangtu1",data:[{type:"echarts|bar|default",name:"条形图",img:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABQCAYAAADvCdDvAAADzUlEQVR4Xu2cz2sTQRTHN4darX+CIIIXFawICSIeYqgiotFLUfCkKDaI9CAIEZRUKlgoViiCRRspqEWa6sHgSUv8gYgSEJF4UvGgF715EFrFkW0FUXd3Om/ey+4s3153vjOz38+bydt5TDMe/hLlQCZRs8FkPABJWBAACIAkzIGETQcrBEAS5kDCpoMV4gKQZrOpstmsMSyqzvckDm0cY+reNdD0F5M31ezIcME0eDpPnGxQdP44cWjjGNN/12Vj442wgA8E8ji3UZnCQPvFO9B1+aoHIIv3S7wlgIhbbDYAgJj5Jd4aQMQtNhsAQMz8Em9tDOTUoZL69O7DI9OZrVi9Kk/R+ePYaEuFXP77vfpZ0/l27CpWKDp/HBvt8rHxilGWtWF7r1Npb/V8OTSNjILkzIchgOjXmhTMwA9DAAEQvQMRLbBlWdnHLwYQfk+tekw9kBtTd9TQlUnj097y0QMNis6nYaOdGCqHnp6mIsuiZhBUna5GoFs+1HGpOsn5BmZZ1IlSdZIvmIoV4lqBKqrgkwogrhWoos6GAES34Qs8BxABU226BBAb9wS0ACJgqk2XAGLjnoA29UBcK1Ct3Lkjf3fmqXGBak/PlgpF58dUlFZlvNbr+9O1sNiL+l5LxfG7wKKz61KpqVcPbu8HEDsb+dQAwuclS08AwmIjXycAwuclS08AwmIjXyfcQFwrUFGLW1SdTy5Kq1Tmc2um9oYty6LWNag61EP+oEOBinBTTDKAcIOKcFPMB7J0+GIjVygYX/vTwcQNKuJPeeeRPm9TXwlAiP6xywCE3VK7DgHEzj92NYCwW2rXIYDY+ceubisQ1wpUUbevNs99ba378e1LEBGbW1BdlcFKrlhsT5bl2v2QqPD/6Xn7wqp3STxZSH3FEEDYd2u7DgHEzj92NYCwW2rXIYDY+ceuTgWQNBWoOmYzrZdPaoFprzNZFnWiVJ3uSFq3bKjjUnWS80WByoUC1fNrVTV3abSoi8x/ny853l+n6BYKPhfquZ5t7F++Ue/gzAqJ4waV1NkQgJguq9/tAWTBiMSUcAEEQGL5X8G6DA0rxIUsCz/q+h9CqQwtcIWcPnxMfXz7/qF+Wn+3yHWv3dr9rDFgqptPeyuDAxIFn1RkWdQCVXb9Gq86cq6t3xK6PRlAAES7QbTtjiFWiJbFfAMACfFJ6odZhwVAAEQXI/8/jyNa4xiTtGVVr99SoxPTxqe9Z/oP1nv37kaWpYlH4y2LGjlUnS5qdOuNOi5VJzlfUjTrDMJzugMAQvdORAkgIrbSOwUQunciyl+nJgacKCJJ0AAAAABJRU5ErkJggg=="},{type:"echarts|bar|stack",name:"堆叠条形图",img:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABQCAYAAADvCdDvAAACiElEQVR4Xu2cP0oDURCHExBscwe9gBAP4AG8gAcQLGwEJZV/OkGwECtBO1OlECwNJNgpKVQ8QHIMJbAi24S4vsy44+wsfKln5738vpl5uzObNBt8QinQDLUbNtMASLAgAAhAgikQbDtkCECCKRBsO2RIHYCMRqOs3W6bw8JvTj+lQ6Hoz93b7OP8bMM6eJb39gd18js5OBycXnXNdehsb/3qtxDI4/paZg2jjv52Wyvu2wZIQnKAuMdjekGAAKS4l8UZkkcGGUKGkCGpGCBDyBAyhAwJlgUAAUhSAZ7UeVKvT4qEucui25sHTZhuL3OLxXOLMnmunocAJBiQCCVrp7UqDsLUwEfspMCwCr9h77KqOFDLwLO6FiBWShr5AYiRkFZuAGKlpJEfgBgJaeUGIFZKGvkBiJGQVm4AYqWkkR+AGAlp5QYgVkoa+QGIkZBWbgBipaSRH4AYCWnlJuzPEej2ziBmHhJsHgKQYEAiDKg0NbmK2bdkf0vT6fhleDeet1WPcOv29nvUYVaWNU7e+r1jgEjC18EGIA4ia5YAiEYtB1uAOIisWQIgGrUcbAHiILJmCYBo1HKwBYiDyJolAKJRy8EWIA4ia5YAiEYtB1uAOIisWcIMCN3eXPayP0cw6/YyDwk2DwFIMCBPN9fZ5+XFpqZeSmwnnaN777/Mm9/Xe783lOz126aKwHR96yTCIOn1oSf+c0+ASEO3hB1AZsQjQxafTZSsRLZRskqUIumllCxK1o9YCfMaEGcIZ8iP6KRkUbIoWakDngwhQ8gQMkT4EMBd1h/vsuj2LhZOGIOFZurnkCpaBv/1BevmV9yKLvPFuFauAEDkWrlYAsRFZvkiAJFr5WL5BeJU77Wa075tAAAAAElFTkSuQmCC"}]},{type:"pie",name:"饼 图",icon:"icon-fsux_tubiao_nandingmeiguitu",data:[{type:"echarts|pie|default",name:"饼 图",img:"data:image/png;base64,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"},{type:"echarts|pie|split",name:"分离型饼图",img:"data:image/png;base64,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"},{type:"echarts|pie|ring",name:"环形饼图",img:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABQCAYAAADvCdDvAAAK9klEQVR4Xu1ce3BU1Rn/fXfDQ3lZoSr4RnxNyD4gUVCyi5NdqNpazW5aHWbqu51a26laR/tgxNZaq21nap3aB5bWWq0uiVYtUyFACA+V3WUfMahYFK0PRoIGJATI3vN1biAYIWTvnnPu5jqz9x+Yud/vd37f98u59+45372E8uGqCpCr1JTFoGyIy/4IyoaUDXFZBVwmpzxDyoa4rAIuk1OeIWVD5CtQWVdf44FRA8KZBJ7IwCQisv6d+FDnZgOMDgDbGNxBZP2LDgPYlBeepy5Kpaxzrj9cPUOqIg0hg0WMgRlEVD1YNX/XufmIpxkwwdxKTHETRtzN5rjOkMrKhuGeiXwlgb8PooDdP+nBDDmMg7mFCQtDicw/7PKXKs41hpw2e/bIsZ4Jt5CBWwB8sdgCFGVIHzkjx+AfhpKZJcWO51S8KwyZGo5+zQA9QIRTZBOVMuSgMbwW4FuDyex62fF14YbUkKl1Ua/HwO8BulA1ISVDDgzOEA9O2J2/vbK9fZ+qHln8kBlSFY5daxD+DMAjK74/TochFh8zp4fnUT8zk9miQ1exHENiiC8c+xkIPylWrOxTVvHj8CdgujaYTDcWj1VDlNoQwxeOPQrCPDXZh6N1zZDPMDMeGt+977ZSXsJKZshJMxuOOnaUeJaIwrrNsPgcMcS6hAEvhRLpmU5oHoizZIb4IrEXAMxxKjGnDOnVy3gymExf6ZT2/rwlMcQXid0B4D4nE3LUkP2u/CiYyPzCyRwsbscN8YbrZ4CMtQQYTibjuCHMTCQuqU3k/uNkHo4aUjm34ViPyRuJcLyTSTh5D+mvm5l3eYhnzEpk253Kx1FDfOHYShBmOyW+P6/jM+TTwbLBRNrvVE6OGVIVjt5oEP3JKeGH8pbQEJAp6ms3ZJ92IjfHDPGFo1tAdKoTopnxLDOeIhavmoanoyu/7cNFnZ0jPR4xGeQ5nYlPJ9CXANQ5MT7AG4OJTKUT3I4YUhWpv8aAsUirYOZOBt1t5mlhe0t8lx3udT7fiflhNA+gb4Jwhh2M3Rhi8Y3aZPbvduPtxjliiC8S3QTQmXZFFIpj8IPcvWdB25p/f1wodqDzb0yZMuKDcaPvYoNuJ6BChuMwDON/tcn0aQQILXwHSLQbUlUXm2cYeEyTyJ0Q4vLs8qaVOvhWBwI+4eFFVMTG16DjCtwUTKUf1qGtj0O7Id5wdCMRnasqkpk3C2HOfWXFM0fem5UYZOVsVBhdgVYC1JdDmNcHk5nzJWQcEaLVEO+c6PnE9JKqQAY+7jFF4NUVTW+rcg2EX+n3H2MMw3rScFkdQd0Tzl//2nZdOrUa4gtH7wSR2vICo4chgrnmJmVjByvSumnTpuQ9nANwlEoxmXFDKJl+RIWjP1avIToWEBn3ZJsXz9eV4GA8q6b77iDDUFpjsx7BQ8n0V3Xp1WdIQ4PH2ym6CDRCQdy27bvo1HdfjHcrcNiG9t5PdgVeJcIU26BDA5n3Hc2e0dWpVI80Rz+gNkN84YZZIF6tIkowf6utubFkv+4tra01/hsBtRUFMsVXajdkn1fJXftTlq8uNh8GfiovindnlzWOgebn+kJ6sl7vqM4Rng4CRhaKHeT8XcFEWiH3T5m1zRBvJBYnIKaQ1NPZZYvrFfDS0NbqwBMgKGxA8cPBROYmaQFOXLK84WiiULvnoIKZr882N/5FR1LFcrRO998Ag6wOGKmDgaZQIh2VAh8C0jZDfOHYNhAmyIpiU0zPrWjaIItXwbXWBEIAWqQ5mNcGk5lZ0nhHZkgkZqrsCnKPODnX0vSujqSK5VgTCEwSFXivWNzBeMbmYDIt/6TmhCG+iNWkLn9kezqGoaUlL88gj1xz9tljxNijd8oyWDuJoWTGeiBRPrRdsryqM4R2js4tXdqlnJEEgeoMYea9oWRG5SntoGpthijfQ4gm55bG35KopzJk9Xm+s5iN16WJmLcFk5njpPFOXLK84eh/iUh6E4hZzHR6/epIBWs5LzDTYKyTLqgb7yHecDRFRNOkkwJ/O7us8Q/yeHnkqhr/Dwj0gCyD1aAdSmYUcv90ZH2XrEisCcAV8knhX7nmxZfL4lVwq6oDS4hwsSwHA8+FEunLZPH9cdoM8YZjdxFhgawoBrpyyxaPLfXSCQOe1mr/DiIapaD9l6FE+k5ZvCOGVNVFLzcMUmqNEYzr2poX622OKFClVTX+eQRS2nJmxtWhZPpRVxly7tz6icOF8b6SKMZ72ebFJylxFAFmwFhd7X8DRJOLgB0WauRRMyudTqpw9GG1XbIsQl8kuhagC5SECb4tu7zxN0ocNsGtNQFrI0x1lba7NpEerav7RK8h4dh3QXjQZj0GDLPuJcw0s6053qbCUwjbUuOvMRgvgUitCZzRGEymVVa5PyNVqyHeOVccB2FsJSIlXmZsJUHV2RVx+fWlQRxJTp8+rsswXyGQ8uWRBV8TSmX+VugPwO55pcINNIgvElsKIGJXwBHjmDdR3pybaXlG68uXL593zvi94qgVIHjVNWK32b3v+Iva2211UtoZT7sh3rr668kwFtoZvFCM1Q7Egi5tWx5/sVCsnfOr/P4zqYKaofA+/GfH0bcx5chN3SK1Po1RMZHfAmGSnSLZiDGZ8UfTQ/PbX4h/ZCP+sJD976mIO8cIM3jvzi01pHrfODBChcife0Gq7TUZTUfCaJ8h1kDeSPQqAj2uUyisZmvCArPHeMRus3XVrEu/YIwceQcT3UxA7w+/6n2ftFy9+0Pld1YYWBFKpLV31ztiSK8p4dg6Ig3tmgO4yuDnIOhxsNhkvY7Q3hx/p3J2wwlGhZgKomkGwwuC9brAgC/WfGfXB+3n5HervU4gzOnBVE77DqdjhlRGGvwV4LTWWaKJzGC8f/+ON8eMAEtuKvHCYCJzoyY5zj32HirQF4kuAugaJ4Srco4XPS8u2PlO0Q3XDGwfJYwzqlOpHaoaBsI7NkOswabWXXa8h4a/DsI4J8Srcl7cvX3NJXs77TcnMDOIIsFEernq2CW9qfcfzFsXqyXCchCGOZWEAm/X/B1vf3Qc50+2w8HMd4eSGekVbTtjODpD+gTs/x4W/qn6C95OQsXGDGfx2n2db04eRjR8UCxzczCZUf/BW0BgSQyxNPgisdsA/KrYgpUi/uyerlU3d221erMGPpjXjdsn5vhyOcebMEpmiJWpNxL9LYG+V4oiFzvGtbu2bpiW7zp8G5a5xezovPiiLVv2FMspE19SQ3pnSl1sPhtYoNJUJ5NoIQwxb79nx9sYC3N8X6zVIjpKGFfqetWgkAbrfMkNOXD5mgPmJ0F0jB2RpYoZI8wN9+7csn+WMN8fTGasj+aU9BgSQ6wMK8MNp1SQWAKQ2i9mzeW6sLtz5VV7tj1Wm8wOSeP3kBli1dH6NOy4ivF/BdHXNddVio7BL5vCuK59eXyjFIEG0JAa0qe/dzGSsQBEZ2nIqWgKa0MMxLfmljU+UTRYM8AVhhzIibzhaD0RWR/HdOxrO/3rx+C9AP962Ec9P0+lntutubZSdG4y5GACvrrYl5n4x0Q0QyqrQUCWCcS0TABN2NP9jOznOnTr6uNzpSF94qrC0ckGMJthfXOLQrJfvrYaJwhYwgJNpknP291Pcarog/G62pBDhfcZZO11MGgSMU4A8Ym9/wdG9d4LwB8DtB3gNgYyMIxU29J4aiiKKzPm58oQmQQ/b5iyIS5zrGxI2RCXVcBlcsozpGyIyyrgMjnlGVI2xGUVcJmc/wNHa7R+ocFFfgAAAABJRU5ErkJggg=="}]}],echartsEN:[{type:"line",name:"Line Chart",icon:"icon-tubiaozhexiantu",data:[{type:"echarts|line|default",name:"Basic Line Chart",img:"data:image/png;base64,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"},{type:"echarts|line|smooth",name:"Smoothed Line Chart",img:"data:image/png;base64,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"},{type:"echarts|line|label",name:"Line with labels",img:"data:image/png;base64,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"}]},{type:"area",name:"Area Chart",icon:"icon-fsux_tubiao_duijimianjitu",data:[{type:"echarts|area|default",name:"Basic Area Chart",img:"data:image/png;base64,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"},{type:"echarts|area|stack",name:"Stacked Area Chart",img:"data:image/png;base64,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"}]},{type:"column",name:"Column Chart",icon:"icon-chart",data:[{type:"echarts|column|default",name:"Basic Column Chart",img:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABQCAYAAADvCdDvAAADHklEQVR4Xu2cvW4TQRSF71b8NSkjpSBSqCiCIuyWBvECKA+ABAiESEUDBVRByRtQ8AhgoMcNtFmECEqLSFoo6IhA8iJhOdjLembueMae3f1cX1/PnnPPzJ2zM86ET1IIZEmNhsEIhCRWBBACIYkhkNhwUAiEJIZAYsNBIXUgJM/zIrFxLnQ4xzvbMjg6NI7h7LPnzmPsdDpThYBCHGB8393IReSyKfTK3scgWAZJ4vBMtQ6BkMTogxAISQyBiMNZv3p905S+yLIfn/u9/ngMColIyKVrm8YOshD5sP/2ZQdCIpIwnhpC5gS0689AiCtSc4qDkDkB7foztSQktnXy63VPiu/fjBieun3XFWNV3M1Hu8b48yvL8uT+jYmYxlsn77obe5nIRCdTRimUFVHOW0uFqErOIxhCpoO2EC8LQiDkBAGmrIpiQCEoBIWY1noUgkJQCArx2A+ILOYoKVOWcsqKbZ0c7z6VweFXYwlpTnFoahHrhLZXUy9MWWW0WvnGkDVEuYaoNOYRDCEQwj5k1n3I1tLaC6P4iuLLp37voVagmIueXdbW0pqFjyLf7/e6EKJFAEJUiCX7ggqFqHicLdily4KQ2TBWfRtClG1vCl6WTSGrK8vyuHRc5+fOthSWm062vL7HgDR5k7tBFUshQfJ6Hra2EVJlyVTppFGLOoSoVo5/wUGAK/7fhwTJi0KqWbVOARDiKYc5bgxRiCdHQYBDIZ7oo5C/CNBlTakf69rEos6iPo4A+5BSPfi+U/dRnvPGcGSd/H7zSga2m0637qgXF5djQLYHrLJOQuRN2jqJdWGeLktpLo7CIWSIhAsONkUH6bJcBuJzFxCFoJATBHwq2aUwffI6L+pMWfr/OoGQNlonLlJlDRnOJygEhVR3BigEhQzd0zYpZGSduPzpysG9B0br5NyZ03LxwupETAiLA+vE186OVcmx8qZsv7t0WdbuIhZwsfJCCO9DnN+HoJAamotMWYm1vRACIe3bh2jcXhSCQlCIaSuOQlBI+xQS+waV+txQw76Q3A2qhuEb9HEWcnIx6BM0LBmEJEboHyG8cJwchLkkAAAAAElFTkSuQmCC"},{type:"echarts|column|stack",name:"Stacked Column Chart",img:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABQCAYAAADvCdDvAAADWUlEQVR4Xu1dPW8TQRCdCwXhHwQFxemoCIqwW9LEvyBpkSgQNPwAEB8FBIUfkAJBE4kW/4K4Ca0vikg6qhgB4h8QCZFFIpzkwxvr+Xafszm/tJ4b7b03b2Zn5+6SWY3+ltrreWZ2K+SWnHP5QbfTGvTB8utbZxay+NSuZQHH8itCgAiSQgCQUBNWJLP8SiEAs1IIABJqwopkll8pBGBWCgFAQk1YkczyCyskz3OHgpCS3Yutbet/+xG0pMX5OXv28G7JR2y/zWbzzHZDfch/9CllBcVz+WJWamH5hVNWRIwm6ooFHMtvMoTcbK+/MWfXw9hynz91Ow8mceZUe0KWVtd6WZY1QwhxZnsHOx9KPljAsfwmoxARcnYonssuS4SIEDg7TuW2VwqRQqSQUQhIIVKIFCKFlBHwbRbUhwA60S4LAMlnok69InC+y1TUxyzq7AHVy61tOwocJDXm5+w5eZBUwHa8uWEnX/pBIZktNOzK46d/fSQ3oLpoCvnYWs4t9IlIs3ylt196IlJFHYhxX1EXIQhwEzx+FyEiBEAgoolqyJi7rIjYe12JEBECx1jSRf3G6tr9zLKr8N14DJ2574fdzrvBn6SQigphDfdFiAiBRZ50ypJCTnlMpg8RISIETy3T2KlLIVKIFDIKASkkEYUUA6rYbw4V5P/c3DAXOPCZWWjY7L+BT+GXtd4YA6qZxqLNPnoSNqBiKWS3tdzLzIKefjezvdu9/Yk8/V77ba8ISaxTFyEiBN+9eb4GpJSFwacaguFUtvId1illKWXBsTSVp70shdxbab//ZZeuweh7DC/b769vd3fuDP6kGoIhOlRDWESLEBHiR+CidepSCBDJk9xliRARAiBwauJiPGytlJXI8XtBuwgRIXAK8B2/17aGFAOqGIOZwTeHCrSPX7+yk/7ROOAP2foGVDS/Ed6gijKgYjVErEhm+WXh4IvIkV8DYi2EBRzLLwsHEYIlyaEjGRFSETgpBADO1xCxgGP5lUIAoqdq21vgwYoMViSz/LJwUFGvqDwRUhE4KQQATkU94mkvS6qsSGb5ZeGgGgIo2rd7EyEVgZNCAOBUQ1RDor/moJQFKG+qOnX2J/4wvOtrldwn/uoLdfidncu/qwhfdn09iJDEuP0DnXmvq+qIgDYAAAAASUVORK5CYII="}]},{type:"bar",name:"Bar Chart",icon:"icon-fsux_tubiao_duijizhuzhuangtu1",data:[{type:"echarts|bar|default",name:"Basic Bar Chart",img:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABQCAYAAADvCdDvAAADzUlEQVR4Xu2cz2sTQRTHN4darX+CIIIXFawICSIeYqgiotFLUfCkKDaI9CAIEZRUKlgoViiCRRspqEWa6sHgSUv8gYgSEJF4UvGgF715EFrFkW0FUXd3Om/ey+4s3153vjOz38+bydt5TDMe/hLlQCZRs8FkPABJWBAACIAkzIGETQcrBEAS5kDCpoMV4gKQZrOpstmsMSyqzvckDm0cY+reNdD0F5M31ezIcME0eDpPnGxQdP44cWjjGNN/12Vj442wgA8E8ji3UZnCQPvFO9B1+aoHIIv3S7wlgIhbbDYAgJj5Jd4aQMQtNhsAQMz8Em9tDOTUoZL69O7DI9OZrVi9Kk/R+ePYaEuFXP77vfpZ0/l27CpWKDp/HBvt8rHxilGWtWF7r1Npb/V8OTSNjILkzIchgOjXmhTMwA9DAAEQvQMRLbBlWdnHLwYQfk+tekw9kBtTd9TQlUnj097y0QMNis6nYaOdGCqHnp6mIsuiZhBUna5GoFs+1HGpOsn5BmZZ1IlSdZIvmIoV4lqBKqrgkwogrhWoos6GAES34Qs8BxABU226BBAb9wS0ACJgqk2XAGLjnoA29UBcK1Ct3Lkjf3fmqXGBak/PlgpF58dUlFZlvNbr+9O1sNiL+l5LxfG7wKKz61KpqVcPbu8HEDsb+dQAwuclS08AwmIjXycAwuclS08AwmIjXyfcQFwrUFGLW1SdTy5Kq1Tmc2um9oYty6LWNag61EP+oEOBinBTTDKAcIOKcFPMB7J0+GIjVygYX/vTwcQNKuJPeeeRPm9TXwlAiP6xywCE3VK7DgHEzj92NYCwW2rXIYDY+ceubisQ1wpUUbevNs99ba378e1LEBGbW1BdlcFKrlhsT5bl2v2QqPD/6Xn7wqp3STxZSH3FEEDYd2u7DgHEzj92NYCwW2rXIYDY+ceuTgWQNBWoOmYzrZdPaoFprzNZFnWiVJ3uSFq3bKjjUnWS80WByoUC1fNrVTV3abSoi8x/ny853l+n6BYKPhfquZ5t7F++Ue/gzAqJ4waV1NkQgJguq9/tAWTBiMSUcAEEQGL5X8G6DA0rxIUsCz/q+h9CqQwtcIWcPnxMfXz7/qF+Wn+3yHWv3dr9rDFgqptPeyuDAxIFn1RkWdQCVXb9Gq86cq6t3xK6PRlAAES7QbTtjiFWiJbFfAMACfFJ6odZhwVAAEQXI/8/jyNa4xiTtGVVr99SoxPTxqe9Z/oP1nv37kaWpYlH4y2LGjlUnS5qdOuNOi5VJzlfUjTrDMJzugMAQvdORAkgIrbSOwUQunciyl+nJgacKCJJ0AAAAABJRU5ErkJggg=="},{type:"echarts|bar|stack",name:"Stacked Bar Chart",img:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABQCAYAAADvCdDvAAACiElEQVR4Xu2cP0oDURCHExBscwe9gBAP4AG8gAcQLGwEJZV/OkGwECtBO1OlECwNJNgpKVQ8QHIMJbAi24S4vsy44+wsfKln5738vpl5uzObNBt8QinQDLUbNtMASLAgAAhAgikQbDtkCECCKRBsO2RIHYCMRqOs3W6bw8JvTj+lQ6Hoz93b7OP8bMM6eJb39gd18js5OBycXnXNdehsb/3qtxDI4/paZg2jjv52Wyvu2wZIQnKAuMdjekGAAKS4l8UZkkcGGUKGkCGpGCBDyBAyhAwJlgUAAUhSAZ7UeVKvT4qEucui25sHTZhuL3OLxXOLMnmunocAJBiQCCVrp7UqDsLUwEfspMCwCr9h77KqOFDLwLO6FiBWShr5AYiRkFZuAGKlpJEfgBgJaeUGIFZKGvkBiJGQVm4AYqWkkR+AGAlp5QYgVkoa+QGIkZBWbgBipaSRH4AYCWnlJuzPEej2ziBmHhJsHgKQYEAiDKg0NbmK2bdkf0vT6fhleDeet1WPcOv29nvUYVaWNU7e+r1jgEjC18EGIA4ia5YAiEYtB1uAOIisWQIgGrUcbAHiILJmCYBo1HKwBYiDyJolAKJRy8EWIA4ia5YAiEYtB1uAOIisWcIMCN3eXPayP0cw6/YyDwk2DwFIMCBPN9fZ5+XFpqZeSmwnnaN777/Mm9/Xe783lOz126aKwHR96yTCIOn1oSf+c0+ASEO3hB1AZsQjQxafTZSsRLZRskqUIumllCxK1o9YCfMaEGcIZ8iP6KRkUbIoWakDngwhQ8gQMkT4EMBd1h/vsuj2LhZOGIOFZurnkCpaBv/1BevmV9yKLvPFuFauAEDkWrlYAsRFZvkiAJFr5WL5BeJU77Wa075tAAAAAElFTkSuQmCC"}]},{type:"pie",name:"Pie Chart",icon:"icon-fsux_tubiao_nandingmeiguitu",data:[{type:"echarts|pie|default",name:"Basic Pie Chart",img:"data:image/png;base64,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"},{type:"echarts|pie|split",name:"Split Pie Chart",img:"data:image/png;base64,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"},{type:"echarts|pie|ring",name:"Doughnut Chart",img:"data:image/png;base64,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"}]}]}},mounted(){if("ch"==this.lang)return this.config[0].data=this.echartsCN,this.close="关闭图表选择菜单",void this.chooseConfig();this.config[0].data=this.echartsEN,this.close="close chart-select menu",this.chooseConfig()},computed:{...Object(f["mapState"])("chartSetting",["chartLists","currentChartIndex"]),chartPro(){return this.chartAllType.split("|")[0]},chartType(){return this.chartAllType.split("|")[1]},chartStyle(){return this.chartAllType.split("|")[2]}},watch:{currentChartType(t){this.chartAllType=t},lang(t){if("ch"==this.lang)return this.config[0].data=this.echartsCN,this.close="关闭图表选择菜单",void this.chooseConfig();this.config[0].data=this.echartsEN,this.close="close chart-select menu",this.chooseConfig()}},methods:{chooseConfig(){for(var t=0;t<this.config.length;t++)if(this.config[t].type==this.chartPro)return void(this.currentConfig=this.config[t]);this.currentConfig=this.config[0]},changeChartType:function(t){return null==this.currentChartIndex?null:(this.chartLists[this.currentChartIndex].chartOptions,Se["chartreuse"]==t?null:(ye(L(this.chartLists[this.currentChartIndex].chartOptions),t),this.$emit("closeChartShowList"),void(this.currentChartType=t)))},quickListScroll:function(t){var e=h()(t.currentTarget).scrollTop(),n=this;h()(t.currentTarget).find("div.luckysheet-datavisual-quick-list-title").each((function(){var t=h()(this),r=t.position();if(e>=n.list_scroll_direction){if(r.top+55>0)return h()("#luckysheet-datavisual-quick-menu div").removeClass("luckysheet-datavisual-quick-menu-active"),h()("#luckysheet-datavisual-chart-menu-"+t.find("a").data("type")).addClass("luckysheet-datavisual-quick-menu-active"),!1}else if(r.top-55>=0){h()("#luckysheet-datavisual-quick-menu div").removeClass("luckysheet-datavisual-quick-menu-active");var i=t.prev().prev();return 0==i.length&&(i=t),h()("#luckysheet-datavisual-chart-menu-"+i.find("a").data("type")).addClass("luckysheet-datavisual-quick-menu-active"),!1}setTimeout((function(){n.list_scroll_direction=e}),0)}))},quickMenu:function(t){var e=h()(t.currentTarget);h()("#luckysheet-datavisual-quick-menu div").removeClass("luckysheet-datavisual-quick-menu-active"),e.addClass("luckysheet-datavisual-quick-menu-active");var n=h()("#luckysheet-datavisual-chart-listtitle-"+e.data("type")).parent(),r=n.position().top;h()("#luckysheet-datavisual-quick-list").scrollTop(r+5+h()("#luckysheet-datavisual-quick-list").scrollTop())}}},De=Ee;n("d81a");function Le(t,e,n,r,i,a,o,s){var l,c="function"===typeof t?t.options:t;if(e&&(c.render=e,c.staticRenderFns=n,c._compiled=!0),r&&(c.functional=!0),a&&(c._scopeId="data-v-"+a),o?(l=function(t){t=t||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext,t||"undefined"===typeof __VUE_SSR_CONTEXT__||(t=__VUE_SSR_CONTEXT__),i&&i.call(this,t),t&&t._registeredComponents&&t._registeredComponents.add(o)},c._ssrRegister=l):i&&(l=s?function(){i.call(this,(c.functional?this.parent:this).$root.$options.shadowRoot)}:i),l)if(c.functional){c._injectStyles=l;var u=c.render;c.render=function(t,e){return l.call(e),u(t,e)}}else{var h=c.beforeCreate;c.beforeCreate=h?[].concat(h,l):[l]}return{exports:t,options:c}}var Ge=Le(De,l,c,!1,null,null,null),je=Ge.exports,Ne=n("63ea"),Me=n.n(Ne),Re=function(){var t=this,e=t._self._c;return e("el-collapse-item",{attrs:{name:"1"}},[e("template",{slot:"title"},[t._v(" "+t._s(t.setItem.modalName)+" "),e("i",{staticClass:"iconfont icon-biaoti"})]),e("chart-base-switch",{attrs:{switchValue:t.title.show},on:{"update:switchValue":function(e){return t.$set(t.title,"show",e)},"update:switch-value":function(e){return t.$set(t.title,"show",e)}}},[e("div",{attrs:{slot:"title"},slot:"title"},[t._v(t._s(t.setItem.show))])]),e("chart-base-input",{attrs:{inputValue:t.title.text,placeholder:t.setItem.placeholder},on:{"update:inputValue":function(e){return t.$set(t.title,"text",e)},"update:input-value":function(e){return t.$set(t.title,"text",e)}}},[e("div",{attrs:{slot:"input"},slot:"input"},[t._v(t._s(t.setItem.text))])]),e("chart-base-label",{attrs:{router:t.router+"/label",baseLabelOption:t.title.label},on:{"update:baseLabelOption":function(e){return t.$set(t.title,"label",e)},"update:base-label-option":function(e){return t.$set(t.title,"label",e)}}},[e("div",{attrs:{slot:"title"},slot:"title"},[t._v(t._s(t.setItem.label))])]),e("chart-base-select",{attrs:{selectOption:t.positionData,selectValue:t.title.position.value},on:{"update:selectValue":function(e){return t.$set(t.title.position,"value",e)},"update:select-value":function(e){return t.$set(t.title.position,"value",e)}}},[e("div",{attrs:{slot:"select"},slot:"select"},[t._v(t._s(t.setItem.position))])]),"custom"===t.title.position.value?e("el-row",[e("chart-base-slider",{attrs:{baseSliderOption:t.title.position.offsetX,unit:"%",content:t.setItem.offsetL},on:{"update:baseSliderOption":function(e){return t.$set(t.title.position,"offsetX",e)},"update:base-slider-option":function(e){return t.$set(t.title.position,"offsetX",e)}}}),e("chart-base-slider",{attrs:{baseSliderOption:t.title.position.offsetY,unit:"%",content:t.setItem.offsetT},on:{"update:baseSliderOption":function(e){return t.$set(t.title.position,"offsetY",e)},"update:base-slider-option":function(e){return t.$set(t.title.position,"offsetY",e)}}})],1):t._e()],2)},Be=[],Ve=function(){var t=this,e=t._self._c;return e("div",[e("el-row",{staticStyle:{"margin-top":"15px"}},[e("el-col",{staticClass:"title",attrs:{span:8}},[t._t("title")],2),e("el-col",{attrs:{span:16}},[e("chart-base-box",{attrs:{boxData:t.baseLabelOptionData.fontGroup,checkboxOption:t.fontStyleObj},on:{"update:boxData":function(e){return t.$set(t.baseLabelOptionData,"fontGroup",e)},"update:box-data":function(e){return t.$set(t.baseLabelOptionData,"fontGroup",e)}}}),e("el-row",{staticStyle:{"margin-top":"5px"}},[e("el-col",{attrs:{span:12}},[e("chart-base-select",{attrs:{hideCol:!0,tooltip:"选择字体大小",selectOption:t.fontSizeList,selectValue:t.baseLabelOptionData.fontSize},on:{"update:selectValue":function(e){return t.$set(t.baseLabelOptionData,"fontSize",e)},"update:select-value":function(e){return t.$set(t.baseLabelOptionData,"fontSize",e)}}})],1),e("el-col",{attrs:{span:8,offset:2}},[e("el-color-picker",{attrs:{size:"mini"},on:{change:t.changeStyle},model:{value:t.baseLabelOptionData.color,callback:function(e){t.$set(t.baseLabelOptionData,"color",e)},expression:"baseLabelOptionData.color"}})],1)],1)],1)],1),"custom"===t.baseLabelOptionData.fontSize?e("chart-base-slider",{attrs:{baseSliderOption:t.baseLabelOptionData.cusFontSize,unit:"px",content:"滑动修改字体大小"},on:{"update:baseSliderOption":function(e){return t.$set(t.baseLabelOptionData,"cusFontSize",e)},"update:base-slider-option":function(e){return t.$set(t.baseLabelOptionData,"cusFontSize",e)}}}):t._e()],1)},Pe=[],ze={name:"chart-base-label",props:{router:String,baseLabelOption:Object},components:{"chart-base-slider":dn,"chart-base-select":ln,"chart-base-box":xn},data:function(){return{baseLabelOptionData:{},fontSizeList:L(O),fontStyleObj:{}}},watch:{baseLabelOption:{handler:function(t){Me.a(this.baseLabelOptionData,t)||(this.baseLabelOptionData=L(t),this.router.includes("title")?this.fontStyleObj=L(T):this.fontStyleObj=L(I))},immediate:!0,deep:!0},baseLabelOptionData:{handler:function(t,e){e&&this.changeStyle()},immediate:!0,deep:!0}},methods:{...f["mapActions"]("chartSetting",["updateChartItem"]),changeStyle(){const t={updateObj:L(this.baseLabelOptionData),router:this.router};this.updateChartItem(t)}}},Qe=ze,We=Le(Qe,Ve,Pe,!1,null,null,null),Fe=We.exports,qe=function(){var t=this,e=t._self._c;return e("el-row",{staticStyle:{"margin-top":"15px"}},[e("el-col",{staticClass:"title",attrs:{span:8}},[t._t("title")],2),e("el-col",{attrs:{span:16}},[e("el-switch",{attrs:{"active-color":"#13ce66","inactive-color":"#d8d8d8"},on:{change:t.changeSwitch},model:{value:t.switchData,callback:function(e){t.switchData=e},expression:"switchData"}})],1)],1)},Xe=[],He={name:"chart-base-switch",props:{switchValue:{type:Boolean,default:!1}},data(){return{switchData:!1}},watch:{switchValue(t){this.switchData=t}},mounted(){this.switchData=!!this.switchValue&&this.switchValue},methods:{changeSwitch(t){this.$emit("update:switchValue",t)}}},Ze=He,Je=Le(Ze,qe,Xe,!1,null,null,null),Ue=Je.exports,Ye=function(){var t=this,e=t._self._c;return e("div",[t.hideCol?e("el-input",{attrs:{type:t.type?t.type:"text",placeholder:t.placeholder,size:"mini"},on:{change:t.changeInput},model:{value:t.input,callback:function(e){t.input=e},expression:"input"}}):e("el-row",{staticStyle:{"margin-top":"15px"}},[e("el-col",{staticClass:"title",attrs:{span:8}},[t._t("input")],2),e("el-col",{attrs:{span:16}},[e("el-input",{attrs:{placeholder:t.placeholder,size:"mini","suffix-icon":"el-icon-edit",type:t.type?t.type:"text"},on:{change:t.changeInput},model:{value:t.input,callback:function(e){t.input=e},expression:"input"}})],1)],1)],1)},Ke=[],_e={name:"chart-base-input",props:{placeholder:{type:String,default:""},inputValue:"",hideCol:Boolean,type:String},data(){return{input:""}},watch:{inputValue(t){this.input=t}},mounted(){this.input=this.inputValue?this.inputValue:""},methods:{changeInput(t){this.$emit("update:inputValue",t)}}},$e=_e,tn=Le($e,Ye,Ke,!1,null,null,null),en=tn.exports,nn=function(){var t=this,e=t._self._c;return t.hideCol?e("el-tooltip",{attrs:{disabled:!t.tooltip,"open-delay":500,content:t.tooltip,effect:"dark",placement:"bottom"}},[e("el-select",{attrs:{size:"mini"},on:{change:t.changeSelect},model:{value:t.select,callback:function(e){t.select=e},expression:"select"}},t._l(t.selectOption,(function(t,n){return e("el-option",{key:n,attrs:{label:t.label,value:t.value}})})),1)],1):e("el-row",{staticStyle:{"margin-top":"15px"}},[e("el-col",{staticClass:"title",attrs:{span:8}},[t._t("select")],2),e("el-col",{attrs:{span:16}},[e("el-tooltip",{attrs:{disabled:!t.tooltip,"open-delay":500,content:t.tooltip,effect:"dark",placement:"bottom"}},[e("el-select",{attrs:{size:"mini"},on:{change:t.changeSelect},model:{value:t.select,callback:function(e){t.select=e},expression:"select"}},t._l(t.selectOption,(function(t,n){return e("el-option",{key:n,attrs:{label:t.label,value:t.value}})})),1)],1)],1)],1)},rn=[],an={props:{selectOption:Array,tooltip:String,selectValue:[String,Number,Array],hideCol:Boolean},data(){return{select:""}},watch:{selectValue(t){this.select=t}},mounted(){this.select=this.selectValue},methods:{changeSelect(t){this.$emit("update:selectValue",t)}}},on=an,sn=Le(on,nn,rn,!1,null,null,null),ln=sn.exports,cn=function(){var t=this,e=t._self._c;return e("div",[t.hideCol?e("el-row",{staticClass:"chart-base-slider",staticStyle:{"margin-top":"15px"}},[e("el-col",{attrs:{span:6}},[t._t("title")],2),e("el-col",{attrs:{span:17}},[e("el-tooltip",{attrs:{"open-delay":500,content:t.content,placement:"top"}},[e("el-slider",{staticStyle:{"padding-left":"12px"},attrs:{"show-input-controls":!1,min:t.min,max:t.max,"input-size":"mini","show-input":"","format-tooltip":t.format?t.formatter:null},on:{change:t.handlerChange},model:{value:t.baseSliderData,callback:function(e){t.baseSliderData=e},expression:"baseSliderData"}})],1)],1),e("el-col",{staticClass:"input_content",attrs:{span:1}},[t._v(t._s(t.unit))])],1):e("el-row",{staticClass:"chart-base-slider",staticStyle:{"margin-top":"15px"}},[e("el-col",{attrs:{span:22}},[e("el-tooltip",{attrs:{"open-delay":500,content:t.content,placement:"top"}},[e("el-slider",{staticStyle:{"padding-left":"12px"},attrs:{"show-input-controls":!1,min:t.min,max:t.max,"input-size":"mini","show-input":"","format-tooltip":t.format?t.formatter:null},on:{change:t.handlerChange},model:{value:t.baseSliderData,callback:function(e){t.baseSliderData=e},expression:"baseSliderData"}})],1)],1),e("el-col",{staticClass:"input_content",attrs:{span:1}},[t._v(t._s(t.unit))])],1)],1)},un=[],hn={name:"chart-base-slider",props:{baseSliderOption:Number,unit:String,min:{type:Number,default:0},max:{type:Number,default:100},content:{type:String,default:"滑动修改值大小"},hideCol:!1,format:[Function,String]},data(){return{baseSliderData:12}},watch:{baseSliderOption(t){this.baseSliderData=t}},mounted(){this.baseSliderData=this.baseSliderOption},methods:{handlerChange(t){this.$emit("update:baseSliderOption",t)},formatter(t){return null}}},fn=hn,pn=(n("8ce1"),Le(fn,cn,un,!1,null,null,null)),dn=pn.exports,gn=function(){var t=this,e=t._self._c;return e("el-row",[e("el-checkbox-group",{attrs:{size:"mini"},on:{change:t.changeStyle},model:{value:t.boxValue,callback:function(e){t.boxValue=e},expression:"boxValue"}},t._l(t.checkboxOption,(function(n,r){return e("el-tooltip",{key:r,attrs:{"open-delay":500,content:n.des,effect:"dark",placement:"bottom"}},[e("el-checkbox-button",{attrs:{label:r}},[t._v(" "+t._s(n.text)+" ")])],1)})),1)],1)},mn=[],bn={props:{checkboxOption:[Object,Array],boxData:{type:Array,default:[]}},data(){return{boxValue:""}},watch:{boxData(t){this.boxValue=t}},mounted(){this.boxValue=this.boxData?L(this.boxData):[]},methods:{changeStyle(t){this.$emit("update:boxData",t)}}},vn=bn,yn=Le(vn,gn,mn,!1,null,null,null),xn=yn.exports;const An=function(t){return{"chart-base-label":t.ChartBaseLabel,"chart-base-input":t.ChartBaseInput,"chart-base-switch":t.ChartBaseSwitch,"chart-base-slider":t.ChartBaseSlider,"chart-base-select":t.ChartBaseSelect}};var wn={name:"ChartTitle",props:{router:String,chartAllType:String,titleOption:Object,lang:{type:String,default:"cn"}},components:{...An(r)},mounted(){"ch"!=this.lang?this.setItem=Ie["chartTitle"]:this.setItem=Oe["chartTitle"]},data:function(){return{title:"",positionData:m,isChange:!1,setItem:{}}},watch:{titleOption:{handler:function(t,e){Me.a(this.title,t)||(e&&(this.isChange=!0),this.title=L(t))},deep:!0,immediate:!0},title:{handler:function(t,e){this.isChange?this.isChange=!this.isChange:e&&this.changeTitle()},deep:!0,immediate:!0},lang(t){this.setItem="ch"!=t?Ie["chartTitle"]:Oe["chartTitle"]}},methods:{...f["mapActions"]("chartSetting",["updateChartItem"]),changeTitle(){const t={updateObj:L(this.title),router:this.router};this.updateChartItem(t)}}},Cn=wn,Sn=Le(Cn,Re,Be,!1,null,null,null),kn=Sn.exports,On=function(){var t=this,e=t._self._c;return e("el-collapse-item",{attrs:{name:"2"}},[e("template",{slot:"title"},[t._v(" "+t._s(t.setItem.modalName)+"     "),e("i",{staticClass:"iconfont icon-biaoti"})]),e("chart-base-input",{attrs:{inputValue:t.subTitle.text,placeholder:t.setItem.placeholder},on:{"update:inputValue":function(e){return t.$set(t.subTitle,"text",e)},"update:input-value":function(e){return t.$set(t.subTitle,"text",e)}}},[e("div",{attrs:{slot:"input"},slot:"input"},[t._v(t._s(t.setItem.text))])]),e("chart-base-label",{attrs:{router:t.router+"/label",baseLabelOption:t.subTitle.label}},[e("div",{attrs:{slot:"title"},slot:"title"},[t._v(t._s(t.setItem.label))])]),e("chart-base-select",{attrs:{selectOption:t.distanceOption,selectValue:t.subTitle.distance.value},on:{"update:selectValue":function(e){return t.$set(t.subTitle.distance,"value",e)},"update:select-value":function(e){return t.$set(t.subTitle.distance,"value",e)}}},[e("div",{attrs:{slot:"select"},slot:"select"},[t._v(t._s(t.setItem.gap))])]),"custom"===t.subTitle.distance.value?e("chart-base-slider",{attrs:{baseSliderOption:t.subTitle.distance.cusGap,unit:"px",content:t.setItem.content},on:{"update:baseSliderOption":function(e){return t.$set(t.subTitle.distance,"cusGap",e)},"update:base-slider-option":function(e){return t.$set(t.subTitle.distance,"cusGap",e)}}}):t._e()],2)},Tn=[],In={name:"ChartSubTitle",props:{router:String,chartAllType:String,subTitleOption:Object,lang:{type:String,default:"cn"}},components:{...An(r)},data:function(){return{subTitle:{},distanceOption:L(b),setItem:{}}},mounted(){"ch"!=this.lang?this.setItem=Ie["chartSubTitle"]:this.setItem=Oe["chartSubTitle"]},watch:{subTitleOption:{handler:function(t){Me.a(this.subTitle,t)||(this.subTitle=L(t))},immediate:!0,deep:!0},subTitle:{handler:function(t,e){e&&this.changeTitle()},deep:!0,immediate:!0},lang(t){this.setItem="ch"!=t?Ie["chartSubTitle"]:Oe["chartSubTitle"]}},methods:{...f["mapActions"]("chartSetting",["updateChartItem"]),changeTitle(){const t={updateObj:L(this.subTitle),router:this.router};this.updateChartItem(t)}}},En=In,Dn=Le(En,On,Tn,!1,null,null,null),Ln=Dn.exports,Gn=function(){var t=this,e=t._self._c;return e("el-collapse-item",{attrs:{name:"4"}},[e("template",{slot:"title"},[t._v(" "+t._s(t.setItem.modalName)+" "),e("i",{staticClass:"iconfont icon-biaoti"})]),e("chart-base-switch",{attrs:{switchValue:t.cursor.show},on:{"update:switchValue":function(e){return t.$set(t.cursor,"show",e)},"update:switch-value":function(e){return t.$set(t.cursor,"show",e)}}},[e("div",{attrs:{slot:"title"},slot:"title"},[t._v(t._s(t.setItem.show))])]),e("chart-base-label",{attrs:{router:t.router+"/label",baseLabelOption:t.cursor.label}},[e("div",{attrs:{slot:"title"},slot:"title"},[t._v(t._s(t.setItem.label))])]),e("el-row",{staticStyle:{"margin-top":"10px"}},[e("el-col",{attrs:{span:6}},[t._v(t._s(t.setItem.background))]),e("el-col",{attrs:{span:3}},[e("el-color-picker",{attrs:{size:"mini"},model:{value:t.cursor.backgroundColor,callback:function(e){t.$set(t.cursor,"backgroundColor",e)},expression:"cursor.backgroundColor"}})],1)],1),e("chart-base-select",{attrs:{selectOption:t.triggerMethodArr,selectValue:t.cursor.triggerOn},on:{"update:selectValue":function(e){return t.$set(t.cursor,"triggerOn",e)},"update:select-value":function(e){return t.$set(t.cursor,"triggerOn",e)}}},[e("div",{attrs:{slot:"select"},slot:"select"},[t._v(t._s(t.setItem.trigger))])]),e("chart-base-select",{attrs:{selectOption:t.triggerTypeArr,selectValue:t.cursor.triggerType},on:{"update:selectValue":function(e){return t.$set(t.cursor,"triggerType",e)},"update:select-value":function(e){return t.$set(t.cursor,"triggerType",e)}}},[e("div",{attrs:{slot:"select"},slot:"select"},[t._v(t._s(t.setItem.type))])]),"item"!=t.cursor.triggerType?e("div",[e("chart-base-select",{attrs:{selectOption:t.lineStyleOption,selectValue:t.cursor.axisPointer.style.type},on:{"update:selectValue":function(e){return t.$set(t.cursor.axisPointer.style,"type",e)},"update:select-value":function(e){return t.$set(t.cursor.axisPointer.style,"type",e)}}},[e("div",{attrs:{slot:"select"},slot:"select"},[t._v(t._s(t.setItem.lineType))])]),e("chart-base-select",{attrs:{selectOption:t.lineWeightOption,selectValue:t.cursor.axisPointer.style.width},on:{"update:selectValue":function(e){return t.$set(t.cursor.axisPointer.style,"width",e)},"update:select-value":function(e){return t.$set(t.cursor.axisPointer.style,"width",e)}}},[e("div",{attrs:{slot:"select"},slot:"select"},[t._v(t._s(t.setItem.lineWidth))])]),e("el-row",{staticStyle:{"margin-top":"15px"}},[e("el-col",{attrs:{span:6}},[t._v(t._s(t.setItem.color))]),e("el-col",{attrs:{span:3}},[e("el-color-picker",{attrs:{size:"mini"},model:{value:t.cursor.axisPointer.style.color,callback:function(e){t.$set(t.cursor.axisPointer.style,"color",e)},expression:"cursor.axisPointer.style.color"}})],1)],1),e("chart-base-select",{attrs:{selectOption:t.axisPointerArr,selectValue:t.cursor.axisPointer.type},on:{"update:selectValue":function(e){return t.$set(t.cursor.axisPointer,"type",e)},"update:select-value":function(e){return t.$set(t.cursor.axisPointer,"type",e)}}},[e("div",{attrs:{slot:"select"},slot:"select"},[t._v(t._s(t.setItem.axisType))])])],1):t._e(),"item"==t.cursor.triggerType?e("chart-base-select",{attrs:{selectOption:t.posOption,selectValue:t.cursor.position},on:{"update:selectValue":function(e){return t.$set(t.cursor,"position",e)},"update:select-value":function(e){return t.$set(t.cursor,"position",e)}}},[e("div",{attrs:{slot:"select"},slot:"select"},[t._v(t._s(t.setItem.position))])]):t._e(),e("el-row",{staticStyle:{"margin-top":"15px"}},[e("el-col",{attrs:{span:2}},[e("i",{staticClass:"el-icon-menu"})]),e("el-col",{attrs:{span:8}},[t._v(t._s(t.setItem.suffix))])],1),t._l(t.seriesOption,(function(n,r){return e("el-row",{key:r,staticStyle:{"margin-top":"15px"}},[e("el-col",{attrs:{span:6}},[t._v(t._s(n))]),e("el-col",{attrs:{span:4}},[e("chart-base-input",{attrs:{hideCol:!0,placeholder:t.setItem.placeholder}})],1),e("el-col",{attrs:{span:6}},[e("chart-base-select",{attrs:{tooltip:t.setItem.ratio,selectOption:t.ratioOption,selectValue:t.cursor.format[r].ratio,hideCol:!0},on:{"update:selectValue":function(e){return t.$set(t.cursor.format[r],"ratio",e)},"update:select-value":function(e){return t.$set(t.cursor.format[r],"ratio",e)}}})],1),e("el-col",{attrs:{span:6}},[e("chart-base-select",{attrs:{tooltip:t.setItem.digit,selectOption:t.digitOption,selectValue:t.cursor.format[r].digit,hideCol:!0},on:{"update:selectValue":function(e){return t.$set(t.cursor.format[r],"digit",e)},"update:select-value":function(e){return t.$set(t.cursor.format[r],"digit",e)}}})],1)],1)}))],2)},jn=[],Nn={components:{...An(r)},props:{router:String,chartAllType:String,cursorOption:Object,lang:{type:String,default:"cn"}},data(){return{cursor:{},fontSizeOption:L(v),lineStyleOption:L(y),lineWeightOption:L(x),posOption:L(A),ratioOption:L(w),digitOption:L(C),triggerTypeArr:[{value:"item",label:"数据项图形触发"},{value:"axis",label:"坐标轴触发"}],axisPointerArr:[{value:"line",label:"直线指示器"},{value:"shadow",label:"阴影指示器"},{value:"cross",label:"十字准星指示器"}],triggerMethodArr:[{value:"mousemove",label:"鼠标移动"},{value:"click",label:"单击左键/鼠标划过"},{value:"mousemove|click",label:"同时触发"}],setItem:{}}},mounted(){"ch"!=this.lang?this.setItem=Ie["chartCursor"]:this.setItem=Oe["chartCursor"]},watch:{cursorOption:{handler(t){Me.a(this.cursor,t)||(this.cursor=L(t))},immediate:!0,deep:!0},cursor:{handler:function(t,e){e&&this.changeCursor()},deep:!0,immediate:!0},lang(t){this.setItem="ch"!=t?Ie["chartCursor"]:Oe["chartCursor"]}},computed:{seriesOption(){for(var t=[],e=0;e<this.cursor.format.length;e++)t.push(this.cursor.format[e].seriesName);return t}},methods:{...f["mapActions"]("chartSetting",["updateChartItem"]),changeCursor(){const t={updateObj:L(this.cursor),router:this.router};this.updateChartItem(t)}}},Mn=Nn,Rn=Le(Mn,Gn,jn,!1,null,null,null),Bn=Rn.exports,Vn=function(){var t=this,e=t._self._c;return e("el-collapse-item",{attrs:{name:"3"}},[e("template",{slot:"title"},[t._v(" "+t._s(t.setItem.modalName)+" "),e("i",{staticClass:"iconfont icon-biaoti"})]),e("chart-base-switch",{attrs:{switchValue:t.legend.show},on:{"update:switchValue":function(e){return t.$set(t.legend,"show",e)},"update:switch-value":function(e){return t.$set(t.legend,"show",e)}}},[e("div",{attrs:{slot:"title"},slot:"title"},[t._v("显示图例")])]),e("div",{directives:[{name:"show",rawName:"v-show",value:t.legend.show,expression:"legend.show"}]},[e("chart-base-label",{attrs:{router:t.router+"/label",baseLabelOption:t.legend.label},on:{"update:baseLabelOption":function(e){return t.$set(t.legend,"label",e)},"update:base-label-option":function(e){return t.$set(t.legend,"label",e)}}},[e("div",{attrs:{slot:"title"},slot:"title"},[t._v("图例样式")])]),e("chart-base-select",{attrs:{selectOption:t.positionOption,selectValue:t.legend.position.value},on:{"update:selectValue":function(e){return t.$set(t.legend.position,"value",e)},"update:select-value":function(e){return t.$set(t.legend.position,"value",e)}}},[e("div",{attrs:{slot:"select"},slot:"select"},[t._v("图例位置")])]),"custom"===t.legend.position.value?e("el-row",[e("chart-base-slider",{attrs:{baseSliderOption:t.legend.position.offsetX,unit:"%",content:"滑动修改水平偏移量"},on:{"update:baseSliderOption":function(e){return t.$set(t.legend.position,"offsetX",e)},"update:base-slider-option":function(e){return t.$set(t.legend.position,"offsetX",e)}}}),e("chart-base-slider",{attrs:{baseSliderOption:t.legend.position.offsetY,unit:"%",content:"滑动修改垂直偏移量"},on:{"update:baseSliderOption":function(e){return t.$set(t.legend.position,"offsetY",e)},"update:base-slider-option":function(e){return t.$set(t.legend.position,"offsetY",e)}}})],1):t._e(),e("chart-base-select",{attrs:{selectOption:t.dirOptions,selectValue:t.legend.position.direction},on:{"update:selectValue":function(e){return t.$set(t.legend.position,"direction",e)},"update:select-value":function(e){return t.$set(t.legend.position,"direction",e)}}},[e("div",{attrs:{slot:"select"},slot:"select"},[t._v("图例朝向")])]),e("chart-base-select",{attrs:{selectOption:t.sizeOption,selectValue:t.legend.width.value},on:{"update:selectValue":function(e){return t.$set(t.legend.width,"value",e)},"update:select-value":function(e){return t.$set(t.legend.width,"value",e)}}},[e("div",{attrs:{slot:"select"},slot:"select"},[t._v("图例宽度")])]),e("chart-base-select",{attrs:{selectOption:t.sizeOption,selectValue:t.legend.height.value},on:{"update:selectValue":function(e){return t.$set(t.legend.height,"value",e)},"update:select-value":function(e){return t.$set(t.legend.height,"value",e)}}},[e("div",{attrs:{slot:"select"},slot:"select"},[t._v("图例高度")])]),"custom"==t.legend.width.value?e("chart-base-slider",{attrs:{baseSliderOption:t.legend.width.cusSize,unit:"px",content:"滑动修改图例宽度"},on:{"update:baseSliderOption":function(e){return t.$set(t.legend.width,"cusSize",e)},"update:base-slider-option":function(e){return t.$set(t.legend.width,"cusSize",e)}}}):t._e(),"custom"==t.legend.height.value?e("chart-base-slider",{attrs:{baseSliderOption:t.legend.height.cusSize,unit:"px",content:"滑动修改图例高度"},on:{"update:baseSliderOption":function(e){return t.$set(t.legend.height,"cusSize",e)},"update:base-slider-option":function(e){return t.$set(t.legend.height,"cusSize",e)}}}):t._e(),e("chart-base-select",{attrs:{selectOption:t.distanceOption,selectValue:t.legend.distance.value},on:{"update:selectValue":function(e){return t.$set(t.legend.distance,"value",e)},"update:select-value":function(e){return t.$set(t.legend.distance,"value",e)}}},[e("div",{attrs:{slot:"select"},slot:"select"},[t._v("图例间距")])]),"custom"==t.legend.distance.value?e("chart-base-slider",{attrs:{baseSliderOption:t.legend.distance.cusGap,unit:"px",content:"滑动修改图例间距"},on:{"update:baseSliderOption":function(e){return t.$set(t.legend.distance,"cusGap",e)},"update:base-slider-option":function(e){return t.$set(t.legend.distance,"cusGap",e)}}},[e("div",{attrs:{slot:"title"},slot:"title"},[t._v("图例间距")])]):t._e()],1)],2)},Pn=[],zn={props:{legendOption:Object,chartAllType:String,router:String,lang:{type:String,default:"cn"}},data(){return{legend:{},positionOption:L(m),sizeOption:L(S),distanceOption:L(b),dirOptions:[{value:"horizontal",label:"水平"},{value:"vertical",label:"垂直"}],setItem:{}}},components:{...An(r)},mounted(){"ch"!=this.lang?this.setItem=Ie["chartLegend"]:this.setItem=Oe["chartLegend"]},watch:{legendOption:{handler(t){Me.a(this.legend,t)||(this.legend=L(t))},immediate:!0,deep:!0},legend:{handler:function(t,e){e&&this.changeLegend()},deep:!0,immediate:!0},lang(t){this.setItem="ch"!=t?Ie["chartLegend"]:Oe["chartLegend"]}},methods:{...f["mapActions"]("chartSetting",["updateChartItem"]),changeLegend(){const t={updateObj:L(this.legend),router:this.router};this.updateChartItem(t)}}},Qn=zn,Wn=Le(Qn,Vn,Pn,!1,null,null,null),Fn=Wn.exports,qn=function(){var t=this,e=t._self._c;return e("el-collapse-item",{attrs:{name:"6"}},[e("template",{slot:"title"},[t._v(" "+t._s(t.setItem.modalName)+" "),e("i",{staticClass:"iconfont icon-biaoti"})]),e("chart-base-select",{attrs:{selectOption:t.axisGroup,selectValue:t.axis.axisType},on:{"update:selectValue":function(e){return t.$set(t.axis,"axisType",e)},"update:select-value":function(e){return t.$set(t.axis,"axisType",e)}}},[e("div",{attrs:{slot:"select"},slot:"select"},[t._v(t._s(t.setItem.select))])]),e("chart-base-switch",{attrs:{switchValue:t.series.show},on:{"update:switchValue":function(e){return t.$set(t.series,"show",e)},"update:switch-value":function(e){return t.$set(t.series,"show",e)}}},[e("div",{attrs:{slot:"title"},slot:"title"},[t._v(t._s(t.series.name))])]),e("div",{directives:[{name:"show",rawName:"v-show",value:t.series.show,expression:"series.show"}]},[e("chart-base-input",{attrs:{inputValue:t.series.title.text,placeholder:t.setItem.placeholder},on:{"update:inputValue":function(e){return t.$set(t.series.title,"text",e)},"update:input-value":function(e){return t.$set(t.series.title,"text",e)}}},[e("div",{attrs:{slot:"input"},slot:"input"},[t._v(t._s(t.setItem.text))])]),e("div",{directives:[{name:"show",rawName:"v-show",value:t.series.title.text,expression:"series.title.text"}],staticStyle:{"margin-top":"15px"}},[e("chart-base-label",{attrs:{router:t.router+"/label",baseLabelOption:t.series.title.label},on:{"update:baseLabelOption":function(e){return t.$set(t.series.title,"label",e)},"update:base-label-option":function(e){return t.$set(t.series.title,"label",e)}}},[e("div",{attrs:{slot:"title"},slot:"title"},[t._v(t._s(t.setItem.label))])]),e("chart-base-select",{attrs:{selectOption:t.fzPosOption,selectValue:t.series.title.fzPosition},on:{"update:selectValue":function(e){return t.$set(t.series.title,"fzPosition",e)},"update:select-value":function(e){return t.$set(t.series.title,"fzPosition",e)}}},[e("div",{attrs:{slot:"select"},slot:"select"},[t._v(t._s(t.setItem.align))])])],1),e("chart-base-switch",{attrs:{switchValue:t.series.inverse},on:{"update:switchValue":function(e){return t.$set(t.series,"inverse",e)},"update:switch-value":function(e){return t.$set(t.series,"inverse",e)}}},[e("div",{attrs:{slot:"title"},slot:"title"},[t._v(t._s(t.setItem.reverse))])]),e("chart-base-slider",{attrs:{hideCol:!0,max:10,baseSliderOption:t.series.tickLabel.optimize,unit:"个",content:t.setItem.content},on:{"update:baseSliderOption":function(e){return t.$set(t.series.tickLabel,"optimize",e)},"update:base-slider-option":function(e){return t.$set(t.series.tickLabel,"optimize",e)}}},[e("div",{attrs:{slot:"title"},slot:"title"},[t._v(t._s(t.setItem.intenval))])]),e("div",{directives:[{name:"show",rawName:"v-show",value:t.series.title.text,expression:"series.title.text"}]},[e("chart-base-slider",{attrs:{hideCol:!0,baseSliderOption:t.series.title.nameGap,unit:"px",content:t.setItem.content1},on:{"update:baseSliderOption":function(e){return t.$set(t.series.title,"nameGap",e)},"update:base-slider-option":function(e){return t.$set(t.series.title,"nameGap",e)}}},[e("div",{attrs:{slot:"title"},slot:"title"},[t._v(t._s(t.setItem.gap))])]),e("chart-base-slider",{attrs:{hideCol:!0,format:t.formatRotation+"",max:180,min:-180,baseSliderOption:t.series.title.rotate,unit:"°",content:t.setItem.content2},on:{"update:baseSliderOption":function(e){return t.$set(t.series.title,"rotate",e)},"update:base-slider-option":function(e){return t.$set(t.series.title,"rotate",e)}}},[e("div",{attrs:{slot:"title"},slot:"title"},[t._v(t._s(t.setItem.title))])])],1),e("chart-base-switch",{attrs:{switchValue:t.series.tickLine.show},on:{"update:switchValue":function(e){return t.$set(t.series.tickLine,"show",e)},"update:switch-value":function(e){return t.$set(t.series.tickLine,"show",e)}}},[e("div",{attrs:{slot:"title"},slot:"title"},[t._v(t._s(t.setItem.showLine))])]),e("chart-base-slider",{attrs:{hideCol:!0,min:1,baseSliderOption:t.series.tickLine.width,unit:"px",content:t.setItem.content3},on:{"update:baseSliderOption":function(e){return t.$set(t.series.tickLine,"width",e)},"update:base-slider-option":function(e){return t.$set(t.series.tickLine,"width",e)}}},[e("div",{attrs:{slot:"title"},slot:"title"},[t._v(t._s(t.setItem.lineWidth))])]),e("el-row",{staticStyle:{"margin-top":"15px"}},[e("el-col",{staticClass:"title",attrs:{span:7}},[t._v(t._s(t.setItem.lineColor))]),e("el-col",{attrs:{push:14,span:3}},[e("el-tooltip",{attrs:{"open-delay":500,content:t.setItem.lineColor,effect:"dark",placement:"bottom"}},[e("el-color-picker",{attrs:{size:"mini"},model:{value:t.series.tickLine.color,callback:function(e){t.$set(t.series.tickLine,"color",e)},expression:"series.tickLine.color"}})],1)],1)],1),e("chart-base-switch",{attrs:{switchValue:t.series.tick.show},on:{"update:switchValue":function(e){return t.$set(t.series.tick,"show",e)},"update:switch-value":function(e){return t.$set(t.series.tick,"show",e)}}},[e("div",{attrs:{slot:"title"},slot:"title"},[t._v(t._s(t.setItem.showTick))])]),e("chart-base-select",{attrs:{selectOption:t.orient,selectValue:t.series.tick.position},on:{"update:selectValue":function(e){return t.$set(t.series.tick,"position",e)},"update:select-value":function(e){return t.$set(t.series.tick,"position",e)}}},[e("div",{attrs:{slot:"select"},slot:"select"},[t._v(t._s(t.setItem.position))])]),e("chart-base-slider",{attrs:{hideCol:!0,min:1,baseSliderOption:t.series.tick.length,unit:"px",content:t.setItem.content4},on:{"update:baseSliderOption":function(e){return t.$set(t.series.tick,"length",e)},"update:base-slider-option":function(e){return t.$set(t.series.tick,"length",e)}}},[e("div",{attrs:{slot:"title"},slot:"title"},[t._v(t._s(t.setItem.tickLength))])]),e("chart-base-slider",{attrs:{hideCol:!0,max:20,min:1,baseSliderOption:t.series.tick.width,unit:"px",content:t.setItem.content5},on:{"update:baseSliderOption":function(e){return t.$set(t.series.tick,"width",e)},"update:base-slider-option":function(e){return t.$set(t.series.tick,"width",e)}}},[e("div",{attrs:{slot:"title"},slot:"title"},[t._v(t._s(t.setItem.tickWidth))])]),e("el-row",{staticStyle:{"margin-top":"15px"}},[e("el-col",{staticClass:"title",attrs:{span:6}},[t._v(t._s(t.setItem.tickColor))]),e("el-col",{attrs:{push:14,span:4}},[e("el-tooltip",{attrs:{"open-delay":500,content:t.setItem.tickColor,effect:"dark",placement:"bottom"}},[e("el-color-picker",{attrs:{size:"mini"},model:{value:t.series.tick.color,callback:function(e){t.$set(t.series.tick,"color",e)},expression:"series.tick.color"}})],1)],1)],1),e("chart-base-switch",{attrs:{switchValue:t.series.tickLabel.show},on:{"update:switchValue":function(e){return t.$set(t.series.tickLabel,"show",e)},"update:switch-value":function(e){return t.$set(t.series.tickLabel,"show",e)}}},[e("div",{attrs:{slot:"title"},slot:"title"},[t._v(t._s(t.setItem.showLabel))])]),e("chart-base-slider",{attrs:{hideCol:!0,format:t.formatRotation,max:180,min:-180,baseSliderOption:t.series.tickLabel.rotate,unit:"°",content:t.setItem.content6},on:{"update:baseSliderOption":function(e){return t.$set(t.series.tickLabel,"rotate",e)},"update:base-slider-option":function(e){return t.$set(t.series.tickLabel,"rotate",e)}}},[e("div",{attrs:{slot:"title"},slot:"title"},[t._v(t._s(t.setItem.rotate))])]),e("div",{directives:[{name:"show",rawName:"v-show",value:t.showLabel,expression:"showLabel"}]},[e("chart-base-input",{attrs:{type:"text",inputValue:t.series.tickLabel.min,placeholder:t.setItem.content7},on:{"update:inputValue":function(e){return t.$set(t.series.tickLabel,"min",e)},"update:input-value":function(e){return t.$set(t.series.tickLabel,"min",e)}}},[e("div",{attrs:{slot:"input"},slot:"input"},[t._v(t._s(t.setItem.min))])]),e("chart-base-input",{attrs:{type:"text",inputValue:t.series.tickLabel.max,placeholder:t.setItem.content8},on:{"update:inputValue":function(e){return t.$set(t.series.tickLabel,"max",e)},"update:input-value":function(e){return t.$set(t.series.tickLabel,"max",e)}}},[e("div",{attrs:{slot:"input"},slot:"input"},[t._v(t._s(t.setItem.max))])]),e("chart-base-select",{attrs:{selectOption:t.ratioOption,selectValue:t.series.tickLabel.ratio},on:{"update:selectValue":function(e){return t.$set(t.series.tickLabel,"ratio",e)},"update:select-value":function(e){return t.$set(t.series.tickLabel,"ratio",e)}}},[e("div",{attrs:{slot:"select"},slot:"select"},[t._v(t._s(t.setItem.ratio))])]),e("chart-base-select",{attrs:{selectOption:t.digitOption,selectValue:t.series.tickLabel.digit},on:{"update:selectValue":function(e){return t.$set(t.series.tickLabel,"digit",e)},"update:select-value":function(e){return t.$set(t.series.tickLabel,"digit",e)}}},[e("div",{attrs:{slot:"select"},slot:"select"},[t._v(t._s(t.setItem.digit))])])],1),e("chart-base-input",{attrs:{inputValue:t.series.tickLabel.prefix,placeholder:t.setItem.content9},on:{"update:inputValue":function(e){return t.$set(t.series.tickLabel,"prefix",e)},"update:input-value":function(e){return t.$set(t.series.tickLabel,"prefix",e)}}},[e("div",{attrs:{slot:"input"},slot:"input"},[t._v(t._s(t.setItem.prefix))])]),e("chart-base-input",{attrs:{inputValue:t.series.tickLabel.suffix,placeholder:t.setItem.content10},on:{"update:inputValue":function(e){return t.$set(t.series.tickLabel,"suffix",e)},"update:input-value":function(e){return t.$set(t.series.tickLabel,"suffix",e)}}},[e("div",{attrs:{slot:"input"},slot:"input"},[t._v(t._s(t.setItem.suffix))])]),e("chart-base-switch",{attrs:{switchValue:t.series.netLine.show},on:{"update:switchValue":function(e){return t.$set(t.series.netLine,"show",e)},"update:switch-value":function(e){return t.$set(t.series.netLine,"show",e)}}},[e("div",{attrs:{slot:"title"},slot:"title"},[t._v(t._s(t.setItem.showNet))])]),e("div",{directives:[{name:"show",rawName:"v-show",value:t.series.netLine.show,expression:"series.netLine.show"}]},[e("chart-base-slider",{attrs:{hideCol:!0,max:20,min:1,baseSliderOption:t.series.netLine.width,unit:"px",content:t.setItem.content11},on:{"update:baseSliderOption":function(e){return t.$set(t.series.netLine,"width",e)},"update:base-slider-option":function(e){return t.$set(t.series.netLine,"width",e)}}},[e("div",{attrs:{slot:"title"},slot:"title"},[t._v(t._s(t.setItem.netWidth))])]),e("chart-base-select",{attrs:{selectOption:t.lineStyleOption,selectValue:t.series.netLine.type},on:{"update:selectValue":function(e){return t.$set(t.series.netLine,"type",e)},"update:select-value":function(e){return t.$set(t.series.netLine,"type",e)}}},[e("div",{attrs:{slot:"select"},slot:"select"},[t._v(t._s(t.setItem.netType))])]),e("el-row",{staticStyle:{"margin-top":"15px"}},[e("el-col",{staticClass:"title",attrs:{span:8}},[t._v(t._s(t.setItem.netColor))]),e("el-col",{attrs:{push:13,span:3}},[e("el-tooltip",{attrs:{"open-delay":500,content:t.setItem.netColor,effect:"dark",placement:"bottom"}},[e("el-color-picker",{attrs:{label:!0,size:"mini"},model:{value:t.series.netLine.color,callback:function(e){t.$set(t.series.netLine,"color",e)},expression:"series.netLine.color"}})],1)],1)],1),e("chart-base-select",{attrs:{selectOption:t.intervalOption,selectValue:t.series.netLine.interval.value},on:{"update:selectValue":function(e){return t.$set(t.series.netLine.interval,"value",e)},"update:select-value":function(e){return t.$set(t.series.netLine.interval,"value",e)}}},[e("div",{attrs:{slot:"select"},slot:"select"},[t._v(t._s(t.setItem.netInterval))])]),"custom"==t.series.netLine.interval.value?e("chart-base-slider",{attrs:{baseSliderOption:t.series.netLine.interval.cusNumber,unit:"个",content:t.setItem.content12},on:{"update:baseSliderOption":function(e){return t.$set(t.series.netLine.interval,"cusNumber",e)},"update:base-slider-option":function(e){return t.$set(t.series.netLine.interval,"cusNumber",e)}}}):t._e()],1),e("chart-base-switch",{attrs:{switchValue:t.series.netArea.show},on:{"update:switchValue":function(e){return t.$set(t.series.netArea,"show",e)},"update:switch-value":function(e){return t.$set(t.series.netArea,"show",e)}}},[e("div",{attrs:{slot:"title"},slot:"title"},[t._v(t._s(t.setItem.showArea))])]),e("div",{directives:[{name:"show",rawName:"v-show",value:t.series.netArea.show,expression:"series.netArea.show"}]},[e("chart-base-select",{attrs:{selectOption:t.intervalOption,selectValue:t.series.netArea.interval.value},on:{"update:selectValue":function(e){return t.$set(t.series.netArea.interval,"value",e)},"update:select-value":function(e){return t.$set(t.series.netArea.interval,"value",e)}}},[e("div",{attrs:{slot:"select"},slot:"select"},[t._v(t._s(t.setItem.areaInterval))])]),"custom"==t.series.netArea.interval.value?e("chart-base-slider",{attrs:{baseSliderOption:t.series.netArea.interval.cusNumber,unit:"个",content:t.setItem.content12},on:{"update:baseSliderOption":function(e){return t.$set(t.series.netArea.interval,"cusNumber",e)},"update:base-slider-option":function(e){return t.$set(t.series.netArea.interval,"cusNumber",e)}}}):t._e(),e("el-row",{staticStyle:{"margin-top":"15px"}},[e("el-col",{attrs:{span:6}},[t._v(t._s(t.setItem.area1))]),e("el-col",{attrs:{span:3}},[e("el-color-picker",{attrs:{size:"mini"},model:{value:t.series.netArea.colorOne,callback:function(e){t.$set(t.series.netArea,"colorOne",e)},expression:"series.netArea.colorOne"}})],1),e("el-col",{attrs:{span:6}},[t._v(t._s(t.setItem.area2))]),e("el-col",{attrs:{span:3}},[e("el-color-picker",{attrs:{size:"mini"},model:{value:t.series.netArea.colorTwo,callback:function(e){t.$set(t.series.netArea,"colorTwo",e)},expression:"series.netArea.colorTwo"}})],1)],1)],1)],1)],2)},Xn=[],Hn={name:"ChartXaxis",props:{chartAllType:String,axisOption:Object,router:String,lang:{type:String,default:"cn"}},components:{...An(r)},data(){return{axis:{},series:{},fontSizeOption:"",lineStyleOption:"",ratioOption:"",digitOption:"",fzPosOption:[{value:"middle",label:"居中"},{value:"start",label:"头部"},{value:"end",label:"尾部"},{value:"hidden",label:"隐藏"}],orient:[{label:"朝内",value:"inside"},{label:"朝外",value:"outside"}],formatRotation:function(t){return t+" °"},setItem:{}}},mounted(){"ch"!=this.lang?this.setItem=Ie["chartAxis"]:this.setItem=Oe["chartAxis"]},watch:{axisOption:{handler(t){Me.a(this.axis,this.axisOption)||(this.axis=L(this.axisOption),this.series=this.axis[t.axisType],this.fontSizeOption=L(v),this.lineStyleOption=L(y),this.intervalOption=L(k),this.ratioOption=L(w),this.digitOption=L(C))},immediate:!0,deep:!0},series:{handler:function(t,e){e&&this.changeAxis()},deep:!0,immediate:!0},lang(t){this.setItem="ch"!=t?Ie["chartAxis"]:Oe["chartAxis"]}},computed:{chartType(){return this.chartAllType.split("|")[1]},chartStyle(){return this.chartAllType.split("|")[2]},axisGroup(){return"bar"==this.chartType&&"compare"!=this.chartStyle?[{value:"xAxisDown",label:"Y轴(左侧垂直)"},{value:"xAxisUp",label:"Y轴(左侧垂直)"},{value:"yAxisLeft",label:"X轴(下方水平)"},{value:"yAxisRight",label:"X轴(上方水平)"}]:"compare"==this.chartStyle?[{value:"xAxisDown",label:"Y轴(右侧坐标轴)"},{value:"xAxisUp",label:"Y轴(左侧坐标轴)"},{value:"yAxisLeft",label:"X轴(右侧坐标轴)"},{value:"yAxisRight",label:"X轴(左侧坐标轴)"}]:[{value:"xAxisDown",label:"X轴(下方水平)"},{value:"xAxisUp",label:"X轴(上方水平)"},{value:"yAxisLeft",label:"Y轴(左侧垂直)"},{value:"yAxisRight",label:"Y轴(右侧垂直)"}]},showLabel(){if("bar"==this.chartType&&"x"==this.axis.axisType.slice(0,1)||"bar"!=this.chartType&&"y"==this.axis.axisType.slice(0,1))return!0}},methods:{...f["mapActions"]("chartSetting",["updateChartItem"]),changeAxis(){const t={updateObj:L(this.series),router:this.router+"/"+this.axis.axisType};this.updateChartItem(t)}}},Zn=Hn,Jn=Le(Zn,qn,Xn,!1,null,null,null),Un=Jn.exports,Yn={name:"ChartSetting",components:{"chart-list":je,"chart-title":kn,"chart-sub-title":Ln,"chart-cursor":Bn,"chart-legend":Fn,"chart-axis":Un},props:{chartOptions:{type:Object,default:null},lang:{type:String,default:"cn"}},data(){return{currentChartType:"echarts|line|default",chart_id:"",titleOption:L(g.title),subTitleOption:L(g.subtitle),cursorOption:L(g.tooltip),legendOption:L(g.legend),axisOption:L(g.axis),showList:!1,setItem:{echarts:{line:{default:"默认折线图"}}},activeName:"data"}},mounted(){"ch"!=this.lang?this.setItem=Ie["chartSetting"]:this.setItem=Oe["chartSetting"]},watch:{chartOptions:{handler:function(t,e){void 0!=t&&t.hasOwnProperty("chartAllType")&&(this.currentChartType=t.chartAllType,this.chart_id=t.chart_id,this.titleOption=t.defaultOption.title,this.subTitleOption=t.defaultOption.subtitle,this.cursorOption=t.defaultOption.tooltip,this.legendOption=t.defaultOption.legend,this.axisOption=t.defaultOption.axis)}},lang(t){this.setItem="ch"!=t?Ie["chartSetting"]:Oe["chartSetting"]}},computed:{...Object(f["mapState"])("chartSetting",["chartLists","currentChartIndex"]),currentRangeColCheck:{get(){return null==this.currentChartIndex?{exits:!1,range:[0,0]}:this.chartLists[this.currentChartIndex].chartOptions.rangeColCheck},set(t){this.updateChartItemChartlistOne({key:"rangeColCheck",value:t,chart_id:this.chart_id})}},currentRangeRowCheck:{get(){return null==this.currentChartIndex?{exits:!1,range:[0,0]}:this.chartLists[this.currentChartIndex].chartOptions.rangeRowCheck},set(t){this.updateChartItemChartlistOne({key:"rangeRowCheck",value:t,chart_id:this.chart_id})}},checkRowDisabled(){return null==this.currentChartIndex||!this.chartLists[this.currentChartIndex].chartOptions.chartData||1==this.chartLists[this.currentChartIndex].chartOptions.chartData.length},checkColDisabled(){return null==this.currentChartIndex||!this.chartLists[this.currentChartIndex].chartOptions.chartData||1==this.chartLists[this.currentChartIndex].chartOptions.chartData.length},currentRangeConfigCheck:{get(){return null!=this.currentChartIndex&&this.chartLists[this.currentChartIndex].chartOptions.rangeConfigCheck},set(t){this.updateChartItemChartlistOne({key:"rangeConfigCheck",value:t,chart_id:this.chart_id})}},chart_pro(){return this.currentChartType.split("|")[0]},chart_type(){return this.currentChartType.split("|")[1]},chart_style(){return this.currentChartType.split("|")[2]},chartTypeTxt:function(){var t,e,n;return"echarts"==this.chart_pro?t="echarts":"highcharts"==this.chart_pro&&(t="highcharts"),"line"==this.chart_type?("default"==this.chart_style&&(e=this.setItem.echarts.line.default),"smooth"==this.chart_style&&(e=this.setItem.echarts.line.smooth),"label"==this.chart_style&&(e=this.setItem.echarts.line.label),"doublex"==this.chart_style&&(e="双Y轴折线图"),"linemix"==this.chart_style&&(e="折线柱状混合图"),n="icon-tubiaozhexiantu",[n,t+" - "+e]):"area"==this.chart_type?("default"==this.chart_style&&(e=this.setItem.echarts.area.default),"stack"==this.chart_style&&(e=this.setItem.echarts.area.stack),"stackRatio"==this.chart_style&&(e="带标签的堆叠面积图"),n="icon-fsux_tubiao_duijimianjitu",[n,t+" - "+e]):"column"==this.chart_type?("default"==this.chart_style&&(e=this.setItem.echarts.column.default),"stack"==this.chart_style&&(e=this.setItem.echarts.column.stack),"stackRatio"==this.chart_style&&(e="百分比堆叠柱状图"),"costComposition"==this.chart_style&&(e="费用构成柱状图"),"polarStack"==this.chart_style&&(e="极坐标系下的堆叠柱状图"),"bar3DPunchCard"==this.chart_style&&(e="3D柱状图"),"contain"==this.chart_style&&(e="比例图"),"special"==this.chart_style&&(e="显示百分比图"),"doubleX"==this.chart_style&&(e="双X轴"),n="icon-chart",[n,t+" - "+e]):"bar"==this.chart_type?("default"==this.chart_style&&(e=this.setItem.echarts.bar.default),"stack"==this.chart_style&&(e=this.setItem.echarts.bar.stack),"stackRatio"==this.chart_style&&(e="百分比堆叠条形图"),"compare"==this.chart_style&&(e="条形比较图"),"contain"==this.chart_style&&(e="比例图"),n="icon-fsux_tubiao_duijizhuzhuangtu1",[n,t+" - "+e]):"pie"==this.chart_type?("default"==this.chart_style&&(e=this.setItem.echarts.pie.default),"split"==this.chart_style&&(e=this.setItem.echarts.pie.split),"ring"==this.chart_style&&(e=this.setItem.echarts.pie.ring),"ringnest"==this.chart_style&&(e="环形嵌套图"),"3D"==this.chart_style&&(e="3D饼图"),"rose"==this.chart_style&&("echarts"==this.chart_pro?e="南丁格玫瑰图":"highcharts"==this.chart_pro&&(e="可变宽度的环形图")),n="icon-fsux_tubiao_nandingmeiguitu",[n,t+" - "+e]):"scatter"==this.chart_type?("default"==this.chart_style&&(e="默认散点图"),"label"==this.chart_style&&(e="带标签的散点图"),"zoom"==this.chart_style&&(e="自由缩放散点图"),"matrix"==this.chart_style&&(e="散点图矩阵"),n="icon-fsux_tubiao_qipaotu",[n,t+" - "+e]):"radar"==this.chart_type?("default"==this.chart_style&&(e="默认雷达图"),n="icon-leidatu",[n,t+" - "+e]):"funnel"==this.chart_type?("default"==this.chart_style&&(e="默认漏斗图"),"inverse"==this.chart_style&&(e="逆漏斗图"),n="icon-fsux_tubiao_loudoutu",[n,t+" - "+e]):"gauge"==this.chart_type?("default"==this.chart_style&&(e="仪表盘"),"percent"==this.chart_style&&(e="百分比仪表盘"),"solid"==this.chart_style&&(e="活动图"),n="icon-fsux_tubiao_yibiaopan",[n,t+" - "+e]):"map"==this.chart_type?("china"==this.chart_style?e="中国地图":"province"==this.chart_style?e="省份地图":"cnscatter"==this.chart_style?e="中国地图散点图":"pvscatter"==this.chart_style?e="省份地图散点图":"percent"==this.chart_style&&(e="百分比地图"),n="icon-fsux_tubiao_ditu",[n,t+" - "+e]):"earth"==this.chart_type?[n,t+" - 3D 地球"]:void 0},currentChartDataCache(){return null==this.currentChartIndex?null:this.chartLists[this.currentChartIndex].chartOptions.chartDataCache},chartXYSeriesList(){if(null==this.currentChartDataCache)return;let t=this.chartLists[this.currentChartIndex].chartOptions.chartDataSeriesOrder;var e=this.currentChartType.split("|"),n=(e[0],e[1]),r=(e[2],{num:"icon-shuzi",string:"icon-format_icon",date:"icon-date"}),i={fix:[],change:[],option:[]};if(("line"==n||"column"==n||"area"==n||"scatter"==n)&&(null!=this.currentChartDataCache.title&&i.fix.push({title:"x轴",type:r["string"],field:this.currentChartDataCache.title.text}),null!=this.currentChartDataCache.label))for(var a=0;a<this.currentChartDataCache.label.length;a++){var o=t[a];i.change[o]={title:"系列"+(o+1),index:o,type:r[this.currentChartDataCache.series_tpye[a]],field:this.currentChartDataCache.label[a],id:a},i.option.push({field:this.currentChartDataCache.label[a],id:a,index:o})}return i},currentChartDataSeriesOrder:{get(){return null==this.currentChartIndex?{}:this.chartLists[this.currentChartIndex].chartOptions.chartDataSeriesOrder},set(t){this.updateChartItemChartlistOne({key:"chartDataSeriesOrder",value:t,chart_id:this.chart_id})}}},methods:{...Object(f["mapActions"])("chartSetting",["updateChartItemChartlistOne"]),handleClick(t){0!=t.index&&(this.showList=!1)},getColRowCheckTxt:function(t){if(t){e="";return e=this.currentRangeRowCheck.range[0]==this.currentRangeRowCheck.range[1]?this.currentRangeRowCheck.range[0]+1:this.currentRangeRowCheck.range[0]+1+"至"+(this.currentRangeRowCheck.range[1]+1),e}var e="";return e=this.currentRangeColCheck.range[0]==this.currentRangeColCheck.range[1]?this.currentRangeColCheck.range[0]+1:this.currentRangeColCheck.range[0]+1+"至"+(this.currentRangeColCheck.range[1]+1),e},checkBoxChange:function(){var t=this.chartLists[this.currentChartIndex].chartOptions.chart_id,e=this.currentRangeRowCheck,n=this.currentRangeColCheck,r=this.currentRangeConfigCheck;xe(t,e,n,r)},handleSeriseCommand:function(t){var e=t.series,n=t.option,r=L(this.currentChartDataSeriesOrder),i=n.id,a=e.index,o=e.id,s=r[i];r[i]=a,r[o]=s,this.currentChartDataSeriesOrder=r,Ae(this.chartLists[this.currentChartIndex].chartOptions,this.currentChartDataSeriesOrder)}}},Kn=Yn,_n=(n("77ad"),Le(Kn,o,s,!1,null,null,null)),$n=_n.exports,tr=function(){var t=this,e=t._self._c;return e("div",{staticClass:"chartRender"})},er=[],nr={name:"ChartRender",props:{active:{type:Boolean,default:!1},chart_id:{type:String,default:""},chartOptions:{type:Object,default:null}},watch:{chartOptions:{handler:function(t){t&&this.renderCharts(t)},immediate:!0,deep:!0}},mounted(){this.$nextTick(()=>{let t=this.chartOptions;this.renderCharts(t)})},methods:{renderCharts(t){const e={chart_id:this.chart_id,chartOptions:t};de(e,this.$el)}}},rr=nr,ir=Le(rr,tr,er,!1,null,"4be53480",null),ar=ir.exports,or=n("164e"),sr=n.n(or);const lr=he.state.chartSetting;function cr(t,e){let n=document.createElement("div");n.id="chartmix",t.appendChild(n),new xt.a({el:"#chartmix",store:he,data(){return{lang:e}},computed:{chartOptions(){return lr.currentChartIndex?lr.chartLists[lr.currentChartIndex].chartOptions:null}},template:'<ChartSetting :lang="lang" :chartOptions="chartOptions"></ChartSetting>'})}function ur(t,e,n,r,i){let a=n||N("chart");t.id=a,D.defaultOption.series=[];let o=10*Math.random();D.chartAllType=o>5?"echarts|pie|default":"echarts|line|default";let s=hr(D,a,D.chartAllType,e,r,i),l=document.createElement("div");l.id="render"+a,t.appendChild(l);let c={chart_id:a,active:!0,chartOptions:L(s)};return lr.currentChartIndex=lr.chartLists.length,lr.chartLists.push(c),new xt.a({el:"#render"+a,store:he,data(){return{chart_Id:a}},computed:{options(){let t=lr.chartLists.find(t=>t.chart_id==this.chart_Id);return t?t.chartOptions:null},active(){let t=lr.chartLists.find(t=>t.chart_id==this.chart_Id);return t?t.active:null}},template:'<ChartRender :chartOptions="options" :chart_id="chart_Id" :active="active"></ChartRneder>'}),{render:t,chart_Id:a,chart_json:c}}function hr(t,e,n,r,i,a,o,s,l,c,u){var h={},f=n.split("|"),p=f[0],d=f[1],g=f[2];h.chart_id=e,h.chartAllType=n,h.chartPro=p,h.chartType=d,h.chartStyle=g,h.height=s,h.width=l,h.left=c,h.top=u;var m=t.defaultOption;h.chartData=r,h.rangeArray=i,h.rangeTxt=a;var b=F(r),v=b[0],y=b[1],x=!1;h.rangeColCheck=y,h.rangeRowCheck=v,h.rangeConfigCheck=x;var A=q(r,i,y,v);h.rangeSplitArray=A;var w=H(r,A,p,d,g);h.chartDataCache=w;var C=Z(w.series[0].length);h.chartDataSeriesOrder=C,h.chartTheme=o;var S=J(m,w,C,p,d,g);return h.defaultOption=S,h}function fr(t){let e=lr.chartLists.findIndex(e=>e.chart_id==t);return lr.currentChartIndex=e,lr.chartLists[lr.currentChartIndex].chartOptions}function pr(t){let e=lr.chartLists.findIndex(e=>e.chart_id==t);var n=lr.chartLists[e].chartOptions.chartAllType,r=n.split("|"),i=r[0];r[1],r[2];"echarts"==i&&sr.a.getInstanceById(h()("#"+t).attr("_echarts_instance_")).resize()}function dr(t){let e=lr.chartLists.findIndex(e=>e.chart_id==t);if(lr.chartLists.splice(e,1),lr.currentChartIndex--,lr.currentChartIndex<0){if(lr.chartLists[0])return void(lr.currentChartIndex=0);lr.currentChartIndex=null}}function gr(t){let e=lr.chartLists.findIndex(e=>e.chart_id==t);return lr.chartLists[e].chartOptions}function mr(t){lr.chartLists.push(t)}const br=[$n,ar],vr=function(t,e={}){vr.componentInstalled||(br.map(e=>{t.component(e.name,e)}),vr.componentInstalled=!0),vr.storeRegistered||e.store&&(e.store.registerModule("chartSetting",ce),vr.storeRegistered=!0)};"undefined"!==typeof window&&window.Vue&&vr(window.Vue);var yr={install:vr,...br,initChart:cr,createChart:ur,highlightChart:fr,deleteChart:dr,resizeChart:pr,changeChartRange:we,changeChartCellData:Ce,renderChart:de,getChartJson:gr,insertToStore:mr};e["default"]=yr},fba5:function(t,e,n){var r=n("cb5a");function i(t){return r(this.__data__,t)>-1}t.exports=i},fc6a:function(t,e,n){var r=n("44ad"),i=n("1d80");t.exports=function(t){return r(i(t))}},fdbf:function(t,e,n){var r=n("04f8");t.exports=r&&!Symbol.sham&&"symbol"==typeof Symbol.iterator}})}));
//# sourceMappingURL=chartmix.umd.min.js.map