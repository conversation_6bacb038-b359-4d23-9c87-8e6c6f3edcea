<template>
  <el-dialog
    v-drag-dialog
    :visible.sync="show"
    :close-on-click-modal="false"
    width="950px"
    class="dialog"
    center
    append-to-body
    @close="show=false"
  >
    <div slot="title" class="images-prview-header">
      <!-- <span class="image-name"> {{ images[currIndex].name }}</span> -->
      {{ title||'图片预览' }}
    </div>
    <div id="prviewArea" element-loading-background="transparent">
      <div id="actionArea" v-loading="loading">
        <img id="actionImg" src="">
      </div>
      <img v-show="loadingFail&&!loading" src="./icons/loadingFail.svg" width="40">
      <div v-show="images.length>1" class="mark">{{ currIndex+1 }}/{{ images.length }}</div>
      <div v-show="images.length>1" class="icon pre-img" @click="changeImage(currIndex - 1)"> <span class="el-icon-arrow-left" /></div>
      <div v-show="images.length>1" class="icon next-img" @click="changeImage(currIndex + 1)"><span class="el-icon-arrow-right" /></div>
      <div v-show="!loadingFail&&!loading" class="tools">
        <div class="icon" @click="init('position scale')"> <span class="scale-icon">{{ scale }}%</span> </div>
        <div class="icon" @click="rotateActionDom(direction-1)">	<span class="el-icon-refresh-left" /></div>
        <div class="icon" @click="rotateActionDom(direction+1)">	<span class="el-icon-refresh-right" />	</div>
        <div class="icon" @click="scaleActionDom(scale+getScaleMagnification(scale,true))">
          <span class="el-icon-zoom-in" />
        </div>
        <div class="icon" @click="scaleActionDom(scale-getScaleMagnification(scale),false)">	<span class="el-icon-zoom-out" />	</div>
        <div class="icon" @click="downloadImg"><span class="el-icon-download" />	</div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
let moveInfo = {}
let actionDom = null
/**
  * 根据id获取dom
  */
function getDomById(id) {
  return document.getElementById(id)
}
// 获取图片名字
function downloadImage(imgsrc, name) { // 下载图片地址和图片名
  const image = new Image()
  // 解决跨域 Canvas 污染问题
  image.setAttribute('crossOrigin', 'anonymous')
  image.src = imgsrc
  image.onload = function() {
    const canvas = document.createElement('canvas')
    canvas.width = image.width
    canvas.height = image.height
    const context = canvas.getContext('2d')
    context.drawImage(image, 0, 0, image.width, image.height)
    const url = canvas.toDataURL('image/png') // 得到图片的base64编码数据
    const a = document.createElement('a') // 生成一个a元素
    const event = new MouseEvent('click') // 创建一个单击事件
    a.download = name || 'photo' // 设置图片名称
    a.href = url // 将生成的URL设置为a.href属性
    a.dispatchEvent(event) // 触发a的单击事件
  }
}
export default {
  data() {
    return {
      show: false,
      scale: 100,
      mouseDownFlag: false,
      direction: 0, // 图片方向
      currIndex: 0,
      images: [],
      loading: false,
      loadingFail: false,
      title: '',
      isAsync: false, // 是否进行异步请求图片
      urlFunc: [] // 异步获取方法
    }
  },
  methods: {
    mouseDownEvent(e) {
      e.preventDefault()
      this.mouseDownFlag = true
      moveInfo = {
        mouseX: e.clientX,
        mouseY: e.clientY,
        scrollX: parseInt(actionDom.style.left || '0'),
        scrollY: parseInt(actionDom.style.top || '0')
      }
    },
    mouseUpEvent() {
      this.mouseDownFlag = false
    },
    mouseMoveEvent(e) {
      e.preventDefault()
      if (!this.mouseDownFlag) {
        return
      }
      actionDom.style.left = moveInfo.scrollX + (e.clientX - moveInfo.mouseX) + 'px'
      actionDom.style.top = moveInfo.scrollY + (e.clientY - moveInfo.mouseY) + 'px'
    },
    getScaleMagnification(scale, add) {	// 获取缩放倍率 是否是放大操作
      if (scale > 1000) {
        return 100
      }
      const scaleArr = [0, 150, 300, 500, 1000]
      const multipleArr = [1, 10, 25, 50, 100]
      let multiple = 0
      if (add) {
        multiple = multipleArr[scaleArr.findIndex(item => scale < item)]
      } else {
        multiple = multipleArr[scaleArr.findIndex(item => scale <= item)]
      }
      return multiple
    },
    downloadImg() {
      if (this.loading || this.loadingFail) return
      downloadImage(this.images[this.currIndex].url, this.images[this.currIndex].name)
    },
    // 初始化  空格分割字符串
    init(action) {
      if (action.indexOf('position') !== -1 || action === 'all') {
        actionDom.style.top =	actionDom.style.left = '0px'
      }
      if (action.indexOf('scale') !== -1 || action === 'all') {
        this.computeInitScaling()
      }
      if (action.indexOf('direction') !== -1 || action === 'all') {
        this.rotateActionDom(0)
      }
    },
    // 计算初始缩放比例
    computeInitScaling() {
      const prviewArea = getDomById('prviewArea')
      const actionImg = getDomById('actionImg')
      // 图片宽高依据方向进行变换
      const w = this.direction % 2 === 1 ? actionImg.clientHeight : actionImg.clientWidth
      const h = this.direction % 2 === 1 ? actionImg.clientWidth : actionImg.clientHeight
      // 计算出比例    尽量将图片完整的展现出来
      const scaleX = prviewArea.clientWidth / (w + 20) // +20 是为了预留一点空白 不让图片占满
      const scaleY = prviewArea.clientHeight / (h + 20)
      let scale = Math.min(scaleY, scaleX) * 100

      if (scale > 100) { // 缩放倍率应在 10-100之间
        scale = 100
      } else if (scale < 10) { // 图太大的话 即使显示不完整 也只能以10%的倍率缩放
        scale = 10
      } else {
        // 向10取整		Math.floor(scale/10)*10
        scale = Math.floor(scale / 10) * 10
      }

      this.scaleActionDom(scale)
    },
    // 缩放图片
    scaleActionDom(scale) {
      if (scale < 10 || scale > 900) {
        return
      }
      // 获取缩放比
      this.scale = parseInt(scale || this.scale)
      // 赋予transform  并保持缩放比不变
      actionDom.style.transform = `scale(${this.scale / 100})`
    },
    rotateActionDom(direction = this.direction) {
      // 获取方向
      if (direction < 0) direction = 3
      this.direction = direction % 4
      // 赋予transform  并保持缩放比不变
      getDomById('actionImg').style.transform = `rotate(${this.direction * 90}deg) `
      this.init('position scale')
    },
    // 鼠标滚动事件
    mouseScroll(e) {
      const evt = e || window.event
      evt.preventDefault()
      if (this.loading) return
      if (evt.deltaY < 0) {
        this.scaleActionDom(this.scale + this.getScaleMagnification(this.scale, true))
      } else {
        this.scaleActionDom(this.scale - this.getScaleMagnification(this.scale, false))
      }
    },
    isDomReady(dom, f, heavyLoad = 300) { // 判断图片是否加载完
      if (dom.offsetHeight === 0) {
        if (heavyLoad === 0) { // 加载失败
          this.loadingFail = true
          getDomById('actionImg').src = ''
          return //
        }
        setTimeout(() => {
          this.isDomReady(dom, f, heavyLoad - 1)
        }, 20)
      } else {
        f()
      }
    },
    changeImage(currIndex) {
      const imgDom = getDomById('actionImg')
      imgDom.src = ''
      this.loadingFail = false
      this.loading = true
      if (!this.isAsync) {
        if (currIndex >= this.images.length) {
          currIndex = 0
        } else if (currIndex < 0) {
          currIndex = this.images.length - 1
        }
        this.currIndex = currIndex
        imgDom.src = this.images[this.currIndex].url
        this.isDomReady(imgDom, () => {
          this.loading = false
          this.init('all')
        })
      } else {
        if (currIndex >= this.urlFunc.length) {
          currIndex = 0
        } else if (currIndex < 0) {
          currIndex = this.urlFunc.length - 1
        }
        this.currIndex = currIndex
        if (typeof this.urlFunc[currIndex] === 'function') {
          this.urlFunc[this.currIndex]().then(res => {
            this.urlFunc[currIndex] = this.checkUrl(res.data.data)
            imgDom.src = this.urlFunc[this.currIndex]
            this.images[this.currIndex] = this.urlFunc[this.currIndex]
            this.isDomReady(imgDom, () => {
              this.loading = false
              this.init('all')
            })
          }).catch(e => {
            this.loadingFail = true
            getDomById('actionImg').src = ''
            this.loading = false
          })
        } else {
          imgDom.src = this.images[this.currIndex]
          this.isDomReady(imgDom, () => {
            this.loading = false
            this.init('all')
          })
        }
      }
    },
    appendImg(images) {
      if (!images || images.length === 0) {
        throw new Error('传参不规范！')
      }
      const itemArr = images.map(item => {
        return {
          url: item,
          // name:name || getFileNameByURL(item) ///不显示图片名字
          name: ''
        }
      })
      this.images = this.images.concat(itemArr)
    },
    setTitle(title) {
      this.title = title
    },
    /**
		 *  [func,func]
		 * @funs 自定义url获取方法数组   需要返回 promise
		 * 数组中放入url的方法
		 */
    customAsyncURLFun(funcs, currIndex = 0) { // 自定义异步 url获取
      if (!funcs || funcs.length === 0) {
        throw new Error('未获取到待预览图片列表！')
      }
      this.currIndex = currIndex
      this.isAsync = true
      this.urlFunc = funcs
      this.images = new Array(funcs.length).fill('')
      this.show = true
      this.$nextTick(() => {
        this.changeImage(this.currIndex)
        // 使图片区域可拖动
        actionDom = getDomById('actionArea')
        actionDom.addEventListener('mousedown', this.mouseDownEvent, false)
        getDomById('prviewArea').addEventListener('wheel', this.mouseScroll, { passive: false })
        document.addEventListener('mouseup', this.mouseUpEvent, false)
        document.addEventListener('mousemove', this.mouseMoveEvent, false)
      })
      return this
    },
    open(images, currIndex = 0) {
      const _this = this
      if (!images || images.length === 0) {
        throw new Error('未获取到待预览图片列表！')
      }
      // getFileNameByURL  如果没有给出图片名 则自动截取
      this.images = images.map(item => {
        return {
          url: typeof item === 'string' ? item : _this.checkUrl(item),
          // name:name || getFileNameByURL(item)   const _this = this
          name: ''
        }
      })
      this.currIndex = currIndex > images.length - 1 ? 0 : currIndex
      this.show = true
      this.$nextTick(() => {
        actionDom = getDomById('actionArea')
        actionDom.addEventListener('mousedown', this.mouseDownEvent, false)
        this.changeImage(this.currIndex)
        // 使图片区域可拖动
        getDomById('prviewArea').addEventListener('wheel', this.mouseScroll, { passive: false })
        document.addEventListener('mouseup', this.mouseUpEvent, false)
        document.addEventListener('mousemove', this.mouseMoveEvent, false)
      })
      return this
    },
    close() {
      this.show = false
      actionDom.removeEventListener('mousedown', this.mouseDownEvent, false)
      document.removeEventListener('mousemove', this.mouseMoveEvent, false)
      document.removeEventListener('mouseup', this.mouseUpEvent, false)
      getDomById('prviewArea').removeEventListener('wheel', this.mouseScroll, { passive: false })
      actionDom = null
    },
    checkUrl(url) {
      function f_check_IP() {
        const host = window.location.host
        const ip = host.split(':')[0]
        var re = /^(\d+)\.(\d+)\.(\d+)\.(\d+)$/// 正则表达式
        if (re.test(ip) || ip === 'localhost') {
          return true
        }
        return false
      }
      let itemURL = ''
      if (f_check_IP()) { // 内网
        itemURL = window.location.protocol + '//' + window.location.host + url
      } else {
        itemURL = url.split('file')[0] + 'api/file/file' + url.split('file')[1]
      }
      return itemURL
    }
  }
}
</script>

<style lang="scss" scoped>
	.image-name{
		font-size:14px;
		// margin-left:10px;
	}
	#prviewArea{
		background-color:#f5f5f5;
		width:100%;
		height:100%;
		overflow: hidden;
		display: flex;
		justify-content: center;
		align-items: center;
		position: relative;
		#actionArea{
			position: relative;
			user-select: none;
			transition: transform .3s;
			#actionImg {
				cursor: url(./icons/mouseOpen.svg),auto;
			}
		}
    &:hover {
			.tools{
				transition-delay: .3s;
				top:5px;
			}
			.pre-img,.next-img{
				opacity: 1;
				display: flex;
			}
		}
		.mark{
			position: absolute;
			bottom:10px;
			display: inline-block;
			margin:0 auto;
			box-shadow: 0px 0px 7px rgba(0,0,0,0.2), 0px 0px 0px 1px rgba(188,188,188,0.1);
			// height:30px;
			padding:3px 10px;
			font-size:12px;
			border-radius: 12px;
			width:auto;
			background:#fff;
		}
		.pre-img,.next-img{
			position:absolute;
			opacity: 0;
				display: none;
				transition:opacity .2s;
			top:calc(50% - 20px);
		}
		.pre-img{
			left:5px;
		}
		.next-img{
			right:5px;
		}
		.icon{
			float: left;
			color:#fff;
			width:40px;
			height:40px;
			border-radius:50%;
			display: flex;
			justify-content: center;
			align-items: center;
			cursor: pointer;
			user-select: none;
			// margin-right: 10px;
			transform: scale(0.8);
			background:rgba(0,0,0,0.3);
			span{
				font-size:24px;
			}
			&:hover{
			background-color:rgba(80, 77, 77, 0.6);
			box-shadow: 0px 0px 3px rgba(80, 77, 77, 0.6);
			}
			&:active{
			box-shadow: 0px 0px 1px rgba(80, 77, 77, 0.6);
			background-color:rgba(80, 77, 77, 0.75);
			transform:scale(0.79) translate3d(1px,1px,0px) !important;
			}
			.scale-icon{
				font-size:12px;
			}
		}
		.tools{
			position:absolute;
			display: inline-block;
			height:40px;
			top:-50px;
			width:auto;
			right:10px;
			transition:top .2s;

			z-index: 999999;

		}
	}
	::v-deep .el-dialog__body{
		overflow: hidden;
		width:100%;
		height:590px;
    flex:inherit  !important;
    padding-top: 0  !important;
    padding-right: 0  !important;
    padding-bottom: 0  !important;
    padding-left: 0  !important;
	}
 ::v-deep .el-dialog--center{
	overflow-y: hidden;
	width:950px;
 }

 .images-prview-header{
	text-align: left;
 }
 ::v-deep .el-dialog__headerbtn{
	top:15px;
 }
 ::v-deep .el-dialog__header{
	padding: 10px 20px 10px;
 }

</style>
