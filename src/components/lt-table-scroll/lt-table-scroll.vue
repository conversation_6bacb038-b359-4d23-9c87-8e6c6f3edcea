<!-- 滚动加载表格组件 -->
<template>
    <div class="wrap">
        <el-table :stripe="stripe" :height="height" :data="visibleData" style="width: 100%"
            v-load-more.expand="{ func: loadMore, target: '.el-table__body-wrapper', delay: 300 }"
            :load-more-disabled="disabledLoad">
            <!-- 动态列表格 -->
            <el-table-column :width="width[index]" :prop="index" :label="item" v-for="(item, index) in tableHeader"
                :key="index">
            </el-table-column>
        </el-table>
    </div>
</template>
<script>

const debounce = function (func, delay) {
    let timer = null
    return function () {
        if (timer) clearTimeout(timer)
        timer = null
        let self = this
        let args = arguments
        timer = setTimeout(() => {
            func.apply(self, args)
        }, delay)
    }
}
export default {
    data() {
        return {
            visibleCount: 30,
            pageIndex: 1,
            pageSize: 10,
        }
    },
    props: {
        //带斑马纹表格
        stripe: {
            type: Boolean,
            default: false
        },
        height: {
            type: Number,
            default: 300
        },
        //表格列信息
        tableHeader: {
            type: Object,
            required: true
        },
        //表格数据 注意需要增量（第一次是30条 第二次为30+10条....）
        tableData: {
            type: Array,
            required: true
        },
        //用于下拉查询新一页的数据信息 入参（页码，页数大小）
        method: {
            type: Function
        },
        //初始行数（最好不要小于当前页面size，否则滚动下载有失效的可能，且size必须为10的倍数）
        initCount: {
            type: Number,
            required: true
        },
        width: {
            type: Object,
            default: () => ({})
        }
    },
    created() {
        console.info("创建表格")
        this.visibleCount = this.initCount
    },
    computed: {
        // 是否禁止无限加载
        disabledLoad() {
            return false
        },
        visibleData() {
            return this.tableData.slice(0, Math.min(this.tableData.length, this.visibleCount))
        }
    },
    directives: {
        'load-more': {
            bind(el, binding, vnode) {
                const { expand } = binding.modifiers
                // 使用更丰富的功能，支持父组件的指令作用在指定的子组件上
                if (expand) {
                    /**
                     * target 目标DOM节点的类名
                     * distance 减少触发加载的距离阈值，单位为px
                     * func 触发的方法
                     * delay 防抖时延，单位为ms
                     * load-more-disabled 是否禁用无限加载
                     */
                    let { target, distance = 1, func, delay = 200 } = binding.value
                    if (typeof target !== 'string') return
                    let targetEl = el.querySelector(target)
                    if (!targetEl) {
                        console.log('Container Not Found')
                        return
                    }
                    binding.handler = debounce(function () {
                        const { scrollTop, scrollHeight, clientHeight } = targetEl
                        let disabled = el.getAttribute('load-more-disabled')
                        disabled = vnode[disabled] || disabled

                        if (scrollHeight <= scrollTop + clientHeight + distance) {
                            if (disabled) return
                            func && func()
                        }
                    }, delay)
                    targetEl.addEventListener('scroll', binding.handler)
                } else {
                    binding.handler = helper.debounce(function () {
                        const { scrollTop, scrollHeight, clientHeight } = el
                        if (scrollHeight === scrollTop + clientHeight) {
                            binding.value && binding.value()
                        }
                    }, 200)
                    el.addEventListener('scroll', binding.handler),
                        { passive: false }
                }
            },
            unbind(el, binding) {
                let { arg } = binding
                // 使用更丰富的功能，支持父组件的指令作用在指定的子组件上
                if (arg === 'expand') {
                    /**
                     * target 目标DOM节点的类名
                     * offset 触发加载的距离阈值，单位为px
                     * method 触发的方法
                     * delay 防抖时延，单位为ms
                     */
                    const { target } = binding.value
                    if (typeof target !== 'string') return
                    let targetEl = el.querySelector(target)
                    targetEl && targetEl.removeEventListener('scroll', binding.handler)
                    targetEl = null
                } else {
                    el.removeEventListener('scroll', binding.handler)
                    el = null
                }
            }
        }
    },
    methods: {
        async loadMore() {
            console.log('滚动到底部了')
            this.pageIndex = this.pageIndex + 1
            await this.method(this.pageIndex, this.pageSize)
            this.visibleCount += 10
            if (this.visibleCount > this.tableData.length) {
                this.content = '暂无更多数据'
            }
        },
    }
}
</script>

<style>
* {
    touch-action: pan-y;
}
</style>