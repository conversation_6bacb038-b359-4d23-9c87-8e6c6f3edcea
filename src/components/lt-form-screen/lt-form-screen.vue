<template>
  <div class="pd-b5">
    <div v-for="(conditions, i) in screenList" :key="i" class="d-flex">
      <div v-for="(item, index) in conditions" :key="index" class="d-flex a-center pd-b10 pd-r30">
        <!-- 筛选项 -->
        <el-select
          v-model="item.elementKey"
          placeholder="请选择"
          style="width: 200px;"
          clearable
          @change="itemChange($event, item)"
        >
          <el-option
            v-for="(item, index) in items"
            :key="index"
            :label="item.elementTitle"
            :value="item.elementKey"
          />
        </el-select>
        <!-- 筛选项目对应的输入框/选择框... -->
        <div class="mr-lr15" style="width: 215px;">
          <el-select
            v-if="item.elementType === 'select'"
            v-model="item.value"
            :placeholder="'请选择' + getScreenLabel(item.elementKey)"
            clearable
          >
            <el-option
              v-for="(item, index) in item.options"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          <el-date-picker
            v-else-if="item.elementType === 'date-timerange' || item.elementType === 'date-time'"
            v-model="item.value"
            style="width: 100%;"
            type="datetimerange"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="请选择相关信息"
            clearable
          />
<!--          <el-date-picker-->
<!--            v-else-if="item.elementType === 'date-time'"-->
<!--            v-model="item.value"-->
<!--            style="width: 100%;"-->
<!--            type="date"-->
<!--            placeholder="请选择相关信息"-->
<!--            clearable-->
<!--            format="yyyy-MM-dd"-->
<!--            value-format="yyyy-MM-dd"-->
<!--          />-->
          <lt-select-tree
            v-else-if="item.elementType && (item.elementType.includes('select-tree') || item.elementType.includes('userinput'))"
            v-model="item.value"
            :options="treeOptions[item.elementType]"
            :disable-branch-nodes="['select-tree-person', 'userinput'].includes('item.elementType')"
            :placeholder="'请选择' + getScreenLabel(item.elementKey)"
            multiple
          />
          <lt-select-page2
            v-else-if="item.elementType === 'select-page' || item.elementType === 'popup-select'"
            v-model="item.value"
            :api-id="item.bandApi"
            select-label="label"
            select-value="value"
            search-key="value"
            clearable
            :placeholder="'请选择' + getScreenLabel(item.elementKey)"
          />
          <el-select
            v-else-if="item.elementType === 'node-select'"
            v-model="item.value"
            placeholder="请选择状态"
            clearable
          >
            <el-option
              v-for="(item, index) in statuList"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          <!-- <div v-else-if="item.elementType === 'popup-select'">
            <div @click="$refs['pageDialogRef'+i+'-'+index][0].open()">
              <el-input v-model="item.value" :placeholder="'请选择'+getScreenLabel(item.elementKey)" clearable />
            </div>
            <lt-page-dailog
              :ref="'pageDialogRef'+i+'-'+index"
              v-model="item.value"
              :api="item.bandApi"
              :title="'请选择'+getScreenLabel(item.elementKey)"
              :column-data="item.options"
              :model-key="item.elementJson.options.popupSelectValue"
            />
          </div> -->
          <el-input v-else v-model="item.value" :placeholder="'请输入'+getScreenLabel(item.elementKey)" clearable />
        </div>
        <!-- 关系 -->
        <el-select
          v-model="item.relation"
          style="width: 110px;"
          placeholder="或者/并且"
        >
          <el-option
            v-for="(item, index) in relationList"
            :key="index"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <i
          v-if="conditions.length > 1 || screenList.length > 1"
          class="el-icon-remove font-20 mr-l10 pointer"
          style="color: #ff4949;"
          @click="deleteCondition(i, index)"
        />
        <template v-if="i === screenList.length - 1 && index === conditions.length-1">
          <i class="el-icon-circle-plus font-20 mr-l10 pointer" style="color: var(--primary-color);" @click="addCondition" />
        </template>
      </div>
    </div>
    <div style="position: absolute;right: 2%;top:0">
      <el-button type="primary" class="mr-l20" @click="query">查询</el-button>
      <el-button class="mr-l20" plain @click="reset">重置</el-button>
    </div>

  </div>
</template>

<script>
import { tableShowItem } from '@/api/flows/process'
import { getStatus } from '@/api/equipment/partChange'
import { executeInterface } from '@/api/interfaces/interfaces'
import { treeselect } from '@/api/system/dept'

export default {
  name: 'LtFormScreen',
  props: {
    subKey: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      screenList: [],
      conditions: {
        elementKey: '', // 选中的项目key
        elementType: '', // 选中的项目类型
        value: undefined, // 选中的项目对应输入的值
        relation: 'or', // 关系(或/且)
        options: [], // select的选项
        bandApi: '', // 下拉分页的请求
        condition: '',
        elementJson: {}
      },
      relationList: [
        { label: '或者', value: 'or' },
        { label: '并且', value: 'and' }
      ],
      items: [],
      statuList:[],
      treeOptions: {
        'select-tree-dept': [], // 部门树
        'select-tree-person': [], // 人员树
        'userinput': [] // 人员树
      }
    }
  },
  mounted() {
    this.screenList = [[{ ...this.conditions }]]
    // 部门树
    treeselect({ dataType: 1 }).then(response => {
      this.treeOptions['select-tree-dept'] = response.data
    })

    // 人员树
    executeInterface({
      apiId: '7daad577d565457f84fe17ebb3291587'
    }).then(res => {
      this.treeOptions['select-tree-person'] = res.data
      this.treeOptions['userinput'] = res.data
    })

    getStatus({key: this.subKey}).then(res=>{
      // console.log(res.data)
      let status = []
      res.data.map(e => {
        status.push({"label":e, "value": e})
      })
      status.push({"label":"已完成", "value": "已完成"})
      this.statuList=status
    })
  },
  methods: {
    getTableShowItemData() {
      return new Promise((resolve, reject) => {
        tableShowItem({ aKey: this.subKey }).then(response => {
          this.items = response.data
          // console.log(this.items)
          let relationDict = {}
          this.items.forEach(item => {
            item.elementTitle = item.elementTitle.replace(/\s*/g, '')
            // 无任何用处,供测试时使用
            relationDict[item.bandKey] = item.elementTitle
          })
          // console.log(relationDict)

          this.items.push({
            elementTitle: '提交人',
            elementKey: 'commit_user_id',
            elementType: 'select-tree-person',
            relation: 'in',
            value: '',
            tableOrder: true
          },
          {
            elementTitle: '提交车间',
            elementKey: 'commit_dept_id',
            elementType: 'select-tree-dept',
            bandDataIdsApi: "7c21ca9c7c0b49589a5564ca0033c8f4",
            relation: 'in',
            value: '',
            tableOrder: true
          },
          {
            elementTitle: ' 提交时间 ',
            elementKey: 'commit_time',
            elementType: 'date-timerange',
            relation: 'between',
            value: [],
            tableOrder: true
          },
          {
            elementTitle: ' 业务节点 ',
            elementKey: 'curr_node_status',
            elementType: 'node-select',
            relation: 'between',
            value:'',
            tableOrder: false
          })
          resolve(response.data)
        }).catch(error => {
          // 失败调用reject函数
          reject(error)
        })
      })
    },
    /**
     * 筛选项变化
     * @param {Object} e
     * @param {Object} item
     */
    itemChange(e, item) {
      // console.log(item)
      let { bandApi, elementType, elementSelection, elementJson } = this.items.find(item => item.elementKey === e) || {}
      item.value = undefined
      item.elementType = elementType
      item.bandApi = bandApi
      elementJson&&(item.elementJson = JSON.parse(elementJson))
      // 根据选择的类型设置condition的值
      let dictData = {
        'select': 'eq',
        'select-page': 'eq',
        'input': 'like'
      }
      item.condition = dictData[elementType]
      if (elementType === 'select') {
        // 优先取api
        if (bandApi) {
          executeInterface({
            apiId: bandApi
          }).then(res => {
            item.options = res.data || []
            this.condition = JSON.parse(elementSelection)
          })

          return
        }
        // 其次取elementSelection
        try {
          item.options = JSON.parse(elementSelection)
        } catch (e) {
          item.options = []
        }
      }

      if (elementType === 'popup-select') {
        item.options = JSON.parse(elementSelection)
      }

    },
    /**
     * 重置
     */
    reset() {
      this.screenList.forEach(item => {
        item.forEach(item2 => {
          item2.elementKey = ''
          item2.elementType = ''
          item2.value = undefined
          item2.relation = 'or'
          item2.options = []
          item2.bandApi = ''
          item2.condition = ''
        })
      })
    },
    /**
     * 查询
     */
    query() {
      this.$emit('query', this.getConditions())
    },
    /**
     * 获取筛选条件
     */
    getConditions() {
      return this.screenList.reduce((a, b) => a.concat(b)).filter(item => item.elementKey && item.value && item.value.length > 0).map(item => {
        return {
          elementKey: item.elementKey,
          value: item.value,
          relation: item.relation,
          condition: item.condition
        }
      })
    },
    /**
     * 添加条件
     */
    addCondition() {
      if (this.screenList[this.screenList.length - 1].length === 2) {
        this.screenList.push([{ ...this.conditions }])
      } else {
        this.screenList[this.screenList.length - 1].push({ ...this.conditions })
      }
      this.$forceUpdate()
    },
    /**
     * 删除条件
     * @param {Object} i
     * @param {Object} index
     */
    deleteCondition(i, index) {
      if (this.screenList[i].length === 2) {
        this.screenList[i].splice(index, 1)
      } else {
        this.screenList.splice(i, 1)
      }

      // 重新排序
      this.conditionChange()
    },
    /**
     * 条件的数据重新排序
     */
    conditionChange() {
      const count = 2
      let arr = []
      let arrItem = []

      this.screenList.reduce((a, b) => a.concat(b)).forEach(item => {
        arrItem.push(item)
        if (arrItem.length === count) {
          arr.push(arrItem)
          arrItem = []
        }
      })

      if (arrItem.length) arr.push(arrItem)

      this.screenList = JSON.parse(JSON.stringify(arr))
    },
    /**
     * 获取筛选项的label
     * @param {Object} value
     */
    getScreenLabel(value) {
      try {
        if (!value) return '相关信息'
        return this.items.find(item => item.elementKey === value).elementTitle
      } catch (e) {
        return '相关信息'
      }
    }
  }
}
</script>
