<template>
  <el-dialog
    :close-on-click-modal="false"
    :visible.sync="dialogVisible"
    v-bind="$attrs"
    :width="width"
    append-to-body
  >
    <el-input v-model="itemName" clearable style="width:300px;margin:0 20px 20px 0" placeholder="请输入名称" />
    <el-button type="primary" @click="query">查询</el-button>
    <el-table
      v-loading="loading"
      :data="tableData"
      highlight-current-row
      style="width: 100%"
      height="360px"
      @current-change="handleCurrentChange"
    >
      <template #empty>
        <lt-empty text="请添加相关数据" />
      </template>
      <el-table-column
        v-for="(item,index) in columnData"
        :key="index"
        :prop="item.value"
        :label="item.label"
      />
    </el-table>
    <lt-pagination
      :total="total"
      :page.sync="current"
      :limit.sync="size"
      :auto="false"
      @pagination="getData"
    />
  </el-dialog>
</template>

<script>
import * as API from '@/api/selectPage/selectPage.js'
import { executeInterface } from '@/api/interfaces/interfaces'
export default {
  name: 'LtPageDailog',
  props: {
    value: null,
    // 请求api
    api: {
      type: String,
      default: ''
    },
    choiceId: {
      type: Number
    },
    mode: {
      type: String,
      default: 'new'
    },
    // 表格需要显示的列数据,必传,不传则显示空
    columnData: {
      type: Array,
      default: () => []
    },
    // 筛选项传递给后端的key值
    searchKey: {
      type: String,
      default: 'itemName'
    },
    // 下拉分页请求的额外参数
    paramObj: {
      type: Object,
      default: () => ({})
    },
    modelKey: {
      type: String,
      default: ''
    },
    modelKeyValue: {
      type: String,
      default: ''
    },
    autoClose: {
      type: Boolean,
      default: true
    },
    width: {
      type: String,
      default: '800px'
    }
  },
  data() {
    return {
      inpitValue: false,
      current: 1, // 当前页码
      size: 10, // 每页的数量
      loading: false,
      dialogVisible: false,
      tableData: [],
      total: 0,
      itemName: '',
      itemCode: ''
    }
  },
  methods: {
    open(params) {
      this.dialogVisible = true
      this.$nextTick(() => {
        this.getData(params)
      })
    },
    query() {
      this.current = 1
      this.getData()
    },
    handleCurrentChange(val) {
      if (val) {
        this.$emit('input', this.modelKey ? val[this.modelKey] : val)
        this.$emit('inputValue', this.$options.propsData.modelKeyValue ? val[this.$options.propsData.modelKeyValue] : val)
        if (val) this.$emit('change', val)
        this.tableData = []
        this.dialogVisible = !this.autoClose
      }
    },
    getData(params) {
      this.loading = true
      let param = {
        pageNum: this.current,
        pageSize: this.size,
        itemCode: this.itemCode,
        choiceId: this.current === 1 ? this.choiceId : ''
      }
      param[this.searchKey] = this.itemName
      param = { ...param, ...this.paramObj,...params }
      if (API[this.api]) {
        API[this.api](param).then(res => {
          this.tableData = eval(this.mode === 'new' ? 'res.data.records' : 'res.rows')
          this.total = parseInt(eval(this.mode === 'new' ? 'res.data.total' : 'res.total'))
        }).finally(e => {
          this.loading = false
        })
      } else {
        executeInterface({ body: param, apiId: this.api }).then(res => {
          if(res.data.records){
            this.tableData = res.data.records
          this.total = res.data.total
          }
        }).finally(() => {
          this.loading = false
        })
      }
    }
  }

}
</script>

<style lang="scss" scoped>
  ::v-deep .el-table {
    border-right: 1px solid #dfe6ec !important;
  }
</style>
