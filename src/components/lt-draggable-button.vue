<template>
  <div>
    <div class="fixed" @click="handleClick()" @mousedown="mousedown($event)" @mouseup="mouseup($event)"
      :style="{
        left: offsetLeft + 'px',
        top: offsetTop + 'px'
      }"
    >
      {{ name }}
    </div>
  </div>
</template>

<script>
export default {
  name: "lt-draggable-button",
  props: {
    name: {
      type: String,
      default: "拖动按钮1",
    },
    x: {
      type: Number,
      default: 0,
    },
    y: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      sb_bkx: 0,
      sb_bky: 0,
      offsetLeft: 0,
      offsetTop: 0,
    };
  },
  mounted() {
    this.offsetLeft = this.x;
    this.offsetTop = this.y;
  },
  methods: {
    mousedown(event) {
      var event = event || window.event;
      var _target = event.target;
      var startx = event.clientX;
      var starty = event.clientY;
      var sb_bkx = startx - event.target.offsetLeft;
      var sb_bky = starty - event.target.offsetTop;
      var ww = document.documentElement.clientWidth;
      var wh = window.innerHeight;
      if (event.preventDefault) {
        event.preventDefault();
      } else {
        event.returnValue = false;
      }
      document.onmousemove = function (ev) {
        var event = ev || window.event;
        // var scrolltop =document.documentElement.scrollTop || document.body.scrollTop;
        if (
          event.clientY < 0 ||
          event.clientX < 0 ||
          event.clientY > wh ||
          event.clientX > ww
        ) {
          return false;
        }
        var endx = event.clientX - sb_bkx;
        var endy = event.clientY - sb_bky;
        _target.style.left = endx + "px";
        _target.style.top = endy + "px";
      };
    },
    mouseup(e) {
      this.offsetLeft = e.target.offsetLeft;
      this.offsetTop = e.target.offsetTop;
      document.onmousemove = null;
    },
    handleClick() {
      const position = {
        x: this.offsetLeft,
        y: this.offsetTop,
      };
      this.$emit("clickBtn", position);
    },
  },
};
</script>
<style lang='scss' scoped>
.fixed {
  position: fixed;
  cursor: move;
  border-radius: 50px;
  background-color: #eaf1ff;
  border-color: #eaf1ff;
  color: var(--primary-color);
  padding: 5px 15px;
  font-size: 14px;
  border-radius: 5px;
}
</style>