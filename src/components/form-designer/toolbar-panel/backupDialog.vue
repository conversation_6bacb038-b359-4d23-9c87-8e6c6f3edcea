<template>
  <el-dialog
    title="历史记录"
    :visible.sync="logDialogFlag"
    v-if="logDialogFlag"
    :show-close="true"
    class="small-padding-dialog"
    center
    v-dialog-drag
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :destroy-on-close="true"
    :append-to-body="true"
    width="95%"
  >
    <div v-loading="logLoading" class="content d-flex" >
      <template v-if="logList.length">
        <div class="box">
          <div
            v-for="(item, index) in logList"
            :key="index"
            :class="{ active: activeIndex === index }"
            @click="detail(index)"
          >
            {{getDateDiff($date.formatDate(item.time, 'yyyy-MM-dd hh:mm:ss') )}}
          </div>
        </div>
        <div class="flex-1 " style="overflow: auto;">
          <!-- <code-editor
            :mode="'json'"
            :readonly="true"
            v-model="formDataJson"
            :key="testKey"
            :maxLines="42"
          /> -->
          <v-form-render :form-data="{}" ref="vForm" :key="Math.random()" :form-json="formDataJson&&JSON.parse(formDataJson) "></v-form-render>
        </div>
      </template>
      <div
        style="width: 100%; height: 100%"
        class="d-flex a-center j-center"
        v-else
      >
        <lt-empty />
      </div>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="copyFormDataJson" v-if="logList.length"
        >复制JSON</el-button
      >
      <el-button type="primary" @click=" $emit('doJsonImport',formDataJson),logDialogFlag = false" v-if="logList.length"
        >替换当前版本</el-button
      >
      <el-button type="" @click="logDialogFlag = false">关闭</el-button>
    </div>
  </el-dialog>
</template>
<script>
import { getBackupDataList, getBackupDataDetail } from '@/api/system/log';
import CodeEditor from '@/components/code-editor/index';
import VFormRender from '@/components/form-render/index';
import { copyToClipboard } from '@/utils/util';
import i18n from '@/utils/i18n';
export default {
  name: 'BackupDialog',
  mixins: [i18n],
  components: {
    CodeEditor,VFormRender
  },
  data() {
    return {
      logDialogFlag: false,
      logLoading: false,
      logList: [],
      activeIndex: -1,
      formDataJson: '',
      testKey: Math.random(),
    };
  },
  methods: {
    getDateDiff(dateStr) {
        let dateTimeStamp= Date.parse(dateStr.replace(/-/gi,"/"));
        let result = '刚刚修改'
        let minute = 1000 * 60;
        let hour = minute * 60;
        let day = hour * 24;
        let month = day * 30;

        let now = new Date().getTime();
        let diffValue = now - dateTimeStamp;
        if (diffValue < 0) {
        //若日期不符则弹出窗口告之
        //alert("结束日期不能小于开始日期！");
        }
        let monthC = diffValue / month;
        let weekC = diffValue / (7 * day);
        let dayC = diffValue / day;
        let hourC = diffValue / hour;
        let minC = diffValue / minute;
        if (monthC >= 1) {
        result = parseInt(monthC) + "个月前";
        } else if (weekC >= 1) {
        result = parseInt(weekC) + "周前";
        } else if (dayC >= 1) {
        result = parseInt(dayC) + "天前";
        } else if (hourC >= 1) {
        result = parseInt(hourC) + "个小时前";
        } else if (minC >= 1) {
        result = parseInt(minC) + "分钟前";
        } else
        result = "刚刚";
        return result;
    },
    open(key) {
      this.logDialogFlag = true;
      this.logLoading = true;
      getBackupDataList({
        pageNum: 1,
        pageSize: 9999999,
        templateId: key,
      }).then((res) => {
        this.logList = res.data.records;
        if (this.logList.length) {
          this.detail(0);
        } else {
          this.logLoading = false;
        }
      });
    },
    detail(index) {
      this.logLoading = true;
      this.activeIndex = index;

      setTimeout(() => {
        getBackupDataDetail(this.logList[index].id).then((res) => {
          this.formDataJson = res.data.json;
          this.testKey = Math.random();
          this.logLoading = false;
        });
      }, 500);
    },
    copyFormDataJson(e) {
      copyToClipboard(
        this.formDataJson,
        e,
        this.$message,
        this.i18nt('designer.hint.copyJsonSuccess'),
        this.i18nt('designer.hint.copyJsonFail'),
      );
    },
  },
};
</script>

<style lang="scss" scoped>
.small-padding-dialog {
  ::v-deep .el-dialog.is-fullscreen {
    //padding-top: 3px;
    //padding-bottom: 3px;
    // background: #f1f2f3;
    left: 0 !important;
    transform: translateX(15px);
  }

  ::v-deep .el-dialog__body {
    padding: 12px 15px 12px 15px;

    .el-alert.alert-padding {
      padding: 0 10px;
    }
  }

  ::v-deep .ace-container {
    border: 1px solid #dcdfe6;
  }
}
.content {
  height: 600px;
  .box {
    width: 150px;
    height: 100%;
    overflow: auto;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    margin-right: 15px;
    & > div {
      padding: 5px 10px;
      border-bottom: 1px solid #e8e8e8;
      cursor: pointer;
      &:hover {
        background-color: #ecf5ff;
      }
      &.active {
        background-color: #ecf5ff;
      }
    }
  }
}

::v-deep .ace-editor {
  height: 600px !important;
}
</style>
