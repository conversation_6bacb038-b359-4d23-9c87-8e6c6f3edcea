<template>
  <div class="toolbar-container">
    <div class="left-toolbar">
      <el-button type="text" :disabled="undoDisabled" :title="i18nt('designer.toolbar.undoHint')" @click="undoHistory">
        <svg-icon icon-class="undo" /></el-button>
      <el-button type="text" :disabled="redoDisabled" :title="i18nt('designer.toolbar.redoHint')" @click="redoHistory">
        <svg-icon icon-class="redo" /></el-button>
           <el-button-group style="margin-left: 20px">
             <el-button
               :type="layoutType === 'PC' ? 'info' : ''"
               @click="changeLayoutType('PC')"
               size="mini"
             >
               {{ i18nt('designer.toolbar.pcLayout') }}</el-button
             >
             <el-button
               :type="layoutType === 'Pad' ? 'info' : ''"
               @click="changeLayoutType('Pad')"
               size="mini"
             >
               {{ i18nt('designer.toolbar.padLayout') }}</el-button
             >
             <el-button
               :type="layoutType === 'H5' ? 'info' : ''"
               @click="changeLayoutType('H5')"
               size="mini"
             >
               {{ i18nt('designer.toolbar.mobileLayout') }}</el-button
             >
           </el-button-group>
      <el-button style="margin-left: 10px" size="mini" :title="i18nt('designer.toolbar.nodeTreeHint')" @click="showNodeTreeDrawer">
        <svg-icon icon-class="node-tree" /></el-button>
    </div>
    <!--  组件层次树  -->
    <el-drawer :title="i18nt('designer.toolbar.nodeTreeTitle')" direction="ltr" :visible.sync="showNodeTreeDrawerFlag" :modal="false" :size="280" :destroy-on-close="true" class="node-tree-drawer">
      <div class="d-flex a-center">
        <el-input placeholder="搜索组件" clearable="" size="mini" v-model="filterText">
        </el-input>
        <i :class="isExpand ? 'el-icon-arrow-up' : 'el-icon-arrow-down'" @click="changeExpand" class="mr-l10" style="cursor: pointer; font-size: 16px" :title="isExpand ? '收缩' : '展开'"></i>
      </div>

      <el-tree ref="nodeTree" :data="nodeTreeData" node-key="id" default-expand-all highlight-current :expand-on-click-node="false" :filter-node-method="filterNode" class="node-tree mr-t10" icon-class="el-icon-arrow-right" @node-click="onNodeTreeClick" draggable @node-drag-end="handleDragEnd" :allow-drop="allowDrop"></el-tree>
    </el-drawer>

    <div class="right-toolbar" style="margin-left: 10px">
      <el-button type="text" @click="clearFormWidget"><i class="el-icon-delete" />{{
          i18nt('designer.toolbar.clear')
        }}</el-button>
      <!--      <el-button type="text" @click="previewForm"-->
      <!--        ><i class="el-icon-view" />{{-->
      <!--          i18nt('designer.toolbar.preview')-->
      <!--        }}</el-button-->
      <!--      >-->
      <el-button type="text" @click="docVisible = true">快捷键</el-button>
      <el-button type="text" @click="$refs.backupDialogRef.open(templateId)">历史</el-button>
      <el-button type="text" @click="oneClickCopy">复制</el-button>
      <el-button type="text" @click="importJson">{{
        i18nt('designer.toolbar.importJson')
      }}</el-button>
      <el-button type="text" @click="exportJson">{{
        i18nt('designer.toolbar.exportJson')
      }}</el-button>
      <el-tooltip class="item" effect="dark" content="必须点击模型保存才能更新其他功能中的数据！！" placement="top-start">
        <el-button type="text" icon="el-icon-warning" style="color: red" @click="refreshModel">模型保存</el-button>
      </el-tooltip>

      <!--      <el-button type="text" @click="generateSave">{{-->
      <!--        i18nt('designer.toolbar.generateSave')-->
      <!--      }}</el-button>-->
      <!--      <el-button type="text" @click="$router.back(-1)">{{-->
      <!--        i18nt('designer.toolbar.exit')-->
      <!--      }}</el-button>-->
    </div>

    <el-dialog title="快捷键" width="80%" v-drag-dialog append-to-body @close="docVisible = false" :visible.sync="docVisible">
      <el-tabs v-model="activeName">
        <el-tab-pane label="快捷键" name="keyboard">
          <el-descriptions title="常用快捷键" border :column="1">
            <el-descriptions-item v-for="item in keyboardList" :key="item.label" :label="item.label">{{ item.value }}</el-descriptions-item>
          </el-descriptions>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>
    <el-dialog :title="i18nt('designer.toolbar.preview')" :visible.sync="showPreviewDialogFlag" v-if="showPreviewDialogFlag" :show-close="true" :close-on-click-modal="false" :close-on-press-escape="false" center v-dialog-drag :destroy-on-close="true" class="small-padding-dialog" width="80%" :fullscreen="isfullscreen">
      <div>
        <el-scrollbar class="side-scroll-bar">
          <div class="form-render-wrapper" :class="[layoutType === 'H5' ? 'h5-layout' : '']">
            <VFormRender ref="preForm" :form-json="formJson" :form-data="testFormData" @appendButtonClick="testOnAppendButtonClick" @buttonClick="testOnButtonClick" @formChange="handleFormChange">
            </VFormRender>
          </div>
        </el-scrollbar>
      </div>

      <code-editor v-model="testFunc" style="display: none"></code-editor>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="isfullscreen = true" v-if="!isfullscreen">
          {{ i18nt('designer.hint.fullscreen') }}</el-button>
        <el-button type="primary" @click="isfullscreen = false" v-else>
          {{ i18nt('designer.hint.cancelfullscreen') }}</el-button>
        <el-button type="primary" @click="getFormData">{{
          i18nt('designer.hint.getFormData')
        }}</el-button>
        <el-button type="primary" @click="resetForm">{{
          i18nt('designer.hint.resetForm')
        }}</el-button>
        <el-button type="primary" @click="setFormDisabled">{{
          i18nt('designer.hint.disableForm')
        }}</el-button>
        <el-button type="primary" @click="setFormEnabled">{{
          i18nt('designer.hint.enableForm')
        }}</el-button>
        <el-button type="" @click="(showPreviewDialogFlag = false), (isfullscreen = false)">{{ i18nt('designer.hint.closePreview') }}</el-button>
      </div>
    </el-dialog>

    <el-dialog :title="i18nt('designer.toolbar.importJson')" :visible.sync="showImportJsonDialogFlag" v-if="showImportJsonDialogFlag" :show-close="true" class="small-padding-dialog" center v-dialog-drag :close-on-click-modal="false" :close-on-press-escape="false" :destroy-on-close="true">
      <el-alert type="info" :title="i18nt('designer.hint.importJsonHint')" show-icon class="alert-padding"></el-alert>
      <code-editor :mode="'json'" :readonly="false" v-model="importTemplate"></code-editor>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="doJsonImport">
          {{ i18nt('designer.hint.import') }}</el-button>
        <el-button @click="showImportJsonDialogFlag = false">
          {{ i18nt('designer.hint.cancel') }}</el-button>
      </div>
    </el-dialog>

    <el-dialog :title="i18nt('designer.toolbar.exportJson')" :visible.sync="showExportJsonDialogFlag" v-if="showExportJsonDialogFlag" :show-close="true" class="small-padding-dialog" center v-dialog-drag :close-on-click-modal="false" :close-on-press-escape="false" :destroy-on-close="true">
      <code-editor :mode="'json'" :readonly="true" v-model="jsonContent"></code-editor>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" class="copy-json-btn" :data-clipboard-text="jsonRawContent" @click="copyFormJson">
          {{ i18nt('designer.hint.copyJson') }}</el-button>
        <el-button @click="saveFormJson">{{
          i18nt('designer.hint.saveFormJson')
        }}</el-button>
        <el-button type="" @click="showExportJsonDialogFlag = false">
          {{ i18nt('designer.hint.closePreview') }}</el-button>
      </div>
    </el-dialog>

    <el-dialog :title="i18nt('designer.hint.exportFormData')" :visible.sync="showFormDataDialogFlag" v-if="showFormDataDialogFlag" :show-close="true" class="dialog-title-light-bg" center v-dialog-drag :close-on-click-modal="false" :close-on-press-escape="false" :destroy-on-close="true" :append-to-body="true">
      <div style="border: 1px solid #dcdfe6">
        <code-editor :mode="'json'" :readonly="true" v-model="formDataJson"></code-editor>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" class="copy-form-data-json-btn" :data-clipboard-text="formDataRawJson" @click="copyFormDataJson">
          {{ i18nt('designer.hint.copyFormData') }}</el-button>
        <el-button @click="saveFormData">{{
          i18nt('designer.hint.saveFormData')
        }}</el-button>
        <el-button type="" @click="showFormDataDialogFlag = false">
          {{ i18nt('designer.hint.closePreview') }}</el-button>
      </div>
    </el-dialog>
    <backupDialog ref="backupDialogRef" @doJsonImport="doJsonImportBack" />
  </div>
</template>

<script>
import VFormRender from '@/components/form-render/index';
import CodeEditor from '@/components/code-editor/index';
import Clipboard from 'clipboard';
import {
  deepClone,
  copyToClipboard,
  generateId,
  getQueryParam,
  traverseAllWidgets,
} from '@/utils/util';
import i18n from '@/utils/i18n';
import { generateCode } from '@/utils/code-generator';
import { genSFC } from '@/utils/sfc-generator';
import loadBeautifier from '@/utils/beautifierLoader';
import { saveAs } from 'file-saver';
import { updateTemplateInfoJson } from '@/api/tool/form';
// import bus from '@/magic-editor/scripts/bus';
import bus from '@/magic-editor/scripts/bus';
import { saveBackupData } from '@/api/system/log';
import backupDialog from './backupDialog.vue';
export default {
  name: 'ToolbarPanel',
  mixins: [i18n],
  components: {
    VFormRender,
    CodeEditor,
    Clipboard,
    backupDialog,
  },
  props: {
    designer: Object,
    templateId: '',
  },
  // inject: ['formObj'],
  data() {
    return {
      showPreviewDialogFlag: false,
      showImportJsonDialogFlag: false,
      showExportJsonDialogFlag: false,
      showExportCodeDialogFlag: false,
      showFormDataDialogFlag: false,
      showExportSFCDialogFlag: false,
      isfullscreen: false,
      testFunc: '',
      importTemplate: '',
      jsonContent: '',
      jsonRawContent: '',

      formDataJson: '',
      formDataRawJson: '',

      vueCode: '',
      htmlCode: '',
      sfcCode: '',
      sfcCodeV3: '',
      filterText: '',
      activeCodeTab: 'vue',
      activeSFCTab: 'vue2',

      testFormData: {
        // 'userName': '666888',
        // 'productItems': [
        //   {'pName': 'iPhone12', 'pNum': 10},
        //   {'pName': 'P50', 'pNum': 16},
        // ]
      },
      nodeTreeData: [],
      showNodeTreeDrawerFlag: false,
      timer: null,
      isExpand: true,
      docVisible: false,
      activeName: 'keyboard',
      keyboardList: [
        { label: 'Ctrl+s', value: '保存当前视图' },
        { label: 'Alt+c', value: '复制组件' },
        { label: 'Alt+v', value: '粘贴组件' },
        { label: 'Alt+z', value: '撤回一步操作' },
        { label: 'Alt+y', value: '前进一步操作' },
        { label: 'Alt+↑', value: '选中父节点' },
        { label: 'Alt+↓', value: '选中第一个子节点' },
        { label: 'Alt+←', value: '选中上一个节点' },
        { label: 'Alt+→', value: '选中下一个节点' },
        { label: 'Delete', value: '删除当前选中节点' },
      ],
    };
  },
  computed: {
    formJson() {
      return {
        widgetList: JSON.parse(JSON.stringify(this.designer.widgetList)),
        formConfig: JSON.parse(JSON.stringify(this.designer.formConfig)),
      };
    },

    undoDisabled() {
      return !this.designer.undoEnabled();
    },

    redoDisabled() {
      return !this.designer.redoEnabled();
    },

    layoutType() {
      return this.designer.getLayoutType();
    },
  },
  mounted() {
    document.addEventListener('keydown', (e) => {
      if (e.altKey && e.keyCode == 90) {
        this.undoHistory();
      } else if (e.altKey && e.keyCode == 89) {
        this.redoHistory();
      }
    });
    this.scrollerHeight = window.innerHeight - 200 + 'px';
    // 点击从预览切换编辑时候触发的事件
    bus.$on('switch-view-status', () => {
      let widgetList = deepClone(this.designer.widgetList);
      let formConfig = deepClone(this.designer.formConfig);
      bus.$emit(
        'view-change',
        JSON.stringify({ widgetList, formConfig }, null, '  '),
        false,
      );
    });
    // magic头部触发事件
    bus.$on('view-import', this.importJson);
    bus.$on('view-export', this.exportJson);
    bus.$on('view-all-clear', this.clearFormWidget);
    if (localStorage.getItem('templateType') === '3') {
      this.designer.changeLayoutType('H5');
    }
    if (!bus._events['shortcutKeys']) {
      bus.$on('shortcutKeys', (e) => {
        if ((e.ctrlKey || e.metaKey) && e.key === 's') {
          //  执行save方法
          //this.generateSave();
          e.preventDefault();
        }
      });
    }
  },
  beforeDestroy() {
    document.removeEventListener('keydown');
  },
  watch: {
    filterText(val) {
      this.$refs.nodeTree.filter(val);
    },
  },
  methods: {
    allowDrop(draggingNode, dropNode, type) {
      if (
        [
          'tab-pane',
          'table-row',
          'table-cell',
          'condition-container-body',
          'condition-container-footer',
          'dialog-body',
          'dialog-footer',
          'collapse-item',
        ].includes(draggingNode.data.type) ||
        ['table-row'].includes(dropNode.data.type)
      ) {
        return false;
      }
      if (dropNode.data.type == 'grid') {
        if (draggingNode.data.type == 'grid-col') {
          return (
            type == 'inner' && draggingNode.data.parentId == dropNode.data.id
          );
        } else {
          return type == 'prev' || type == 'next';
        }
      }
      if (draggingNode.data.type == 'grid-col') {
        if (type == 'inner') {
          return (
            dropNode.data.type == 'grid' &&
            draggingNode.data.parentId == dropNode.data.id
          );
        } else {
          return (
            dropNode.data.type == 'grid-col' &&
            draggingNode.data.parentId == dropNode.data.parentId
          );
        }
      }
      if (dropNode.data.type == 'grid-col') {
        if (draggingNode.data.type == 'grid-col') {
          return (
            draggingNode.data.parentId == dropNode.data.parentId &&
            (type == 'prev' || type == 'next')
          );
        }
        return type == 'inner';
      }

      if (
        [
          'tab-pane',
          'table-cell',
          'condition-container-body',
          'condition-container-footer',
          'dialog-body',
          'dialog-footer',
          'collapse-item',
        ].includes(dropNode.data.type)
      ) {
        return type == 'inner';
      }
      if (type == 'inner') {
        return dropNode.data.hasOwnProperty('children');
      }
      return true;
    },
    handleDragEnd(draggingNode, dropNode, dropType, ev) {
      this.handleSort();
    },
    handleSort() {
      let widgetList = [];
      let fun = (arr, res) => {
        arr.forEach((item) => {
          let widget = JSON.parse(JSON.stringify(this.findWidgetById(item.id)));
          if (widget.widgetList && widget.widgetList.length > 0) {
            widget.widgetList = [];
          }
          if (widget.cols && widget.cols.length > 0) {
            widget.cols = [];
          }
          res.push(widget);
          if (item.children && item.children.length > 0) {
            if (widget.type == 'grid') {
              fun(item.children, widget.cols);
            } else {
              fun(item.children, widget.widgetList);
            }
          }
        });
      };
      fun(this.nodeTreeData, widgetList);
      this.designer.widgetList = widgetList;
      this.designer.emitHistoryChange();
    },
    changeExpand() {
      this.isExpand = !this.isExpand;
      for (
        var i = 0;
        i < this.$refs.nodeTree.store._getAllNodes().length;
        i++
      ) {
        // 根据isExpand， tree展开或折叠
        this.$refs.nodeTree.store._getAllNodes()[i].expanded = this.isExpand;
      }
    },

    filterNode(value, data, node) {
      if (!value) {
        return true;
      }
      let level = node.level;
      let _array = []; //这里使用数组存储 只是为了存储值。
      this.getReturnNode(node, _array, value);
      let result = false;
      _array.forEach((item) => {
        result = result || item;
      });
      return result;
    },
    getReturnNode(node, _array, value) {
      let isPass =
        node.data && node.data.label && node.data.label.indexOf(value) !== -1;
      isPass ? _array.push(isPass) : '';
      if (!isPass && node.level != 1 && node.parent) {
        this.getReturnNode(node.parent, _array, value);
      }
    },
    refreshModel() {
      bus.$emit("updateViewTemplate");
      this.$notify.success({
        message: '保存成功！',
        offset: 30
      })
    },
    oneClickCopy(e) {
      let widgetList = deepClone(this.designer.widgetList);
      let formConfig = deepClone(this.designer.formConfig);
      let data = { widgetList, formConfig };

      copyToClipboard(
        this.$string.dataTemplateReplace(data),
        e,
        this.$message,
        this.i18nt('designer.hint.copyJsonSuccess'),
        this.i18nt('designer.hint.copyJsonFail'),
      );
    },
    buildTreeNodeOfWidget(widget, treeNode) {
      let curNode = {
        id: widget.id,
        label: widget.options.label || widget.options.title || widget.id,
        type: widget.type,
      };
      treeNode.push(curNode);

      if (widget.category === undefined) {
        return;
      }

      curNode.children = [];
      if (widget.type === 'grid') {
        widget.cols.map((col) => {
          let colNode = {
            id: col.id,
            label: col.options.label || col.id,
            children: [],
            type: 'grid-col',
            parentId: widget.id,
          };
          curNode.children.push(colNode);
          col.widgetList &&
            col.widgetList.map((wChild) => {
              this.buildTreeNodeOfWidget(wChild, colNode.children);
            });
        });
      } else if (widget.type === 'table') {
        //TODO: 需要考虑合并单元格！！
        widget.rows.map((row) => {
          let rowNode = {
            id: row.id,
            label: 'table-row',
            selectable: false,
            children: [],
            type: 'table-row',
          };
          curNode.children.push(rowNode);

          row.cols.map((cell) => {
            if (!!cell.merged) {
              //跳过合并单元格！！
              return;
            }

            let rowChildren = rowNode.children;
            let cellNode = {
              id: cell.id,
              label: 'table-cell',
              children: [],
              type: 'table-cell',
            };
            rowChildren.push(cellNode);

            cell.widgetList.map((wChild) => {
              this.buildTreeNodeOfWidget(wChild, cellNode.children);
            });
          });
        });
      } else if (widget.type === 'tab') {
        widget.tabs.map((tab) => {
          let tabNode = {
            id: tab.id,
            label: tab.options.label || tab.id,
            selectable: false,
            children: [],
            type: 'tab-pane',
          };
          curNode.children.push(tabNode);
          tab.widgetList.map((wChild) => {
            this.buildTreeNodeOfWidget(wChild, tabNode.children);
          });
        });
      } else if (widget.type === 'sub-form') {
        widget.widgetList.map((wChild) => {
          this.buildTreeNodeOfWidget(wChild, curNode.children);
        });
      } else if (widget.type === 'trends-tab') {
        widget.widgetList.map((wChild) => {
          this.buildTreeNodeOfWidget(wChild, curNode.children);
        });
      } else if (widget.type === 'collapse') {
        widget.items.map((wChild) => {
          this.buildTreeNodeOfWidget(wChild, curNode.children);
        });
      } else if (widget.category === 'container') {
        //自定义容器
        widget.widgetList.map((wChild) => {
          this.buildTreeNodeOfWidget(wChild, curNode.children);
        });
      }
    },
    findWidgetById(wId) {
      let foundW = null;
      traverseAllWidgets(this.designer.widgetList, (w) => {
        if (w.id === wId) {
          foundW = w;
        }
      });

      return foundW;
    },
    onNodeTreeClick(nodeData, node, nodeEl) {
      //console.log('test', JSON.stringify(nodeData))

      if (nodeData.selectable !== undefined && !nodeData.selectable) {
        this.$message.info(
          this.i18nt('designer.hint.currentNodeCannotBeSelected'),
        );
      } else {
        const selectedId = nodeData.id;
        const foundW = this.findWidgetById(selectedId);
        if (!!foundW) {
          this.designer.setSelected(foundW);
        }
      }
    },
    refreshNodeTree() {
      this.nodeTreeData.length = 0;
      this.designer.widgetList.forEach((wItem) => {
        this.buildTreeNodeOfWidget(wItem, this.nodeTreeData);
      });
    },
    showNodeTreeDrawer() {
      this.refreshNodeTree();
      this.showNodeTreeDrawerFlag = true;
      this.$nextTick(() => {
        if (!!this.designer.selectedId) {
          //同步当前选中组件到节点树！！！
          this.$refs.nodeTree.setCurrentKey(this.designer.selectedId);
        }
      });
    },
    undoHistory() {
      this.designer.undoHistoryStep();
    },

    redoHistory() {
      this.designer.redoHistoryStep();
    },

    changeLayoutType(newType) {
      this.designer.changeLayoutType(newType);
    },

    clearFormWidget() {
      this.designer.clearDesigner();
    },

    previewForm() {
      this.showPreviewDialogFlag = true;
    },

    saveAsFile(fileContent, defaultFileName) {
      this.$prompt(
        this.i18nt('designer.hint.fileNameForSave'),
        this.i18nt('designer.hint.saveFileTitle'),
        {
          inputValue: defaultFileName,
          closeOnClickModal: false,
          inputPlaceholder: this.i18nt(
            'designer.hint.fileNameInputPlaceholder',
          ),
        },
      )
        .then(({ value }) => {
          if (!value) {
            value = defaultFileName;
          }

          if (getQueryParam('vscode') == 1) {
            this.vsSaveFile(value, fileContent);
            return;
          }

          const fileBlob = new Blob([fileContent], {
            type: 'text/plain;charset=utf-8',
          });
          saveAs(fileBlob, value);
        })
        .catch(() => {
          //
        });
    },

    vsSaveFile(fileName, fileContent) {
      const msgObj = {
        cmd: 'writeFile',
        data: {
          fileName,
          code: fileContent,
        },
      };
      window.parent.postMessage(msgObj, '*');
    },

    importJson() {
      this.importTemplate = JSON.stringify(
        this.designer.getImportTemplate(),
        null,
        '  ',
      );
      this.showImportJsonDialogFlag = true;
    },

    doJsonImport() {
      try {
        let importObj = JSON.parse(this.importTemplate);
        this.designer.loadFormJson(importObj);

        this.showImportJsonDialogFlag = false;
        this.$message.success(this.i18nt('designer.hint.importJsonSuccess'));

        this.designer.emitHistoryChange();

        this.designer.emitEvent('form-json-imported', []);
      } catch (ex) {
        this.$message.error(ex + '');
      }
    },

    doJsonImportBack(json) {
      try {
        let importObj = JSON.parse(json || this.importTemplate);
        this.designer.loadFormJson(importObj);

        this.showImportJsonDialogFlag = false;
        this.$message.success(
          this.i18nt(json ? '替换成功' : 'designer.hint.importJsonSuccess'),
        );

        this.designer.emitHistoryChange();

        this.designer.emitEvent('form-json-imported', []);
      } catch (ex) {
        this.$message.error(ex + '');
      }
    },

    exportJson() {
      let widgetList = deepClone(this.designer.widgetList);
      let formConfig = deepClone(this.designer.formConfig);
      this.jsonContent = JSON.stringify({ widgetList, formConfig }, null, '  ');
      this.jsonRawContent = JSON.stringify({ widgetList, formConfig });
      this.showExportJsonDialogFlag = true;
    },

    copyFormJson(e) {
      copyToClipboard(
        this.jsonRawContent,
        e,
        this.$message,
        this.i18nt('designer.hint.copyJsonSuccess'),
        this.i18nt('designer.hint.copyJsonFail'),
      );
    },

    saveFormJson() {
      this.saveAsFile(this.jsonContent, `vform${generateId()}.json`);
    },

    exportCode() {
      this.vueCode = generateCode(this.formJson);
      this.htmlCode = generateCode(this.formJson, 'html');
      this.showExportCodeDialogFlag = true;
    },

    copyVueCode(e) {
      copyToClipboard(
        this.vueCode,
        e,
        this.$message,
        this.i18nt('designer.hint.copyVueCodeSuccess'),
        this.i18nt('designer.hint.copyVueCodeFail'),
      );
    },

    copyHtmlCode(e) {
      copyToClipboard(
        this.htmlCode,
        e,
        this.$message,
        this.i18nt('designer.hint.copyHtmlCodeSuccess'),
        this.i18nt('designer.hint.copyHtmlCodeFail'),
      );
    },

    saveVueCode() {
      this.saveAsFile(this.vueCode, `vform${generateId()}.vue`);
    },

    saveHtmlCode() {
      this.saveAsFile(this.htmlCode, `vform${generateId()}.html`);
    },

    generateSFC() {
      loadBeautifier((beautifier) => {
        this.sfcCode = genSFC(
          this.designer.formConfig,
          this.designer.widgetList,
          beautifier,
        );
        this.sfcCodeV3 = genSFC(
          this.designer.formConfig,
          this.designer.widgetList,
          beautifier,
          true,
        );
        this.showExportSFCDialogFlag = true;
      });
    },
    generateSave() {
      let widgetList = deepClone(this.designer.widgetList);
      let formConfig = deepClone(this.designer.formConfig);
      updateTemplateInfoJson({
        formId: this.$route.params.formId,
        templateJson: JSON.stringify({ widgetList, formConfig }, null, '  '),
      }).then((res) => {
        this.$notify.success({ message: '保存成功' });
        // this.$router.back(-1)
      });
    },
    copyV2SFC(e) {
      copyToClipboard(
        this.sfcCode,
        e,
        this.$message,
        this.i18nt('designer.hint.copySFCSuccess'),
        this.i18nt('designer.hint.copySFCFail'),
      );
    },

    copyV3SFC(e) {
      copyToClipboard(
        this.sfcCodeV3,
        e,
        this.$message,
        this.i18nt('designer.hint.copySFCSuccess'),
        this.i18nt('designer.hint.copySFCFail'),
      );
    },

    saveV2SFC() {
      this.saveAsFile(this.sfcCode, `vformV2-${generateId()}.vue`);
    },

    saveV3SFC() {
      this.saveAsFile(this.sfcCodeV3, `vformV3-${generateId()}.vue`);
    },

    getFormData() {
      this.$refs['preForm']
        .getFormData()
        .then((formData) => {
          this.formDataJson = JSON.stringify(formData, null, '  ');
          this.formDataRawJson = JSON.stringify(formData);

          this.showFormDataDialogFlag = true;
        })
        .catch((error) => {
          this.$message.error(error);
        });
    },

    copyFormDataJson(e) {
      copyToClipboard(
        this.formDataRawJson,
        e,
        this.$message,
        this.i18nt('designer.hint.copyJsonSuccess'),
        this.i18nt('designer.hint.copyJsonFail'),
      );
    },

    saveFormData() {
      this.saveAsFile(this.htmlCode, `formData${generateId()}.json`);
    },

    resetForm() {
      this.$refs['preForm'].resetForm();
    },

    setFormDisabled() {
      this.$refs['preForm'].disableForm();
    },

    setFormEnabled() {
      this.$refs['preForm'].enableForm();
    },

    handleFormChange(fieldName, newValue, oldValue, formModel) {
      console.log('---formChange start---');
      console.log('fieldName', fieldName);
      console.log('newValue', newValue);
      console.log('oldValue', oldValue);
      console.log('formModel', formModel);
      console.log('---formChange end---');
    },

    testOnAppendButtonClick(clickedWidget) {
      console.log('test', clickedWidget);
    },

    testOnButtonClick(button) {
      console.log('test', button);
    },
  },
  created() {
    clearInterval(this.timer);
    // 定时备份当前视图模版
    this.timer = setInterval(() => {
      let widgetList = deepClone(this.designer.widgetList);
      let formConfig = deepClone(this.designer.formConfig);

      saveBackupData({
        json: JSON.stringify({ widgetList, formConfig }, null, '  '),
        module: 'view',
        templateId: this.templateId,
      });
    }, 15000);
    bus.$on('designer-view-preview', () => {
      this.previewForm()
    });
  },
  beforeDestroy() {
    clearInterval(this.timer);
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-drawer__body {
  padding: 0 5px;
}
.toolbar-container {
  padding-top: 3px;
  display: flex;
  //justify-content: space-between;
  align-items: center;
}
::v-deep .side-scroll-bar .el-scrollbar__wrap {
  overflow-x: hidden;
  height: calc(100vh - 220px);
}
// ::v-deep .side-scroll-bar .is-horizontal {
//   display: none;
// }
.left-toolbar {
  font-size: 16px;
  ::v-deep .el-button--text {
    padding: 10px !important;
  }
}

.right-toolbar {
  ::v-deep .el-button--text {
    font-size: 14px !important;
    padding: 10px 0 !important;
  }
}

.el-button i {
  margin-right: 3px;
}

.small-padding-dialog {
  ::v-deep .el-dialog.is-fullscreen {
    //padding-top: 3px;
    //padding-bottom: 3px;
    // background: #f1f2f3;
    left: 0 !important;
    transform: translateX(15px);
  }

  ::v-deep .el-dialog__body {
    padding: 12px 15px 12px 15px;

    .el-alert.alert-padding {
      padding: 0 10px;
    }
  }

  ::v-deep .ace-container {
    border: 1px solid #dcdfe6;
  }
}

.dialog-title-light-bg {
  ::v-deep .el-dialog__header {
    background: #f1f2f3;
  }
}

.no-box-shadow {
  box-shadow: none;
}

.no-padding.el-tabs--border-card {
  ::v-deep .el-tabs__content {
    padding: 0;
  }
}

.form-render-wrapper {
  //height: calc(100vh - 142px);
  all: revert !important; /* 防止表单继承el-dialog等外部样式，未生效，原因不明？？ */
}

.form-render-wrapper.h5-layout {
  margin: 0 auto;
  width: 420px;
  border-radius: 15px;
  //border-width: 10px;
  box-shadow: 0 0 1px 10px #495060;
  height: calc(100vh - 142px);
}
</style>
