/**
 * title: 右键菜单的菜单名称
 * codeBlock: 触发的代码块, #id为占位符带指组件的唯一id
 * widgets: true->所有组件都有  ['input']->只有input才有
 */

const menuData = [
  {
    title: '获取组件的实例对象',
    codeBlock: 'this.getWidgetRef(#id)',
    widgets: true,
  },
  {
    title: '设置组件label值',
    codeBlock: `/**
 * 设置组件label值
 * @param { label } string
 */
this.getWidgetRef(#id).setLabel(label)
`,
    widgets: true,
  },
  {
    title: '获取组件的别名',
    codeBlock: 'this.getWidgetRef(#id).getAlias()',
    widgets: [
      'input',
      'select',
      'cascader',
      'steps',
      'date-range',
      'select-page',
      'tab',
      'time-range',
      'borad',
      'textarea',
      'radio',
      'checkbox',
      'custom-condition',
    ],
  },

  {
    title: '获取组件的值',
    codeBlock: 'this.$getValue(#id)',
    widgets: [
      'input',
      'select',
      'cascader',
      'steps',
      'date-range',
      'select-page',
      'tab',
      'time-range',
      'borad',
      'textarea',
      'radio',
      'custom-condition',
      'transfer',
      'descriptions',
      'rich-editor',
      'progress',
      'checkbox',
      'static-text',
      'pictureupload',
      'fileupload',
      'map',
      'number',
      'tree',
      'time',
      'date',
      'date-time',
      'switch',
      'rate',
      'color',
      'slider',
      'html-text',
      'codeConfig',
      'image',
      'timeline',
      'carousel',
      'qrcode',
      'barCode',
      'data',
      'statisticCard',
      'iteration',
      'picture-upload',
      'file-upload',
      'spcAnalysis',
      'scoringCard',
      'ruleEngine',
    ],
  },
  {
    title: '设置组件的值',
    codeBlock: 'this.$setValue(#id, )',
    widgets: [
      'input',
      'select',
      'cascader',
      'steps',
      'date-range',
      'select-page',
      'tab',
      'time-range',
      'borad',
      'textarea',
      'radio',
      'custom-condition',
      'transfer',
      'descriptions',
      'rich-editor',
      'progress',
      'checkbox',
      'static-text',
      'pictureupload',
      'fileupload',
      'map',
      'number',
      'tree',
      'time',
      'date',
      'date-time',
      'switch',
      'rate',
      'color',
      'slider',
      'html-text',
      'codeConfig',
      'image',
      'timeline',
      'carousel',
      'qrcode',
      'barCode',
      'data',
      'statisticCard',
      'picture-upload',
      'file-upload',
      'spcAnalysis',
      'pointer',
      'custom-steps'
      // 'ruleEngine',
    ],
  },
  {
    title: '设置组件的标题',
    codeBlock: `/**
 * 设置组件的标题
 * @data { data } String
 */
this.getWidgetRef(#id).setTitle(data)
`,
    widgets: ['statisticCard'],
  },
  {
    title: '设置组件的单位',
    codeBlock: `/**
 * 设置组件的单位
 * @data { data } String
 */
this.getWidgetRef(#id).setUnit(data)
`,
    widgets: ['statisticCard'],
  },
  {
    title: '设置统计数据',
    codeBlock: `/**
 * 设置组件的统计数据
 * @data { data } Array
 */
this.getWidgetRef(#id).setStatisticalData([
  { label: 'label1', value: 'value1' },
  { label: 'label2', value: 'value2' },
])
`,
    widgets: ['statistics'],
  },
  {
    title: '设置仪表盘数据',
    codeBlock: `/**
 * 设置仪表盘数据
 * @data { data } Object 字段在组件设置中配置,默认字段为label,value,desc
 */
this.getWidgetRef(#id).setDashboardData(data)
`,
    widgets: ['dashboard'],
  },
  {
    title: '设置组件是否隐藏',
    codeBlock: `/**
 * 设置组件是否隐藏
 * @param { val } Boolean true:隐藏 false:显示
 */
this.getWidgetRef(#id).setHidden(val)
`,
    widgets: true,
  },
  {
    title: '设置组件是否必填',
    codeBlock: `/**
 * 设置组件是否必填
 * @param { val } Boolean true false
 */
this.getWidgetRef(#id).setRequired(val)
`,
    widgets: [
      'input',
      'select',
      'date-range',
      'cascader',
      'radio',
      'checkbox',
      'time-range',
    ],
  },
  {
    title: '校验组件',
    codeBlock: `/**
 * 需配置必填
 * @param { val } str 提示内容
 */

this.getWidgetRef(#id).validate(str)
`,
    widgets: [
      'input',
      'textarea',
      'number',
      'tree,',
      'select-page',
      'time',
      'date',
      'date-time',
      'radio',
      'checkbox',
      'select',
      'date-range',
      'cascader',
      'time-range',
    ],
  },
  {
    title: '组件清除校验',
    codeBlock: `/**
 * @param { flag } boolean 默认为true,如果传入false则只清除校验信息不清除校验规则
 */

this.getWidgetRef(#id).clearValidate(flag)
`,
    widgets: [
      'input',
      'textarea',
      'number',
      'tree,',
      'select-page',
      'time',
      'date',
      'date-time',
      'radio',
      'checkbox',
      'select',
      'date-range',
      'cascader',
      'time-range',
    ],
  },

  {
    title: '数据表格刷新数据',
    codeBlock: `/**
 * 数据表格刷新数据
 * @param { page } Number|Boolean 指定页码,默认为1,传入0或者false则为当前页刷新
 */
this.getWidgetRef(#id).updateRefresh(1)
`,
    widgets: ['data-table'],
  },
  {
    title: '获取表格列属性',
    codeBlock: 'this.getWidgetRef(#id).getCol()',
    widgets: ['data-table'],
  },
  {
    title: '设置表格列属性',
    codeBlock: `/**
    cols数据格式:[{
      label: "", //表头
      prop: "", //字段
      fix: "",  //固定
      align: "",  //文字对齐
      width: "",  //宽度
      editable: true,  //可编辑
      editType: "input",  //编辑方式
      icon: "",  //图标
      code: '',  //自定义html
      overflow: true,   //溢出显示
      link: false,   //链接
      tag: false,    //标签
      total: 0,    //合计
      hidden: false,   //隐藏
      isEdit: true,
    },...]
  */
  this.getWidgetRef(#id).updateTableRow(cols)`,
    widgets: ['data-table'],
  },

  {
    title: '设置表格数据',
    codeBlock: `/**
 * 设置表格数据,可传入第二个参数isPagination是否分页,默认为false
 * @param { data } Array/Object  如果分页,则传入的格式为{records:数据,current:当前页码,size:单页行数,total:数据总数}
 * @param { isPagination } Boolean
 */
this.getWidgetRef(#id).updateTableData(data)`,
    widgets: ['data-table'],
  },
  {
    title: '获取表格当前选中的数据',
    codeBlock: 'this.getWidgetRef(#id).getCurrentSelectData()',
    widgets: ['data-table'],
  },
  {
    title: '新增通过主键获取表格数据',
    codeBlock: 'this.getWidgetRef(#id).getByPrimaryData(id)',
    widgets: ['data-table'],
  },
  {
    title: '新增通过主键更新表格数据',
    codeBlock: 'this.getWidgetRef(#id).updateByPrimaryData(data)',
    widgets: ['data-table'],
  },
  {
    title: '新增通过主键删除表格数据',
    codeBlock: 'this.getWidgetRef(#id).delByPrimaryData(id)',
    widgets: ['data-table'],
  },
  {
    title: '设置加载动画状态',
    codeBlock: `/**
 * 设置加载动画状态
 * @param { val } Boolean true:开启 false:关闭
 */
this.getWidgetRef(#id).setLoading(val)
`,
    widgets: ['data-table', 'button', 'custom-tree', 'gantt'],
  },

  {
    title: '设置表格列标签字典',
    codeBlock:
      'this.getWidgetRef(#id).setDictMap({"列属性": {"类型一": "success", "类型二":"error"}})',
    widgets: ['data-table'],
  },
  {
    title: '清除表格当前选中数据',
    codeBlock: 'this.getWidgetRef(#id).clearCurrentSelectData()',
    widgets: ['data-table'],
  },

  {
    title: '设置组件是否只读',
    codeBlock: `/**
 * 设置组件是否只读
 * @param { val } Boolean true:只读 false:可写
 */
this.getWidgetRef(#id).setReadonly(val)
`,
    widgets: ['input', 'select', 'date-range', 'cascader', 'time-range'],
  },
  {
    title: '设置组件是否禁用',
    codeBlock: `/**
 * 设置组件是否禁用
 * @param { val } Boolean true:禁用 false:不禁用
 */
this.getWidgetRef(#id).setDisabled(val)
`,
    widgets: [
      'input',
      'select',
      'radio',
      'checkbox',
      'date-range',
      'cascader',
      'button',
      'time-range',
    ],
  },
  {
    title: '获取组件是否禁用',
    codeBlock: `/**
 * 获取组件是否禁用
 * Boolean true:禁用 false:不禁用
 */
this.getWidgetRef(#id).getDisabled()
`,
    widgets: [
      'input',
      'select',
      'radio',
      'checkbox',
      'date-range',
      'cascader',
      'button',
      'time-range',
    ],
  },
  {
    title: '获取当前选择的对象',
    codeBlock: 'this.getWidgetRef(#id).getSelectItem()',
    widgets: ['select', 'select-page', 'radio', 'checkbox', 'custom-steps'],
  },
  {
    title: '设置选项',
    codeBlock: 'this.getWidgetRef(#id).reloadOptions(options)',
    widgets: ['select', 'select-page', 'checkbox', 'radio', 'custom-steps'],
  },
  {
    title: '设置选项(会清空已选择的内容)',
    codeBlock: 'this.getWidgetRef(#id).loadOptions(options)',
    widgets: ['select', 'select-page', 'cascader', 'checkbox', 'radio'],
  },
  {
    title: '报表打印',
    codeBlock: `/*** 报表打印
 ****    使用示例下
let reportParam = {
  pageNum: 1,
  pageSize: 10,
  name: '张三',
  age: 10
}
this.getWidgetRef(#id).printReport(reportParam)
 */
let reportParam = {}
this.getWidgetRef(#id).printReport(reportParam)
`,
    widgets: ['tablePrint'],
  },
  {
    title: '报表合并打印',
    codeBlock: `/*** 报表打印
 * @param { queryParams }   参数说明见下
    {
     "param": [
        {
          "报表参数key": "报表参数值",
          "reportUid": "报表code2"  reportUid:报表uid
        },
        {
          "user_id": "报表参数值",
          "reportUid": "报表uid2"  reportUid:报表uid
        }
     ]
    }
 */
this.getWidgetRef(#id).allPrintList({param:[{"报表参数名":"报表code私有参数值","reportUid":"报表uid1"},{"user_id":"报表uid2私有参数值","reportUid":"报表uid2"}]})
`,
    widgets: ['tablePrint'],
  },
  {
    title: '初始化报表',
    codeBlock: `this.getWidgetRef(#id).previewDataDefault()`,
    widgets: ['tablePrint'],
  },
  {
    title: '打印',
    codeBlock: 'this.getWidgetRef(#id).print()',
    widgets: ['codeConfig'],
  },
  {
    title: '打开弹窗',
    codeBlock: `this.getWidgetRef(#id).setDialogVisible(true, () => {
  // 可以通过return的形式设置弹窗中表单组件的值
  // object 由表单组件的组件别名组成的键值对
  // return {}
})`,
    widgets: ['dialog'],
  },
  {
    title: '关闭弹窗',
    codeBlock: 'this.getWidgetRef(#id).setDialogVisible(false, () => {})',
    widgets: ['dialog'],
  },
  {
    title: '设置子组件值',
    codeBlock: `/**
 * 设置容器中表单组件的值(可在setDialogVisible中进行return以调用)
 * @param { data } Object 由表单组件的组件别名组成的键值对
 * @param { flag } Boolean data中不存在的key是否清空值, 默认为true
 */
this.getWidgetRef(#id).setCategoryValue({})
`,
    widgets: ['dialog', 'grid', 'drawer'],
  },
  {
    title: '检验子组件内容',
    codeBlock: `/**
 * 检验子组件内容
 * data 父组件内字段的对象
 * msg 失败提示内容和失败字段
 */
this.getWidgetRef(#id).validate().then(data => {
  console.log(data)
}).catch(msg => {
  console.error(msg)
})
`,
    widgets: ['dialog', 'grid', 'drawer'],
  },

  {
    title: '清空子组件内容',
    codeBlock: 'this.getWidgetRef(#id).clear()',
    widgets: ['dialog', 'grid', 'drawer'],
  },
  {
    title: '清除校验提示',
    codeBlock: 'this.getWidgetRef(#id).clearValidate()',
    widgets: ['dialog', 'grid', 'drawer'],
  },
  {
    title: '获取页码',
    codeBlock: 'this.getWidgetRef(#id).getPageNum()',
    widgets: ['data-table'],
  },
  {
    title: '获取单页数据量',
    codeBlock: 'this.getWidgetRef(#id).getPageSize()',
    widgets: ['data-table'],
  },
  {
    title: '设置表格选中的值(多选)',
    codeBlock: `/**
 * 设置表格选中的值
 * @param { rowIds } Array 由表格数据中的主键组成的一维数组
 * @param { isSelectionChange } boolean 是否触发表格的多选选中回调,默认为触发
 */
this.getWidgetRef(#id).setCurrentSelectData(rowIds, true)
`,
    widgets: ['data-table'],
  },
  {
    title: '重置组件值',
    codeBlock: `/**
 * 设置表格选中的值
 * @param { flag } Boolean 是否校验 默认false 不检验
 */
this.getWidgetRef(#id).resetField()
`,
    widgets: ['input'],
  },
  {
    title: '设置下拉框的值',
    codeBlock: `/**
 * 设置下拉框的值
 * @param { val } Array 例: [1,2]  '1,2'  '1, 2'   支持以上三种格式
 */
this.getWidgetRef(#id).setSelectValue(val)
`,
    widgets: ['select', 'select-page'],
  },
  {
    title: '设置左侧默认选中',
    codeBlock: 'this.getWidgetRef(#id).setLeftChecked(val)',
    widgets: ['transfer'],
  },
  {
    title: '设置右侧默认选中',
    codeBlock: 'this.getWidgetRef(#id).setRightChecked(val)',
    widgets: ['transfer'],
  },
  {
    title: '设置数据源',
    codeBlock: 'this.getWidgetRef(#id).setData(val)',
    widgets: ['transfer', 'spcAnalysis'],
  },
  {
    title: '图表初始化',
    codeBlock: 'this.getWidgetRef(#id).initChart(option)',
    widgets: ['chart'],
  },
  {
    title: '图表显示空布局',
    codeBlock: 'this.getWidgetRef(#id).showEmpty()',
    widgets: ['chart'],
  },
  {
    title: '设置是否禁用',
    codeBlock: 'this.getWidgetRef(#id).setIsDisable(flag)',
    widgets: ['dialog'],
  },
  {
    title: '获取表格数据',
    codeBlock: 'this.getWidgetRef(#id).getData()',
    widgets: ['data-table'],
  },
  {
    title: '设置树的选中',
    codeBlock: `/**
 * 设置树的选中
 * @param { val } Array 传入由node-key组成的一维数组,空数组则为清空
 * @param { flag } Boolean 是否选中下级 默认false 不选中
 */
this.getWidgetRef(#id).setCheckedKeys(val)
`,
    widgets: ['custom-tree'],
  },
  {
    title: '获取树的选中',
    codeBlock: 'this.getWidgetRef(#id).getCheckedKeys()',
    widgets: ['custom-tree'],
  },
  {
    title: '获取选中简单对象',
    codeBlock: `
this.getWidgetRef(#id).getCascaderCheckItem()
`,
    widgets: ['cascader'],
  },
  {
    title: '获取选中对象',
    codeBlock: `
this.getWidgetRef(#id).getCascaderCheckObjItem()
`,
    widgets: ['cascader'],
  },
  {
    title: '新增一行到表头',
    codeBlock: `/**
 * 新增一行到表头
 * @param { data } Object 表格标准数据 例如: {name: '王小虎'}
 */
this.getWidgetRef(#id).addSubFormFirstRow(data).then(() => {})
`,
    widgets: ['sub-form', 'data-table'],
  },
  {
    title: '新增一行到表尾',
    codeBlock: `/**
 * 新增一行到表尾
 * @param { data } Object 表格标准数据 例如: {name: '王小虎'}
 */
this.getWidgetRef(#id).addSubFormLastRow(data).then(() => {})
`,
    widgets: ['sub-form', 'data-table'],
  },
  {
    title: '新增一行数据',
    codeBlock: `/**
 * 新增一行数据
 * @param { data } Object 表格标准数据 例如: {name: '王小虎'}
 * @param { index } Number 新增到那个位置, index下标
 */
this.getWidgetRef(#id).addSubFormRow(data, index).then(rowId => {})
`,
    widgets: ['sub-form', 'data-table'],
  },
  {
    title: '修改一行数据',
    codeBlock: `/**
 * 修改一行数据
 * @param { index } Number 新增到那个位置， index下标
 * @param { data } Object 表格标准数据 例如: {name: '王小虎'}
 */
this.getWidgetRef(#id).setSubFormRow(index, data).then(rowId => {})
`,
    widgets: ['sub-form', 'data-table'],
  },
  {
    title: '删除一行数据',
    codeBlock: `/**
 * 删除一行数据
 * @param { index } Number index下标
 */
this.getWidgetRef(#id).delSubFormRow(index)
`,
    widgets: ['sub-form', 'data-table'],
  },
  {
    title: '清空子表单数据',
    codeBlock: `this.getWidgetRef(#id).clearFromRowIdData()`,
    widgets: ['sub-form'],
  },
  {
    title: '前进',
    codeBlock: `this.getWidgetRef(#id).advance()`,
    widgets: ['steps'],
  },
  {
    title: '后退',
    codeBlock: `this.getWidgetRef(#id).back()`,
    widgets: ['steps'],
  },
  {
    title: '获取当前tab项',
    codeBlock: `this.getWidgetRef(#id).getCurrentTab()`,
    widgets: ['tab'],
  },
  {
    title: '通过fromId设置视图',
    codeBlock: `this.getWidgetRef(#id).setFormId(formId, {})`,
    widgets: ['vfrom-quote'],
  },
  {
    title: '校验模板内容并获取值',
    codeBlock: `this.getWidgetRef(#id).validate().then(data => {})`,
    widgets: ['vfrom-quote'],
  },
  {
    title: '设置列隐藏',
    codeBlock: `/**
 * 设置列隐藏
 * @param { prop } String 列的prop值
 * @param { state } Boolean   true: 隐藏   false: 显示
 */
this.getWidgetRef(#id).setColHidden(prop, state)`,
    widgets: ['data-table'],
  },
  {
    title: '设置分页单页数据量组',
    codeBlock: `this.getWidgetRef(#id).setPageSizes([20,30,40,50])`,
    widgets: ['data-table'],
  },
  {
    title: '设置行编辑状态',
    codeBlock: `/**
 * 设置行编辑状态
 * @param { row } Object 行数据
 * @param { flag } Boolean  true:编辑状态  false:非编辑状态
 * @param { callback } Function 当状态为false时传入回调函数, obj为编辑后的行数据
 */
this.getWidgetRef(#id).setRowEditable(row, flag, (obj) => {})`,
    widgets: ['data-table'],
  },

  {
    title: '获取展开行内容对象',
    codeBlock: `this.getWidgetRef(#id).getSubTable(0)`,
    widgets: ['data-table'],
  },
  {
    title: '设置树状表格某行展开',
    codeBlock: `/**
 * 设置树状表格某行展开
 * @param { data } Array  由行数据的key组成的一维数组
 * @param flag Boolean 是否展示所有的子级
 */
this.getWidgetRef(#id).setRowsExpansion(data, flag)`,
    widgets: ['data-table'],
  },
  {
    title: '请求数据绑定接口',
    codeBlock: `this.getWidgetRef(#id).sendApi()`,
    widgets: [
      'cascader',
      'select',
      'radio',
      'checkbox',
      'custom-condition',
      'data',
      'descriptions',
      'dropdown',
      'radio',
      'transfer',
      'tree',
      'ruleEngine',
      'custom-steps'
    ],
  },
  {
    title: '图表显示loading',
    codeBlock: `this.getWidgetRef(#id).showLoading()`,
    widgets: ['chart'],
  },
  {
    title: '图表关闭loading',
    codeBlock: `this.getWidgetRef(#id).hideLoading()`,
    widgets: ['chart'],
  },
  {
    title: '获取所引用组件的实例',
    codeBlock: `/**
* @param { name } String  所引用的在线组件的 name 名称
* 注意: 所引用的在线组件的data中必须要有个属性为onlineFlag,值随意
*/
this.getWidgetRef(#id).getCodeWidget(name).then(res => {
  // res为引用组件的ref实例

})`,
    widgets: ['onlineComponents'],
  },

  {
    title: '设置行展开表格(非树表)展开',
    codeBlock: `/**
* 设置行展开表格(非树表)展开
* @param { data } Array  由行数据的key组成的一维数组
*/
this.getWidgetRef(#id).setExpandRow(data)`,
    widgets: ['data-table'],
  },
  {
    title: '生成编码',
    codeBlock: `
this.getWidgetRef(#id).buildNumber()`,
    widgets: ['order-input'],
  },
  {
    title: '设置',
    codeBlock: `
this.getWidgetRef(#id).buildNumber()`,
    widgets: ['order-input'],
  },
  {
    title: '设置科室(当前登录人)的值',
    codeBlock: `
// id Number 科室(当前登录人)的id
this.getWidgetRef(#id).fieldModel = id`,
    widgets: ['deptinput', 'userinput'],
  },
  {
    title: '获取已选字段',
    codeBlock: `this.getWidgetRef(#id).getSelectedFields()`,
    widgets: ['transfer'],
  },
  {
    title: '获取备选字段',
    codeBlock: `this.getWidgetRef(#id).getUnSelectedFields()`,
    widgets: ['transfer'],
  },
  {
    title: '设置迭代次数',
    codeBlock: `/**
* @param { data } array 根据数组长度进行遍历的
* @param { callback } 渲染成功后的回调
* 通过原本的组件id + '-索引'就可以获取到第n个组件的实例对象,然后再进行别的赋值等操作
*/
this.getWidgetRef(#id).setIterationCount(data, () => {

})`,
    widgets: ['iteration'],
  },
  {
    title: '设置卡片展开收缩',
    codeBlock: `/**
 * @param { val } Boolean  true: 展开  false: 收缩
 */
this.getWidgetRef(#id).toggleState(val)`,
    widgets: ['card'],
  },
  {
    title: '设置底部按钮加载状态',
    codeBlock: `/**
 * 设置底部按钮加载状态
 * @param { index }  Number  下标
 * @param { state }  Boolean 状态 true:加载 false:取消
 */
this.getWidgetRef(#id).setLoading(index, state)`,
    widgets: ['dialog'],
  },
  {
    title: '图表重新调整大小',
    codeBlock: 'this.getWidgetRef(#id).resize()',
    widgets: ['chart'],
  },
  {
    title: '表格主动触发行内改变事件',
    codeBlock: `/**
 * 表格主动触发onEditChange
 * @param { val } String|Number  值
 * @param { fieldName } String   字段名
 * @param { rowIndex } Number    所在的行索引
 * @param { colIndex } Number    所在的列索引
 * @param { rowData }  Object    行数据
 * @param { colData }  Object    列的相对属性
 * @param { extra }    Object    额外参数
 * 传什么 onEditChange 那边就接到什么
 */
this.getWidgetRef(#id).triggerEditChange(val, fieldName, rowIndex, colIndex, rowData, colData, extra)`,
    widgets: ['data-table'],
  },
  {
    title: '表格主动触发行内失焦事件',
    codeBlock: `/**
 * 表格主动触发onBlurChange
 * @param { val } String|Number  值
 * @param { fieldName } String   字段名
 * @param { rowIndex } Number    所在的行索引
 * @param { colIndex } Number    所在的列索引
 * @param { rowData }  Object    行数据
 * @param { colData }  Object    列的相对属性
 * @param { extra }    Object    额外参数
 * 传什么 onBlurChange 那边就接到什么
 */
this.getWidgetRef(#id).triggerBlurChange(val, fieldName, rowIndex, colIndex, rowData, colData, extra)`,
    widgets: ['data-table'],
  },
  {
    title: '设置编辑列不可编辑关系',
    codeBlock: `/**
 * 设置编辑列不可编辑关系
 * @param { id } Number|String  当前编辑数据的主键
 * @param { relation } Object  {prop: Boolean} 组成的键值对
 *
 * 例如: {name: true}
 *      就表示name这个字段在当前情况是不能编辑的(禁用,不参与编辑)
 */
this.getWidgetRef(#id).setDisableRelation(id, relation)`,
    widgets: ['data-table'],
  },
  {
    title: '获取树的数据源',
    codeBlock: `this.getWidgetRef(#id).getDataSource()`,
    widgets: ['custom-tree'],
  },
  {
    title: '设置树的数据源',
    codeBlock: `/**
 * 设置树的数据
 * @param { data } Array  树的数据源
 */
this.getWidgetRef(#id).setDataSource(data)`,
    widgets: ['custom-tree'],
  },
  {
    title: '设置条码内容',
    codeBlock: `/**
 * 设置条码内容
 * @param { data } String  条码内容
 */
this.getWidgetRef(#id).initBarcode(data)`,
    widgets: ['barCode'],
  },
  {
    title: '获取当前的所有选项',
    codeBlock: `this.getWidgetRef(#id).getSelectData()`,
    widgets: ['select', 'radio', 'checkbox', 'custom-steps'],
  },
  {
    title: '刷新数据来源',
    codeBlock: `this.getWidgetRef(#id).refresh()`,
    widgets: ['tree'],
  },
  {
    title: '设置容器下所有子节点的属性',
    codeBlock: `/**
 * 设置容器下所有子节点的属性
 * @param { data } config  Object
 * 例如: { disabled: true } 设置所有节点为禁用
 */
this.getWidgetRef(#id).setGridAllNode(config)`,
    widgets: ['grid', 'table'],
  },
  {
    title: '设置样式',
    codeBlock: `this.getWidgetRef(#id).setStyle({
  color: 'red'
})`,
    widgets: ['static-text'],
  },
  {
    title: '组件主动聚焦',
    codeBlock: 'this.getWidgetRef(#id).focus()',
    widgets: ['input'],
  },
  {
    title: '设置弹窗加载状态',
    codeBlock: `/**
    * @param { flag }  Boolean
    */
   this.getWidgetRef(#id).setDialogLoading(flag)`,
    widgets: ['dialog'],
  },
  {
    title: '获取子组件实例对象',
    codeBlock: `/**
  * 获取子组件实例对象
  * @param { name }  String 被引用的PC视图内的组件的唯一名称
  * res 子组件实例对象
  * msg 失败提示内容和失败字段
  */
this.getWidgetRef(#id).getCustomComponentRef(name).then(res => {
  console.log(res)
}).catch(msg => {
  console.error(msg)
})`,
    widgets: ['custom-component', 'vfrom-quote'],
  },
  {
    title: '获取子组件实例对象（多个）',
    codeBlock: `/**
 * 获取子组件实例对象（多个）
 * @param names  Array 被引用的PC视图内的组件的唯一名称组成的数组，用逗号分隔
 * res 子组件实例对象
 * msg 失败提示内容和失败字段
 */
this.getWidgetRef(#id).getCustomComponentRefs(names).then(res => {
  console.log(res)
}).catch(msg => {
  console.error(msg)
})`,
    widgets: ['custom-component', 'vfrom-quote'],
  },
  {
    title: '设置折线图',
    codeBlock: `/**
 * 设置折线图
 * @param { xAxis }  Array    x轴数据
 * @param { yAxis }  Array    y轴数据
 * @param { smooth } Boolean  是否为曲线, 默认为true
 *
 */
this.getWidgetRef(#id).setLineChart({
  xAxis: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
  yAxis: [120, 200, 150, 80, 70, 110, 130]
})`,
    widgets: ['chart'],
  },
  {
    title: '设置多折线图',
    codeBlock: `/**
 * 设置多折线图
 * @param { source }  Array   数据集
 * @param { smooth } Boolean  是否为曲线, 默认为true
 *
 */
this.getWidgetRef(#id).setManyLineChart({
  source: [
    ['product', '2012', '2013', '2014', '2015', '2016', '2017'],
    ['Milk Tea', 56.5, 82.1, 88.7, 70.1, 53.4, 85.1],
    ['Matcha Latte', 51.1, 51.4, 55.1, 53.3, 73.8, 68.7],
    ['Cheese Cocoa', 40.1, 62.2, 69.5, 36.4, 45.2, 32.5],
    ['Walnut Brownie', 25.2, 37.1, 41.2, 18, 33.9, 49.1]
  ]
})`,
    widgets: ['chart'],
  },
  {
    title: '设置柱状图',
    codeBlock: `/**
 * 设置柱图
 * @param { xAxis }  Array    x轴数据
 * @param { yAxis }  Array    y轴数据
 */
this.getWidgetRef(#id).setBarChart({
  xAxis: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
  yAxis: [120, 200, 150, 80, 70, 110, 130]
})`,
    widgets: ['chart'],
  },
  {
    title: '设置多柱状图',
    codeBlock: `/**
 * 设置多柱状图
 * @param { source }  Array   数据集
 */
this.getWidgetRef(#id).setManyBarChart({
  source: [
    ['product', '2012', '2013', '2014', '2015', '2016', '2017'],
    ['Milk Tea', 56.5, 82.1, 88.7, 70.1, 53.4, 85.1],
    ['Matcha Latte', 51.1, 51.4, 55.1, 53.3, 73.8, 68.7],
    ['Cheese Cocoa', 40.1, 62.2, 69.5, 36.4, 45.2, 32.5],
    ['Walnut Brownie', 25.2, 37.1, 41.2, 18, 33.9, 49.1]
  ]
})`,
    widgets: ['chart'],
  },
  {
    title: '设置饼状图',
    codeBlock: `/**
 * 设置饼状图
 * @param { data }    Array    数据
 * @param { radius }  String   半径, 默认: '50%'
 */
this.getWidgetRef(#id).setPieChart({
  data: [
    { value: 1048, name: 'Search Engine' },
    { value: 735, name: 'Direct' },
    { value: 580, name: 'Email' },
    { value: 484, name: 'Union Ads' },
    { value: 300, name: 'Video Ads' }
  ],
  radius: '50%'
})`,
    widgets: ['chart'],
  },
  {
    title: '设置桑基图',
    codeBlock: `/**
 * 设置桑基图
 * @param { title }    String    标题
 * @param { data }    Array    节点数据
 * @param { links }  Array   节点间的关系数据
 * links-source:边的源节点名称的字符串，也支持使用数字表示源节点的索引。
 * links-target:边的目标节点名称的字符串，也支持使用数字表示源节点的索引。
 * links-value:边的数值，可以在力引导布局中用于映射到边的长度。
 */
this.getWidgetRef(#id).setSanKeyChart({
  title:'桑基图',
  data: [
    {
      name: "进入",
      itemStyle: {
        color: '#f18bbf',
        borderColor: '#f18bbf'
      }
    },
    {
      name: "首页",
      itemStyle: {
        color: '#0078D7',
        borderColor: '#0078D7'
      }
    },
    {
      name: "个人中心",
      itemStyle: {
        color: '#3891A7',
        borderColor: '#3891A7'
      }
    },
    {
      name: "购物车",
      itemStyle: {
        color: '#0037DA',
        borderColor: '#0037DA'
      }
    },
    {
      name: "特价",
      itemStyle: {
        color: '#C0BEAF',
        borderColor: '#C0BEAF'
      }
    },
    {
      name: "打折",
      itemStyle: {
        color: '#EA005E',
        borderColor: '#EA005E'
      }
    }
  ],
  links:[{
    source: "进入",
    target: "首页",
    value: 5,
  },
  {
    source: "进入",
    target: "个人中心",
    value: 3,
  },
  {
    source: "进入",
    target: "购物车",
    value: 8,
  },
  {
    source: "首页",
    target: "特价",
    value: 2,
  },
  {
    source: "首页",
    target: "打折",
    value: 3,
  }]
})`,
    widgets: ['chart'],
  },
  {
    title: '回显',
    codeBlock: `/**
 * @param { val }    String    base64字符串
 * @param { isRewrite }  Boolean   是否可重写默认为true
 */
this.getWidgetRef(#id).preview(val,isRewrite)`,
    widgets: ['borad'],
  },
  //   {
  //     title: '根据别名获取实例对象',
  //     codeBlock: `/**
  //  * 根据别名获取实例对象
  //  * @param { alias } String  别名
  //  */
  // this.getWidgetRef(#id).getAliasWidgetRef(alias)`,
  //     widgets: ['vfrom-quote'],
  //   },
  {
    title: '设置选项',
    codeBlock: `/**
 * @param { arr } Array  选项名称组成的一维数组
 */
this.getWidgetRef(#id).setTabs([1,2,3,4])`,
    widgets: ['tab'],
  },
  {
    title: '获取单元格选中数据',
    codeBlock: `this.getWidgetRef(#id).getCellSelectData()`,
    widgets: ['data-table'],
  },
  {
    title: '设置单元格选中数据',
    codeBlock: `/**
 * @param { arr } Array  数据项格式：{rowIndex:行下标,columnIndex:列下标,prop:列字段,value:单元格值}
 */
this.getWidgetRef(#id).setCellSelectData(arr)`,
    widgets: ['data-table'],
  },
  {
    title: '设置标签页是否隐藏',
    codeBlock: `/**
 * @param { id } String  标签页的id
 * @param { flag } Boolean  是否隐藏
 */
this.getWidgetRef(#id).setTabPaneHidden(id,flag)`,
    widgets: ['tab'],
  },
  {
    title: '设置甘特图时间粒度',
    codeBlock: `/**
 * @param { val } Number|String  小时
 */
this.getWidgetRef(#id).setGrain(val)`,
    widgets: ['gantt'],
  },
  {
    title: '重新加载列下拉数据',
    codeBlock: `/**
 * @param { prop } String  列字段
 */
this.getWidgetRef(#id).getColApiByParam(prop)`,
    widgets: ['data-table'],
  },
  {
    title: '手动设置行内编辑下拉框数据源',
    codeBlock: `/**
 手动设置行内编辑下拉框数据源
 * @param { data } Object  数据对象, 为主键为key,数组为value的键值对
   示例1:
      {
        '1': [{lable: '', value: ''}]
        '2':: [{lable: '', value: ''}]
      }
      以上会把主键为1的那一行数据中所有的下拉框的数据源 都设置为同一个

   示例2:
      {
        '1-a': [{lable: '', value: ''}]
        '2-a': [{lable: '', value: ''}]
      }
      以上会把主键为1的那一行数据中 列字段为a 的列的下拉框的数据源进行设置
 */
this.getWidgetRef(#id).setSelectDict(data)`,
    widgets: ['data-table'],
  },
  {
    title: '树数据刷新',
    codeBlock: `/**
 * @param { defaultNodeId } String|Number  节点id, 会自动选择该节点并触发节点点击事件
 */
this.getWidgetRef(#id).sendApi(defaultNodeId = '')`,
    widgets: ['custom-tree'],
  },
  {
    title: '设置甘特图数据',
    codeBlock: `/**
 * @param { data } Array  对象数组，格式如下
   {
    id: 1, // 任务主键,必传
    text: '任务1', // 任务名,必传
    start_date: '2023-11-03 00:00:00', // 开始时间,必传
    end_date: '2023-11-05 00:00:00', // 结束时间,必传
    parent:0, // 父任务id,展示树形结构时传入
    duration: 10, // 时长
    color: 'var(--primary-color)', // 颜色
    ...  可传入其他自定义字段
  },
   @param { range } String/Array  时间范围
 */
this.getWidgetRef(#id).setData(data,range)`,
    widgets: ['gantt'],
  },
  {
    title: '撤回',
    codeBlock: `
this.getWidgetRef(#id).undo()`,
    widgets: ['gantt'],
  },
  {
    title: '前进',
    codeBlock: `
this.getWidgetRef(#id).redo()`,
    widgets: ['gantt'],
  },
  //   {
  //     title: '设置显隐',
  //     codeBlock: `/**
  //  * @param { flag }  Boolean
  //  * @param { callback }  Function
  //  */
  // this.getWidgetRef(#id).setVisible(flag,callback)`,
  //     widgets: ['drawer'],
  //   },
  {
    title: '打开抽屉',
    codeBlock: `this.getWidgetRef(#id).setDrawerVisible(true, () => {
   // 可以通过return的形式设置抽屉中表单组件的值
  // object 由表单组件的组件别名组成的键值对
  // return {}
})`,
    widgets: ['drawer'],
  },
  {
    title: '关闭抽屉',
    codeBlock: 'this.getWidgetRef(#id).setDrawerVisible(false, () => {})',
    widgets: ['drawer'],
  },
  {
    title: '获取搜索内容',
    codeBlock: 'this.getWidgetRef(#id).getSearchKey()',
    widgets: ['data-table'],
  },
  {
    title: '设置搜索内容',
    codeBlock: 'this.getWidgetRef(#id).setSearchKey(key)',
    widgets: ['data-table'],
  },
  {
    title: '报表打印',
    codeBlock: `/*** 报表打印
 * @param { queryParams }   参数说明见下
 *    common?: Object,            // 公共参数
 *    pageNum?: Number,           // 页码
 *    pageSize?: Number,          // 每页的条数
 *    axios?: Object,             // axios请求对象
 *    printConfig?: {
 *      copies?: number;          // 份数
 *      paperSizeType?: number;   // 纸张大小, 见 papersizeTypeEnum
 *      printLayoutType?: number; // 布局类型 0:默认 1:平居中 2:垂直居中 3:水平垂直居中
 *      zoom?: number;            // 缩放比
 *      printGridLines?: Boolean; // 是否显示网格线
 *      printFooterPageNum?: Boolean;   // 是否显示页码
 *      hasHomePageCover?: Boolean;     // 首页是否封面
 *      printMode?: number;       // 0:单面打印 1: 双面打印
 *      orientation?: number;      // 0:横向 1:纵向
 *      margin?: Array;            // 边距, [1, 0.75, 1, 0.75], 解释: [上,右,下,左]
 *      isDialogPreview?: Boolean; // 弹窗预览
 *      isJumpPreview?: Boolean;   // 跳转预览
 *    }
 *    .....
 *
 *  const papersizeTypeEnum = [
 *    {label: '信纸', value: 1},
 *    {label: '小纸信', value: 2},
 *    {label: 'Tabloid', value: 3},
 *    {label: 'PAPER LEDGER', value: 4},
 *    {label: '法律专用纸', value: 5},
 *    {label: 'STATEMENT', value: 6},
 *    {label: 'EXECUTIVE', value: 7},
 *    {label: 'A3', value: 8},
 *    {label: 'A4', value: 9},
 *    {label: 'A4_small', value: 10},
 *    {label: 'A5', value: 11},
 *    {label: 'B4', value: 12},
 *    {label: 'B5', value: 13},
 *    {label: 'P80列', value: 45, feetWidth: 9.5, feetHigh: 11 },
 *    {label: 'P40列', value: -45, feetWidth: 10, feetHigh: 5.5},
 *  ]
 */
this.getWidgetRef(#id).allPrint({})
`,
    widgets: ['reportPrint'],
  },
  {
    title: '报表导出',
    codeBlock: `/*** 报表导出
 * @param { queryParams }   参数说明见下
 *    common?: Object,            // 公共参数
 *    pageNum?: Number,           // 页码
 *    pageSize?: Number,          // 每页的条数
 *    axios?: Object,             // axios请求对象
 *    fileName?. String           // 导出文件名(需要带文件类型后缀),例如: 1.xlsx
 */
this.getWidgetRef(#id).allExport({})
`,
    widgets: ['reportPrint'],
  },
  {
    title: '报表合并打印',
    codeBlock: `/*** 报表打印
 * @param { queryParams }   参数说明见下
    {
     "param": [
        {
          "报表参数key": "报表参数值",
          "reportUid": "报表uid2"  reportUid:报表uid
        },
        {
          "user_id": "报表参数值",
          "reportUid": "报表uid2"  reportUid:报表uid
        }
     ]
    }
 */
this.getWidgetRef(#id).allPrintList({param:[{"报表参数名":"报表code私有参数值","reportUid":"报表uid1"},{"user_id":"报表uid2私有参数值","reportUid":"报表uid2"}]})
`,
    widgets: ['reportPrint'],
  },
  {
    title: '初始化报表',
    codeBlock: `/**
 * 初始化报表
 * @param { extraParam } Object 额外参数,可不传,会在请求中体现
 */
 this.getWidgetRef(#id).previewDataDefault({})`,
    widgets: ['reportPrint'],
  },
  {
    title: '获取echarts实例',
    codeBlock: `/**
 * 获取echarts实例
 * 比如说给echarts增加点击事件
 */
this.getWidgetRef(#id).getChartObj().on('click', (params) => {
  console.log(params);
});`,
    widgets: ['chart'],
  },
  {
    title: '设置标注点',
    codeBlock: `/**
 * 设置标注点
 * @param { markers } Array/Object  传入数组则覆盖，传入对象则新增
 * @param { type } String  标注点类型(circle/triangle/square/customHtml)
 * @param { color } String  标注点颜色
 * @param { size } Number  标注点大小
 * @param { solid } Boolean  标注点是否实心
 * @param { top } Number  标注点顶部距离
 * @param { left } Number  标注点左侧距离
 * @param { html } String  标注点html内容
 */
this.getWidgetRef(#id).setMarkers(markers)`,
    widgets: ['pointer'],
  },
  {
    title: '初始化规则树',
    codeBlock: `/**
 * 初始化规则树
 * @param { data } Array  初始化数据
 * @param { flag } Boolean  是否禁用编辑，默认为false
 */
this.getWidgetRef(#id).initRule([
    [
        {
            "conditions": [
                {
                    "field": "user_id",
                    "operator": "==",
                    "value": "1",
                    "relation": ""
                }
            ],
            "action": [
                {
                    "type": "EXPR",
                    "target": "nick_name",
                    "value": "这是超级管理员",
                    "exprField": "user_id + 3",
                    "exprStr": "用户ID + 3"
                },
                {
                    "type": "SET",
                    "target": "user_name",
                    "value": "3333",
                    "exprField": ""
                },
                {
                    "type": "EXPR",
                    "target": "sex",
                    "value": "",
                    "exprField": "phonenumber * 4 * 5",
                    "exprStr": "手机号码 * 4 * 5"
                }
            ],
            "children": [ ]
        }
    ]
], false);`,
    widgets: ['ruleEngine'],
  },
  {
    title: '初始化规则字段',
    codeBlock: `/**
 * 初始化规则字段
 */
this.getWidgetRef(#id).initFieldOptions([
        { label: '用户ID', type: '', field: 'user_id' },
        { label: '部门ID', type: '', field: 'dept_id' },
        { label: '用户账号', type: '', field: 'user_name' },
        { label: '用户昵称', type: '', field: 'nick_name' },
        { label: '用户邮箱', type: '', field: 'email' },
        { label: '手机号码', type: '', field: 'phonenumber' },
        { label: '用户性别', type: '', field: 'sex' }
      ]);`,
    widgets: ['ruleEngine'],
  },
  {
    title: '初始化评分卡列数据',
    codeBlock: `/**
 * 初始化评分卡列数据
 */
this.getWidgetRef(#id).initCardCols([
  {
    label: '厚度',
    fieldType: '1',
    field: 'thickness',
    attribute: '1',
    option: [],
    isShow: true,
    width: 'auto',
  },
  {
    label: '精度',
    fieldType: '4',
    field: 'accuracy',
    attribute: '1',
    option: [
      { label: '普通', value: 'normal' },
      { label: '较高', value: 'high' },
    ],
    isShow: true,
    width: 150,
  },
  {
    label: '厚度范围',
    fieldType: '1',
    field: 'precision',
    attribute: '3',
    option: [],
    isShow: true,
    width: 'auto',
  },
  {
    label: '测试列',
    fieldType: '2',
    field: 'precision1',
    attribute: '3',
    option: [],
    isShow: false,
    width: 'auto',
  },
])`,
    widgets: ['scoringCard'],
  },
  {
    title: '初始化评分卡表格数据',
    codeBlock: `/**
 * 初始化评分卡表格数据
 * @param { data } Array  初始化数据
 * @param { flag } Boolean  是否禁用编辑，默认为false
 */
this.getWidgetRef(#id).initCardData([
  {
    thickness: {
      fieldType: '1',
      attribute: '1',
      exprStr: ['0.05', '0.10'],
      operator: ['[', ']'],
      exprField: ['0.05', '0.10'],
    },
    accuracy: { fieldType: '4', attribute: '1', value: 'normal' },
    precision: {
      fieldType: '1',
      attribute: '2',
      exprStr: ['厚度 - 0.015', '厚度'],
      operator: ['[', ']'],
      exprField: ['thickness - 0.015', 'thickness'],
    },
    precision1: { fieldType: '2', attribute: '3', value: '' },
  },
  {
    thickness: {
      fieldType: '1',
      attribute: '1',
      exprStr: ['0.10', '0.15'],
      operator: ['(', ']'],
      exprField: ['0.10', '0.15'],
    },
    accuracy: { fieldType: '4', attribute: '1', value: 'high' },
    precision: {
      fieldType: '1',
      attribute: '2',
      exprStr: ['厚度 - 0.03', '厚度'],
      operator: ['[', ']'],
      exprField: ['thickness - 0.03', 'thickness'],
    },
    precision1: { fieldType: '2', attribute: '3', value: '' },
  },
  {
    thickness: {
      fieldType: '1',
      attribute: '1',
      exprStr: ['0.15', '0.25'],
      operator: ['(', ']'],
      exprField: ['0.15', '0.25'],
    },
    accuracy: { fieldType: '4', attribute: '1', value: 'normal' },
    precision: {
      fieldType: '1',
      attribute: '2',
      exprStr: ['厚度 - 0.03', '厚度'],
      operator: ['[', ']'],
      exprField: ['thickness - 0.03', 'thickness'],
    },
    precision1: { fieldType: '2', attribute: '3', value: '' },
  },
  {
    thickness: {
      fieldType: '1',
      attribute: '1',
      exprStr: ['0.25', '0.5'],
      operator: ['(', ']'],
      exprField: ['0.25', '0.5'],
    },
    accuracy: { fieldType: '4', attribute: '1', value: 'normal' },
    precision: {
      fieldType: '1',
      attribute: '2',
      exprStr: ['厚度 - 0.04', '厚度'],
      operator: ['[', ']'],
      exprField: ['thickness - 0.04', 'thickness'],
    },
    precision1: { fieldType: '2', attribute: '3', value: '' },
  },
], false)`,
    widgets: ['scoringCard'],
  },

  {
    title: '获取引用模板中某个组件的值',
    codeBlock: `/**
* 获取引用模板中某个组件的值
* @param { name } 唯一名称或者别名
* @param { #id } 引用模板的id
* @return { data }  返回值
*/
this.getWidgetRef(#id).getVfromWigetValue(name).then(data => {
  console.log(data)
})`,
    widgets: ['vfrom-quote'],
  },
  {
    title: '设置引用模板中某个组件的值',
    codeBlock: `/**
* 设置引用模板中某个组件的值
* @param { name } 唯一名称或者别名
* @param { value } 值
*/
this.getWidgetRef(#id).setVfromWigetValue(name, value)`,
    widgets: ['vfrom-quote'],
  },
  {
    title: '获取引用模板外某个组件的实例对象',
    codeBlock: `/**
* 获取引用模板外某个组件的实例对象
* 注意: 此代码在引用模板引用的PC视图的某个组件中使用
*/
this.getParentWidgetRef('组件id')
`,
    widgets: ['vfrom-quote'],
  },
  {
    title: '引用模板A内部某个组件获取引用模板B内部的组件的值',
    codeBlock: `/**
* 引用模板A内部某个组件获取引用模板B内部的组件的值
* @param { #id } 引用模板B的id
* @param { name } 唯一名称或者别名
* @return { data }  返回值
*/
this.getParentWidgetRef(#id).getVfromWigetValue(name).then(data => {
  console.log(data)
})`,
    widgets: ['vfrom-quote'],
  },
{
    title: '渲染工序图数据',
    codeBlock: `/**
* 将节点数据渲染到工序图画布中
* @param { data } Array 通过'获取工序图数据'接口返回的数据的cells
*/
this.getParentWidgetRef(#id).initGraphData(data)`,
    widgets: ['processRoute'],
  },
{
    title: '获取工序图数据',
    codeBlock: `/**
* 将工序图画布中的节点数据
*/
this.getParentWidgetRef(#id).getGraphData()`,
    widgets: ['processRoute'],
  },
{
    title: '获取工序图数据(树状数据)',
    codeBlock: `/**
* 将工序图画布中的节点数据，二次处理为树状结构的数据，排序字段sort
*/
this.getParentWidgetRef(#id).getGraphTreeData()`,
    widgets: ['processRoute'],
  },  
  {
    title: '初始化变量列表',
    codeBlock: `/**
 * 初始化变量列表
 */
this.getWidgetRef(#id).initFieldOptions([
  {
    label: '厚度',
    field: 'thickness',
  },
  {
    label: '精度',
    field: 'accuracy',
  },
  {
    label: '厚度范围',
    field: 'precision',
  },
  {
    label: '测试列',
    field: 'precision1',
  },
])`,
    widgets: ['expression'],
  },
  {
    title: '获取表达式',
    codeBlock: `/**
 * 获取表达式
 */
this.getWidgetRef(#id).getExpression()`,
    widgets: ['expression'],
  }
];


/**
 * 根据组件的type值来决定有哪些右键菜单
 * @param {*} type
 */
let getContextMenu = (type) => {
  return menuData.filter(
    (item) => item.widgets === true || item.widgets.includes(type),
  );
};

export { getContextMenu };
