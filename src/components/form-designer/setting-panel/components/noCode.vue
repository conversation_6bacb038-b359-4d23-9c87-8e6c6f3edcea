<template>
  <div class="d-flex">
    <div class="leftBox">
      <div
        class="dragItem"
        v-for="(item, index) in configList"
        :key="index"
        draggable="true"
        @dragstart="handleDragstart($event, item)"
      >
        {{ item.name }}
      </div>
    </div>
    <div class="content">
      <el-tree :data="data" node-key="id" default-expand-all draggable>
        <div
          class="flex-1 d-flex"
          slot-scope="{ node, data }"
          @dragover="handleDragover"
          @drop="handleDrop"
          @click="settingNode(data)"
          :cust-id="data.uid"
        >
          <span class="flex-1" :cust-id="data.uid">{{ data.name }}</span>
          <i
            class="el-icon-plus"
            :cust-id="data.id"
            @click.stop="addNode(data.uid)"
          />
          <i
            class="el-icon-minus"
            :cust-id="data.uid"
            @click.stop="delNode(data.uid)"
          />
        </div>
      </el-tree>

      <el-button @click="addNodeFirst" style="width: 100%">新增</el-button>
    </div>
    <div class="flex-1 rightBox">
      <code-editor
        ref="codeEditorRef"
        :mode="'javascript'"
        v-model="generateCode"
        :key="randomKey"
      />
    </div>
    <el-dialog
      v-if="setting"
      v-dialog-drag
      title="配置详情"
      :visible.sync="setting"
      :show-close="true"
      width="20vw"
      class="small-padding-dialog"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :destroy-on-close="true"
    >
      <el-form ref="loginForm">
        <el-form-item label="目标弹窗">
          <el-select v-model="settingData.import[0].point.componentId">
            <el-option :label="item.label" :value="item.id" v-for="item in nodeTreeData"/>
          </el-select>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button type="info">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import CodeEditor from '@/components/code-editor/index';
import componentTree from "./componentTree.vue";

export default {
  name: "NoCode",
  data() {
    return {
      settingData: null,
      setting:false,
      nodeTreeData: [],
      nodeConfig: {
        "f1": {
          "name": "打开弹窗",
          "nodeId": "f1",
          "nodeType": "execute",
          "import": [
            {
              "name": "目标组件",
              "key": "thisComponentId",
              "type": "componentId",
              "suitType": ["component"],
              "point": {
                "type": "component",
                "componentId": null
              }
            }
          ],
          "callbacks": [
            {
              "callbackKey": 'callback',
            }
          ],
          "return": [],
          "content": `this.getWidgetRef($\{thisComponentId}).setDialogVisible(true, () =>{$\{callback}})`
        },
        "f2": {
          "name": "组件赋值",
          "nodeType": "execute",
          "nodeId": "f2",
          "import": [
            {
              "name": "目标组件",
              "key": "thisComponentId",
              "type": "componentId",
              "suitType": ["component"]
            },
            {
              "name": "值",
              "key": "value",
              "type": "value",
              "suitType": ["component", "componentValue", "nodeResult", "constant"]
            }
          ],
          "content": "this.$setValue(${thisComponentId}, ${value})"
        },
        "f3": {
          "name": "请求数据",
          "nodeId": "f3",
          "nodeType": "execute",
          "import": [],
          "content": "this.waitHttp()"
        },
        "f4": {
          "name": "创建变量",
          "nodeId": "4",
          "nodeType": "execute",
          "import": [
            {
              "name": "值",
              "key": "variable",
              "type": "value",
              "suitType": ["component", "componentValue", "nodeResult", "constant"]
            }
          ],
          "content": "${variable}"
        }
      },
      data: [],
      defaultProps: {
        children: 'children',
        label: 'label',
      },
      generateCode: '',
      randomKey: '',
    };
  },
  components: {
    componentTree,
    CodeEditor,
  },
  mounted() {
  },
  computed: {
    configList() {
      var arr = [];
      for (var key in this.nodeConfig) {
        arr.push(this.nodeConfig[key]);
      }
      return arr;
    }
  },
  methods: {
    init(widgetList) {
      this.nodeTreeData.length = 0;
      widgetList.forEach((wItem) => {
        this.buildTreeNodeOfWidget(wItem, this.nodeTreeData);
      });
    },
    buildTreeNodeOfWidget(widget, treeNode) {
      let curNode = {
        id: widget.id,
        label: widget.options.label || widget.type,
        type: widget.type,
        //selectable: true,
      };
      treeNode.push(curNode);

      if (widget.category === undefined) {
        return;
      }
      curNode.children = [];

      if (widget.type === "grid") {
        widget.cols.map((col) => {
          // let colNode = {
          //   id: col.id,
          //   label: col.options.name || widget.type,
          //   type: col.type,
          //   children: [],
          // };
          // curNode.children.push(colNode);
          col.widgetList &&
          col.widgetList.map((wChild) => {
            this.buildTreeNodeOfWidget(wChild, curNode.children);
          });
        });
      } else if (widget.type === "table") {
        //TODO: 需要考虑合并单元格！！
        widget.rows.map((row) => {
          let rowNode = {
            id: row.id,
            label: "table-row",
            selectable: false,
            children: [],
          };
          curNode.children.push(rowNode);

          row.cols.map((cell) => {
            if (!!cell.merged) {
              //跳过合并单元格！！
              return;
            }

            let rowChildren = rowNode.children;
            let cellNode = {
              id: cell.id,
              label: "table-cell",
              children: [],
            };
            rowChildren.push(cellNode);

            cell.widgetList.map((wChild) => {
              this.buildTreeNodeOfWidget(wChild, cellNode.children);
            });
          });
        });
      } else if (widget.type === "tab") {
        widget.tabs.map((tab) => {
          let tabNode = {
            id: tab.id,
            label: tab.options.name || widget.type,
            selectable: false,
            children: [],
            type: tab.type,
          };
          curNode.children.push(tabNode);
          tab.widgetList.map((wChild) => {
            this.buildTreeNodeOfWidget(wChild, tabNode.children);
          });
        });
      } else if (widget.type === "sub-form") {
        widget.widgetList.map((wChild) => {
          this.buildTreeNodeOfWidget(wChild, curNode.children);
        });
      } else if (widget.type === "trends-tab") {
        widget.widgetList.map((wChild) => {
          this.buildTreeNodeOfWidget(wChild, curNode.children);
        });
      } else if(widget.type === "collapse"){
        widget.items.map((wChild) => {
          this.buildTreeNodeOfWidget(wChild, curNode.children);
        });
      }else if (widget.category === "container") {
        //自定义容器
        widget.widgetList.map((wChild) => {
          this.buildTreeNodeOfWidget(wChild, curNode.children);
        });
      }
    },
    addNode(uid) {
      let temp = Math.random();
      let obj = {
        uid: temp,
        name: '执行内容',
      };
      this.$array.handleDFS(this.data, 'children', (item) => {
        if (item.uid == uid) {
          if (item.children) {
            item.children.push(obj);
          } else {
            this.$set(item, 'children', [obj]);
          }
        }
      });
    },
    addNodeFirst() {
      let temp = Math.random();
      let obj = {
        uid: temp,
        name: '执行内容',
      };
      this.data.push(obj);
    },
    delNode(id) {
      this.$array.handleDFS(this.data, 'children', (item) => {
        if (item.children && item.children.length) {
          let i = item.children.findIndex((item) => item.id === id);
          if (i !== -1) {
            item.children.splice(i, 1);
          }
        }
      });
    },
    handleDragstart(e, item) {
      item.uid = Math.random();
      e.dataTransfer.setData('text/plain', JSON.stringify(item));
    },
    handleDragover(e) {
      e.preventDefault();
    },
    handleDrop(e) {
      let target = e.srcElement.getAttribute('cust-id');
      let data = e.dataTransfer.getData('text/plain');
      data = JSON.parse(data);
      console.log('你传递的数据为：', data);

      this.$array.handleDFS(this.data, 'children', (item) => {
        if (item.uid == target) {
          data.uid = item.uid
          this.copyObject(data, item)
        }
      });
      this.$forceUpdate()
      this.handleCodeGenerate();
      console.table(this.data)
    },
    build(target) {
      let targetNodeConfig = this.nodeConfig[target.nodeId]
      let targetScript = targetNodeConfig.content;
      target.import.forEach(item => {
        let rsStr = "";
        if (item.type === 'componentId') {
          if (item.point.type === 'component') {
            rsStr = '\'' + item.point.componentId + '\''
          }
        } else if (item.type === 'value') {
          if (item.point.type === 'componentValue') {
            rsStr = "this.$getValue('" + item.point.componentId + "')"
          } else if (item.point.type === 'constant') {
            if (item.point.valueType === 'string') {
              rsStr = '\'' + item.point.value + '\''
            } else if (item.point.valueType === 'number') {
              rsStr = item.point.value
            } else if (item.point.valueType === 'datetime') {
              rsStr = '\'' + item.point.value + '\''
            }
          } else if (item.point.type === 'nodeResult') {
            rsStr = item.point.uid
          }
        }
        targetScript = targetScript.replace('${' + item.key + '}', rsStr);
      })

      // 回调函数
      if (target.callbacks) {
        target.callbacks.forEach(item => {
          let allBkStr = '\n'
          if (item.targets) {
            item.targets.forEach(tg => {
              allBkStr += this.build(tg) + '\n'
            })
          }
          targetScript = targetScript.replace('${' + item.callbackKey + '}', allBkStr);
        })
      }

      return 'let ' + target.uid + ' = ' + targetScript + '\n'
    },
    convert(targets) {
      let allScript = "";
      for (let i = 0; i < targets.length; i++) {
        allScript += this.build(targets[i])
      }
      return allScript
    },
    copyObject(obj1, obj2) {
      for (let key in obj1) {
        if (obj1.hasOwnProperty(key)) {
          if (Array.isArray(obj2[key])) {
            // 如果 obj2[key] 是一个数组，则递归地将 obj1[key] 复制到数组中的每个元素中
            obj2[key].forEach((item) => {
              this.copyObject(obj1[key], item);
            });
          } else if (typeof obj2[key] === 'object') {
            // 如果 obj2[key] 是一个对象，则递归地将 obj1[key] 复制到该对象中
            this.copyObject(obj1[key], obj2[key]);
          } else {
            // 否则直接将 obj1[key] 的值赋给 obj2[key]
            obj2[key] = obj1[key];
          }
        }
      }
    },
    // 代码生成
    handleCodeGenerate() {

      this.generateCode = this.convert(this.data);
      this.randomKey = Math.random();
      this.$refs.codeEditorRef.format();
    },
    settingNode(data) {
      // this.$set(this,'settingData',data)
      this.settingData = data
      this.setting = true
    }
  },
};
</script>

<style lang="scss" scoped>
.leftBox {
  width: 300px;
  display: flex;
  flex-wrap: wrap;
  height: 400px;
  overflow: auto;
}

.dragItem {
  width: 100px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  text-align: center;
  height: 30px;
  line-height: 30px;
  margin: 10px;
  cursor: pointer;
}

.content {
  width: 300px;
  margin-right: 30px;
}
</style>
