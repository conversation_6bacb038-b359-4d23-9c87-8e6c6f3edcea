<template>
  <div class="componentTree" @contextmenu.prevent :style="{'height':height}">
     <div class="d-flex a-center" style="padding-top: 8px;margin-bottom: 8px;">
        <el-input placeholder="搜索组件" clearable="" size="mini" v-model="filterText" suffix-icon="el-icon-search">
        </el-input>
        <i
          :class="isExpand ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"
          @click="changeExpand"
          class="mr-l10"
          style="cursor: pointer; font-size: 16px"
          :title="isExpand ? '收缩' : '展开'"
        ></i>
      </div>

    <el-tree
      ref="nodeTree"
      :data="nodeTreeData"
      node-key="id"
      :default-expand-all="isExpand"
      highlight-current
      class="node-tree"
      icon-class="el-icon-arrow-right"
      :expand-on-click-node="false"
      @node-click="onNodeTreeClick"
      :filter-node-method="filterNode"
      :indent="10"
    >
      <template #default="{ node }">
        <div @contextmenu.prevent="showMenu($event, node)" style="width: 150px">
          {{ node.label }}
        </div>
      </template>
    </el-tree>
    <treeContextMenu ref="treeContextMenuRef" :menuType="menuType"/>
  </div>
</template>

<script>
import treeContextMenu from "./treeContextMenu.vue";
export default {
  name: "ComponentTree",
  props:{
    menuType: { // 菜单类型，默认是组件
      type: String,
      default: 'component'
    },
    height: { // 树的高度
      type: String,
      default: '570px'
    },
    type: { // 使用类型
      type: String,
      default: ''
    },
  },
  components: {
    treeContextMenu,
  },
  data() {
    return {
      nodeTreeData: [],
      filterText: "",
      isExpand: true,
    };
  },
  watch: {
    filterText(val) {
      this.$refs.nodeTree.filter(val);
    },
  },
  methods: {
    changeExpand() {
      this.isExpand = !this.isExpand;
      for (
        var i = 0;
        i < this.$refs.nodeTree.store._getAllNodes().length;
        i++
      ) {
        // 根据isExpand， tree展开或折叠
        this.$refs.nodeTree.store._getAllNodes()[i].expanded = this.isExpand;
      }
    },

    filterNode(value, data, node) {
      if (!value) {
        return true;
      }
      let level = node.level;
      let _array = []; //这里使用数组存储 只是为了存储值。
      this.getReturnNode(node, _array, value);
      let result = false;
      _array.forEach((item) => {
        result = result || item;
      });
      return result;
    },
     getReturnNode(node, _array, value) {
      let isPass =
        node.data && node.data.label && node.data.label.indexOf(value) !== -1;
      isPass ? _array.push(isPass) : "";
      if (!isPass && node.level != 1 && node.parent) {
        this.getReturnNode(node.parent, _array, value);
      }
    },
    init(widgetList) {
      this.nodeTreeData.length = 0;
      widgetList.forEach((wItem) => {
        this.buildTreeNodeOfWidget(wItem, this.nodeTreeData);
      });
    },
    buildTreeNodeOfWidget(widget, treeNode) {
      let curNode = {
        id: widget.id,
        label: widget.options.label || widget.type,
        type: widget.type,
        //selectable: true,
      };
      treeNode.push(curNode);

      if(widget.type === "data-table" && this.menuType === "event") {
        curNode.children = [];
        let {options} = widget
        if (options.leftBtns.length){
          let leftBtnsMenu = {
            id: 'data-table-leftBtns',
            label: '左侧按钮',
            type: "leftBtnsMenu",
            children: [],
          }

          options.leftBtns.forEach((e) => {
            let btnNode = {
              columnKey: e['cust-id'],
              id: widget.id,
              label: e.name,
              type: "data-table-leftBtns",
              children: [],
            };
            leftBtnsMenu.children.push(btnNode)
          })

          curNode.children.push(leftBtnsMenu)
        }

        if (options.btns.length) {
          let btnsMenu = {
            id: 'data-table-btns',
            label: '操作栏按钮',
            type: "btnsMenu",
            children: [],
          }

          options.btns.forEach((e) => {
            let btnNode = {
              columnKey: e['cust-id'],
              id: widget.id,
              label: e.name,
              type: "data-table-btns",
              children: [],
            }

            btnsMenu.children.push(btnNode)
            
          });
          curNode.children.push(btnsMenu)
        }

        if (options.cols.length) {
          let colsMenu = {
            id: 'data-table-cols',
            label: '列选项',
            type: "colsMenu",
            children: [],
          }

          options.cols.forEach((e) => {
            if((e.other == 'nzlx' && ['link', 'switch'].includes(e.colType)) || (e.other == 'html')){
              let colNode = {
                columnKey: e.id,
                id: widget.id,
                label: e.label,
                type: "data-table-cols",
                other: e.other === 'html' ? 'html' : e.colType,
                children: [],
              }
              colsMenu.children.push(colNode)
            }
          });

          if(colsMenu.children.length){
            curNode.children.push(colsMenu)
          }
        }
      }

      if (widget.category === undefined) {
        return;
      }
      curNode.children = [];
      if (widget.type === "grid") {
        widget.cols.map((col) => {
          // let colNode = {
          //   id: col.id,
          //   label: col.options.name || widget.type,
          //   type: col.type,
          //   children: [],
          // };
          // curNode.children.push(colNode);
          col.widgetList &&
            col.widgetList.map((wChild) => {
              this.buildTreeNodeOfWidget(wChild, curNode.children);
            });
        });
      } else if (widget.type === "table") {
        //TODO: 需要考虑合并单元格！！
        widget.rows.map((row) => {
          let rowNode = {
            id: row.id,
            label: "table-row",
            selectable: false,
            children: [],
          };
          curNode.children.push(rowNode);

          row.cols.map((cell) => {
            if (!!cell.merged) {
              //跳过合并单元格！！
              return;
            }

            let rowChildren = rowNode.children;
            let cellNode = {
              id: cell.id,
              label: "table-cell",
              children: [],
            };
            rowChildren.push(cellNode);

            cell.widgetList.map((wChild) => {
              this.buildTreeNodeOfWidget(wChild, cellNode.children);
            });
          });
        });
      } else if (widget.type === "tab") {
        widget.tabs.map((tab) => {
          let tabNode = {
            id: tab.id,
            // label: tab.options.name || widget.type,
            label: tab.id,
            selectable: false,
            children: [],
            type: tab.type,
          };
          curNode.children.push(tabNode);
          tab.widgetList.map((wChild) => {
            this.buildTreeNodeOfWidget(wChild, tabNode.children);
          });
        });
      } else if (widget.type === "sub-form") {
        widget.widgetList.map((wChild) => {
          this.buildTreeNodeOfWidget(wChild, curNode.children);
        });
      } else if (widget.type === "trends-tab") {
        widget.widgetList.map((wChild) => {
          this.buildTreeNodeOfWidget(wChild, curNode.children);
        });
      } else if(widget.type === "collapse"){
        widget.items.map((wChild) => {
          this.buildTreeNodeOfWidget(wChild, curNode.children);
        });
      }else if (widget.category === "container") {
        //自定义容器
        widget.widgetList.map((wChild) => {
          this.buildTreeNodeOfWidget(wChild, curNode.children);
        });
      }
    },
    async onNodeTreeClick(e) {
      try {
        if (this.type == 'select') {
          this.$message({
            message: "选择成功:" + e.id,
            type: "success",
          });
          this.$emit("nodeClick", e.id);
        } else {
          await navigator.clipboard.writeText(e.id);
          this.$message({
            message: "复制成功:" + e.id,
            type: "success",
          });
        }
      } catch (err) {
        console.error("Failed to copy: ", err);
      }
    },
    showMenu(e, node) {
      this.$refs.treeContextMenuRef.show({
        top: e.clientY,
        left: e.clientX,
        data: node,
      });
    },
  },
};
</script>

<style lang="scss">
.componentTree {
  width: 200px;
  height: 570px;
  margin-right: 10px;
  overflow: auto;
}
</style>
