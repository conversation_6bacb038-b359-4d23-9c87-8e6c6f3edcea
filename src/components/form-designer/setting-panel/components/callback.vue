<template>
  <div
    style="
      position: relative;
      margin: 15px 0;
      padding: 10px;
      border-radius: 5px;
      border: 2px dashed #e8e8e8;
    "
  >
    <el-button
      @click="addRuleItem"
      icon="el-icon-plus"
      style="
        position: absolute;
        padding: 5px;
        top: -15px;
        left: calc(50% - 95px);
        background: white;
        border-color: transparent;
        background: 0 0;
        padding: 2px;
        height: auto;
      "
    >
      添加动作
    </el-button>
    <div v-for="(item, itemIndex) in callbackList" :key="itemIndex">
      <div
        style="
          position: relative;
          margin: 7px 0;
          padding: 8px;
          border-radius: 5px;
          background: #f7f7f9;
        "
      >
        <span
          @click="delRuleItem(itemIndex)"
          class="icon el-icon icon el-icon-delete"
          style="
            cursor: pointer;
            position: absolute;
            right: 0;
            top: -12px;
            padding: 5px;
            border-radius: 50%;
            background: white;
            box-shadow: 0 0 10px #e8e8e8;
          "
        />
        {{ itemIndex + 1 }}
        <el-cascader
          style="width: 190px"
          v-model="item.type"
          :props="{
            value: 'type',
            label: 'name',
            children: 'children',
            checkStrictly: false,
          }"
          :options="ruleActions"
        ></el-cascader>
        <div style="display: inline; margin-left: 10px">
          <!--       选择组件       -->
          <div
            style="display: inline"
            v-if="
              item.type &&
              findComponent(item.type[item.type.length - 1], ruleActions) &&
              findComponent(item.type[item.type.length - 1], ruleActions)
                .setting === 1
            "
          >
            <el-cascader
              style="width: calc(70vw - 550px)"
              v-model="item.component"
              :show-all-levels="false"
              :props="{
                value: 'id',
                label: 'label',
                children: 'children',
                checkStrictly: true,
              }"
              @change="componentChange"
              :options="componentTreeData"
            ></el-cascader>
          </div>
          <div
            style="display: inline"
            v-if="
              item.type &&
              findComponent(item.type[item.type.length - 1], ruleActions) &&
              findComponent(item.type[item.type.length - 1], ruleActions)
                .setting === 2
            "
          >
            <el-radio-group v-model="item.value.type" style="margin-left: 10px">
              <el-radio-button label="d">动态</el-radio-button>
              <el-radio-button label="g">固定</el-radio-button>
            </el-radio-group>
            <div
              v-if="item.value.type == 'g'"
              style="width: 400px; margin-left: 10px; display: inline-block"
            >
              <!--         固定类型直接将选中的组件显示在条件值部分         -->
              <!--                  <component-->
              <!--                      :is="subWidget.type + '-widget'"-->
              <!--                      :widget="subWidget"-->
              <!--                      :designer="designer"-->
              <!--                      :key="subWidget.id"-->
              <!--                      :parent-list="widget.widgetList"-->
              <!--                      :index-of-parent-list="swIdx"-->
              <!--                      :parent-widget="widget"-->
              <!--                  ></component>-->
              <el-input
                v-model="item.value.value"
                placeholder="请输入固定值"
              ></el-input>
            </div>
          </div>
          <!--       选择弹出组件       -->
          <div
            style="display: inline"
            v-if="
              item.type &&
              findComponent(item.type[item.type.length - 1], ruleActions) &&
              findComponent(item.type[item.type.length - 1], ruleActions)
                .setting === 3
            "
          >
            <el-cascader
              style="width: calc(70vw - 550px)"
              v-model="item.component"
              :show-all-levels="false"
              :props="{
                value: 'id',
                label: 'label',
                children: 'children',
                checkStrictly: false,
              }"
              @change="componentChange"
              :options="componentDialogTreeData"
            ></el-cascader>
          </div>
          <!--       选择表格组件       -->
          <div
            style="display: inline"
            v-if="
              item.type &&
              findComponent(item.type[item.type.length - 1], ruleActions) &&
              findComponent(item.type[item.type.length - 1], ruleActions)
                .setting === 6
            "
          >
            <el-cascader
              style="width: calc(70vw - 550px)"
              v-model="item.component"
              :show-all-levels="false"
              :props="{
                value: 'id',
                label: 'label',
                children: 'children',
                checkStrictly: false,
              }"
              @change="componentChange"
              :options="componentDataTableData"
            ></el-cascader>
          </div>
          <!--       选择弹出组件       -->
          <div
            style="display: inline"
            v-if="
              item.type &&
              findComponent(item.type[item.type.length - 1], ruleActions) &&
              findComponent(item.type[item.type.length - 1], ruleActions)
                .setting === 3
            "
          >
          </div>

          <!--       接口调用      -->
          <div
            style="display: inline"
            v-if="
              item.type &&
              findComponent(item.type[item.type.length - 1], ruleActions) &&
              findComponent(item.type[item.type.length - 1], ruleActions)
                .setting === 4
            "
          >
            <el-select>
              <el-option value="接口一" label="接口一"></el-option>
            </el-select>
            <el-button
              size="mini"
              type="primary"
              round
              plain
              style="margin-left: 10px; width: 80px"
              >参数配置</el-button
            >
            <br />
          </div>
          <!--      选择模型          -->
          <div
            style="display: inline"
            v-if="
              item.type &&
              findComponent(item.type[item.type.length - 1], ruleActions) &&
              findComponent(item.type[item.type.length - 1], ruleActions)
                .setting === 5
            "
          >
            <el-select v-model="item.component">
              <el-option
                v-for="tb in setup.tables"
                :key="tb.tableName"
                :value="tb.tableName"
                :label="tb.tableComment"
              ></el-option>
            </el-select>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {};
  },
  computed: {
    setup() {
      return this.$store.state.design;
    },
  },
  props: {
    callbackList: {
      type: Array,
    },
    ruleActions: {
      type: Array,
    },
    componentTreeData: {
      type: Array,
    },
    componentDialogTreeData: {
      type: Array,
    },
    componentDataTableData: {
      type: Array,
    },
  },
  methods: {
    addRuleItem() {
      let item = {
        type: ["field", "hiddenField"],
        component: "",
        value: {
          type: "g",
          value: "",
        },
        children: [],
      };

      this.callbackList.push(item);

      this.$forceUpdate();
    },
    delRuleItem(index) {
      this.callbackList.splice(index, 1);
    },
    findComponent(type, actions) {
      for (let i = 0; i < actions.length; i++) {
        if (actions[i].children && actions[i].children.length > 0) {
          for (let j = 0; j < actions[i].children.length; j++) {
            if (actions[i].children[j].type === type) {
              return actions[i].children[j]
            }
          }
        }
      }
    },
    componentChange(items) {
      console.log(items)
    },
  },
};
</script>

<style>
</style>