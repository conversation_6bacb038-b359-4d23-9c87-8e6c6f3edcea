<template>
  <el-dialog
    :visible.sync="dialogVisible"
    title="参数配置"
    center
    width="70vw"
    :close-on-click-modal="false"
    append-to-body
    @close="close"
  >
    <div class="d-flex">
      <el-scrollbar
        wrap-style="overflow-x:hidden;"
        style="height: 360px"
        class="flex-1"
      >
        <vue-json-pretty
          path="jsonData"
          :data="jsonData"
          @node-click="nodeClick"
        />
      </el-scrollbar>
      <div class="flex-1">
        <template v-if="activeKey">
          <el-form label-width="90px">
            <el-form-item label="属性:" style="font-weight: bold">{{
              activeKey
            }}</el-form-item>
            <el-form-item :label="keysArr.length?'字段:':'表单项:'">
              <el-select v-model="role[activeKey]" v-if="keysArr">
                <el-option
                  v-for="item in keysArr"
                  :key="item"
                  :label="item"
                  :value="item"
                />
              </el-select>
              <el-cascader
                v-else
                v-model="role[activeKey]"
                :options="allFormItem"
                :props="{
                  expandTrigger: 'hover',
                  value: 'id',
                  checkStrictly: true,
                  emitPath: false,
                }"
                placeholder="请选择"
                clearable
                filterable
              />
            </el-form-item>
          </el-form>
        </template>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import VueJsonPretty from "vue-json-pretty";
export default {
  components: {
    VueJsonPretty,
  },
  props: ["allFormItem", "cProps" , 'keysArr'],
  data() {
    return {
      dialogVisible: false, // 关闭开启标识
      activeKey: "", // 当前点击的key
      activeForm: "", // 当前点击节点对应的formId
      jsonData: {},
      index: 0,
      role: {}, // 当前接口的参数配置
    };
  },
  methods: {
    nodeClick(e) {
      this.jsonClick(e.key, e.content);
    },
    /**
     * 打开
     * @param {Object} data
     * @param {Object} index
     */
    open(data, role, index = 0) {
      this.dialogVisible = true;
      this.role = JSON.parse(JSON.stringify(role));
      this.jsonData = data;
      this.index = index;
    },
    /**
     * json串的点击
     * @param {Object} key
     * @param {Object} value
     */
    jsonClick(key, value) {
      this.activeKey = key;
      if (!this.role.hasOwnProperty(key) && value) {
        this.$set(this.role, key, value || "");
      }
    },
    /**
     * 关闭
     */
    close() {
      this.activeKey = "";
      this.dialogVisible = false;
      this.$emit("close", this.role);
    },
  },
};
</script>

<style lang="scss" scoped>

</style>
