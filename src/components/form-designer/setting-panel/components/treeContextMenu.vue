<template>
  <v-contextmenu ref="contextMenuRef" style="max-height: 350px; overflow: auto" v-if="menuType == 'component'">
    <v-contextmenu-item v-for="(item, index) in componentMenuList" :key="index"
      :class="{ 'filterColor': index < 3 && item.clickNum >= 5 }">
      <div @click="analyticExpression($event, item, item.codeBlock)">
        <span> {{ item.title }}</span>
      </div>
    </v-contextmenu-item>
  </v-contextmenu>

  <v-contextmenu ref="ctxEventMenu" style="max-height: 350px; overflow: auto" v-else-if="menuType == 'event'">
    <template >
      <span v-for="(item, index) in eventMenuList" :key="index">
        <template v-if="item.other">
          <v-contextmenu-item  @click="handleEventMenuClick(item)" v-if="item.other.includes(currentData.other)">
            <span> {{ item.label = currentData.other == 'html'?item.label: currentData.other == 'link'? '表格列链接事件': "表格列开关事件"}} </span>
          </v-contextmenu-item>
        </template>
        <template v-else>
          <v-contextmenu-item  @click="handleEventMenuClick(item)" >
            <span> {{ item.label }} </span>
          </v-contextmenu-item>
        </template>
      </span>
    </template>
  </v-contextmenu>

</template>

<script>
import { getContextMenu } from './contextMenuConfig';
import { getEventtMenu } from '@/components/form-render/ruleEvent';
import { copyToClipboard } from '@/utils/util';
import bus from "@/magic-editor/scripts/bus";
export default {
  props:{
    menuType: { // 菜单类型，默认是组件
      type: String,
      default: 'component'
    }
  },
  data() {
    return {
      componentMenuList: [],
      eventMenuList: [], // 事件菜单列表
      currentData: {},
    };
  },
  methods: {
    handleEventMenuClick(item){
      bus.$emit('addEvent', {"item":item, "data":this.currentData})
    },
    show(data) {
      this.currentData = data.data.data;
      if (this.menuType == 'component') { // 组件菜单
        this.componentMenuList = getContextMenu(this.currentData.type);
        if (localStorage.getItem('TreeContextMenuClickNum')) {
          this.componentMenuList.forEach(i => {
            this.$set(i, 'clickNum', 0)
            JSON.parse(localStorage.getItem('TreeContextMenuClickNum')).forEach(e => {
              if (e.key === this.currentData.type + '-' + i.title) {
                i.clickNum = e.value
              }
            })
          })
          this.componentMenuList = this.menuListSort(this.componentMenuList)
        }
        if (!this.componentMenuList.length) return;
        this.$refs.contextMenuRef.show(data);
        setTimeout(() => {
          document.addEventListener('click', this.hide);
        }, 10);
      } else if(this.menuType == 'event') { // 事件菜单
        this.eventMenuList = getEventtMenu(this.currentData.type)
        if (!this.eventMenuList.length) return;
        this.$refs.ctxEventMenu.show(data);
        setTimeout(() => {
          document.addEventListener('click', this.hide);
        }, 10);
      }
    },
    menuListSort(data) {  //处理/排序/去重数据
      if (data.length <= 1) {
        return data
      }
      let filterList = data.filter(item => item.clickNum >= 5).sort((a, b) => {
        if (a.clickNum < b.clickNum) {
          return 1
        } else if (a.clickNum > b.clickNum) {
          return -1
        } else return 0
      })
      if (filterList.length > 3) {
        filterList.forEach((e, index) => {
          if (index > 3) {
            filterList.splice(index, 1)
          }
        })
      }
      let arr = [...filterList, ...data]
      const map = new Map();
      return arr.filter(v => !map.has(v.title) && map.set(v.title, 1));
    },
    hide() {
      document.removeEventListener('click', this.hide);
      if(this.menuType == 'event') { // 事件菜单
        this.$refs.ctxEventMenu.hide();
      }else if(this.menuType == 'component') { // 组件菜单
        this.$refs.contextMenuRef.hide();
      }
    },
    /**
     * 解析表达式
     * @param {*} codeBlock
     */
    analyticExpression(e, item, codeBlock) {
      if (!codeBlock) {
        this.$message({ message: '代码块不能为空' });
        return;
      }
      codeBlock = codeBlock.replace(/#id/g, `'${this.currentData.id}'`);
      copyToClipboard(codeBlock, e, this.$message, '复制成功', '复制失败');
      this.componentMenuList.forEach(i => {
        if (i.title === item.title) {
          i.clickNum = i.clickNum + 1;
        }
      })
      if (localStorage.getItem('TreeContextMenuClickNum')) {
        let componentMenuList = JSON.parse(localStorage.getItem('TreeContextMenuClickNum'))
        let isInclude = false;
        componentMenuList.forEach(e => {
          if (e.key == (this.currentData.type + '-' + item.title)) {
            e.value = e.value + 1;
            isInclude = true;
          }
        })
        if (!isInclude) {
          componentMenuList.push({
            key: this.currentData.type + '-' + item.title,
            value: item.clickNum
          })
        }
        localStorage.setItem('TreeContextMenuClickNum', JSON.stringify(componentMenuList));
      } else {
        localStorage.setItem('TreeContextMenuClickNum', JSON.stringify([{
          key: this.currentData.type + '-' + item.title,
          value: item.clickNum
        }]));
      }
    },
  },
};
</script>
<style>
.v-contextmenu {
  z-index: 9999;
}

.v-contextmenu .v-contextmenu-item.v-contextmenu-item--hover {
  color: white !important;
}

.filterColor {
  color: #5cb6ff !important;
}
</style>
