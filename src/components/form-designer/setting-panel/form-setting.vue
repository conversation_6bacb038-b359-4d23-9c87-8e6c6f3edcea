<template>
  <div>
    <el-form
      :model="formConfig"
      size="mini"
      label-position="left"
      label-width="120px"
      class="setting-form"
      @submit.native.prevent
    >
      <el-collapse v-model="formActiveCollapseNames" class="setting-collapse">
        <el-collapse-item
          name="1"
          :title="i18nt('designer.setting.basicSetting')"
        >
          <el-form-item label="数据表自定义">
            <el-switch @change="custonChange" v-model="isCustom"></el-switch>
          </el-form-item>
          <el-form-item label-width="0">
            <div>
              <el-button style="width: 100%;"  @click="addAuth">添加联添加权限项动规则</el-button>
            </div>
          </el-form-item>

          <el-form-item label-width="0">
            <li
              v-for="(colItem, colIdx) in formConfig.authoritys"
              :key="colIdx"
              class="col-item"
            >
              <div style="float: right">
                <el-button
                  circle
                  plain
                  size="mini"
                  style="float: right"
                  type="danger"
                  @click="delAuth(colIdx)"
                  icon="el-icon-minus"
                ></el-button>
              </div>
              <el-form label-position="left" inline>
                <el-form-item label="权限名:">
                  <el-input
                    size="mini"
                    placeholder="输入权限项名称"
                    v-model="colItem.authName"
                  ></el-input>
                </el-form-item>
                <el-form-item label="权限值:">
                  <el-input
                    size="mini"
                    placeholder="输入权限项唯一code"
                    v-model="colItem.authCode"
                  ></el-input>
                </el-form-item>
              </el-form>
            </li>
          </el-form-item>
          <el-form-item label-width="0" label-position="top">
            <div>
              <el-button style="width: 100%;"  @click="addRule">添加联动规则</el-button>
            </div>
          </el-form-item>
          <el-form-item label-width="0">
            <li
              v-for="(ruleItem, ruleIndex) in formConfig.linkRules"
              :key="ruleIndex"
              class="col-item"
            >
              <template v-if="formConfig.linkRules" >
                <div style="float: right">
                  <el-button
                    circle
                    plain
                    size="mini"
                    style="float: right"
                    type="danger"
                    @click="delRule(ruleIndex)"
                    icon="el-icon-minus"
                  ></el-button>
                </div>
                <div
                  style="cursor: pointer"
                  @click="settingRule(ruleItem, ruleIndex)"
                >
                  <span>{{ ruleItem.ruleName }}</span>
                </div>
              </template>
            </li>
          </el-form-item>

          <el-form-item label-width="0" label-position="top">
            <div>
              <el-button style="width: 100%;"  @click="openEvent">添加界面事件</el-button>
            </div>
          </el-form-item>

          <el-form-item label="弹窗分组">
            <el-switch
              @change="groupDialogChange"
              v-model="groupDialog"
            ></el-switch>
          </el-form-item>
          <el-form-item label="全局变量">
            <el-button
              type="primary"
              icon="el-icon-edit"
              plain
              round
              @click="showStore"
              >配置</el-button
            >
          </el-form-item>
          <el-form-item :label="i18nt('designer.setting.formSize')">
            <el-select v-model="formConfig.size">
              <el-option
                v-for="item in formSizes"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item :label="i18nt('designer.setting.labelPosition')">
            <el-radio-group
              v-model="formConfig.labelPosition"
              class="radio-group-custom"
            >
              <el-radio-button label="left"
                >{{ i18nt("designer.setting.leftPosition") }}
              </el-radio-button>
              <el-radio-button label="top"
                >{{ i18nt("designer.setting.topPosition") }}
              </el-radio-button>
            </el-radio-group>
          </el-form-item>
          <el-form-item :label="i18nt('designer.setting.labelAlign')">
            <el-radio-group
              v-model="formConfig.labelAlign"
              class="radio-group-custom"
            >
              <el-radio-button label="label-left-align"
                >{{ i18nt("designer.setting.leftAlign") }}
              </el-radio-button>
              <el-radio-button label="label-center-align"
                >{{ i18nt("designer.setting.centerAlign") }}
              </el-radio-button>
              <el-radio-button label="label-right-align"
                >{{ i18nt("designer.setting.rightAlign") }}
              </el-radio-button>
            </el-radio-group>
          </el-form-item>
          <el-form-item :label="i18nt('designer.setting.labelWidth')">
            <el-input-number
              v-model="formConfig.labelWidth"
              :min="0"
              style="width: 100%"
            ></el-input-number>
          </el-form-item>
          <el-form-item :label="i18nt('designer.setting.formCss')">
            <el-button
              type="primary"
              icon="el-icon-edit"
              plain
              round
              @click="editFormCss"
              >{{ i18nt("designer.setting.addCss") }}
            </el-button>
          </el-form-item>
          <!-- -->
          <el-form-item :label="i18nt('designer.setting.customClass')">
            <el-select
              v-model="formConfig.customClass"
              multiple
              filterable
              allow-create
              default-first-option
            >
              <el-option
                v-for="(item, idx) in cssClassList"
                :key="idx"
                :label="item"
                :value="item"
              ></el-option>
            </el-select>
          </el-form-item>
          <!-- -->
          <el-form-item :label="i18nt('designer.setting.globalFunctions')">
            <el-button
              type="primary"
              icon="el-icon-edit"
              plain
              round
              @click="editGlobalFunctions"
              >{{ i18nt("designer.setting.addEventHandler") }}
            </el-button>
          </el-form-item>
          <el-form-item label="视图引导配置">
            <el-button type="primary" icon="el-icon-edit" plain round @click="editIntroConfig">
              配置</el-button>
          </el-form-item>
          <el-form-item label-width="0">
            <el-divider class="custom-divider"
              >{{ i18nt("designer.setting.formSFCSetting") }}
            </el-divider>
          </el-form-item>
          <el-form-item :label="i18nt('designer.setting.formModelName')">
            <el-input type="text" v-model="formConfig.modelName"></el-input>
          </el-form-item>
          <el-form-item :label="i18nt('designer.setting.formRefName')">
            <el-input type="text" v-model="formConfig.refName"></el-input>
          </el-form-item>
          <el-form-item :label="i18nt('designer.setting.formRulesName')">
            <el-input type="text" v-model="formConfig.rulesName"></el-input>
          </el-form-item>
          <el-form-item label-width="0">
            <el-divider class="custom-divider">视图弹窗配置
            </el-divider>
          </el-form-item>
          <el-form-item label="宽度(%)">
            <el-input type="number" v-model="formConfig.dialogWidth"></el-input>
          </el-form-item>
          <el-form-item label="底部按钮配置">
            <el-button
              type="info"
              icon="el-icon-edit"
              plain
              round
              @click="dialogVisible = true"
            >
              配置</el-button
            >
          </el-form-item>
          <el-dialog
            title="按钮配置"
            width="80%"
            :visible="dialogVisible"
            :show-close="false"
          >
            <lt-sort-table  :isEnable="true" row-key="cust-id" v-model="formConfig.leftBtns" max-height="500">
              <lt-table-column-edit prop="name" label="名称" />
              <lt-table-column-edit
                prop="type"
                label="类型"
                state="select"
                :selectOpt="{
            options: [
              { label: 'default', value: 'default' },
              { label: 'primary', value: 'primary' },
              { label: 'success', value: 'success' },
              { label: 'warning', value: 'warning' },
              { label: 'danger', value: 'danger' },
              { label: 'info', value: 'info' },

            ],
            label: 'label',
            value: 'value',
          }"
              />
              <lt-table-column-edit prop="icon" label="图标">
                <template #preview="{ row }">
                  <i v-if="row.icon&&row.icon.includes('el-icon')" :class="row.icon"></i>
                  <svg-icon
                    v-else
                    :icon-class="row.icon"
                    :style="{ color: row.color }"
                  />
                </template>
                <template #edit="{ row, $index }">
                  <el-popover
                    placement="bottom-start"
                    width="460"
                    trigger="click"
                    @show="$refs['iconSelect'].reset()"
                  >
                    <IconSelect
                      ref="iconSelect"
                      @selected="selected($event, $index)"
                    />
                    <el-input
                      slot="reference"
                      placeholder="点击选择图标"
                      readonly
                    >
                      <svg-icon
                        v-if="row.icon"
                        slot="prefix"
                        :icon-class="row.icon"
                        class="el-input__icon"
                        style="height: 32px; width: 16px"
                      />
                      <i v-else slot="prefix" class="el-icon-search el-input__icon" />
                    </el-input>
                  </el-popover>
                </template>
              </lt-table-column-edit>
              <lt-table-column-edit prop="color" label="图标颜色">
                <template #header>
                  图标颜色<el-tooltip
                  effect="light"
                  content="支持16进制(例: #000)和rgb(例: rgb(255,255,255))"
                >
                  <i class="el-icon-info" />
                </el-tooltip>
                </template>
                <template #edit="{ row}">
                  <template>
                    <el-color-picker v-model="row.color" show-alpha
                                     :predefine="predefineColors" size="mini"></el-color-picker>
                  </template>
                </template>
              </lt-table-column-edit>
              <lt-table-column-edit prop="fun" label="点击事件" edit-disable>
                <template #preview="{ $index }">
                  <el-link
                    :underline="false"
                    type="primary"
                    @click="showCodeDialog('编写函数代码', $index, 'fun')"
                  >编辑点击事件</el-link
                  >
                </template>
              </lt-table-column-edit>
              <lt-table-column-operation
                ref="operationRef"
                width="160"
                v-model="formConfig.leftBtns"
                :row-data="rowObj"
                :unshift="false"
                validateProp="name, type"
                primary-key="cust-id"
                unlimitedAdd
                @addBefore="addBefore"
                @save="custSave"
                @delete="custDelete"
              />
            </lt-sort-table>

            <div slot="footer" class="dialog-footer">
              <el-button type="primary" @click="checkDialog"> 确认</el-button>
            </div>

            <EventEditor
              ref="eventEditorRef"
              :designer="designer"
              :title="codeInterface.desc"
              :eventHandlerCode="codeInterface.code"
              @saveEventHandler="codeConfirm"
              :key="codeEditorKey"
            >
            </EventEditor>
          </el-dialog>
        </el-collapse-item>

        <el-collapse-item
          name="2"
          :title="i18nt('designer.setting.eventSetting')"
        >
          <el-form-item label="表单创建事件" label-width="150px">
            <el-button
              type="primary"
              icon="el-icon-edit"
              plain
              round
              @click="editFormEventHandler('onFormCreated')"
            >
              {{ i18nt("designer.setting.addEventHandler") }}
            </el-button>
          </el-form-item>
          <el-form-item label="表单挂载事件" label-width="150px">
            <el-button
              type="primary"
              icon="el-icon-edit"
              plain
              round
              @click="editFormEventHandler('onFormMounted')"
            >
              {{ i18nt("designer.setting.addEventHandler") }}
            </el-button>
          </el-form-item>
          <el-form-item label="扫码提交事件" label-width="150px">
            <el-button
              type="primary"
              icon="el-icon-edit"
              plain
              round
              @click="editFormEventHandler('onScanCommit')"
            >
              {{ i18nt("designer.setting.addEventHandler") }}
            </el-button>
          </el-form-item>
          <!-- -->
          <el-form-item label="表单数据改变事件" label-width="150px">
            <el-button
              type="primary"
              icon="el-icon-edit"
              plain
              round
              @click="editFormEventHandler('onFormDataChange')"
            >
              {{ i18nt("designer.setting.addEventHandler") }}
            </el-button>
          </el-form-item>

          <el-form-item label="提交前置方法" label-width="150px">
            <el-button
              type="primary"
              icon="el-icon-edit"
              plain
              round
              @click="editFormEventHandler('onPreCommit')"
            >
              {{ i18nt("designer.setting.addEventHandler") }}
            </el-button>
          </el-form-item>

          <el-form-item label="拦截流程提交" label-width="150px">
            <el-button
              type="primary"
              icon="el-icon-edit"
              plain
              round
              @click="editFormEventHandler('onFormCommit')"
            >
              {{ i18nt("designer.setting.addEventHandler") }}
            </el-button>
          </el-form-item>

          <el-form-item label="表单数据加载完成" label-width="150px">
            <el-button
              type="primary"
              icon="el-icon-edit"
              plain
              round
              @click="editFormEventHandler('onFormDataComplete')"
            >
              {{ i18nt("designer.setting.addEventHandler") }}
            </el-button>
          </el-form-item>
        </el-collapse-item>
      </el-collapse>
    </el-form>

    <EventEditor
      ref="eventEditorRef2"
      :designer="designer"
      :title="i18nt('designer.setting.editFormEventHandler')"
      :eventHeader="'form.' + eventParamsMap[curEventName]"
      :eventHandlerCode="formEventHandlerCode"
      @saveEventHandler="saveFormEventHandler"
      :docFlag="true"
      @showDoc="showEventDocumentDialogFlag = true"
      :key="codeEditorKey2"
    >
    </EventEditor>
    <!-- 开发文档   -->
    <el-dialog
      title="开发文档"
      :visible.sync="showEventDocumentDialogFlag"
      v-if="showEventDocumentDialogFlag"
      :show-close="true"
      class="small-padding-dialog"
      v-dialog-drag
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :destroy-on-close="true"
    >
      <pre>
        <code>
获取指定组件Vue对象 getWidgetRef(widgetId)  widgetId 唯一Id值
数据表格刷新数据 this.getWidgetRef(widgetId).updateRefresh() widgetId 对应数据表的Id
获取组件值  this.getWidgetRef(widgetId).getValue() widgetId 对应数据表的Id,仅对表单组件生效
设置组件值(不会触发onChange this.getWidgetRef(widgetId).setValue(value)  widgetId 对应数据表的Id, value 设置的值
改变组件值(触发onChange) changeValue()
发送http请求 this.getRequest({
  url: 'xxxxxxx',
  method: 'post',
  data: {
    name: 'xxxxx',
    age: 'xxxxx'
  },
  params: {
    name: '12312312'
  }
}).then(res => {
  console.log(res)
}).catch(err => {
  console.log(err)
})
初始化图表数据 this.getWidgetRef(widgetId).initChart(obj)  obj echarts配置项
设置弹窗是否显示 this.getWidgetRef(widgetId).setDialogVisible(bol)   bol 显示与否， true 显示，false 隐藏
消息提示 this.$message({message: "提示内容", type:"success"}) 系统弹窗消息提示
        </code>
      </pre>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="showEventDocumentDialogFlag = false"
          >关闭</el-button
        >
      </div>
    </el-dialog>

    <el-dialog
      :title="i18nt('designer.setting.formCss')"
      :visible.sync="showEditFormCssDialogFlag"
      center
      v-if="showEditFormCssDialogFlag"
      :show-close="true"
      class="small-padding-dialog"
      v-dialog-drag
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :destroy-on-close="true"
    >
      <code-editor
        :mode="'css'"
        :readonly="false"
        v-model="formCssCode"
      ></code-editor>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="showEventDocumentDialogFlag = true">
          {{ i18nt("designer.hint.document") }}
        </el-button>
        <el-button @click="showEditFormCssDialogFlag = false">
          {{ i18nt("designer.hint.cancel") }}
        </el-button>
        <el-button type="primary" @click="saveFormCss">
          {{ i18nt("designer.hint.confirm") }}
        </el-button>
      </div>
    </el-dialog>

    <EventEditor
      ref="eventEditorRef3"
      :designer="designer"
      :title="i18nt('designer.setting.globalFunctions')"
      :eventHandlerCode="functionsCode"
      @saveEventHandler="saveGlobalFunctions"
      :docFlag="true"
      @showDoc="showEventDocumentDialogFlag = true"
      :key="codeEditorKey3"
    >
    </EventEditor>

    <el-dialog
      @close="storeVisible = false"
      :visible="storeVisible"
      width="70%"
      title="全局变量配置"
    >
      <el-button type="primary" class="mr-b10" @click="addKey">新增</el-button>
      <el-table max-height="350" :data="storeArr">
        <el-table-column prop="label" label="变量名" />
        <el-table-column prop="value" label="默认值" />
        <el-table-column prop="remark" label="备注" />
        <el-table-column>
          <template slot-scope="{ row }">
            <el-link
              class="mr-5"
              :underline="false"
              type="primary"
              @click="editKey(row)"
              >编辑</el-link
            >
            <el-link :underline="false" type="danger" @click="deleteKey(row)"
              >删除</el-link
            >
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <el-dialog
      @close="keyVisible = false"
      :visible="keyVisible"
      :title="keyMode == 'add' ? '新增变量' : '修改变量'"
      width="30%"
    >
      <el-form>
        <el-form-item label="变量名">
          <el-input v-model="curKey.label"></el-input>
        </el-form-item>
        <el-form-item label="默认值">
          <el-input v-model="curKey.value"></el-input>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="curKey.remark"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="info" @click="keyVisible = false"> 取消 </el-button>
        <el-button type="primary" @click="confirmKey"> 确认 </el-button>
      </div>
    </el-dialog>

    <el-dialog
      @close="eventInfo.eventVisible = false"
      :visible="eventInfo.eventVisible"
      append-to-body
      title="表单事件"
      width="90%"
    >
      <el-alert title="界面事件统一在表单事件部分配置" type="success"  />
      <el-row class="mr-t15">
        <el-col :span="24">
          <el-cascader
            style="width: 200px"
            :show-all-levels="false"
            v-model="eventInfo.component"
            :props="{
              value: 'id',
              label: 'label',
              children: 'children',
            }"
            :options="componentTreeData"
          ></el-cascader>

          <el-select style="width: 200px; margin-left: 10px" v-model="eventInfo.eventType">
            <el-option
              v-for="item in eventMap[findScript(eventInfo)]"
              :key="item.value"
              :value="item.value"
              :label="item.label"
            />
          </el-select>

          <el-button type="primary" class="mr-l10">添加事件</el-button>
        </el-col>
        <el-col class="cardList">
          <el-card
            v-for="(item, index) in eventInfo.eventData"
            class="box-card"
            :key="index"
            shadow="hover"
          >
            <div slot="header" class="clearfix">
              {{ item.name }}
            </div>
            <div>
              <el-descriptions :column="1" :colon="false">
                <el-descriptions-item label="" >
                  <el-row>
                    <el-col :span="24">
                      <el-tag size="mini" style="border-radius: 10px;padding: 0 10px;font-weight: bolder;">{{ item.eventType }}</el-tag>
                    </el-col>
                    <el-col :span="24" class="mr-t10" @click.native="editEvent(item)">
                      <pre style="zoom: 0.7;">{{ item.code }}</pre>
                    </el-col>
                  </el-row>
                </el-descriptions-item>
              </el-descriptions>
            </div>
          </el-card>
        </el-col>
      </el-row>


      <div slot="footer" class="dialog-footer">
        <el-button type="info" @click="eventInfo.eventVisible = false"> 取消 </el-button>
        <el-button type="primary" > 确认 </el-button>
      </div>
    </el-dialog>

    <el-dialog
      @close="ruleVisible = false"
      :visible="ruleVisible"
      append-to-body
      title="表单联动规则"
      width="90%"
    >
      <el-scrollbar style="height: 70vh">
        <el-alert
          title="当前表单中，所有变量的值发生变化时，会触发联动规则，满足配置的规则，会触发成功动作，反之执行失败动作"
          type="success"
        >
        </el-alert>
        <el-form label-width="80px" style="margin-top: 10px">
          <el-form-item label="规则名称">
            <el-input
              v-model="ruleItem.ruleName"
              placeholder="请输入规则名称"
            ></el-input>
          </el-form-item>
        </el-form>
        <div
          style="
            position: relative;
            margin: 15px 0;
            padding: 10px;
            border-radius: 5px;
            border: 3px dashed #e8e8e8;
          "
        >
          <el-button
            @click="addConditionGroup"
            icon="el-icon-plus"
            style="
              position: absolute;
              padding: 5px;
              top: -15px;
              right: 15px;
              background: white;
              border-color: transparent;
              background: 0 0;
            "
          >
            添加条件组
          </el-button>
          <div
            v-for="(item, itemIndex) in ruleItem.conditions"
            :key="itemIndex"
          >
            <div
              style="
                position: relative;
                margin: 15px 0;
                padding: 10px;
                border-radius: 5px;
                border: 2px dashed #e8e8e8;
              "
            >
              <el-button
                @click="addCondition(item)"
                icon="el-icon-plus"
                style="
                  position: absolute;
                  padding: 5px;
                  top: -15px;
                  left: calc(50% - 95px);
                  background: white;
                  border-color: transparent;
                  background: 0 0;
                  padding: 2px;
                  height: auto;
                "
              >
                添加条件
              </el-button>
              <div v-for="(itemCondition, cdIndex) in item.cd" :key="cdIndex">
                <div
                  style="
                    position: relative;
                    margin: 7px 0;
                    padding: 8px;
                    border-radius: 5px;
                    background: #f7f7f9;
                    display: flex;
                    align-items: center;
                  "
                >
                  <span
                    @click="delConditionGroupItem(itemIndex, cdIndex)"
                    class="icon el-icon icon el-icon-delete"
                    style="
                      cursor: pointer;
                      position: absolute;
                      right: 0;
                      top: -12px;
                      padding: 5px;
                      border-radius: 50%;
                      background: white;
                      box-shadow: 0 0 10px #e8e8e8;
                    "
                  />
                  <el-cascader
                    style="width: 200px"
                    :show-all-levels="false"
                    v-model="itemCondition.component"
                    :props="{
                      value: 'id',
                      label: 'label',
                      children: 'children',
                    }"
                    @change="componentChange"
                    :options="componentTreeData"
                  ></el-cascader>
                  <el-radio-group
                    v-model="itemCondition.type"
                    style="margin-left: 10px; width: 135px"
                  >
                    <el-radio-button label="value">值</el-radio-button>
                    <el-radio-button label="event">事件</el-radio-button>
                  </el-radio-group>
                  <template v-if="itemCondition.type === 'value'">
                    <el-select
                      v-model="itemCondition.condition"
                      style="width: 100px; margin-left: 10px"
                    >
                      <el-option
                        v-for="item in conditions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                    <el-radio-group
                      v-model="itemCondition.value.type"
                      style="margin-left: 10px; width: 160px"
                    >
                      <el-radio-button label="d">动态</el-radio-button>
                      <el-radio-button label="g">固定</el-radio-button>
                    </el-radio-group>
                    <div
                      v-if="itemCondition.value.type == 'g'"
                      style="
                        width: 400px;
                        margin-left: 10px;
                        display: inline-block;
                      "
                    >
                      <el-input
                        v-model="itemCondition.value.value"
                        placeholder="请输入固定值"
                      ></el-input>
                    </div>
                    <el-cascader
                      v-else
                      style="width: 400px; margin-left: 10px"
                      v-model="itemCondition.value.value"
                      :show-all-levels="false"
                      :props="{
                        value: 'id',
                        label: 'label',
                        children: 'children',
                        checkStrictly: true,
                        emitPath: false,
                      }"
                      :options="componentTreeData"
                    ></el-cascader>
                  </template>
                  <template v-if="itemCondition.type === 'event'">
                    <el-select
                      style="width: 200px; margin-left: 10px"
                      v-model="itemCondition.value.value"
                    >
                      <el-option
                        v-for="item in eventMap[findScript(itemCondition)]"
                        :key="item.value"
                        :value="item.value"
                        :label="item.label"
                      />
                    </el-select>
                  </template>
                  <div
                    style="
                      width: 100px;
                      margin-left: 10px;
                      display: inline-block;
                    "
                  >
                    <el-select
                      v-model="itemCondition.leftLink"
                      v-if="cdIndex !== item.cd.length - 1"
                      style="width: 100px"
                    >
                      <el-option label="且" value="and" />
                      <el-option label="或" value="or" />
                    </el-select>
                  </div>
                </div>
              </div>
              <!--    删除条件组        -->
              <span
                @click="delConditionGroup(itemIndex)"
                class="icon el-icon icon el-icon-delete"
                style="
                  cursor: pointer;
                  position: absolute;
                  right: 0;
                  bottom: -12px;
                  padding: 5px;
                  border-radius: 50%;
                  background: white;
                  box-shadow: 0 0 10px #e8e8e8;
                "
              />
            </div>
            <div v-if="itemIndex !== ruleItem.conditions.length - 1">
              <el-select v-model="item.link" style="width: 70px" size="mini">
                <el-option label="且" value="and" />
                <el-option label="或" value="or" />
              </el-select>
            </div>
          </div>
        </div>
        <el-alert
          title="执行动作会按照配置的顺序一步步执行，如果逻辑对执行顺序有要求，请谨慎按照顺序配置"
          type="success"
        >
        </el-alert>
        <div
          style="
            position: relative;
            margin: 15px 0;
            padding: 10px;
            border-radius: 5px;
            border: 2px dashed #e8e8e8;
          "
        >
          <el-button
            @click="addRuleItem('ok')"
            icon="el-icon-plus"
            style="
              position: absolute;
              padding: 5px;
              top: -15px;
              left: calc(50% - 95px);
              background: white;
              border-color: transparent;
              background: 0 0;
              padding: 2px;
              height: auto;
            "
          >
            添加动作（满足条件时执行）
          </el-button>
          <div v-for="(item, itemIndex) in ruleItem.ok" :key="itemIndex">
            <div
              style="
                position: relative;
                margin: 7px 0;
                padding: 8px;
                border-radius: 5px;
                background: #f7f7f9;
              "
            >
              <span
                @click="delRuleItem(itemIndex, 'ok')"
                class="icon el-icon icon el-icon-delete"
                style="
                  cursor: pointer;
                  position: absolute;
                  right: 0;
                  top: -12px;
                  padding: 5px;
                  border-radius: 50%;
                  background: white;
                  box-shadow: 0 0 10px #e8e8e8;
                "
              />

              <div class="d-flex a-center">
                <span>{{ itemIndex + 1 }}</span>
                <el-cascader
                  style="width: 190px; margin-left: 10px"
                  v-model="item.type"
                  :props="{
                    value: 'type',
                    label: 'name',
                    children: 'children',
                    checkStrictly: false,
                  }"
                  :options="ruleActions"
                ></el-cascader>
                <div style="display: inline; margin-left: 10px">
                  <!--       选择组件       -->
                  <div style="display: inline" v-if="judgeType(item.type, 1)">
                    <el-cascader
                      style="width: calc(70vw - 550px)"
                      v-model="item.component"
                      :show-all-levels="false"
                      :props="{
                        value: 'id',
                        label: 'label',
                        children: 'children',
                        checkStrictly: true,
                      }"
                      :options="componentTreeData"
                    ></el-cascader>
                  </div>
                  <div
                    style="display: inline"
                    v-else-if="judgeType(item.type, 2)"
                  >
                    <el-radio-group
                      v-model="item.value.type"
                      style="margin-left: 10px; width: 160px"
                    >
                      <el-radio-button label="d">动态</el-radio-button>
                      <el-radio-button label="g">固定</el-radio-button>
                    </el-radio-group>
                    <div
                      v-if="item.value.type == 'g'"
                      style="
                        width: 400px;
                        margin-left: 10px;
                        display: inline-block;
                      "
                    >
                      <el-input
                        v-model="item.value.value"
                        placeholder="请输入固定值"
                      ></el-input>
                    </div>
                    <el-cascader
                      v-else
                      style="width: 400px"
                      v-model="item.value.value"
                      :show-all-levels="false"
                      :props="{
                        value: 'id',
                        label: 'label',
                        children: 'children',
                        checkStrictly: true,
                        emitPath: false,
                      }"
                      :options="componentTreeData"
                    ></el-cascader>
                  </div>
                  <!--       选择弹出组件       -->
                  <div
                    style="display: inline"
                    v-else-if="judgeType(item.type, 3)"
                  >
                    <el-cascader
                      style="width: calc(70vw - 550px)"
                      v-model="item.component"
                      :show-all-levels="false"
                      :props="{
                        value: 'id',
                        label: 'label',
                        children: 'children',
                        checkStrictly: false,
                      }"
                      @change="componentChange"
                      :options="componentDialogTreeData"
                    ></el-cascader>
                  </div>
                  <!--       接口调用      -->
                  <div
                    style="display: inline"
                    v-else-if="judgeType(item.type, 4)"
                  >
                    <el-select v-model="item.interfacesUid">
                      <el-option
                        v-for="e in interfaceList"
                        :key="e.value"
                        :value="e.value"
                        :label="e.label"
                      ></el-option>
                    </el-select>
                    <el-button
                      size="mini"
                      type="primary"
                      round
                      plain
                      @click="openParamsDialog(item)"
                      style="margin-left: 10px; width: 80px"
                      >参数配置</el-button
                    >
                    <paramDialog
                      ref="paramDialogRef"
                      @close="paramsClose"
                      :allFormItem="componentTreeData"
                    />
                    <br />
                  </div>
                  <!--      选择模型          -->
                  <div
                    class="d-flex a-center"
                    v-else-if="judgeType(item.type, 5)"
                  >
                    <el-select
                      style="width: 150px"
                      filterable
                      v-model="item.component"
                    >
                      <el-option
                        v-for="tb in setup.tables"
                        :key="tb.tableName"
                        :value="tb.tableName"
                        :label="tb.tableComment"
                      ></el-option>
                    </el-select>
                    <template v-if="item.component">
                      <template
                        v-if="
                          currentForm(item.component).templateType === 1 ||
                          currentForm(item.component).templateType === 4
                        "
                      >
                        <span class="mr-l10">数据主键:</span>
                        <el-radio-group
                          v-model="item.commitType"
                          @change="item.commitId = null"
                          style="margin-left: 10px; width: 160px"
                        >
                          <el-radio-button label="widget">组件</el-radio-button>
                          <el-radio-button label="tableCol"
                            >表格列</el-radio-button
                          >
                        </el-radio-group>

                        <el-cascader
                          v-if="item.commitType == 'widget'"
                          style="width: 150px"
                          v-model="item.commitId"
                          :show-all-levels="false"
                          filterable
                          placeholder="请选择组件"
                          :props="{
                            value: 'id',
                            label: 'label',
                            children: 'children',
                            checkStrictly: true,
                            emitPath: false,
                          }"
                          :options="componentTreeData"
                        ></el-cascader>

                        <el-cascader
                          style="width: 150px"
                          v-else
                          :show-all-levels="false"
                          placeholder="请选择表格列"
                          filterable
                          v-model="item.commitId"
                          :props="{
                            value: 'id',
                            label: 'label',
                            children: 'cols',
                            emitPath: false,
                          }"
                          :options="componentDataTableData"
                        >
                        </el-cascader>
                      </template>

                      <template
                        v-if="currentForm(item.component).templateType === 1"
                      >
                        <span class="mr-l10">任务ID:</span>

                        <el-radio-group
                          v-model="item.taskType"
                          @change="item.taskId = null"
                          style="margin-left: 10px; width: 160px"
                        >
                          <el-radio-button label="widget">组件</el-radio-button>
                          <el-radio-button label="tableCol"
                            >表格列</el-radio-button
                          >
                        </el-radio-group>

                        <el-cascader
                          filterable
                          v-if="item.taskType == 'widget'"
                          style="width: 150px"
                          v-model="item.taskId"
                          :show-all-levels="false"
                          placeholder="请选择组件"
                          :props="{
                            value: 'id',
                            label: 'label',
                            children: 'children',
                            checkStrictly: true,
                            emitPath: false,
                          }"
                          :options="componentTreeData"
                        ></el-cascader>

                        <el-cascader
                          style="width: 150px"
                          filterable
                          :show-all-levels="false"
                          placeholder="请选择表格列"
                          v-else
                          v-model="item.taskId"
                          :props="{
                            value: 'id',
                            label: 'label',
                            children: 'cols',
                            emitPath: false,
                          }"
                          :options="componentDataTableData"
                        >
                        </el-cascader>
                      </template>
                      <!--    流程实例Id                  -->
                      <template
                        v-if="currentForm(item.component).templateType === 1"
                      >
                        <span class="mr-l10">流程实例ID:</span>

                        <el-radio-group
                          v-model="item.definitionType"
                          @change="item.definitionId = null"
                          style="margin-left: 10px; width: 160px"
                        >
                          <el-radio-button label="widget">组件</el-radio-button>
                          <el-radio-button label="tableCol"
                            >表格列</el-radio-button
                          >
                        </el-radio-group>

                        <el-cascader
                          filterable
                          v-if="item.definitionType === 'widget'"
                          style="width: 150px"
                          v-model="item.definitionId"
                          :show-all-levels="false"
                          placeholder="请选择组件"
                          :props="{
                            value: 'id',
                            label: 'label',
                            children: 'children',
                            checkStrictly: true,
                            emitPath: false,
                          }"
                          :options="componentTreeData"
                        ></el-cascader>

                        <el-cascader
                          style="width: 150px"
                          filterable
                          :show-all-levels="false"
                          placeholder="请选择表格列"
                          v-else
                          v-model="item.definitionId"
                          :props="{
                            value: 'id',
                            label: 'label',
                            children: 'cols',
                            emitPath: false,
                          }"
                          :options="componentDataTableData"
                        >
                        </el-cascader>
                      </template>
                    </template>
                  </div>
                  <!--       选择表格组件       -->
                  <div
                    style="display: inline"
                    v-else-if="judgeType(item.type, 6)"
                  >
                    <el-cascader
                      style="width: calc(70vw - 550px)"
                      v-model="item.component"
                      :show-all-levels="false"
                      :props="{
                        value: 'id',
                        label: 'label',
                        children: 'children',
                        checkStrictly: false,
                      }"
                      :options="componentDataTableData"
                    ></el-cascader>
                  </div>
                </div>
              </div>

              <div v-if="judgeType(item.type, 3)">
                <div style="text-align: center">--------回调---------</div>
                <Callback
                  :callbackList="item.children"
                  :ruleActions="ruleActions"
                  :componentTreeData="componentTreeData"
                  :componentDialogTreeData="componentDialogTreeData"
                  :componentDataTableData="componentDataTableData"
                ></Callback>
              </div>
              <div v-if="judgeType(item.type, 4)">
                <div style="text-align: center">--------成功回调---------</div>
                <Callback
                  :callbackList="item.success"
                  :ruleActions="ruleActions"
                  :componentTreeData="componentTreeData"
                  :componentDialogTreeData="componentDialogTreeData"
                  :componentDataTableData="componentDataTableData"
                ></Callback>
                <div style="margin-top: 10px; text-align: center">
                  --------失败回调---------
                </div>
                <Callback
                  :callbackList="item.fail"
                  :ruleActions="ruleActions"
                  :componentTreeData="componentTreeData"
                  :componentDialogTreeData="componentDialogTreeData"
                  :componentDataTableData="componentDataTableData"
                ></Callback>
              </div>
            </div>
            <!--       选择弹出组件       -->
          </div>
        </div>
        <div
          style="
            position: relative;
            margin: 15px 0;
            padding: 10px;
            border-radius: 5px;
            border: 2px dashed #e8e8e8;
          "
        >
          <el-button
            @click="addRuleItem('ng')"
            icon="el-icon-plus"
            style="
              position: absolute;
              padding: 5px;
              top: -15px;
              left: calc(50% - 95px);
              background: white;
              border-color: transparent;
              background: 0 0;
              padding: 2px;
              height: auto;
            "
          >
            添加动作（不满足条件时执行）
          </el-button>
          <div v-for="(item, itemIndex) in ruleItem.ng" :key="itemIndex">
            <div
              style="
                position: relative;
                margin: 7px 0;
                padding: 8px;
                border-radius: 5px;
                background: #f7f7f9;
                display: flex;
                align-items: center;
              "
            >
              <span
                @click="delRuleItem(itemIndex, 'ng')"
                class="icon el-icon icon el-icon-delete"
                style="
                  cursor: pointer;
                  position: absolute;
                  right: 0;
                  top: -12px;
                  padding: 5px;
                  border-radius: 50%;
                  background: white;
                  box-shadow: 0 0 10px #e8e8e8;
                "
              />
              {{ itemIndex + 1 }}
              <el-cascader
                style="width: 190px"
                v-model="item.type"
                :props="{
                  value: 'type',
                  label: 'name',
                  children: 'children',
                  checkStrictly: false,
                }"
                :options="ruleActions"
              ></el-cascader>
              <div style="display: inline; margin-left: 10px">
                <!--       选择组件       -->
                <div
                  style="display: inline"
                  v-if="
                    item.type &&
                    findComponent(
                      item.type[item.type.length - 1],
                      ruleActions
                    ) &&
                    findComponent(item.type[item.type.length - 1], ruleActions)
                      .setting === 1
                  "
                >
                  <el-cascader
                    style="width: calc(70vw - 550px)"
                    v-model="item.component"
                    :show-all-levels="false"
                    :props="{
                      value: 'id',
                      label: 'label',
                      children: 'children',
                      checkStrictly: true,
                    }"
                    @change="componentChange"
                    :options="componentTreeData"
                  ></el-cascader>
                </div>
                <div
                  style="display: inline"
                  v-if="
                    item.type &&
                    findComponent(
                      item.type[item.type.length - 1],
                      ruleActions
                    ) &&
                    findComponent(item.type[item.type.length - 1], ruleActions)
                      .setting === 2
                  "
                >
                  <el-radio-group
                    v-model="item.value.type"
                    style="margin-left: 10px; width: 160px"
                  >
                    <el-radio-button label="d">动态</el-radio-button>
                    <el-radio-button label="g">固定</el-radio-button>
                  </el-radio-group>
                  <div
                    v-if="item.value.type == 'g'"
                    style="
                      width: 400px;
                      margin-left: 10px;
                      display: inline-block;
                    "
                  >
                    <el-input
                      v-model="item.value.value"
                      placeholder="请输入固定值"
                    ></el-input>
                  </div>
                  <el-cascader
                    v-else
                    style="width: 400px"
                    v-model="item.value.value"
                    :show-all-levels="false"
                    :props="{
                      value: 'id',
                      label: 'label',
                      children: 'children',
                      checkStrictly: true,
                      emitPath: false,
                    }"
                    :options="componentTreeData"
                  ></el-cascader>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-scrollbar>
      <div slot="footer" class="dialog-footer">
        <el-button type="info" @click="ruleVisible = false"> 取消 </el-button>
        <el-button type="primary" @click="ruleCommit"> 确认 </el-button>
      </div>
    </el-dialog>
    <el-dialog :visible="introVisible" title="表单引导配置" width="68%" @close="introVisible = false">
      <draggable tag="ul" animation="300" :list="steps">
        <el-form :inline="true" class="demo-form-inline mr-10" v-for="(item, index) in steps" :key="index" size="small">
          <el-form-item><i class="el-icon-rank drag-option" /></el-form-item>
          <el-form-item :label="(index + 1) + '：'" size="small" label-width="50px"></el-form-item>
          <el-form-item label="引导标题:">
            <el-input v-model="item.title" placeholder="请输入引导标题" size="small" style="width: 160px"></el-input>
          </el-form-item>
          <el-form-item label="绑定组件:">
            <el-input v-model="item.element" placeholder="请选择绑定组件" readonly size="small" style="width: 160px" class="pointer" @focus="showComponentTree(index)"></el-input>
          </el-form-item>
          <el-form-item label="提示内容:">
            <el-input v-model="item.intro" placeholder="输入提示内容" size="small" style="width: 160px"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" size="small" plain round @click="addSteps(index)">添加</el-button>
            <el-button type="danger" size="small" plain round @click="steps.splice(index, 1)">删除</el-button>
          </el-form-item>
        </el-form>
      </draggable>
      <div slot="footer" class="dialog-footer">
        <el-button type="info" @click="introVisible = false"> 取消 </el-button>
        <el-button type="primary" @click="saveIntroConfig"> 确认 </el-button>
      </div>
    </el-dialog>
    <el-dialog :visible="treeVisible" title="选择组件" width="260px" @close="treeVisible = false">
      <componentTree ref="componentTreeRef" @nodeClick="nodeClick" type="select"/>
    </el-dialog>
  </div>
</template>

<script>
import EventEditor from "@/components/EventEditor/index";
import ruleScript from "@/components/form-render/ruleScript.js";
import {eventMap} from "@/components/form-render/ruleEvent.js";
import paramDialog from "./components/param-dialog.vue";
import i18n from "@/utils/i18n";
import CodeEditor from "@/components/code-editor/index";
import bus from "@/magic-editor/scripts/bus";
import {
  deepClone,
  insertCustomCssToHead,
  insertGlobalFunctionsToHtml,
} from "@/utils/util";
import componentTree from "./components/componentTree.vue";
import Callback from "./components/callback.vue";
import IconSelect from '@/components/IconSelect';
import Draggable from "vuedraggable";
export default {
  name: "form-setting",
  mixins: [i18n],
  components: {
    CodeEditor,
    componentTree,
    Callback,
    paramDialog,
    IconSelect,
    Draggable,
    EventEditor
  },
  props: {
    designer: Object,
    formConfig: Object,
    str: String,
  },
  data() {
    return {
      eventInfo: {
        eventVisible: false,
        component: "",
        eventType: "",
        eventData: [],
        code: ""
      },
      isCustom: false,
      groupDialog: true,
      formActiveCollapseNames: ["1", "2"],
      formSizes: [
        { label: "default", value: "" },
        { label: "large", value: "large" },
        { label: "medium", value: "medium" },
        { label: "small", value: "small" },
        { label: "mini", value: "mini" },
      ],
      formTables: [],
      showEditFormCssDialogFlag: false,
      formCssCode: "",
      cssClassList: [],

      functionsCode: "",

      showEventDocumentDialogFlag: false,
      formEventHandlerCode: "",
      curEventName: "",

      eventParamsMap: {
        onFormCreated: "onFormCreated() {",
        onFormMounted: "onFormMounted() {",
        onFormDataChange:
          "onFormDataChange(fieldName, newValue, oldValue, formModel, subFormName, subFormRowIndex) {",
        onFormCommit: "onFormCommit(commitData, historyCommitData, callback, draft, type) {",
        onPreCommit:
          "onPreCommit(commitData, historyCommitData,commitType,hasCommitForm) {",
        onScanCommit: "onScanCommit(commitCode) {",
        onFormDataComplete: "onFormDataComplete() {",

        //'onFormValidate':     'onFormValidate() {',
      },
      storeVisible: false,
      keyVisible: false,
      ruleVisible: false,
      ruleIndex: 0,
      curKey: {},
      keyMode: "",
      ruleItem: {}, //当前规则
      ruleActions: ruleScript,
      eventMap: eventMap,
      componentTreeData: [],
      componentDialogTreeData: [],
      componentDataTableData: [],
      storeArr: [],
      conditions: [
        {
          label: "等于",
          value: "eq",
        },
        {
          label: "不等于",
          value: "neq",
        },
        {
          label: "包含",
          value: "inc",
        },
        {
          label: "不包含",
          value: "ninc",
        },
      ],
      currentInterfaceItem: {},
      // 弹窗配置
      rowObj: {
        'cust-id': '',
        name: '',
        type: '',
        icon: '',
        color: '#FFFFFF',
        bgColor: '',
        fun: '',
        displayFun: '',
      },
      codeInterface: {
        code: '',
        index: -1,
        desc: '',
        key: '',
      },
      codeEditor: '',
      dialogVisible: false,
      introVisible: false,
      steps: [],
      treeVisible: false,
      stepIndex: 0,
      codeEditorKey: '',
      codeEditorKey2: '',
      codeEditorKey3: '',
    };
  },
  computed: {
    setup() {
      return this.$store.state.design;
    },
    interfaceList() {
      return this.setup.interfacesInfos.map((item) => {
        return {
          label: item.interfacesContent.name,
          value: item.interfacesUid,
          parameters: item.interfacesContent.parameters,
          requestBody: item.interfacesContent.requestBody,
          method: item.interfacesContent.method,
        };
      });
    },
    predefineColors() {
      return [
        this.$store.state.settings.theme,
        '#67C23A',
        '#E6A23C',
        '#F56C6C',
        '#909399',
      ]
    }
  },
  created() {
    //导入表单JSON后需要重新加载自定义CSS样式！！！
    this.designer.handleEvent("form-json-imported", () => {
      this.formCssCode = this.formConfig.cssCode;
      insertCustomCssToHead(this.formCssCode);
      this.extractCssClass();
      this.designer.emitEvent("form-css-updated", deepClone(this.cssClassList));


    });


    // 空缺字段补全
    if (this.formConfig.leftBtns === undefined) {
      this.$set(this.formConfig, 'leftBtns', [])
    }
    if (this.formConfig.dialogWidth === undefined) {
      this.$set(this.formConfig, 'dialogWidth', null)
    }

    bus.$on("saveEventHandler", (res) => {
      let eventDataItem = this.eventInfo.eventData.find(e => e.component === res.id && e.eventType === res.eventType)
      if(eventDataItem){
        eventDataItem.code = res.code
      }
    });
  },
  mounted() {
    /* SettingPanel和FormWidget为兄弟组件, 在FormWidget加载formConfig时，
         此处SettingPanel可能无法获取到formConfig.cssCode, 故加个延时函数！ */

    let attempts = 0;
    const maxAttempts = 5;
    const checkInterval = 1200; // 1.2 秒

    const checkFormConfig = () => {
      if (this.formConfig) {
        this.formCssCode = this.formConfig.cssCode;
        insertCustomCssToHead(this.formCssCode);
        this.extractCssClass();
        this.designer.emitEvent("form-css-updated", deepClone(this.cssClassList));

        if (this.formConfig?.storeLabel) {
          this.storeArr = Object.values(this.formConfig?.storeLabel);
        }

      } else if (attempts < maxAttempts) {
        attempts++;
        setTimeout(checkFormConfig, checkInterval);
      } else {
        console.log('尝试 5 次后仍未获取到 formConfig');
      }
    };

    setTimeout(checkFormConfig, checkInterval);
  },
  methods: {
    editIntroConfig() {
      this.steps = this.designer.formConfig.introSteps;
      if (this.steps.length == 0) {
        this.steps.push({
          title: "温馨提示",
          element: "",
          intro: "",
        });
      }
      this.introVisible = true;
    },
    saveIntroConfig() {
      let flag = true;
      let msg = "";
      for (let i = 0; i < this.steps.length; i++) {
        if (this.steps[i].element == "") {
          msg = "第" + (i + 1) + "条未绑定组件，请先绑定组件再保存";
          flag = false;
          break;
        }
      }
      if (!flag) {
        this.$message.warning(msg);
        return
      }
      this.$set(this.designer.formConfig, 'introSteps', this.steps);
      this.introVisible = false;
    },
    showComponentTree(index) {
      this.treeVisible = true;
      this.stepIndex = index;
      this.$nextTick(() => {
        this.$refs.componentTreeRef.init(this.designer.widgetList);
      });
    },
    addSteps(index) {
      this.steps.splice(index + 1, 0, {
        title: "温馨提示",
        element: "",
        intro: "",
      });
    },
    nodeClick(id) {
      this.steps[this.stepIndex].element = id;
      this.treeVisible = false;
    },
    // 弹窗配置
    addBefore() {
      this.rowObj['cust-id'] = this.$string.getUUID(7);
    },
    custSave({ data }) {
      this.$notify.success({ message: '保存成功' });
      this.$refs.operationRef.saveComplete(data);
    },
    custDelete({ index }) {
      this.formConfig.leftBtns.splice(index, 1);
      this.$notify.success({ message: '删除成功' });
    },
    checkDialog() {
      for (let i = 0; i < this.formConfig.leftBtns.length; i++) {
        if (this.formConfig.leftBtns[i].isEdit) {
          this.$message({ message: `请保存第${i + 1}条数据` });
          return;
        }
      }
      this.dialogVisible = false;
    },
    // 选择图标
    selected(e, index) {
      this.formConfig.leftBtns[index].icon = e;
    },
    showCodeDialog(desc, index, key) {
      this.codeInterface = {
        code: this.formConfig.leftBtns[index][key],
        index,
        desc,
        key,
      };
      this.codeEditorKey = new Date().getTime() + 1;
      this.$nextTick(()=> {
        this.$refs.eventEditorRef.open(true);
      })
    },
    codeConfirm(eventCode) {
      this.codeInterface.code = eventCode;
      let { index, code, key } = this.codeInterface;
      this.formConfig.leftBtns[index][key] = code;
      this.$refs.eventEditorRef.open(false);
    },
    // 寻找当前选择模型的视图模版数据
    currentForm(tableName) {
      if (tableName) {
        return (
          this.setup.forms.find((item) => item.tableName === tableName) || {}
        );
      }
      return {};
    },

    openParamsDialog(item) {
      if (!item.interfacesUid) {
        this.$message.warning("请先选择接口");
        return;
      }
      this.currentInterfaceItem = item;
      let itemObj = this.interfaceList.find(
        (e) => e.value == item.interfacesUid
      );
      if (
        (itemObj.method === "GET" && !itemObj.parameters) ||
        (itemObj.method !== "GET" && !itemObj.requestBody)
      ) {
        this.$message.error("接口不存在入参");
        return;
      }
      let apiParam = {};
      if (itemObj.parameters.length) {
        itemObj.parameters.forEach((e) => {
          apiParam[e.name] = e.value;
        });
      } else if (itemObj.requestBody.length) {
        itemObj.requestBody.forEach((e) => {
          apiParam[e.name] = e.value;
        });
      }

      // 去除掉一些固定参数 ，比如：分页的pageSize和pageNum
      ["pageSize", "pageNum", "choiceId"].map((item) => {
        delete apiParam[item];
      });

      this.$refs.paramDialogRef[0].open(apiParam, item.param);
    },
    paramsClose(data) {
      this.currentInterfaceItem.param = data;
    },
    judgeType(type, val) {
      let res = this.findComponent(type.at(-1), this.ruleActions);
      return res && res.setting === val;
    },
    findScript(itemCondition) {
      if(!itemCondition.component.length){
        return
      }
      if (
        itemCondition.component[itemCondition.component.length - 1].includes(
          "datatable"
        )
      ) {
        return "datatable";
      }
      if (
        itemCondition.component[itemCondition.component.length - 1].includes(
          "conditioncontainer"
        )
      ) {
        return "condition-container";
      }
      return itemCondition.component[
        itemCondition.component.length - 1
      ].replace(/\d+/g, "");
    },
    findComponent(type, actions) {
      for (let i = 0; i < actions.length; i++) {
        if (actions[i].children && actions[i].children.length > 0) {
          for (let j = 0; j < actions[i].children.length; j++) {
            if (actions[i].children[j].type === type) {
              return actions[i].children[j];
            }
          }
        }
        // if (actions[i].type === type) {
        //   return actions[i]
        // } else {
        //   if (actions[i].children && actions[i].children.length > 0) {
        //     return this.findComponent(type, actions[i].children )
        //   }
        // }
      }
    },
    confirmKey() {
      if (!this.curKey.label) {
        this.$message.warning("请输入变量名");
        return;
      }
      if (
        this.keyMode == "add" &&
        this.formConfig.store.hasOwnProperty(this.curKey.label)
      ) {
        this.$message.warning("已存在该变量");
        return;
      }
      if (!this.formConfig["storeLabel"]) {
        this.$set(this.formConfig, "storeLabel", {});
      }
      this.$set(this.formConfig.store, this.curKey.label, this.curKey.value);
      this.$set(this.formConfig["storeLabel"], this.curKey.label, {
        label: this.curKey.label,
        value: this.curKey.value,
        remark: this.curKey.remark,
      });

      this.storeArr = Object.values(this.formConfig?.storeLabel);

      this.keyVisible = false;
    },
    addKey() {
      this.curKey = { label: "", value: "", remark: "" };
      this.keyMode = "add";
      this.keyVisible = true;
    },
    editKey(row) {
      this.curKey = JSON.parse(JSON.stringify(row));
      this.keyMode = "edit";
      this.keyVisible = true;
    },
    deleteKey(row) {
      this.$confirm(`是否确认删除变量${row.label}`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.$set(this.formConfig.store, row.label, undefined);
        this.formConfig.store = Object.entries(this.formConfig.store).reduce(
          (a, [k, v]) => (v == null ? a : ((a[k] = v), a)),
          {}
        );

        if (Object.keys(this.formConfig.store).length <= 0) {
          this.$set(this.formConfig, "storeLabel", {});
        } else {
          this.$set(this.formConfig.storeLabel, row.label, undefined);
          this.formConfig.storeLabel = Object.entries(
            this.formConfig.storeLabel
          ).reduce((a, [k, v]) => (v == null ? a : ((a[k] = v), a)), {});
        }
        this.storeArr = Object.values(this.formConfig?.storeLabel);
      });
    },
    showStore() {
      console.log(this.formConfig)
     if(this.formConfig.storeLabel) {
       this.storeArr = Object.values(this.formConfig?.storeLabel)
     }
      this.storeVisible = true;
    },
    groupDialogChange() {
      bus.$emit("onVFromGroupDialogChange", this.groupDialog);
    },
    custonChange(e) {
      if (e) {
        this.formConfig.dataTable = "flow_bus_";
      } else {
        this.formConfig.dataTable = null;
      }
    },
    async copyBoard() {
      let url = this.str; //拿到想要复制的值
      let copyInput = document.createElement("input"); //创建input元素
      document.body.appendChild(copyInput); //向页面底部追加输入框
      copyInput.setAttribute("value", url); //添加属性，将url赋值给input元素的value属性
      copyInput.select(); //选择input元素
      document.execCommand("Copy"); //执行复制命令
      this.$message.success("复制成功"); //弹出提示信息，不同组件可能存在写法不同
      //复制之后再删除元素，否则无法成功赋值
      copyInput.remove(); //删除动态创建的节点
    },
    ruleCommit() {
      if (!this.ruleItem.conditions.length) {
        this.$message.warning("至少要有一个条件组");
        return;
      }
      if (this.ruleItem.conditions.some((item) => !item.cd.length)) {
        this.$message.warning("每个条件组至少选择一个条件");
        return;
      }
      if (
        this.ruleItem.conditions.some((item) =>
          item.cd.some((e) => !e.component.length || !e.value.value)
        )
      ) {
        this.$message.warning("请填写完整条件");
        return;
      }
      this.formConfig.linkRules[this.ruleIndex] = this.ruleItem;
      this.ruleVisible = false;
    },
    // 新增权限项目
    addAuth() {
      if (
        this.designer.formConfig.authoritys == "" ||
        this.designer.formConfig.authoritys == null
      ) {
        this.designer.formConfig.authoritys = [{ authName: "", authCode: "" }];
      } else {
        this.designer.formConfig.authoritys.push({
          authName: "",
          authCode: "",
        });
      }
    },

    openEvent() {
      this.initcomponentTreeData();
      this.eventInfo.eventVisible = true;
    },
    // 删除权限项目
    delAuth(index) {
      this.designer.formConfig.authoritys.splice(index, 1);
    },
    addRule() {
      let rule = {
        ruleName: this.designer.formConfig.linkRules
          ? "联动规则" + this.designer.formConfig.linkRules.length
          : "联动规则1",
        ok: [],
        ng: [],
        conditions: [],
      };
      if (
        this.designer.formConfig.linkRules == "" ||
        this.designer.formConfig.linkRules == null
      ) {
        this.designer.formConfig.linkRules = [rule];
        this.ruleIndex = 0;
      } else {
        this.designer.formConfig.linkRules.push(rule);
        this.ruleIndex = this.designer.formConfig.linkRules.length - 1;
      }
      this.ruleVisible = true;
      this.initcomponentTreeData();
      this.ruleItem = rule;
      this.addRuleItem("ok");
      // this.addRuleItem('ng')
      this.addConditionGroup();
    },
    // 删除联动规则
    delRule(index) {
      this.designer.formConfig.linkRules.splice(index, 1);
    },
    settingRule(ruleItem, ruleIndex) {
      this.ruleVisible = true;
      this.ruleIndex = ruleIndex;
      this.ruleItem = JSON.parse(JSON.stringify(ruleItem))
      this.initcomponentTreeData();
    },
    // 初始化组件树
    initcomponentTreeData() {
      this.componentDataTableData = [];
      this.componentTreeData.length = 0;
      this.designer.widgetList.forEach((wItem) => {
        this.buildTreeNodeOfWidget(wItem, this.componentTreeData);
      });
      this.componentDialogTreeData = [];
      this.designer.widgetList.forEach((wItem) => {
        // 只找第一层的弹出层
        if (wItem.type === "dialog") {
          this.componentDialogTreeData.push({
            id: wItem.id,
            label: wItem.options.label || wItem.type,
            type: wItem.type,
          });
        }
      });
    },
    addConditionGroup() {
      let notSetting = this.ruleItem.conditions.find((e) => e.cd.length == 0);
      if (notSetting) {
        this.$message.warning("存在空条件组，请设置空条件组在新增！");
        return;
      }
      let conditionGroup = {
        link: "and",
        cd: [],
      };
      this.ruleItem.conditions.push(conditionGroup);
      this.addCondition(conditionGroup);
    },
    addCondition(item) {
      item.cd.push({
        component: "",
        condition: "eq",
        type: "value",
        value: {
          type: "g",
          value: "",
        },
        leftLink: "and",
      });
    },
    delConditionGroupItem(groupIndex, itemIndex) {
      this.ruleItem.conditions[groupIndex].cd.splice(itemIndex, 1);
    },
    delConditionGroup(index) {
      this.ruleItem.conditions.splice(index, 1);
    },
    addRuleItem(type) {
      let item = {
        type: ["field", "hiddenField"],
        component: "",
        value: {
          type: "g",
          value: "",
        },
        children: [],
        interfacesUid: "",
        success: [],
        fail: [],
        param: {},
        commitType: "widget",
        commitId: null,
        taskType: "widget",
        taskId: null,
        definitionType: "widget",
        definitionId: null,
      };
      if (this.ruleItem[type]) {
        this.ruleItem[type].push(item);
      } else {
        this.ruleItem[type] = [item];
      }
      this.$forceUpdate();
    },
    delRuleItem(index, type) {
      this.ruleItem[type].splice(index, 1);
      this.$forceUpdate();
    },
    componentChange(items) {
      console.log(items);
    },
    editFormCss() {
      this.formCssCode = this.designer.formConfig.cssCode;
      this.showEditFormCssDialogFlag = true;
    },
    buildTreeNodeOfWidget(widget, treeNode) {
      let curNode = {
        id: widget.id,
        label: widget.options.label || widget.type,
        type: widget.type,
        //selectable: true,
      };
      treeNode.push(curNode);
      if (widget.type === "data-table") {
        this.componentDataTableData.push({
          id: widget.id,
          label: widget.options.label || widget.type,
          type: widget.type,
          cols: widget.options.cols.map((item) => ({
            id: item.prop,
            label: item.label,
          })),
        });

        if (widget.options.btns) {
          curNode.children = [];
          widget.options.btns.forEach((btn) => {
            curNode.children.push({
              id: widget.id + "-" + btn["cust-id"],
              label: btn.name,
              type: "button",
            });
          });
          widget.options.leftBtns.forEach((btn) => {
            curNode.children.push({
              id: widget.id + "-" + btn["cust-id"],
              label: btn.name,
              type: "button",
            });
          });
        }
      }
      if (widget.category === undefined) {
        return;
      }
      curNode.children = [];

      if (widget.type === "grid") {
        widget.cols.map((col) => {
          // let colNode = {
          //   id: col.id,
          //   label: col.options.name || widget.type,
          //   type: col.type,
          //   children: [],
          // };
          // curNode.children.push(colNode);
          col.widgetList &&
            col.widgetList.map((wChild) => {
              this.buildTreeNodeOfWidget(wChild, curNode.children);
            });
        });
      } else if (widget.type === "table") {
        //TODO: 需要考虑合并单元格！！
        widget.rows.map((row) => {
          let rowNode = {
            id: row.id,
            label: "table-row",
            selectable: false,
            children: [],
          };
          curNode.children.push(rowNode);

          row.cols.map((cell) => {
            if (!!cell.merged) {
              //跳过合并单元格！！
              return;
            }

            let rowChildren = rowNode.children;
            let cellNode = {
              id: cell.id,
              label: "table-cell",
              children: [],
            };
            rowChildren.push(cellNode);

            cell.widgetList.map((wChild) => {
              this.buildTreeNodeOfWidget(wChild, cellNode.children);
            });
          });
        });
      } else if (widget.type === "tab") {
        widget.tabs.map((tab) => {
          let tabNode = {
            id: tab.id,
            label: tab.options.name || widget.type,
            selectable: false,
            children: [],
            type: tab.type,
          };
          curNode.children.push(tabNode);
          tab.widgetList.map((wChild) => {
            this.buildTreeNodeOfWidget(wChild, tabNode.children);
          });
        });
      } else if (widget.type === "sub-form") {
        widget.widgetList.map((wChild) => {
          this.buildTreeNodeOfWidget(wChild, curNode.children);
        });
      } else if (widget.type === "trends-tab") {
        widget.widgetList.map((wChild) => {
          this.buildTreeNodeOfWidget(wChild, curNode.children);
        });
      } else if (widget.type === "collapse") {
        widget.items.map((wChild) => {
          this.buildTreeNodeOfWidget(wChild, curNode.children);
        });
      } else if (widget.type === "condition-container") {
        curNode.children.push({
          id: widget.id + "-search",
          label: "搜索",
          selectable: false,
        });
        curNode.children.push({
          id: widget.id + "-reset",
          label: "重置",
          selectable: false,
        });
        //自定义容器
        widget.widgetList.map((wChild) => {
          this.buildTreeNodeOfWidget(wChild, curNode.children);
        });
      } else if (widget.category === "container") {
        //自定义容器
        widget.widgetList.map((wChild) => {
          this.buildTreeNodeOfWidget(wChild, curNode.children);
        });
      }
    },
    extractCssClass() {
      let regExp = /\..*{/g;
      let result = this.formCssCode.match(regExp);
      let cssNameArray = [];

      if (!!result && result.length > 0) {
        result.forEach((rItem) => {
          let classArray = rItem.split(","); //切分逗号分割的多个class
          if (classArray.length > 0) {
            classArray.forEach((cItem) => {
              let caItem = cItem.trim();
              if (caItem.indexOf(".", 1) !== -1) {
                //查找第二个.位置
                let newClass = caItem.substring(
                  caItem.indexOf(".") + 1,
                  caItem.indexOf(".", 1)
                ); //仅截取第一、二个.号之间的class
                if (!!newClass) {
                  cssNameArray.push(newClass.trim());
                }
              } else if (caItem.indexOf(" ") !== -1) {
                //查找第一个空格位置
                let newClass = caItem.substring(
                  caItem.indexOf(".") + 1,
                  caItem.indexOf(" ")
                ); //仅截取第一、二个.号之间的class
                if (!!newClass) {
                  cssNameArray.push(newClass.trim());
                }
              } else {
                if (caItem.indexOf("{") !== -1) {
                  //查找第一个{位置
                  let newClass = caItem.substring(
                    caItem.indexOf(".") + 1,
                    caItem.indexOf("{")
                  );
                  cssNameArray.push(newClass.trim());
                } else {
                  let newClass = caItem.substring(caItem.indexOf(".") + 1);
                  cssNameArray.push(newClass.trim());
                }
              }
            });
          }
        });
      }

      //this.cssClassList.length = 0
      this.cssClassList.splice(0, this.cssClassList.length); //清除数组必须用splice，length=0不会响应式更新！！
      this.cssClassList = Array.from(new Set(cssNameArray)); //数组去重
    },

    saveFormCss() {
      this.extractCssClass();
      this.designer.formConfig.cssCode = this.formCssCode;
      insertCustomCssToHead(this.formCssCode);
      this.showEditFormCssDialogFlag = false;

      this.designer.emitEvent("form-css-updated", deepClone(this.cssClassList));
    },

    editGlobalFunctions() {
      this.functionsCode = this.designer.formConfig.functions;
      this.codeEditorKey3 = new Date().getTime() + 1;
      this.$nextTick(()=> {
        this.$refs.eventEditorRef3.open(true);
      })
    },

    saveGlobalFunctions(eventCode) {
      this.functionsCode = eventCode;
      this.designer.formConfig.functions = this.functionsCode;
      insertGlobalFunctionsToHtml(this.functionsCode);
      this.$refs.eventEditorRef3.open(false);
    },

    editFormEventHandler(eventName) {
      this.curEventName = eventName;
      this.formEventHandlerCode = this.formConfig[eventName];
      this.codeEditorKey2 = new Date().getTime() + 1;
      this.$nextTick(()=> {
        this.$refs.eventEditorRef2.open(true);
      })
    },

    saveFormEventHandler(eventCode) {
      this.formEventHandlerCode = eventCode;
      this.formConfig[this.curEventName] = this.formEventHandlerCode;
      this.$refs.eventEditorRef2.open(false);
    },
  },
};
</script>

<style lang="scss" scoped>

.setting-form {
  li.col-item {
    list-style: none;

    margin: 5px 0;
  }

  ::v-deep .el-form-item__label {
    font-size: 13px;
    //text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }

  ::v-deep .el-form-item--mini.el-form-item {
    margin-bottom: 6px;
  }

  .radio-group-custom {
    ::v-deep .el-radio-button__inner {
      padding-left: 12px;
      padding-right: 12px;
    }
  }

  .custom-divider.el-divider--horizontal {
    margin: 10px 0;
  }
}

.setting-collapse {
  ::v-deep .el-collapse-item__content {
    padding-bottom: 6px;
  }

  ::v-deep .el-collapse-item__header {
    font-style: italic;
    font-weight: bold;
  }
}

.small-padding-dialog {
  ::v-deep .el-dialog__body {
    padding: 6px 15px 0 15px;
  }
}

::v-deep .el-dialog {
  // width: 70% !important;
}
.pointer ::v-deep .el-input__inner {
  cursor: pointer;
}
</style>
