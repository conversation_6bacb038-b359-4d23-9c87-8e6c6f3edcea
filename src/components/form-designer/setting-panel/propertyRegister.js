import Vue from 'vue';

/**
 * 格式说明：属性名称==对应属性编辑器的组件名称
 */
const COMMON_PROPERTIES = {
  // 字段
  name: 'name-editor',
  label: 'label-editor',
  title: 'title-editor',
  alias: 'alias-editor',
  tabType: 'tabType-editor',
  tabPosition: 'tabPosition-editor',
  stretch: 'stretch-editor',
  busType: 'busType-editor',
  flowType: 'flowType-editor',
  organizationalList: 'organizational-list-editor',
  showNodeType: 'showNodeType-editor',
  addAble: 'addAble-editor',

  showCheckAll: 'showCheckAll-editor',
  deleteAble: 'deleteAble-editor',
  min: 'min-editor',
  max: 'max-editor',
  query: 'query-editor',
  showWordLimit: 'showWordLimit-editor',
  prefixIcon: 'prefixIcon-editor',
  suffixIcon: 'suffixIcon-editor',
  rowEdit: 'rowEdit-editor',

  labelAlign: 'labelAlign-editor',
  type: 'type-editor',
  defaultValue: 'defaultValue-editor',
  placeholder: 'placeholder-editor',
  startPlaceholder: 'startPlaceholder-editor',
  endPlaceholder: 'endPlaceholder-editor',
  size: 'size-editor',
  steps: 'steps-editor',
  customSteps: 'customSteps-editor',
  mark: 'mark-editor',
  showStops: 'showStops-editor',
  showIndex: 'showIndex-editor',
  displayStyle: 'displayStyle-editor',
  buttonStyle: 'buttonStyle-editor',
  border: 'border-editor',
  codeManage: 'codeManage-editor',
  subtitle: 'subtitle-editor',
  subFormCols: 'subFormCols-editor',
  isLazy: 'isLazy-editor',
  isVirtual: 'isVirtual-editor',
  isCustom: 'isCustom-editor',
  closeOnClickModal: 'closeOnClickModal-editor',
  destroyOnClose: 'destroyOnClose-editor',
  fullscreen: 'fullscreen-editor',
  showClose: 'showClose-editor',
  hasFrame: 'hasFrame-editor',
  hasExpand: 'hasExpand-editor',
  tableStripe: 'stripe-editor',
  alreadyClickLable: 'alreadyClickLable-editor',
  hasRole: 'hasRole-editor',
  showSearch:'showSearch-editor',
  hasPrint: 'hasPrint-editor',
  hasExport: 'hasExport-editor',
  mouseFlag: 'mouseFlag-editor',
  isShowTableField: 'isShowTableField-editor',
  labelWidth: 'labelWidth-editor',
  labelHidden: 'labelHidden-editor',
  formId: 'formId-editor',
  required: 'required-editor',

  readonly: 'readonly-editor',
  disabled: 'disabled-editor',
  direction: 'direction-editor',
  hidden: 'hidden-editor',
  hideDemo: 'hideDemo-editor',
  // append: 'append-editor',
  // prepend: 'prepend-editor',
  showSummary: 'showSummary-editor',

  clearable: 'clearable-editor',
  editable: 'editable-editor',
  showPassword: 'showPassword-editor',
  textContent: 'textContent-editor',
  htmlContent: 'htmlContent-editor',

  filterable: 'filterable-editor',
  fit: 'fit-editor',
  allowCreate: 'allowCreate-editor',

  automaticDropdown: 'automaticDropdown-editor',
  multiple: 'multiple-editor',
  multipleLimit: 'multipleLimit-editor',
  selectTreeFlat: 'selectTreeFlat-editor',
  disableBranchNodes: 'disableBranchNodes-editor',
  isUser: 'isUser-editor',
  autoRefresh: 'autoRefresh-editor',

  isEnable: 'isEnable-editor',
  contentPosition: 'contentPosition-editor',

  isShowTip: 'isShowTip-editor',
  showFileList: 'showFileList-editor',
  withCredentials: 'withCredentials-editor',
  multipleSelect: 'multipleSelect-editor',
  limit: 'limit-editor',
  fileMaxSize: 'fileMaxSize-editor',
  fileTypes: 'fileTypes-editor',
  customClass: 'customClass-editor',
  listCustomClass: 'listCustomClass-editor',

  targetOrder: 'targetOrder-editor',
  checkStrictly: 'checkStrictly-editor',
  showAllLevels: 'showAllLevels-editor',
  selectChildren: 'selectChildren-editor',
  samePage: 'samePage-editor',
  groupPage: 'groupPage-editor',

  height: 'height-editor',
  minHeight: 'minHeight-editor',
  minimumHeight: 'minimumHeight-editor',
  colWidth: 'colWidth-editor',
  width: 'width-editor',
  writeWidth: 'writeWidth-editor',
  collapseTag: 'collapseTag-editor',
  dialogWidth: 'dialogWidth-editor',
  borderRadius: 'borderRadius-editor',
  backgroundColor: 'backgroundColor-editor',
  // 容器
  showBlankRow: 'showBlankRow-editor',
  showRowNumber: 'showRowNumber-editor',
  cellWidth: 'cellWidth-editor',
  cellHeight: 'cellHeight-editor',
  gutter: 'gutter-editor',

  dashboard: 'dashboard-editor',
  color: 'color-editor',
  bgColor: 'bgColor-editor',
  labelColor: 'labelColor-editor',
  upload: 'upload-editor',
  operation: 'operation-editor',
  responsive: 'responsive-editor',
  span: 'span-editor',
  offset: 'offset-editor',
  push: 'push-editor',
  pull: 'pull-editor',
  popupSelect: 'popupSelect-editor',
  interval: 'interval-editor',
  drawerDirection: 'drawer-direction-editor',
  autoplay: 'autoplay-editor',
  textarea: 'textarea-editor',
  heightFit: 'heightFit-editor',
  isCard: 'isCard-editor',

  reverse: 'reverse-editor',
  collapse: 'collapse-editor',
  accordion: 'accordion-editor',
  slot: 'slot-editor',
  showFold: 'fold-editor',
  showCheckbox: 'showCheckbox-editor',
  emptyText: 'emptyText-editor',
  indent: 'indent-editor',
  iconClass: 'iconClass-editor',
  hasSearch: 'hasSearch-editor',
  hasCloseTips: 'hasCloseTips-editor',
  margin: 'margin-editor',
  padding: 'padding-editor',
  border2: 'border2-editor',
  reportUid: 'report-code-editor',
  isShowExport: 'isShowExport-editor',
  isShowPrint: 'isShowPrint-editor',
  isShowQuery: 'isShowQuery-editor',
  allowCopy: 'allowCopy-editor',
  barCode: 'barCode-editor',
  onlineRelevance: 'online-relevance-editor',
  fileUploadType: 'fileUploadType-editor',
  expandAll: 'expandAll-editor',
  isDrag: 'isDrag-editor',
  numberMode: 'numberMode-editor',
  theme: 'theme-editor',

  switchWidth: 'switchWidth-editor',
  activeText: 'activeText-editor',
  inactiveText: 'inactiveText-editor',
  activeColor: 'activeColor-editor',
  inactiveColor: 'inactiveColor-editor',
  lowThreshold: 'lowThreshold-editor',
  highThreshold: 'highThreshold-editor',
  allowHalf: 'allowHalf-editor',
  showText: 'showText-editor',
  showScore: 'showScore-editor',
  range: 'range-editor',
  vertical: 'vertical-editor',
  plain: 'plain-editor',
  round: 'round-editor',
  circle: 'circle-editor',
  icon: 'icon-editor',
  // labelIconClass: 'labelIconClass-editor',
  // labelIconPosition: 'labelIconPosition-editor',
  // labelTooltip: 'labelTooltip-editor',
  appendButton: 'appendButton-editor',
  appendButtonDisabled: 'appendButtonDisabled-editor',
  highlightCurrentRow: 'highlightCurrentRow-editor',
  // modelQuery: 'modelQuery-editor'
  layoutModel: 'layoutModel-editor',
  shrink: 'shrink-editor',
  statisticCard: 'statisticCard-editor',
  statistics: 'statistics-editor',
  processConfig: 'processConfig-editor',
  markers: 'pointer-editor',
};

const interactive_PROPERTIES = {
  value: 'value-editor',
  bandKey: 'bandKey-editor',
  authority: 'authority-editor',
  indexes: 'indexes-editor',
  tableShow: 'tableShow-editor',
  validation: 'validation-editor',
  validationHint: 'validationHint-editor',
  bandDataIdsApi: 'bandDataIdsApi-editor',
  optionItems: 'optionItems-editor',
  nodeKey: 'nodeKey-editor',
  format: 'format-editor',
  defaultRange: 'defaultRange-editor',
  defaultTime: 'defaultTime-editor',
  valueFormat: 'valueFormat-editor',
  selectLabel: 'selectLabel-editor',
  selectValue: 'selectValue-editor',
  selectDesc: 'selectDesc-editor',
  optionsModel: 'optionsModel-editor',
  optionsDict: 'optionsDict-editor',
  hasSelection: 'hasSelection-editor',
  uploadURL: 'uploadURL-editor',
  rowKey: 'rowKey-editor',
  cols: 'cols-editor',
  btns: 'btns-editor',
  leftBtns: 'leftBtns-editor',
  precision: 'precision-editor',
  step: 'step-editor',
  rows: 'rows-editor',
  dialogTable: 'dialogTable-editor',
  pageStyle: 'pageStyle-editor',
  pagerCount: 'pagerCount-editor',
  controlsPosition: 'controlsPosition-editor',
  minLength: 'minLength-editor',
  maxLength: 'maxLength-editor',
  remote: 'remote-editor',
  activities: 'activities-editor',

  treeTable: 'treeTable-editor',
  progressType: 'progress-type',
  dropItems: 'dropItems-editor',
  showReply: 'showReply-editor',
  dateWidth: 'dateWidth-editor',
  ganttCols: 'ganttCols-editor',
  tableCellSelect: 'tableCellSelect-editor',
  abbreviation: 'abbreviation-editor',
  ruleDefinition: 'ruleDefinition-editor'
};



const NETWORK_PROPERTIES = {
  api: 'api-editor',
  polling: 'polling-editor',
  paramConfig: 'paramConfig-editor',
};

const EVENT_PROPERTIES = {
  // 字段
  onCreated: 'onCreated-editor',
  onMounted: 'onMounted-editor',
  onClick: 'onClick-editor',
  onInput: 'onInput-editor',
  onChange: 'onChange-editor',
  allowCreateFn: 'allowCreateFn-editor',
  tabChange: 'tabChange-editor',
  onFocus: 'onFocus-editor',
  onBlur: 'onBlur-editor',
  onRemoteQuery: 'onRemoteQuery-editor',
  onBeforeUpload: 'onBeforeUpload-editor',
  onUploadSuccess: 'onUploadSuccess-editor',
  onUploadError: 'onUploadError-editor',
  onValidate: 'onValidate-editor',
  onDisabledDate: 'onDisabledDate-editor',
  onDialogClose: 'onDialogClose-editor',
  onNodeClick: 'onNodeClick-editor',
  onNodeCheck: 'onNodeCheck-editor',
  onFilterNodeMethod: 'onFilterNodeMethod-editor',
  onSubSave: 'onSubSave-editor',
  onUseData: 'onUseData-editor',
  onFormCommit: 'onFormCommit-editor',
  onFormDataComplete: 'onFormDataComplete-editor',
  onClickPrint: 'onClickPrint-editor',
  onClickExport: 'onClickExport-editor',
  onClickSearch: 'onClickSearch-editor',
  onPrependClick: 'onPrependClick-editor',
  onAppendClick: 'onAppendClick-editor',
  onCellDblclick: 'onCellDblclick-editor',
  onExpandChange: 'onExpandChange-editor',
  onEditChange: 'onEditChange-editor',
  onFocusChange: 'onFocusChange-editor',
  onBlurChange: 'onBlurChange-editor',
  onTableEnter: 'onTableEnter-editor',
  onHandleEmit: 'onHandleEmit-editor',
  onMessage: 'onMessage-editor',
  // 容器
  onSubFormRowAdd: 'onSubFormRowAdd-editor',
  onSubFormRowInsert: 'onSubFormRowInsert-editor',
  onSubFormRowDelete: 'onSubFormRowDelete-editor',
  onSubFormRowChange: 'onSubFormRowChange-editor',
  onRowStyle: 'onRowStyle-editor',
  onLazy: 'onLazy-editor',
  onCellStyle: 'onCellStyle-editor',
  onSpanMethod: 'onSpanMethod-editor',
  onRowClick: 'onRowClick-editor',
  onRowClass: 'onRowClass-editor',
  onCellClick: 'onCellClick-editor',
  onQuery: 'onQuery-editor',
  onSearchClick: 'onSearchClick-editor',
  onResetClick: 'onResetClick-editor',
  onPointerClick: 'onPointerClick-editor',
  onRenderContent: 'onRenderContent-editor',
  onSortChange: 'onSortChange-editor',
  onImageClick: 'onImageClick-editor',
  onScrollEnd: 'onScrollEnd-editor',
  onNodeStyle: 'onNodeStyle-editor',
  onValueClick: 'onValueClick-editor',
  onBeforeDestroy: 'onBeforeDestroy-editor',
  onCommand: 'onCommand-editor',
  onProgressFormat: 'onProgressFormat-editor',
  onTaskDblClick: 'onTaskDblClick-editor',
  onAfterTaskDrag: 'onAfterTaskDrag-editor',
  onPaginationChange: 'onPaginationChange-editor',
  onGanttTooltip: 'onGanttTooltip-editor',
  onEnter: 'onEnter-editor',
  onReceive: 'onReceive-editor',
  onCopyCallback: 'onCopyCallback-editor',
  onFormChange:'onFormChange-editor'
};


const EVENT_LANG_DIC = {
  // 字段
  onCreated: '创建事件',
  onMounted: '挂载事件',
  onClick: '点击事件',
  onInput: '输入事件',
  onChange: '改变事件',
  allowCreateFn: '创建选项事件',
  tabChange: '标签切换事件',
  onFocus: '聚焦事件',
  onBlur: '失焦事件',
  onRemoteQuery: '远程搜索事件',
  onBeforeUpload: '上传前事件',
  onUploadSuccess: '上传成功事件',
  onUploadError: '上传失败事件',
  onValidate: '验证事件',
  onDisabledDate: '日期禁用事件',
  onDialogClose: '弹窗关闭事件',
  onNodeClick: '点击节点',
  onNodeCheck: '选中节点',
  onFilterNodeMethod: '筛选方法',
  onSubSave: '保存事件',
  onUseData: '应用事件',
  onFormCommit: '表单提交事件',
  onFormDataComplete: '表单数据加载完成',
  onClickPrint: '点击打印事件',
  onClickExport: '点击导出事件',
  onClickSearch: '点击搜索事件',
  onPrependClick: '前缀点击事件',
  onAppendClick: '后缀点击事件',
  onCellDblclick: '单元格双击事件',
  onExpandChange: '展开行改变事件',
  onEditChange: '行内编辑改变事件',
  onFocusChange: '行内编辑聚焦事件',
  onBlurChange: '行内编辑失焦事件',
  onHandleEmit: '接收事件',
  onMessage: '消息接收事件',
  onSubFormRowAdd: '子表单行添加事件',
  onSubFormRowInsert: '子表单插入行事件',
  onSubFormRowDelete: '子表单行删除事件',
  onSubFormRowChange: '子表单行改变事件',
  onRowStyle: '行样式',
  onLazy: '懒加载',
  onCellStyle: '单元格样式',
  onSpanMethod: '合并行列方法',
  onRowClick: '单击行',
  onRowClass: '表头样式',
  onCellClick: '单击单元格',
  onQuery: '查询点击事件',
  onSearchClick: '搜索点击事件',
  onResetClick: '重置点击事件',
  onPointerClick: '坐标点击事件',
  onRenderContent: '内容区JSX',
  onSortChange: '拖拽回调',
  onImageClick: '图片点击事件',
  onScrollEnd: '滚动条触底事件',
  onNodeStyle: '节点样式',
  onValueClick: '点击右侧值',
  onBeforeDestroy: '销毁前事件',
  onCommand: '选中菜单项事件',
  onProgressFormat: '文字内容',
  onTaskDblClick: '双击任务事件',
  onAfterTaskDrag: '拖拽任务事件',
  onPaginationChange: '分页改变事件',
  onGanttTooltip: '任务悬浮框内容',
  tableLeftClick: '表格左侧按钮点击事件',
  tableLeftShow: '表格左侧按钮显示条件',
  tableOperateClick: '表格操作列点击事件',
  tableOperateShow: '表格操作列显示条件',
  tableColHtml: '表格列自定义html',
  tableColLink: '表格列链接事件',
  onEnter: '回车事件',
  onTableEnter: '行内编辑回车事件',
  onReceive: '接收事件',
  onCopyCallback:'粘贴回调事件',
  onFormChange:'表单改变事件',
};


/**
 * 注册组件常见属性
 * 如属性编辑器的组件名称propEditorName设置为null，则不显示该属性编辑器！！
 * @param uniquePropName 属性名称（保证名称唯一，不跟其他组件属性冲突）
 * @param propEditorName 对应属性编辑器的组件名称
 */
export function registerCommonProperty(uniquePropName, propEditorName) {
  COMMON_PROPERTIES[uniquePropName] = propEditorName;
}

/**
 * 注册组件高级属性
 * 如属性编辑器的组件名称propEditorName设置为null，则不显示该属性编辑器！！
 * @param uniquePropName 属性名称（保证名称唯一，不跟其他组件属性冲突）
 * @param propEditorName 对应属性编辑器的组件名称
 */
export function registerAdvancedProperty(uniquePropName, propEditorName) {
  NETWORK_PROPERTIES[uniquePropName] = propEditorName;
}

/**
 * 注册组件事件属性
 * 如属性编辑器的组件名称propEditorName设置为null，则不显示该属性编辑器！！
 * @param uniquePropName 属性名称（保证名称唯一，不跟其他组件属性冲突）
 * @param propEditorName 对应属性编辑器的组件名称
 */
export function registerEventProperty(uniquePropName, propEditorName) {
  EVENT_PROPERTIES[uniquePropName] = propEditorName;
}

/**
 * 注册常见属性对应的属性编辑器
 * @param uniquePropName
 * @param propEditorName
 * @param editorComponent
 */
export function registerCPEditor(
  uniquePropName,
  propEditorName,
  editorComponent,
) {
  Vue.component(propEditorName, editorComponent);
  registerCommonProperty(uniquePropName, propEditorName);
}

/**
 * 注册高级属性对应的属性编辑器
 * @param uniquePropName
 * @param propEditorName
 * @param editorComponent
 */
export function registerAPEditor(
  uniquePropName,
  propEditorName,
  editorComponent,
) {
  Vue.component(propEditorName, editorComponent);
  registerAdvancedProperty(uniquePropName, propEditorName);
}

/**
 * 注册事件属性对应的属性编辑器
 * @param uniquePropName
 * @param propEditorName
 * @param editorComponent
 */
export function registerEPEditor(
  uniquePropName,
  propEditorName,
  editorComponent,
) {
  Vue.component(propEditorName, editorComponent);
  registerEventProperty(uniquePropName, propEditorName);
}

export default {
  COMMON_PROPERTIES,
  NETWORK_PROPERTIES,
  EVENT_PROPERTIES,
  interactive_PROPERTIES,
  EVENT_LANG_DIC
};
