<template>
  <div>
    <el-form-item label="高亮颜色">
      <ColorPicker
        v-model="optionModel.highColor"
        :predefine="predefineColors"
      />
    </el-form-item>
    <el-form-item label="高亮时文字颜色">
      <ColorPicker
        v-model="optionModel.textColor"
        :predefine="predefineColors"
      />
    </el-form-item>
    <el-form-item label="步骤配置">
      <el-button type="info" icon="el-icon-edit" plain roundsteps @click="dialogShow = true">
        配置</el-button>
    </el-form-item>
    <el-dialog title="步骤配置" width="60%" :visible="dialogShow" :show-close="false">
      <lt-sort-table v-model="optionModel.customSteps" :isEnable="true" max-height="500" row-key="id">
        <lt-table-column-edit prop="label" label="标题"></lt-table-column-edit>
        <lt-table-column-edit prop="value" label="值"></lt-table-column-edit>
        <lt-table-column-edit prop="disabled" label="是否禁用">
          <template #preview="{ row }">
            <el-switch v-model="row.disabled"></el-switch>
          </template>
          <template #edit="{ row }">
            <el-switch v-model="row.disabled"></el-switch>
          </template>
        </lt-table-column-edit>
        <lt-table-column-edit prop="icon" label="图标">
          <template #preview="{ row }">
            <i v-if="row.icon && row.icon.includes('el-icon')" :class="row.icon"></i>
            <svg-icon v-else :icon-class="row.icon" :style="{ color: row.color }" />
          </template>
          <template #edit="{ row }">
            <template>
              <el-popover placement="bottom-start" width="460" trigger="click" @show="$refs['iconSelect'].reset()">
                <IconSelect ref="iconSelect" @selected="selected($event, row)" />
                <el-input slot="reference" readonly>
                  <svg-icon v-if="row.icon" slot="prefix" :icon-class="row.icon" class="el-input__icon"
                    style="height: 32px; width: 16px" />
                </el-input>
              </el-popover>
            </template>
          </template>
        </lt-table-column-edit>
        <lt-table-column-edit prop="color" label="图标颜色">
          <template #header>
            图标颜色<el-tooltip effect="light" content="支持16进制(例: #000)和rgb(例: rgb(255,255,255))">
              <i class="el-icon-info" />
            </el-tooltip>
          </template>
          <template #edit="{ row }">
            <template>
              <el-color-picker v-model="row.color" show-alpha :predefine="predefineColors"
                size="mini"></el-color-picker>
            </template>
          </template>
        </lt-table-column-edit>
        <lt-table-column-operation v-model="optionModel.customSteps" :row-data="rowObj" :unshift="false"
          ref="operationRef" @save="save" @addBefore="addBefore" @delete="custDelete" :unlimitedAdd="true"
          :validateProp="false" primary-key="id" :layout="['delete', 'edit', 'save']">
        </lt-table-column-operation>
      </lt-sort-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogShow = false"> 取消</el-button>
        <el-button type="primary" @click="dialogShow = false"> 确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import i18n from "@/utils/i18n"
import propertyMixin from "@/components/form-designer/setting-panel/property-editor/propertyMixin"
import IconSelect from '@/components/IconSelect'
import ColorPicker from '@/components/ColorPicker/index.vue'

export default {
  name: "customSteps-editor",
  mixins: [i18n, propertyMixin],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  components: { IconSelect, ColorPicker },
  data() {
    return {
      dialogShow: false,
      rowObj: {
        id: '',
        label: '',
        value: '',
        icon: '',
        disabled: false,
        color: "",
        isEdit: true
      },
      options: [{
        value: 'horizontal',
        label: '水平'
      }, {
        value: 'vertical',
        label: '垂直'
      }],
    }
  },
  computed: {
    predefineColors() {
      return [
        this.$store.state.settings.theme,
        '#67C23A',
        '#E6A23C',
        '#F56C6C',
        '#909399',
      ]
    }
  },
  methods: {
    selected(e, row) {
      row.icon = e;
    },
    addBefore() {
      this.rowObj.id = + new Date().getTime();
    },
    save({ data }) {
      if (data.label === '' || data.value === '') {
        this.$notify.error({ message: '标题和值不能为空' });
        return;
      }
      this.$notify.success({ message: '保存成功' });
      this.$refs.operationRef.saveComplete(data);
    },
    custDelete({ index }) {
      this.optionModel.customSteps.splice(index, 1);
      this.$notify.success({ message: '删除成功' });
    },
  }
}
</script>

<style lang="scss" scoped></style>