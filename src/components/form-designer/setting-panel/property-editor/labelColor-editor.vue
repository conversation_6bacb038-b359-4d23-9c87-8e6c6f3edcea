<template>
  <div>
        <el-form-item label="标题颜色:">
          <el-color-picker
            size="small"
            v-model="optionModel.labelColor"
            show-alpha
            :predefine="predefineColors"
          ></el-color-picker>
        </el-form-item>
  </div>
</template>

<script>
import i18n from "@/utils/i18n";
export default {
  name: "labelColor-editor",
  mixins: [i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  data() {
    return {
    };
  },
  computed: {
    predefineColors() {
      return [
        this.$store.state.settings.theme,
        '#67C23A',
        '#E6A23C',
        '#F56C6C',
        '#909399',
      ]
    }
  },
  methods: {},
};
</script>

