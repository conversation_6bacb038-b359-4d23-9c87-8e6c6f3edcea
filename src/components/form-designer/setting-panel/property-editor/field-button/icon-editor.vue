<template>
  <div>
    <el-form-item :label="i18nt('designer.setting.appendButtonIcon')">
      <el-popover
        placement="bottom-start"
        width="460"
        trigger="click"
        @show="$refs['iconSelect'].reset()"
      >
        <IconSelect ref="iconSelect" @selected="selected" />
        <el-input slot="reference" placeholder="点击选择图标" readonly>
          <svg-icon
            v-if="optionModel.icon"
            slot="prefix"
            :icon-class="optionModel.icon"
            class="el-input__icon"
            style="height: 32px; width: 16px"
          />
          <i v-else slot="prefix" class="el-icon-search el-input__icon" />
        </el-input>
      </el-popover>
    </el-form-item>
    <el-form-item label="标签颜色">
      <el-color-picker
        v-model="optionModel.iconColor"
        show-alpha
        :predefine="predefineColors"
        size="mini"
      ></el-color-picker>
    </el-form-item>
  </div>
</template>

<script>
import i18n from "@/utils/i18n";
import IconSelect from "@/components/IconSelect";
export default {
  name: "icon-editor",
  mixins: [i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  components: { IconSelect },
  data() {
    return {
    };
  },
  computed: {
    predefineColors() {
      return [
        this.$store.state.settings.theme,
        '#67C23A',
        '#E6A23C',
        '#F56C6C',
        '#909399',
      ]
    }
  },
  methods: {
    selected(e, index) {
      this.optionModel.icon = e;
    },
  },
};
</script>

<style scoped>
</style>
