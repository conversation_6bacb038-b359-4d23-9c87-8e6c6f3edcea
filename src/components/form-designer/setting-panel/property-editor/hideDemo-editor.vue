<template>
  <div>
    <el-form-item :label="i18nt('designer.setting.hideDemo')">
      <el-switch v-model="optionModel.hideDemo"></el-switch>
    </el-form-item>
    <el-form-item :label="i18nt('designer.setting.hideField')">
      <el-switch v-model="optionModel.hideField"></el-switch>
    </el-form-item>
  </div>
</template>

<script>
  import i18n from "@/utils/i18n"

  export default {
    name: "hideDemo-editor",
    mixins: [i18n],
    props: {
      designer: Object,
      selectedWidget: Object,
      optionModel: Object,
    },
  }
</script>

<style scoped>

</style>
