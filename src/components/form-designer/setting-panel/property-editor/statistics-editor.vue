<template>
  <div class="container">
    <el-form-item label="标题文字">
      <el-input
        v-model="optionModel.titleContent"
        clearable
        placeholder="请输入标题内容"
      />
    </el-form-item>
    <el-form-item label="标题文字字体大小">
      <el-input-number
        v-model="optionModel.titleSize"
        :precision="0"
        label="请输入标题文字字体大小"
      />
    </el-form-item>
    <el-form-item label="字体颜色">
      <ColorPicker
        v-model="optionModel.statisticsColor"
      />
    </el-form-item>
    <el-form-item label="整体背景色">
      <ColorPicker
        v-model="optionModel.backgdColor"
      />
    </el-form-item>
    <el-form-item label="统计标题颜色">
      <ColorPicker
        v-model="optionModel.titleColor"
      />
    </el-form-item>
    <el-form-item label="统计标题字体大小">
      <el-input-number
        v-model="optionModel.labelSize"
        :precision="0"
        label="请输入统计标题字体大小"
      />
    </el-form-item>
    <el-form-item label="统计值颜色">
      <ColorPicker
        v-model="optionModel.textColor"
      />
    </el-form-item>
    <el-form-item label="统计值字体大小">
      <el-input-number
        v-model="optionModel.valueSize"
        :precision="0"
        label="请输入统计值字体大小"
      />
    </el-form-item>
    <el-form-item label="分隔线颜色">
      <ColorPicker
        v-model="optionModel.lineColor"
      />
    </el-form-item>
  </div>
</template>

<script>
  import ColorPicker from '@/components/ColorPicker/index.vue'
  export default {
    name: "statistics-editor",
    props: {
      designer: Object,
      selectedWidget: Object,
      optionModel: Object,
    },
    components: {
      ColorPicker,
    },
  }
</script>
<style lang="scss" scoped>
</style>