<template>
  <el-form-item :label="i18nt('designer.setting.label')">
    <el-input type="text" v-model="optionModel.label" @change="change"></el-input>
  </el-form-item>
</template>

<script>
import i18n from "@/utils/i18n";
import { MD5 } from "@/assets/js/md5.js";
import axios from "axios";
export default {
  name: "label-editor",
  mixins: [i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  methods: {
    change(e) {
      if (e) {
        this.translate(e);
      }
    },
    translate(text) {
      var appid = "20240220001968242";
      var key = "e8FLQ2OsQjsl3WU4nuCN";
      var salt = new Date().getTime();
      var query = text;
      var from = "zh";
      var to = "en";
      var str1 = appid + query + salt + key;
      var sign = MD5(str1);

      axios
        .get(
          "/baiduApi?q=" +
          query +
          "&from=" +
          from +
          "&to=" +
          to +
          "&appid=" +
          appid +
          "&salt=" +
          salt +
          "&sign=" +
          sign,
        )
        .then((response) => {
          let res = response.data.trans_result[0].dst
          let str = res.split(' ').map(item => item.slice(0, 1).toUpperCase() + item.slice(1).toLowerCase()).join('')

          if (str.includes('-')) {
            str = str.split('-').map(item => item.slice(0, 1).toUpperCase() + item.slice(1).toLowerCase()).join('')
          }
          if (str.length > 1) {
            str = str.charAt(0).toLowerCase() + str.slice(1)
            this.optionModel.alias = str
          }
        });
    },
  },
};
</script>

<style lang="scss" scoped>
</style>
