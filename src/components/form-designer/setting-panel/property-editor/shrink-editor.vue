<template>
  <el-form-item label="是否开启收缩按钮">
    <el-switch v-model="optionModel.shrink" />
  </el-form-item>
</template>

<script>
import i18n from '@/utils/i18n';
import propertyMixin from '@/components/form-designer/setting-panel/property-editor/propertyMixin';

export default {
  name: 'shrink-editor',
  mixins: [i18n, propertyMixin],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  }
};
</script>
