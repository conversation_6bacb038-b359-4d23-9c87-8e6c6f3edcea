<template>
  <div>
    <el-form-item label="显示类型">
      <el-radio-group v-model="optionModel.showType" size="mini">
        <el-radio-button label="text"> 文字 </el-radio-button>
        <el-radio-button label="icon"> 图标 </el-radio-button>
      </el-radio-group>
    </el-form-item>
    <el-form-item label="文字内容" v-if="optionModel.showType == 'text'">
      <el-input v-model="optionModel.textContent"></el-input>
    </el-form-item>
    <template v-else>
      <el-form-item label="图标">
        <el-popover
          placement="bottom-start"
          width="460"
          trigger="click"
          @show="$refs['iconSelect'].reset()"
        >
          <IconSelect ref="iconSelect" @selected="selected($event)" />
          <el-input slot="reference" readonly>
            <svg-icon
              v-if="optionModel.textIcon"
              slot="prefix"
              :icon-class="optionModel.textIcon"
              class="el-input__icon"
              style="height: 32px; width: 16px"
            />
            <i v-else slot="prefix" class="el-icon-search el-input__icon" />
          </el-input>
        </el-popover>
      </el-form-item>

      <el-form-item label="提示内容">
        <el-input v-model="optionModel.tooltip"></el-input>
      </el-form-item>
      <el-form-item label="提示主题">
        <el-radio-group v-model="optionModel.effect" size="mini">
          <el-radio-button label="dark"> dark </el-radio-button>
          <el-radio-button label="light"> light </el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="提示位置">
        <el-select v-model="optionModel.placement" size="mini">
          <el-option label="上左" value="top-start"></el-option>
          <el-option label="上" value="top"></el-option>
          <el-option label="上右" value="top-end"></el-option>
          <el-option label="左上" value="left-start"></el-option>
          <el-option label="左" value="left"></el-option>
          <el-option label="左下" value="left-end"></el-option>
          <el-option label="下左" value="bottom-start"></el-option>
          <el-option label="下" value="bottom"></el-option>
          <el-option label="下右" value="bottom-end"></el-option>
          <el-option label="右上" value="right-start"></el-option>
          <el-option label="右" value="right"></el-option>
          <el-option label="右下" value="right-end"></el-option>
        </el-select>
      </el-form-item>
    </template>

    <el-form-item label="字体大小">
      <el-input-number
        :min="0"
        v-model="optionModel.fontSize"
      ></el-input-number>
    </el-form-item>
  </div>
</template>

<script>
import i18n from "@/utils/i18n";
import IconSelect from "@/components/IconSelect";
export default {
  name: "textContent-editor",
  mixins: [i18n],
  components: { IconSelect },
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  data() {
    return {
    };
  },
  computed: {
    predefineColors() {
      return [
        this.$store.state.settings.theme,
        '#67C23A',
        '#E6A23C',
        '#F56C6C',
        '#909399',
      ]
    }
  },
  methods: {
    selected(e) {
      this.optionModel.textIcon = e;
    },
  },
};
</script>

<style scoped>
</style>
