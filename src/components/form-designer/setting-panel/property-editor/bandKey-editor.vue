<template>
  <el-form-item :label="i18nt('designer.setting.bandKey')">
    <el-select v-model="optionModel.bandKey" filterable clearable allow-create>
      <el-option v-for="(item, index) in options"
                 :key="index" :label="item.columnDesc"
                 :value="item.columnName"></el-option>
    </el-select>
  </el-form-item>
</template>

<script>
  import { queryTableColumn } from '@/api/tool/form'
  import i18n from "@/utils/i18n"
  export default {
    name: "bandKey-editor",
    mixins: [i18n],
    props: {
      designer: Object,
      selectedWidget: Object,
      optionModel: Object
    },
    data(){
      return {
        options: []
      }
    },
    watch: {
      optionModel: {
        handler(newName, oldName) {
          // this.initial();
        },
        immediate: true,
        deep: true,
      }
    },
    mounted() {
    },
    methods:{
      selectBlur(e){
        if(e.target.value){
           this.optionModel.bandKey = e.target.value
        }
      },
      initial() {
        this.options = []
        let parent =  this.searchParentWidget(this.selectedWidget.id)
        if (this.designer.formConfig.dataTable && (parent == null?true: (parent.type != 'sub-form' && parent.type != 'trends-tab'))){
          queryTableColumn(this.designer.formConfig.dataTable).then(res => {
            this.options = res.data
          })
        } else if (parent != null) {
          let parentKey = parent.options.bandKey
          if (parentKey) {
            queryTableColumn(this.designer.formConfig.dataTable +"_"+parentKey+"_sub").then(res => {
              this.options = res.data
            })
          }
        }
      },
      searchParentWidget(thisWidgetId) {
        let result = this.f(this.designer.widgetList, thisWidgetId)
        if ('boolean' == typeof result){
          return null
        } else {
          return result
        }
      },
      f(widgetList, thisWidgetId) {
        for (let i = 0; i < widgetList.length; i++) {
          let widget = widgetList[i]
          if (widget.id == thisWidgetId){
            return true
          } else if (widget.widgetList || widget.cols){
            let re = this.f(widget.widgetList || widget.cols, thisWidgetId)
            if (typeof re != 'boolean' ){
              return re
            } else if(re){
              return widget
            }
          }
        }
        return false
      }
    },
    computed: {
      noLabelSetting() {
        return (this.selectedWidget.type === 'static-text') || (this.selectedWidget.type === 'html-text')
        //|| (this.selectedWidget.type === 'divider')
      },

    }
  }
</script>

<style lang="scss" scoped>

</style>
