<template>
  <el-form-item label="宽度(%)" v-if="formConfig.layoutType === 'PC'">
    <el-input type="number" v-model="optionModel.dialogWidth"></el-input>
  </el-form-item>
  <el-form-item label="Pad宽度(%)" v-else-if="formConfig.layoutType === 'Pad'">
    <el-input type="number" v-model="optionModel.dialogWidthPad"></el-input>
  </el-form-item>
</template>

<script>
import i18n from "@/utils/i18n"

export default {
  name: "dialogWidth-editor",
  mixins: [i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  computed: {
    formConfig() {
      return this.designer.formConfig
    },
  },
  watch: {
    optionModel: {
      immediate: true,
      handler(val) {
        // 如果没有这个字段则添加这个字段
        if (val.dialogWidthPad == null) {
          this.$set(this.optionModel, 'dialogWidthPad', 100);
        }
      },
      deep: true,
    },
  },
}
</script>

<style scoped></style>
