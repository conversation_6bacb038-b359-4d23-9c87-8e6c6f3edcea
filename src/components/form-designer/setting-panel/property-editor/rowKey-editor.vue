<template>
  <el-form-item>
    <template #label>
      <div style="color: red;">主键</div>
    </template>
    <el-input type="text" v-model="optionModel.rowKey" class="rowKeyInput" ></el-input>
  </el-form-item>
</template>

<script>
  import i18n from "@/utils/i18n"

  export default {
    name: "rowKey-editor",
    mixins: [i18n],
    props: {
      designer: Object,
      selectedWidget: Object,
      optionModel: Object,
    },
  }
</script>

<style scoped>

</style>
