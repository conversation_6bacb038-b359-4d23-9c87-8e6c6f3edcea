<template>
  <div>
    <el-form-item label="模型配置">
      <el-select v-model="optionModel.optionsModel" @change="changeModel" @visible-change="handleVisibleChange" clearable>
        <el-option v-for="item in modelArray" :key="item.tableName" :label="item.tableComment" :value="item.tableName">
        </el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="标题">
      <el-select v-model="optionModel.modelLabel">
        <el-option v-for="item in cols" :key="item.columnName" :label="item.columnComment" :value="item.columnName">
        </el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="值">
      <el-select v-model="optionModel.modelValue">
        <el-option v-for="item in cols" :key="item.columnName" :label="item.columnComment" :value="item.columnName">
        </el-option>
      </el-select>
    </el-form-item>
  </div>

</template>

<script>
import i18n from "@/utils/i18n"
import fieldMixin from '@/components/form-designer/form-widget/field-widget/fieldMixin';
export default {
  name: "optionsModel-editor",
  mixins: [i18n,fieldMixin],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  data() {
    return {
      oldVal: undefined,
    }
  },
  computed: {
    setup() {
      return this.$store.state.design
    },
    cols() {
      if (this.modelArray && this.modelArray.length > 0) {
        let table = this.modelArray.filter(e => e.tableName == this.optionModel.optionsModel)
        if (table != null && table.length > 0) {
          return table[0].columns
        }
        return []
      }
    },
    modelArray(){
      let tables = this.setup.tables;
      return (this.setup.refBus?tables.concat(this.$store.state["refModel"]):tables)
    }
  },
  watch: {
    optionModel: {
      immediate: true,
      handler(val) {
        // 如果没有这个字段则添加这个字段
        if (!val.optionsModel) this.$set(val, 'optionsModel', '');
        if (!val.modelLabel) this.$set(val, 'modelLabel', '');
        if (!val.modelValue) this.$set(val, 'modelValue', '');

      },
      deep: true,
    },
  },
  methods: {
    handleVisibleChange(e) {
      if (e) {
        this.oldVal = this.optionModel.optionsModel;
      }
    },
    changeModel() {
      if (this.cols.length >= 2) {
        this.optionModel.modelLabel = this.cols[0].columnName;
        this.optionModel.modelValue = this.cols[1].columnName;
      } else {
        this.optionModel.modelLabel = null;
        this.optionModel.modelValue = null;
      }
      this.checkMutex('optionsModel', this.optionModel.optionsModel, this.oldVal)
    },
  }
}
</script>

<style scoped>
</style>
