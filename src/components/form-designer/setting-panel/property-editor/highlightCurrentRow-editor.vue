<template>
  <div>
    <el-form-item label="单选">
      <el-switch
        v-model="optionModel.highlightCurrentRow"
        :min="1"
        class="hide-spin-button"
        style="width: 100%"
      ></el-switch>
    </el-form-item>
    <el-form-item v-if="optionModel.highlightCurrentRow" label="选择事件">
      <el-button type="primary" icon="el-icon-edit" plain round @click="WriteCode">
        编写代码</el-button
      >
    </el-form-item>
    <EventEditor
      ref="eventEditorRef"
      :designer="designer"
      title="选择事件函数"
      eventHeader="((currentRow, oldCurrentRow) => {"
      :eventHandlerCode="optionModel.onCurrentChange"
      @saveEventHandler="saveEventHandler"
      :key="codeEditorKey"
    >
    </EventEditor>
  </div>
</template>

<script>
import i18n from '@/utils/i18n';
import propertyMixin from '@/components/form-designer/setting-panel/property-editor/propertyMixin';
import EventEditor from '@/components/EventEditor/index';
export default {
  name: 'highlightCurrentRow-editor',
  mixins: [i18n, propertyMixin],
  components: {
    EventEditor,
  },
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  data() {
    return {
      codeEditorKey: 0,
    };
  },
  methods: {
    WriteCode() {
      this.codeEditorKey = new Date().getTime() + 1;
      this.$nextTick(() => {
        this.$refs.eventEditorRef.open(true);
      });
    },
    saveEventHandler(eventCode) {
      this.optionModel.onCurrentChange = eventCode
    },
  },
};
</script>

<style scoped></style>
