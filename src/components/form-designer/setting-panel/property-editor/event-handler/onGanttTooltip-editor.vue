<template>
  <el-form-item
    :label="langDic.onGanttTooltip"
    title="onGanttTooltip"
    label-width="150px"
  >
    <el-button
      type="primary"
      icon="el-icon-edit"
      plain
      round
      @click="editEventHandler('onGanttTooltip', eventParams)"
    >
      {{ i18nt('designer.setting.addEventHandler') }}</el-button
    >
  </el-form-item>
</template>

<script>
import i18n from '@/utils/i18n';
import eventMixin from '@/components/form-designer/setting-panel/property-editor/event-handler/eventMixin';
export default {
  name: 'onGanttTooltip-editor',
  mixins: [i18n, eventMixin],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  data() {
    return {
      eventParams: ['task'],
    };
  },
  watch: {
    optionModel: {
      immediate: true,
      handler(val) {
        // 如果没有这个字段则添加这个字段
        if (!'onGanttTooltip' in val) {
          this.$set(this.optionModel, 'onGanttTooltip', '');
        }
      },
      deep: true,
    },
  },
};
</script>

<style scoped></style>
