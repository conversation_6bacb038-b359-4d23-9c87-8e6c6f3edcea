<template>
  <el-form-item :label="langDic.onBlurChange" title="onBlurChange" label-width="150px">
    <el-button
      type="primary"
      icon="el-icon-edit"
      plain
      round
      @click="editEventHandler('onBlurChange', eventParams)"
    >
      {{ i18nt('designer.setting.addEventHandler') }}</el-button
    >
  </el-form-item>
</template>

<script>
import i18n from '@/utils/i18n';
import eventMixin from '@/components/form-designer/setting-panel/property-editor/event-handler/eventMixin';

export default {
  name: 'onBlurChange-editor',
  mixins: [i18n, eventMixin],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  data() {
    return {
      eventParams: [
        'val',
        'fieldName',
        'rowIndex',
        'colIndex',
        'rowData',
        'colData',
        'extra',
      ],
    };
  },
  watch: {
    optionModel: {
      immediate: true,
      handler(val) {
        // 如果没有这个字段则添加这个字段
        if (val.onBlurChange == null || val.onBlurChange === '') {
          this.$set(
            this.optionModel,
            'onBlurChange',
            `/*
  表格开启行内编辑后input的blur回调
    val: String|Number,  // 值
    fieldName: String,   // 字段名
    rowIndex: Number     // 所在的行索引
    colIndex: Number     // 所在的列索引
    rowData: Object      // 行数据
    colData: Object      // 列的相对属性
    extra: Object        // 额外参数
*/
`,
          );
        }
      },
      deep: true,
    },
  },
};
</script>

<style scoped></style>
