import emitter from 'element-ui/lib/mixins/emitter'
import propertyRegister from '../../propertyRegister.js';
const {EVENT_LANG_DIC} =propertyRegister
export default {
  mixins: [emitter],
  data(){
    return{
      langDic:EVENT_LANG_DIC
    }
  },
  created() {
  },
  methods: {
    editEventHandler(eventName, eventParams) {
      this.dispatch('SettingPanel', 'editEventHandler', [eventName, [...eventParams]])
    },

  }
}
