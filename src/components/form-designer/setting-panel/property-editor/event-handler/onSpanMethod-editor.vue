<template>
  <el-form-item :label="langDic.onSpanMethod" title="onSpanMethod" label-width="150px">
    <el-button
      type="primary"
      icon="el-icon-edit"
      plain
      round
      @click="editEventHandler('onSpanMethod', eventParams)"
    >
      {{ i18nt("designer.setting.addEventHandler") }}</el-button
    >
  </el-form-item>
</template>

<script>
import i18n from "@/utils/i18n";
import eventMixin from "@/components/form-designer/setting-panel/property-editor/event-handler/eventMixin";

export default {
  name: "onSpanMethod-editor",
  mixins: [i18n, eventMixin],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  data() {
    return {
      eventParams: ["row", "column", "rowIndex", "columnIndex"],
    };
  },
  watch: {
    optionModel: {
      immediate: true,
      handler(val) {
        // 如果没有这个字段则添加这个字段
        if (val.onSpanMethod === "") {
          this.$set(
            this.optionModel,
            "onSpanMethod",
            `//  当前行row、当前列column、  当前行号rowIndex、当前列号columnIndex 该函数可以返回一个包含两个元素的数组，第一个元素代表rowspan，第二个元素代表colspan。 也可以返回一个键名为rowspan和colspan的对象。`
          );
        }
      },
      deep: true,
    },
  },
};
</script>

<style scoped>
</style>
