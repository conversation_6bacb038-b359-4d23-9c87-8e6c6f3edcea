<template>
  <el-form-item :label="langDic.onReceive" title="onReceive" label-width="150px">
    <el-button
      type="primary"
      icon="el-icon-edit"
      plain 
      round
      @click="editEventHandler('onReceive', eventParams)"
    >
      编写代码</el-button
    >
  </el-form-item>
</template>

<script>
import i18n from '@/utils/i18n';
import eventMixin from '@/components/form-designer/setting-panel/property-editor/event-handler/eventMixin';

export default {
  name: 'onReceive-editor',
  mixins: [i18n, eventMixin],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  data() {
    return {
      eventParams: ['event','params'],
    };
  },
  watch: {
    optionModel: {
      immediate: true,
      handler(val) {
        // 如果没有这个字段则添加这个字段
        if (val.onReceive == null) {
          this.$set(
            this.optionModel,
            'onReceive',
            `/** 接收并处理其它组件通过'$emitUp'方法传过来的参数
 * 其它组件中可通过this.$emitUp(event, params)触发此处函数
 * event 事件名称 String 用于区分处理不同的事件
 * params 参数 类型和其它组件传值一致
 */
`,
          );
        }
      },
      deep: true,
    },
  },
};
</script>

<style scoped></style>
