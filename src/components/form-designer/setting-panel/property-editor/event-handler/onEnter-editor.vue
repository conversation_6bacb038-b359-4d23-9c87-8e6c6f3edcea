<template>
  <el-form-item :label="langDic.onEnter" title="onEnter" label-width="150px">
    <el-button type="primary" icon="el-icon-edit" plain round @click="editEventHandler('onEnter', eventParams)">
      {{i18nt('designer.setting.addEventHandler')}}</el-button>
  </el-form-item>
</template>

<script>
  import i18n from "@/utils/i18n"
  import eventMixin from "@/components/form-designer/setting-panel/property-editor/event-handler/eventMixin"

  export default {
    name: "onEnter-editor",
    mixins: [i18n, eventMixin],
    props: {
      designer: Object,
      selectedWidget: Object,
      optionModel: Object,
    },
    data() {
      return {
        eventParams: ['value'],
      }
    },
    watch: {
    optionModel: {
      immediate: true,
      handler(val) {
        // 如果没有这个字段则添加这个字段
        if (val.onEnter == null) {
          this.$set(this.optionModel, 'onEnter', '');
        }
      },
      deep: true,
    },
  },
  }
</script>

<style scoped>

</style>
