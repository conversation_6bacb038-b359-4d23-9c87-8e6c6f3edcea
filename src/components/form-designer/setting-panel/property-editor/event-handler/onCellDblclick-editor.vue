<template>
  <el-form-item :label="langDic.onCellDblclick" title="onCellDblclick" label-width="150px">
    <el-button
      type="primary"
      icon="el-icon-edit"
      plain
      round
      @click="editEventHandler('onCellDblclick', eventParams)"
    >
      {{ i18nt("designer.setting.addEventHandler") }}</el-button
    >
  </el-form-item>
</template>

<script>
import i18n from "@/utils/i18n";
import eventMixin from "@/components/form-designer/setting-panel/property-editor/event-handler/eventMixin";

export default {
  name: "onCellDblclick-editor",
  mixins: [i18n, eventMixin],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  data() {
    return {
      eventParams: [
        "row",
        "column",
        "cell",
        "event",
        "rowIndex",
        "columnIndex",
      ],
    };
  },
  watch: {
    optionModel: {
      immediate: true,
      handler(val) {
        // 如果没有这个字段则添加这个字段
        if (val.onCellDblclick == null) {
          this.$set(this.optionModel, "onCellDblclick", "");
        }
      },
      deep: true,
    },
  },
};
</script>

<style scoped></style>
