<template>
  <el-form-item :label="langDic.onRowClass" title="onRowClass" label-width="150px">
    <el-button
      type="primary"
      icon="el-icon-edit"
      plain
      round
      @click="editEventHandler('onRowClass', eventParams)"
    >
      {{ i18nt("designer.setting.addEventHandler") }}</el-button
    >
  </el-form-item>
</template>

<script>
import i18n from "@/utils/i18n";
import eventMixin from "@/components/form-designer/setting-panel/property-editor/event-handler/eventMixin";

export default {
  name: "onRowClass-editor",
  mixins: [i18n, eventMixin],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  data() {
    return {
      eventParams: ["row", "column", "rowIndex", "columnIndex"],
    };
  },
  watch: {
    optionModel: {
      immediate: true,
      handler(val) {
        // 如果没有这个字段则添加这个字段
        if (!val.onRowClass || val.onRowClass === "") {
          this.$set(
            this.optionModel,
            "onRowClass",
            `// return {
// 'background': 'red !important'
// }`
          );
        }
      },
      deep: true,
    },
  },
};
</script>

<style scoped>
</style>
