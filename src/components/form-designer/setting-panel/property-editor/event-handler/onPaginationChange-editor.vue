<template>
  <el-form-item :label="langDic.onPaginationChange" title="onPaginationChange" label-width="150px">
    <el-button
      type="primary"
      icon="el-icon-edit"
      plain
      round
      @click="editEventHandler('onPaginationChange', eventParams)"
    >
      {{ i18nt("designer.setting.addEventHandler") }}</el-button
    >
  </el-form-item>
</template>

<script>
import i18n from "@/utils/i18n";
import eventMixin from "@/components/form-designer/setting-panel/property-editor/event-handler/eventMixin";

export default {
  name: "onPaginationChange-editor",
  mixins: [i18n, eventMixin],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  data() {
    return {
      eventParams: [
        "current",
        "size",
        "total",
      ],
    };
  },
   mounted(){
     if(!this.optionModel.hasOwnProperty("onPaginationChange")){
        this.$set(this.optionModel, "onPaginationChange", '')
     }
   }
};
</script>

<style scoped></style>
