<template>
  <el-form-item label-width="150px" title="onLazy">
    <template slot="label">
      {{langDic.onLazy}}
      <el-tooltip effect="light" content="使用传入的resolve方法返回数据">
        <i class="el-icon-info" />
      </el-tooltip>
    </template>
    <el-button
      type="primary"
      icon="el-icon-edit"
      plain
      round
      @click="editEventHandler('onLazy', eventParams)"
    >
      {{ i18nt("designer.setting.addEventHandler") }}</el-button
    >
  </el-form-item>
</template>

<script>
import i18n from "@/utils/i18n";
import eventMixin from "@/components/form-designer/setting-panel/property-editor/event-handler/eventMixin";

export default {
  name: "onLazy-editor",
  mixins: [i18n, eventMixin],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  computed:{
      eventParams(){
          return this.optionModel.hasOwnProperty('cols')?['tree','treeNode','resolve']:["node", "resolve"]
      } 
    
  },
};
</script>

<style scoped>
</style>
