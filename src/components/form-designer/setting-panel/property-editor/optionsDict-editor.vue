<template>
  <div>
    <el-form-item label="绑定字典">
      <el-select v-model="optionModel.optionsDict" @change="changeModel" @visible-change="handleVisibleChange" filterable clearable @focus="getModelArray()">
        <el-option v-for="item in dictList" :key="item.dictName" :label="item.dictName" :value="item.dictType">
        </el-option>
      </el-select>
    </el-form-item>
  </div>

</template>

<script>
import i18n from "@/utils/i18n"
import { listType } from "@/api/system/dict/type.js"
import fieldMixin from '@/components/form-designer/form-widget/field-widget/fieldMixin';
export default {
  name: "optionsDict-editor",
  mixins: [i18n, fieldMixin],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  data() {
    return {
      dictList: [],
      oldVal: undefined,
    }
  },
  computed: {
    setup() {
      return this.$store.state.design
    },
    cols() {
      if (this.modelArray && this.modelArray.length > 0) {
        let table = this.modelArray.filter(e => e.tableName == this.optionModel.optionsModel)
        if (table != null && table.length > 0) {
          return table[0].columns
        }
        return []
      }
    }
  },
  mounted() {
    listType().then(res => {
      this.dictList = res.rows
    })
  },
  watch: {
    optionModel: {
      immediate: true,
      handler(val) {
        // 如果没有这个字段则添加这个字段
        if (!val.optionsDict) this.$set(val, 'optionsDict', '');

      },
      deep: true,
    },
  },
  methods: {
    handleVisibleChange(e) {
      if (e) {
        this.oldVal = this.optionModel.optionsDict;
      }
    },
    changeModel() {
      if (this.cols.length >= 2) {
        this.optionModel.modelLabel = this.cols[0].columnName;
        this.optionModel.modelValue = this.cols[1].columnName;
      } else {
        this.optionModel.modelLabel = null;
        this.optionModel.modelValue = null;
      }
      this.checkMutex('optionsDict', this.optionModel.optionsDict, this.oldVal)
    },
    getModelArray() {
      this.$nextTick(() => {
        let tables = this.setup.tables;
        if (this.setup.refBus) {
          let model = tables.concat(this.$store.state["refModel"])
          this.modelArray = model
        } else {
          this.modelArray = tables
        }
      })
    },
  }
}
</script>

<style scoped>
</style>
