<template>
  <el-form-item v-if="!optionModel.height && !optionModel.heightFit && !optionModel.minimumHeight " label="固定高度">
    <template #label>
      <span> 固定高度 </span>
      <el-tooltip
        effect="light"
        content="固定高度为空时，可设置最大高度或最小高度"
      >
        <i class="el-icon-info" />
      </el-tooltip>
    </template>
    <el-input type="number" v-model="optionModel.minHeight"></el-input>
  </el-form-item>
</template>

<script>
  import i18n from "@/utils/i18n"

  export default {
    name: "minHeight-editor",
    mixins: [i18n],
    props: {
      designer: Object,
      selectedWidget: Object,
      optionModel: Object,
    },
  }
</script>

<style scoped>

</style>
