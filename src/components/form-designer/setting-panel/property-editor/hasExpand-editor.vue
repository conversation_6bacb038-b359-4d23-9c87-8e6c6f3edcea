<template>
  <el-form-item label="开启展开行">
    <el-switch v-model="optionModel.hasExpand" @change="switchChange" />
  </el-form-item>
</template>

<script>
import i18n from '@/utils/i18n';
import propertyMixin from '@/components/form-designer/setting-panel/property-editor/propertyMixin';

export default {
  name: 'hasExpand-editor',
  mixins: [i18n, propertyMixin],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  watch: {
    optionModel: {
      immediate: true,
      handler(val) {
        // 如果没有这个字段则添加这个字段
        if (!('hasExpand' in val)) {
          // alert('设置为true');
          this.$set(this.optionModel, 'hasExpand', false);
        }
      },
      deep: true,
    },
  },
  methods: {
    switchChange(e) {
      if (e) {
        this.$confirm(
          '使用展开行这个功能,需要设置主键才能正常使用,请去进行设置主键?',
          '提示',
          {
            confirmButtonText: '好,这就去设置主键',
            cancelButtonText: '算了,我不想用这个功能',
            type: 'warning',
          },
        )
          .then(() => {
            document.querySelector('.rowKeyInput').scrollIntoView({
              behavior: 'smooth',
              block: 'start',
              inline: 'nearest',
            });

            document.querySelector('.rowKeyInput input').focus(); // 聚焦 input 元素
          })
          .catch(() => {
            this.optionModel.hasExpand = false;
          });
      }
    },
  },
};
</script>

<style scoped></style>
