<template>
  <el-form-item label="组件别名">
    <el-input type="text" v-model="optionModel.alias" @change="change"></el-input>
  </el-form-item>
</template>

<script>
import i18n from "@/utils/i18n"
import { keywordList } from "../../../../utils/enum";

export default {
  name: "alias-editor",
  mixins: [i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  watch: {
    optionModel: {
      immediate: true,
      handler(val) {
        // 如果没有这个字段则添加这个字段
        if (!('alias' in val)) {
          // alert('设置为true');
          this.$set(this.optionModel, 'alias', "");
        }
      },
      deep: true,
    },
  },
  methods: {
    change(e) {
      if (e) {
        let alias = e.replace(/([A-Z])/g, '_\$1').toLowerCase();
        if(this.$enum.keywordList.includes(alias)){
          this.optionModel.alias = ""
          this.$message.warning("无法创建关键字")
        }else{
          this.optionModel.alias = e.replace(/([A-Z])/g, '_\$1').toLowerCase();
        }
      }
    },
  }
}
</script>

<style scoped></style>
