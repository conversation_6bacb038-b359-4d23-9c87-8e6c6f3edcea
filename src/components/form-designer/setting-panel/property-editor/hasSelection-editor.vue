<template>
  <div>
    <el-form-item label="多选">
      <el-switch
        v-model="optionModel.hasSelection"
      ></el-switch>
    </el-form-item>
     <el-form-item label="点击行可选">
      <el-switch
        v-model="optionModel.allowRowSelect"
      ></el-switch>
    </el-form-item>
    <el-form-item label="可控制是否可选">
      <el-switch
        v-model="optionModel.checkIsCustom"
      ></el-switch>
    </el-form-item>
    <el-form-item label="控制字段" v-if="optionModel.checkIsCustom">
      <el-input
        v-model="optionModel.checkField"
      ></el-input>
    </el-form-item>

    <el-form-item label="跨页多选" v-if="optionModel.hasSelection">
      <template #label>
        跨页多选
        <el-tooltip effect="light" content="开启时,切换分页后会保留当前页选中的数据">
          <i class="el-icon-info" />
        </el-tooltip>
      </template>
      <el-switch
        v-model="optionModel.reserveSelection"
      ></el-switch>
    </el-form-item>

    <el-form-item label="个数限制" v-if="optionModel.hasSelection && !optionModel.linkAble">
      <template #label>
        个数限制
        <el-tooltip effect="light" content="需要配置主键才会生效,默认为id">
          <i class="el-icon-info" />
        </el-tooltip>
      </template>
      <el-input-number
       :min="0"
        v-model="optionModel.selectionLimit"
        style="width: 100px"
      />
    </el-form-item>
    <el-form-item v-if="optionModel.hasSelection" label="选择事件">
      <el-button type="primary" icon="el-icon-edit" plain round @click="WriteCode">
        编写代码</el-button
      >
    </el-form-item>
    <EventEditor
      ref="eventEditorRef"
      :designer="designer"
      title="选择事件函数"
      eventHeader="((val) => {"
      :eventHandlerCode="optionModel.onSelection"
      @saveEventHandler="saveEventHandler"
      :key="codeEditorKey"
    >
    </EventEditor>
  </div>
</template>

<script>
import i18n from '@/utils/i18n';
import propertyMixin from '@/components/form-designer/setting-panel/property-editor/propertyMixin';
import EventEditor from '@/components/EventEditor/index';
export default {
  name: 'hasSelection-editor',
  mixins: [i18n, propertyMixin],
  components: {
    EventEditor,
  },
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  data() {
    return {
      codeEditorKey: 0,
    };
  },
  methods: {
    WriteCode() {
      this.codeEditorKey = new Date().getTime() + 1;
      this.$nextTick(() => {
        this.$refs.eventEditorRef.open(true);
      })
    },
    saveEventHandler(eventCode) {
      this.optionModel.onSelection = eventCode;
    }
  },
  mounted(){
    if(!this.optionModel.hasOwnProperty('checkIsCustom')){
      this.$set(this.optionModel, 'checkIsCustom', false)
    }
    if(!this.optionModel.hasOwnProperty('checkField')){
      this.$set(this.optionModel, 'checkField', 'allowCheck')
    }
    if(!this.optionModel.hasOwnProperty('allowRowSelect')){
      this.$set(this.optionModel, 'allowRowSelect', false)
    }
    if(!this.optionModel.hasOwnProperty('reserveSelection')){
      this.$set(this.optionModel, 'reserveSelection', true)
    }
  }
};
</script>

<style scoped></style>
