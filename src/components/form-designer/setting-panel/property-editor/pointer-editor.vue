<template>
  <div>
    <el-form-item label="图片形式">
      <el-radio-group size="mini" v-model="optionModel.imageType" @change="changeImageType">
        <el-radio-button label="fileId">上传</el-radio-button>
        <el-radio-button label="http">网络路径</el-radio-button>
      </el-radio-group>
    </el-form-item>
    <el-form-item label="图片宽度">
      <el-input-number 
        v-model="optionModel.imageWidth"
        :min="0"
        size="mini"
        placeholder="请输入图片宽度"
      ></el-input-number>
    </el-form-item>
    <el-form-item label="图片高度">
      <el-input-number
        v-model="optionModel.imageHeight" 
        :min="0"
        size="mini"
        placeholder="请输入图片高度"
      ></el-input-number>
    </el-form-item>
    <el-form-item label="图片上传" v-if="optionModel.imageType === 'fileId'">
      <lt-upload-list
        ref="fieldEditor"
        :file-id="optionModel.fileId"
        list-type="picture-card"
        :limit="1"
        @onChange="change"
      />
    </el-form-item>
    <el-form-item label="图片路径" v-else>
      <el-input
        v-model="optionModel.imageUrl"
        placeholder="请输入图片路径"
      ></el-input>
    </el-form-item>
    <el-form-item label="标注点配置">
      <el-button @click="dialogShow = true" type="primary"
        >配置标注点</el-button
      >
    </el-form-item>

    <el-dialog
      title="标注点配置"
      width="80%"
      :visible="dialogShow"
      :show-close="false"
    >
      <lt-sort-table
        v-model="optionModel.markers"
        max-height="500"
        row-key="id"
        :isEnable="true"
      >
        <lt-table-column-edit prop="index" label="索引"></lt-table-column-edit>
        <lt-table-column-edit
          prop="type"
          label="点类型"
          state="select"
          :selectOpt="{
            options: [
              { label: '圆形', value: 'circle' },
              { label: '三角形', value: 'triangle' },
              { label: '正方形', value: 'square' },
              { label: '自定义html', value: 'customHtml' },
            ],
            label: 'label',
            value: 'value',

          }"
        ></lt-table-column-edit>
        <lt-table-column-edit prop="color" label="颜色">
          <template #header>
            颜色<el-tooltip
              effect="light"
              content="支持16进制(例: #000)和rgb(例: rgb(255,255,255))"
            >
              <i class="el-icon-info" />
            </el-tooltip>
          </template>
          <template #edit="{ row }">
              <el-color-picker
                v-model="row.color"
                show-alpha
                :predefine="predefineColors"
                size="mini"
              ></el-color-picker>
          </template>
          <template #preview="{ row }">
              <el-color-picker
                v-model="row.color"
                show-alpha
                :predefine="predefineColors"
                size="mini"
              ></el-color-picker>
          </template>
        </lt-table-column-edit>
        <lt-table-column-edit label="尺寸" type="number" prop="size" />
        <lt-table-column-edit label="实心" state="switch" prop="solid" />
        <lt-table-column-edit label="上边距" type="number" prop="top" />
        <lt-table-column-edit label="左边距" type="number" prop="left" />
        <lt-table-column-edit prop="html" label="html内容">
          <template #edit="{ row }">
              <el-link type="primary" :underline="false" @click="editHtml(row)">编辑</el-link>
          </template>
          <template #preview="{ row }">
              <el-link type="primary" :underline="false" @click="editHtml(row)">编辑</el-link>
          </template>
        </lt-table-column-edit>

        <lt-table-column-operation
          v-model="optionModel.markers"
          :row-data="rowObj"
          :unshift="false"
          ref="operationRef"
          @save="save"
          @addBefore="addBefore"
          @delete="deleteMarker"
          :unlimitedAdd="true"
          :validateProp="false"
          primary-key="id"
          :layout="['delete', 'edit', 'save']"
        >
        </lt-table-column-operation>
      </lt-sort-table>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogShow = false"> 取消</el-button>
        <el-button type="primary" @click="dialogShow = false"> 确认</el-button>
      </div>
    </el-dialog>
    <EventEditor
      ref="eventEditorRef"
      :designer="designer"
      title="自定义HTML"
      eventHeader="customHtml(item){"
      :eventHandlerCode="codeSave"
      @saveEventHandler="saveHtml"
      :key="codeEditorKey"
    >
    </EventEditor>
 </div>
</template>

<script>
import EventEditor from "@/components/EventEditor/index";
export default {
  name: "pointer-editor",
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  components: {
    EventEditor,
  },
  computed: {
    predefineColors() {
      return [

        this.$store.state.settings.theme,
        '#67C23A',
        '#E6A23C',
        '#F56C6C',
        '#909399',
      ]
    }
  },
  data() {
    return {
      dialogShow: false,
      rowObj: {
        id: "",
        color:'#409eff',
        index: "",
        type: "circle",
        solid: true,
        top: 0,
        left: 0,
        size:10,
        html:'',
      },
      currentRow:{},
      codeSave:'',
      codeEditorKey: '',
    };
  },


  methods: {
    saveHtml(eventCode) {
      this.codeSave = eventCode;
      this.currentRow.html = this.codeSave;
    },
    editHtml(row) {
      this.currentRow = row;
      this.codeSave = row.html;
      this.codeEditorKey = new Date().getTime() + 1;
      this.$nextTick(() => {
        this.$refs.eventEditorRef.open(true);
      });
    },


    async change() {
      this.$set(this.optionModel, 'fileId', await this.$refs.fieldEditor.upload());
    },

    changeImageType() {
      if (this.optionModel.imageType === "fileId") {
        this.$refs.fieldEditor.clear();
      }
      this.optionModel.imageUrl = "";
    },
    addBefore() {
      this.rowObj.id = Math.random().toString(36).substring(2, 15);
      this.rowObj.index = this.optionModel.markers.length+'';
    },
    deleteMarker(row) {
      this.optionModel.markers.splice(row.index, 1);
      this.$notify.success({ message: "删除成功" });
    },

    save({ data }) {
      // 校验索引唯一性
      const existingIndexes = this.optionModel.markers.filter(item => item.id !== data.id).map(item => item.index);

      if (existingIndexes.includes(data.index)) {
        this.$notify.error({ message: "索引值不能重复" });
        return false;
      }
      this.$notify.success({ message: "保存成功" });
      this.$refs.operationRef.saveComplete(data);
    },
  },
};
</script>
