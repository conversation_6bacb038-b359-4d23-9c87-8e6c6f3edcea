<template>
  <el-form-item v-if="!optionModel.height && !optionModel.heightFit && !optionModel.minHeight " label="最小高度">
    <template #label>
      <span> 最小高度 </span>
      <el-tooltip
        effect="light"
        content="最小高度为空时，可设置最大高度或固定高度"
      >
        <i class="el-icon-info" />
      </el-tooltip>
    </template>
    <el-input type="number" v-model="optionModel.minimumHeight"></el-input>
  </el-form-item>
</template>

<script>
  import i18n from "@/utils/i18n"

  export default {
    name: "minimumHeight-editor",
    mixins: [i18n],
    props: {
      designer: Object,
      selectedWidget: Object,
      optionModel: Object,
    },
  }
</script>

<style scoped>

</style>
