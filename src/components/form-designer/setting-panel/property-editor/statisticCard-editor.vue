<template>
  <div class="container">
    <el-form-item label="宽度">
      <el-input type="text" v-model="optionModel.cardWidth"></el-input>
    </el-form-item>
    <el-form-item label="渐变背景色一">
      <ColorPicker
        v-model="optionModel.gradientColor0"
        :predefine="predefineThemeColors"
      />
    </el-form-item>
    <el-form-item label="渐变背景色二">
      <ColorPicker
        v-model="optionModel.gradientColor1"
        :predefine="predefineThemeColors"
      />
    </el-form-item>
    <el-form-item label="渐变色方向">
      <el-select
        v-model="optionModel.gradientDirection"
      >
        <el-option
          v-for="iconPosition in options"
          :key="iconPosition.value"
          :label="iconPosition.label"
          :value="iconPosition.value"
        />
      </el-select>
    </el-form-item>
    <el-form-item label="单位">
      <el-input
        v-model="optionModel.unit"
        clearable
        placeholder="请输入单位"
      />
    </el-form-item>
    <el-form-item label="单位字体大小">
      <el-input-number
        v-model="optionModel.unitSize"
        :precision="0"
        label="请输入单位字体大小"
      />
    </el-form-item>
    <el-form-item label="单位字体颜色">
      <ColorPicker
        v-model="optionModel.unitColor"
        :predefine="predefineThemeColors"
      />
    </el-form-item>
    <el-form-item label="左右间距">
      <el-input-number
        v-model="optionModel.distance"
        :precision="0"
        label="请输入左右间距"
      />
    </el-form-item>
    <el-form-item label="上下间距">
      <el-input-number
        v-model="optionModel.lineDistance"
        :precision="0"
        label="请输入上下间距"
      />
    </el-form-item>
    <el-form-item label="数据文字类型">
      <el-select
        v-model="optionModel.fontFamily"
      >
        <el-option
          v-for="item in fontFamilyList"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </el-form-item>
    <el-form-item label="第一行文字内容">
      <el-input
        v-model="optionModel.firstLine"
        clearable
        placeholder="请输入第一行文字内容"
      />
    </el-form-item>
    <el-form-item label="第一行字体大小">
      <el-input
        v-model="optionModel.firstSize"
        clearable
        placeholder="请输入第一行字体大小"
      />
    </el-form-item>
    <el-form-item label="第一行字体颜色">
      <ColorPicker
        v-model="optionModel.firstColor"
        :predefine="predefineThemeColors"
      />
    </el-form-item>
    <el-form-item label="第一行字体粗细">
      <el-input-number
        v-model="optionModel.firstWeight"
        :precision="0"
        :step="100"
        label="请输入第一行字体粗细"
      />
    </el-form-item>
    <el-form-item label="第二行文字内容">
      <el-input
        v-model="optionModel.secondLine"
        clearable
        placeholder="请输入第二行文字内容"
      />
    </el-form-item>
    <el-form-item label="第二行字体大小">
      <el-input
        v-model="optionModel.secondSize"
        clearable
        placeholder="请输入第二行字体大小"
      />
    </el-form-item>
    <el-form-item label="第二行字体颜色">
      <ColorPicker
        v-model="optionModel.secondColor"
        :predefine="predefineThemeColors"
      />
    </el-form-item>
    <el-form-item label="第二行字体粗细">
      <el-input-number
        v-model="optionModel.secondWeight"
        :precision="0"
        :step="100"
        label="请输入第二行字体粗细"
      />
    </el-form-item>
    <el-form-item label="图标">
      <div class="flex">
        <div @click="$refs.statisticIconRef.open(optionModel.statisticIcon);">
          <el-input type="text" readonly>
            <el-button slot="prepend" :icon="optionModel.statisticIcon"></el-button>
          </el-input>
        </div>
        <i class="el-icon-delete" @click="delIcon"></i>
      </div>
      <selectElementIcon ref="statisticIconRef" @iconSelect="iconSelect" />
    </el-form-item>
    <el-form-item label="图标大小" v-if="optionModel.statisticIcon">
      <el-input-number
        v-model="optionModel.iconSize"
        :precision="0"
        label="请输入图标大小"
      />
    </el-form-item>
    <el-form-item label="图标颜色" v-if="optionModel.statisticIcon">
      <ColorPicker
        v-model="optionModel.iconColor"
        :predefine="predefineThemeColors"
      />
    </el-form-item>
    <el-form-item label="图标背景色" v-if="optionModel.statisticIcon">
      <ColorPicker
        v-model="optionModel.iconBackColor"
        :predefine="predefineThemeColors"
      />
    </el-form-item>
    <el-form-item label="显示模式">
      <el-select v-model="optionModel.mode" filterable allow-create>
        <el-option v-for="item in modeList" :label="item.label" :value="item.value" :key="item.value"></el-option>
      </el-select>
    </el-form-item>
  </div>
</template>

<script>
  import selectElementIcon from './components/selectElementIcon.vue';
  import ColorPicker from '@/components/ColorPicker/index.vue'
  import { predefineColors } from '@/utils/colorList'
  import fontList from '@/utils/fontList'
  export default {
    name: "statisticCard-editor",
    props: {
      designer: Object,
      selectedWidget: Object,
      optionModel: Object,
    },
    components: {
      selectElementIcon,
      ColorPicker,
    },
    data() {
      return {
        options: [
          {
            label: '从左到右',
            value: 'to right'
          },
          {
            label: '从右到左',
            value: 'to left'
          },
          {
            label: '从上到下',
            value: 'to bottom'
          },
          {
            label: '从下到上',
            value: 'to top'
          },
          {
            label: '从左上到右下',
            value: 'to bottom right'
          },
          {
            label: '从右上到左下',
            value: 'to bottom left'
          },
          {
            label: '从左下到右上',
            value: 'to top right'
          },
          {
            label: '从右下到左上',
            value: 'to top left'
          }
        ],
        modeList: [
          { label: '上标题下数值', value: 1 },
          { label: '上数值下标题', value: 2 }
        ],
        predefineThemeColors: predefineColors,
        fontFamilyList: fontList
      }
    },
    methods: {
      iconSelect(e) {
        this.optionModel.statisticIcon = e
      },
      delIcon() {
        this.optionModel.statisticIcon = ''
      },
    },
  }
</script>
<style lang="scss" scoped>
.flex {
  display: flex;
}
.el-icon-delete {
  line-height: 28px;
  margin-left: 5px;
}
.input-num {
  width: 100%;
}
::v-deep .el-input__inner {
  background-color: #FFFFFF!important;
  background-image: none;
  border-radius: 4px;
  border: 1px solid #DCDFE6!important;
  color: #606266!important;
}
::v-deep .el-color-picker__trigger {
  border-color: #e6e6e6!important;
  background-color: #FFFFFF!important;
}
</style>
