<template>
  <div>
    <el-form-item label="PC视图">
      <el-select v-model="optionModel.formId" placeholder="请选择加载的PC视图" clearable>
        <el-option v-for="(item, index) in formIdList" :key="index"
         :label="item.templateName" :value="item.formId"></el-option>
      </el-select>
    </el-form-item>
  </div>

</template>

<script>
import i18n from "@/utils/i18n"

export default {
  name: "formId-editor",
  mixins: [i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  data() {
    return {
      formIdList: []
    }
  },
  mounted() {
    this.loadPCList();
  },
  methods: {
    loadPCList() {
      this.formIdList = []
      // 获取当前应用的视图模版
      this.$store.state?.design?.forms.forEach(item => {
        if (item.templateType == 2) {
          this.formIdList.push({
            formId: item.formId,
            templateId: item.templateId,
            templateName: item.templateName,
          })
        }
      });
      // 勾选了外部应用
      if (this.$store.state.design.refBus) {
        this.$store.state?.refBusDataList.forEach(item => {
          item?.forms.forEach(e => {
            if (e.templateType == 2) {
              this.formIdList.push({
                formId: e.formId,
                templateId: e.templateId,
                templateName: e.templateName,
              })
            }
          })
        })
      }    
    }
  }
}
</script>

<style scoped></style>
