<template>
  <el-form-item :label="i18nt('designer.setting.prefixIcon')">
    <el-input type="text" v-model="optionModel.prefixIcon" >
      <div slot="prepend" @click="$refs.selectElementIconRef.open(optionModel.prefixIcon)">
        选择图标
      </div>
    </el-input>
    <selectElementIcon ref="selectElementIconRef" @iconSelect="iconSelect" />
  </el-form-item>
</template>

<script>
import i18n from '@/utils/i18n';
import selectElementIcon from './components/selectElementIcon.vue';
export default {
  name: 'prefixIcon-editor',
  mixins: [i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },

  mounted() {},
  components: {
    selectElementIcon,
  },
  methods: {
    iconSelect(e) {
      this.optionModel.prefixIcon = e;
    },
  },
};
</script>

<style scoped></style>
