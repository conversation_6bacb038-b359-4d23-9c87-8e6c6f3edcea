<template>
  <div class="container">
    <el-form-item label="边框颜色">
      <ColorPicker
        v-model="optionModel.bandColor"
      />
    </el-form-item>
    <el-form-item label="描述颜色">
      <ColorPicker
        v-model="optionModel.descColor"
      />
    </el-form-item>
  </div>
</template>

<script>
import ColorPicker from '@/components/ColorPicker/index.vue'
export default {
  name: 'dashboard-editor',
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  components: {
    ColorPicker,
  },
}
</script>

<style scoped>

</style>

