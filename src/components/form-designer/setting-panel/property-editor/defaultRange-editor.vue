<template>
<div>
  <el-form-item label="默认范围">
    <el-radio-group v-model="optionModel.defaultRange" style="display:flex;flex-direction: column">
      <el-radio label="">
        无</el-radio>
      <el-radio label="day">
        当天</el-radio>
      <el-radio label="week">
        本周</el-radio>
      <el-radio label="month">
        本月</el-radio>
      <el-radio label="lastWeek">
        最近一周</el-radio>
      <el-radio label="lastMonth">
        最近一个月</el-radio>
      <el-radio label="last3Month">
        最近三个月</el-radio>

    </el-radio-group>
  </el-form-item>
  <el-form-item label="快捷选项">
    <el-switch v-model="optionModel.shortcuts"></el-switch>
  </el-form-item>
</div>

</template>

<script>
  import i18n from "@/utils/i18n"
  import propertyMixin from "@/components/form-designer/setting-panel/property-editor/propertyMixin"

  export default {
    name: "defaultRange-editor",
    mixins: [i18n, propertyMixin],
    props: {
      designer: Object,
      selectedWidget: Object,
      optionModel: Object,
    },
  }
</script>

<style scoped>

</style>
