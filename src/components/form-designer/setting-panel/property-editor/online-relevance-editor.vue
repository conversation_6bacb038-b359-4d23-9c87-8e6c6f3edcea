<template>
  <el-form-item label="代码编辑">
    <el-button type="info" icon="el-icon-edit" plain round @click="showDialog">
      编辑</el-button
    >
    <el-dialog title="代码编辑" :visible.sync="dialogVisible" width="80%">
      <codemirror
        v-model="code"
        :options="cmOptions"
        @ready="onCmReady"
        style="height: 600px"
      >
      </codemirror>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirm">确 定</el-button>
      </span>
    </el-dialog>
  </el-form-item>
</template>

<script>
import i18n from "@/utils/i18n";
import { codemirror } from "vue-codemirror";
// 引入高亮样式
import "codemirror/lib/codemirror.css";
import "codemirror/theme/base16-light.css";
// 引入语言
import "codemirror/mode/vue/vue.js";
import "codemirror/mode/javascript/javascript";
import "codemirror/addon/selection/active-line";
import "codemirror/addon/hint/show-hint";
import "codemirror/addon/hint/javascript-hint";
import "codemirror/addon/hint/css-hint";
import "codemirror/addon/hint/html-hint";
import "codemirror/addon/hint/xml-hint";
import "codemirror/addon/hint/anyword-hint";
import "codemirror/addon/hint/show-hint.css";

import "codemirror/addon/fold/foldgutter.css";
import "codemirror/addon/fold/foldcode";
import "codemirror/addon/fold/foldgutter";
import "codemirror/addon/fold/brace-fold";
import "codemirror/addon/fold/comment-fold";
import "codemirror/addon/fold/markdown-fold";
import "codemirror/addon/fold/xml-fold";
import "codemirror/addon/fold/indent-fold";

import "codemirror/addon/edit/matchbrackets";
import "codemirror/addon/edit/closebrackets";
export default {
  name: "online-relevance-editor",
  mixins: [i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  components: { codemirror },
  mounted() {
    if (!this.optionModel.onlineCode) {
      this.$set(
        this.optionModel,
        "onlineCode",
        `<template>
</template>
<script>
export default {
}
<\/script>
<style>
</style>`
      );
    }
  },
  data() {
    return {
      cmOptions: {
        mode: "vue",
        theme: "base16-light",
        gutters: [
          "CodeMirror-lint-markers",
          "CodeMirror-linenumbers",
          "CodeMirror-foldgutter",
        ],
        foldGutter: true,
        highlightSelectionMatches: {
          minChars: 2,
          trim: true,
          style: "matchhighlight",
          showToken: false,
        },

        lineNumbers: true,
        autoCloseBrackets: true, // 自动闭合符号
        matchBrackets: true,
      },
      code: "",
      dialogVisible: false,
    };
  },
  methods: {
    onCmReady(cm) {
      // 这里的 cm 对象就是 codemirror 对象，等于 this.$refs.myCm.codemirror
      cm.on("inputRead", (cm, obj) => {
        if (obj.text && obj.text.length > 0) {
          let c = obj.text[0].charAt(obj.text[0].length - 1);
          if ((c >= "a" && c <= "z") || (c >= "A" && c <= "Z")) {
            cm.showHint({ completeSingle: false });
          }
        }
      });
    },
    showDialog() {
      this.code = this.optionModel.onlineCode;
      this.dialogVisible = true;
    },
    confirm() {
      this.optionModel.onlineCode = this.code;
      this.dialogVisible = false;
    },
  },
};
</script>

<style lang="scss" scoped></style>
