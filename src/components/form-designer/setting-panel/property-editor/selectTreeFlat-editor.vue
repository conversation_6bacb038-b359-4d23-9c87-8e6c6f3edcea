<template>
  <el-form-item>
    <template #label>
      节点分离
      <el-tooltip effect="light" content="开启后每个树节点都是独立的个体，不会进行数据合并">
        <i class="el-icon-info" />
      </el-tooltip>
    </template>
    <el-switch v-model="optionModel.selectTreeFlat"></el-switch>
  </el-form-item>
</template>

<script>
  import i18n from "@/utils/i18n"

  export default {
    name: "selectTreeFlat-editor",
    mixins: [i18n],
    props: {
      designer: Object,
      selectedWidget: Object,
      optionModel: Object,
    },
  }
</script>

<style scoped>

</style>
