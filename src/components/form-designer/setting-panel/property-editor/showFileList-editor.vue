<template>
  <div>
    <el-form-item label="仅拍照(APP)">
      <el-switch v-model="optionModel.onlyCamera"> </el-switch>
    </el-form-item>
    <el-form-item label="显示文件列表">
      <el-switch v-model="optionModel.showFileList"> </el-switch>
    </el-form-item>
  </div>
</template>

<script>
import i18n from "@/utils/i18n";

export default {
  name: "showFileList-editor",
  mixins: [i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  watch: {
    optionModel: {
      immediate: true,
      handler(val) {
        // 如果没有这个字段则添加这个字段
        if (!('onlyCamera' in val)) {
          this.$set(this.optionModel, 'onlyCamera', false);
        }
      },
      deep: true,
    },
  },
};
</script>

<style scoped></style>
