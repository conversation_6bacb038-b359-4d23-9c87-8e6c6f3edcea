<template>
  <div>
    <el-form-item
      :label=" i18nt('designer.setting.organizationalList')"
    >
      <el-cascader
        v-model="apiComp"
        :options="options"
        filterable
        clearable
        :show-all-levels="false"
        :props="{ value: 'interfaceId', label: 'name' }"
        @change="apiChange"
      />
    </el-form-item>
    <el-form-item
      label="绑定属性"
    >
      <el-input v-model="config.id" placeholder="绑定接口中的属性值"></el-input>
    </el-form-item>
    <el-form-item
      label="显示字段属性"
    >
      <el-input v-model="config.label" placeholder="绑定接口中的显示字段"></el-input>
    </el-form-item>
    <el-form-item
      label="选中默认挂载节点"
    >
      <el-switch
        v-model="config.isDefault">
      </el-switch>
    </el-form-item>
  </div>

</template>

<script>
import i18n from '@/utils/i18n'
import { getInterfaceTree } from '@/api/interfaces/interfaces'
export default {
  name: 'OrganizationalListEditor',
  mixins: [i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  data() {
    return {
      options: [],
      apiComp: [],
      config: {
        id: '',
        label: '',
        isDefault: true
      }
    }
  },
  watch: {
    optionModel: {
      handler(newName, oldName) {
        if (this.options.length !== 0) {
          this.calculation()
        } else {
          this.initApis()
        }
      },
      immediate: true,
      deep: true,
    },

  },
  created() {},
  methods: {
    labelChange(){
      this.config.api = this.apiComp[this.apiComp.length - 1]
      this.optionModel.organizationalList = this.config
    },
    apiChange(e) {
      this.config.api = this.apiComp[this.apiComp.length - 1]
      this.optionModel.organizationalList = this.config
      if ('paramConfig' in this.optionModel)
        this.optionModel['paramConfig'] = ''
      if ('optionItems' in this.optionModel)
        this.optionModel['optionItems'] = [{}]
      if ('popupSelect' in this.optionModel)
        this.optionModel['popupSelect'] = []
      if ('data' in this.optionModel)
        this.optionModel['data'] = []
    },
    initApis() {
      getInterfaceTree().then((res) => {
        this.options = res.data
        this.calculation()
      })
    },
    calculation() {
      function recursion(ls, target, datas) {
        var is = false
        for (var i = 0; i < datas.length; i++) {
          var item = datas[i]
          if (item.interfacesUid === target) {
            ls.unshift(item.interfacesUid)
            is = true
            break
          } else {
            if (item.children != null) {
              var childResult = recursion(ls, target, item.children)
              if (childResult) {
                ls.unshift(item.interfacesUid)
                is = true
                break
              }
              is = childResult
            } else {
              is = false
            }
          }
        }
        return is
      }
      this.apiComp = []
      if (this.optionModel && this.optionModel.organizationalList) {
        recursion(this.apiComp, this.optionModel.organizationalList.api, this.options)
        this.config = this.optionModel.organizationalList
      }
    },
  },
}
</script>
