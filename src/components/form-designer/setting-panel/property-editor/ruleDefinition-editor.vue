<template>
  <div :key="lt">
    <el-divider content-position="center">规则定义</el-divider>
    <draggable tag="ul" v-model="optionModel.ruleDefinition" @update="draggableUp" v-bind="{group: 'optionsGroup',ghostClass: 'ghost',handle: '.drag-option'}">
      <li v-for="rule,index_1 in optionModel.ruleDefinition" :key="index_1" class="draggable_li">
        <div style="position: relative;">
          <el-tag class="elTag" type="info" size="mini" v-if="rule.context && rule.ruleType != 4">{{rule.context}}</el-tag>
          <el-tag class="elTag" type="info" size="mini" v-if="rule.context && rule.ruleType == 4">{{rule.numberLength + ' 位数'}}</el-tag>
          <el-input readonly="" size="mini" style="cursor: move;" class="drag-option" v-model="rule.placeholder"></el-input>
        </div>

        <el-popover placement="top-start" width="280" trigger="click">
          <el-descriptions class="margin-top" :column="1" size="mini">
            <el-descriptions-item label="规则类型">
              <el-select v-model="rule.ruleType" placeholder="" style="width: 180px;" size="mini" @change="ruleTypeChange(index_1)">
                <div v-for="item,index in ruleTypeOption" :key="index">
                  <el-option :label="item.label" :value="item.value" v-if="item.show"></el-option>
                </div>
              </el-select>
            </el-descriptions-item>

            <el-descriptions-item v-if="rule.ruleType == 1" label="固定内容">
              <el-input v-model="rule.context" placeholder="" size="mini" style="width: 180px;"></el-input>
            </el-descriptions-item>

            <el-descriptions-item v-if="rule.ruleType == 2" label="提交日期">
              <el-select v-model="rule.format" placeholder="" style="width: 180px;" size="mini" @change="ruleTypeChange(index_1)">
                <el-option v-for="item,index in dateFormatOption" :label="item.label" :value="item.format" :key="index"></el-option>
              </el-select>
            </el-descriptions-item>

            <el-descriptions-item v-if="rule.ruleType == 3" label="显示字段">
              <el-select v-model="rule.column" placeholder="" style="width: 180px;" size="mini" @change="selectChanged(index_1)">
                <el-option v-for="item,index in widgetListOption" :label="item.label" :value="item.value" :key="index"></el-option>
              </el-select>
            </el-descriptions-item>
          </el-descriptions>
          <i class="el-icon-edit-outline edit" slot="reference" v-if="rule.ruleType != 4" />
        </el-popover>

        <el-popover placement="top-start" width="280" trigger="click">
          <el-descriptions class="margin-top" :column="1" size="mini" :colon="false">
            <el-descriptions-item label="计数位数">
              <el-input-number v-model="rule.numberLength" @keydown.native="channelInputLimit" size="mini" controls-position="right" style="width: 180px;" :min="2" :max="8"></el-input-number>
            </el-descriptions-item>

            <el-descriptions-item label="固定位数">
              <el-switch v-model="rule.openLength"></el-switch>
            </el-descriptions-item>

            <el-descriptions-item>
              <span class="desSpan">开启后，根据计数位数显示计数值。例如当计数位数为 4，则 1 的流水号显示为 0001</span>
            </el-descriptions-item>

            <el-descriptions-item label="重置周期">
              <el-select v-model="rule.resetCycle" placeholder="" style="width: 180px;" size="mini" @change="ruleTypeChange(index_1)">
                <div v-for="item,index in resetCycleOption" :key="index">
                  <el-option :label="item.label" :value="item.value"></el-option>
                </div>
              </el-select>
            </el-descriptions-item>

            <el-descriptions-item>
              <span class="desSpan">设置了重置周期以后，当满足重置周期后将会以初始值再重新开始计数</span>
            </el-descriptions-item>

            <el-descriptions-item label="初始值">
              <el-input-number v-model="rule.initialValue" size="mini" controls-position="right" style="width: 180px;" :min="1" :max="getMaxNumberByDigits(rule.numberLength)"></el-input-number>
            </el-descriptions-item>

          </el-descriptions>

          <i class="el-icon-edit-outline edit" slot="reference" v-if="rule.ruleType == 4" />
        </el-popover>

        <i class="el-icon-delete del" @click="deleteOption(index_1)" v-if="rule.isDel" />
      </li>
    </draggable>

    <el-button plain class="add" size="mini" @click="add">
      <i class="el-icon-circle-plus-outline"> 添加规则</i>
    </el-button>
  </div>
</template>

<script>
import i18n from "@/utils/i18n";
import Draggable from 'vuedraggable';
export default {
  components: {
    Draggable,
  },
  name: "ruleDefinition-editor",
  mixins: [i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  data() {
    return {
      lt: "",
      widgetListOption: [],
      ruleTypeOption: [
        { value: 1, label: '固定字符', show: true },
        { value: 2, label: '提交日期', show: true },
        { value: 3, label: '表单字段', show: true },
        { value: 4, label: '自动递增', show: false },
      ],
      dateFormatOption: [
        { label: '年', format: 'yyyy' },
        { label: '年月', format: 'yyyyMM' },
        { label: '年月日', format: 'yyyyMMdd' },
        { label: '年月日时分', format: 'yyyyMMddHHmm' },
        { label: '年月日时分秒', format: 'yyyyMMddHHmmss' },
      ],
      resetCycleOption: [
        { value: 1, label: '按日' },
        { value: 2, label: '按月' },
        { value: 3, label: '按年' },
        { value: 4, label: '不自动重置' }
      ],
      widgetListItem: ""
    };
  },
  mounted() {
    this.handleWidgetList();
  },
  methods: {
    draggableUp() {
      this.lt = Math.random()
    },
    ruleTypeChange(index) {
      let rule = this.optionModel.ruleDefinition[index]
      if (rule.ruleType == 2) {
        this.dateFormatOption.find((item) => {
          if (item.format == rule.format) {
            rule["context"] = item.label
            return true
          }
        })
        rule["placeholder"] = "提交日期"
        rule["isDel"] = true
        this.ruleTypeOption[1].show = false
      } else if (rule.ruleType == 3) {
        this.handleWidgetList();
        rule["placeholder"] = "表单字段"
        rule["isDel"] = true
      }
      this.$set(this.optionModel.ruleDefinition, index, rule)
    },
    getMaxNumberByDigits(digits) {
      if (digits <= 0) {
        return 0;
      }

      let maxNumber = Math.pow(10, digits) - 1;
      return maxNumber;
    },
    channelInputLimit(e) {
      e.returnValue = ''
    },
    selectChanged(index) {
      let rule = this.optionModel.ruleDefinition[index]
      this.widgetListOption.find((item) => {
        if (item.value == rule.column) {
          rule["context"] = item.label
          rule["column"] = item.value
          this.$set(this.optionModel.ruleDefinition, index, rule)
          return true
        }
      });

    },
    add() {
      this.optionModel.ruleDefinition.push(
        {
          "context": "",
          "placeholder": "固定字符",
          "isDel": true,
          "ruleType": 1
        }
      )
    },
    deleteOption(index) {
      if (this.optionModel.ruleDefinition[index].ruleType == 2) {
        this.ruleTypeOption[1].show = true
      }
      this.optionModel.ruleDefinition.splice(index, 1);
    },
    handleWidgetList() {
      this.widgetListOption = []
      this.$array.handleWidgetList(this.designer["widgetList"], (item) => {
        if (item.type == 'data-table') {
          for (let i in item.options.cols) {
            this.widgetListOption.push({
              "label": item.options.cols[i]['label'],
              "value": item.options.cols[i]['prop']
            })
          }
        }
        if (item.type === "input" || item.type == "order-input" || item.type == "select") {
          this.widgetListOption.push({
            "label": item.options.label,
            "value": item.options.alias
          })
        }
      });
    }
  },
};
</script>
<style>
.add {
  width: 100%;
}
.ghost {
  background: #fff;
  border: 2px dotted var(--primary-color);
}
.draggable_li {
  margin-bottom: 6px;
  position: relative;
}
.edit:hover,
.del:hover {
  cursor: pointer;
  color: var(--primary-color);
}

.drag-option .el-input__inner {
  cursor: move;
}
.edit {
  position: absolute;
  font-size: 16px;
  right: 28px;
  top: 6px;
}
.del {
  position: absolute;
  font-size: 16px;
  right: 10px;
  top: 6px;
}
.elTag {
  position: absolute;
  z-index: 1;
  left: 26%;
  top: 4px;
  font-weight: 500;
  font-size: 12px;
  color: #171a1d;
}
.desSpan {
  font-size: 12px;
  color: rgb(162, 163, 165);
  line-height: 18px;
  font-weight: 400;
  margin-top: -6px;
}
</style>
