<template>
  <el-form-item label="报表名称">
    <el-select
      v-model="optionModel.reportUid"
      placeholder="请选择报表"
    >
      <el-option
        v-for="item in reportData"
        :key="item.reportUid"
        :label="item.reportName"
        :value="item.reportUid"
      />
    </el-select>
  </el-form-item>
</template>

<script>
import i18n from '@/utils/i18n';

export default {
  name: 'report-code-editor',
  mixins: [i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  data() {
    return {
      reportData: [],
    };
  },
  created() {
    this.reportData = this.designer.reportInfos;
  },
};
</script>

<style lang="scss" scoped></style>
