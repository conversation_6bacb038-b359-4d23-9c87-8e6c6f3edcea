<template>
  <div>
    <el-form-item label="自动加载数据">
      <el-switch v-model="optionModel.autoLoadData" />
    </el-form-item>
    <el-form-item label="配置数据源">
      <el-switch v-model="optionModel.configData" />
    </el-form-item>
    <ModelQuery v-if="optionModel.configData" :designer="designer" :selectedWidget="selectedWidget" :optionModel="optionModel"></ModelQuery>
    <el-form-item v-else :label="
        selectedWidget && selectedWidget.type === 'button'
          ? '绑定接口'
          : i18nt('designer.setting.api')
      ">
      <el-row v-show="options.length !== 0">
        <el-col :span="24">
          <el-tooltip class="item" effect="dark" :content="echoDataSource?echoDataSource:'请选择'" placement="top-start">
            <!-- <el-button type="primary"  plain round  @click="showDataSourceFn">选择</el-button> -->
            <el-cascader :filterable="true" v-model="optionModel.api" :options="options" :props="{ value:'interfacesUid', label: 'name', children: 'children',emitPath:false }" @visible-change="handleVisibleChange" @change="handleChange"></el-cascader>
          </el-tooltip>
        </el-col>
        <el-col :span="12">
          <i class="el-icon-circle-close mr-l3" @click="confirmApi('close')"></i>
          <i class="el-icon-refresh mr-lr3" @click="refresh"></i>
          <i class="el-icon-document" @click="getFormat"></i>
        </el-col>
      </el-row>
    </el-form-item>
    <el-form-item label="成功回调">
      <el-button type="primary" icon="el-icon-edit" plain round @click="editFormEventHandler('onSuccess')">
        编写代码</el-button>
    </el-form-item>
    <el-form-item label="失败回调">
      <el-button type="primary" icon="el-icon-edit" plain round @click="editFormEventHandler('onFail')">
        编写代码</el-button>
    </el-form-item>
    <el-form-item label="是否开启轮询">
      <el-switch v-model="optionModel.isPolling" />
    </el-form-item>
    <el-form-item label="轮询时间(秒)" v-if="optionModel.isPolling">
      <el-input type="number" min="10" v-model="optionModel.pollTimes"></el-input>
    </el-form-item>
    <el-dialog title="当前组件接口可返回的标准数据格式" :visible.sync="dialogVisible" width="45%">
      <vue-json-pretty style="
          font-size: 12px;
          line-height: 15px;
          overflow: auto;
          max-height: 300px;
        " :data="dataFormatParse" />
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="dialogVisible = false">确 定</el-button>
      </span>
    </el-dialog>

    <EventEditor
      ref="eventEditorRef"
      :designer="designer"
      title="成功回调函数"
      eventHeader="api.then(res => {"
      :eventHandlerCode="optionModel.onSuccessCallback"
      @saveEventHandler="saveEventHandler"
      :key="eventEditorRef"
    >
    </EventEditor>
    <EventEditor
      ref="eventEditorRef2"
      :designer="designer"
      title="失败回调函数"
      eventHeader="api.catch(e => {"
      :eventHandlerCode="optionModel.onFailCallback"
      @saveEventHandler="saveEventHandler2"
      :key="eventEditorRef2"
    >
    </EventEditor>

    <el-dialog title="数据来源" :visible.sync="showDataSource" v-if="showDataSource" :show-close="true" class="small-padding-dialog" v-dialog-drag :close-on-click-modal="false" :close-on-press-escape="false" :destroy-on-close="true">
      <el-input placeholder="搜索接口" v-model="filterDataSource" />
      <div style="max-height: 500px; overflow-y: auto">
        <el-tree highlight-current :default-expanded-keys="DataSourceExpandedKeys" class="filter-tree" node-key="interfaceId" :data="options" :props="{ label: 'name', children: 'children' }" ref="dataSourceTree" :filter-node-method="filterDataSourceNode" @node-click="handlerDataSource">
        </el-tree>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="showDataSource = false">取 消</el-button>
        <el-button type="primary" @click="confirmApi('dialog')">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import EventEditor from "@/components/EventEditor/index";
import i18n from "@/utils/i18n";
import ModelQuery from "./modelQuery/modelQuery-editor.vue";
import VueJsonPretty from "vue-json-pretty";
import { throttle } from "@/utils/decorator";
import fieldMixin from '@/components/form-designer/form-widget/field-widget/fieldMixin';

export default {
  name: "ApiEditor",
  mixins: [i18n, fieldMixin],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  data() {
    return {
      options: [],
      apiComp: [],
      dialogVisible: false,
      dataFormat: "",
      customTree: `
      [{
          label: '一级 1',
          children: [{
            label: '二级 1-1',
            children: [{
              label: '三级 1-1-1'
            }]
          }]
        }, {
          label: '一级 2',
          children: [{
            label: '二级 2-1',
            children: [{
              label: '三级 2-1-1'
            }]
          }, {
            label: '二级 2-2',
            children: [{
              label: '三级 2-2-1'
            }]
          }]
        }, {
          label: '一级 3',
          children: [{
            label: '二级 3-1',
            children: [{
              label: '三级 3-1-1'
            }]
          }, {
            label: '二级 3-2',
            children: [{
              label: '三级 3-2-1'
            }]
          }]
        }]
      `,
      dataTable:
        '{"records": [{"key1": 1, "key2": 2}], \n' +
        '  "total":12, \n' +
        '  "pages": 2, \n' +
        '  "current": 1, \n' +
        '  "size": 10 \n' +
        "}",
      cascader: `
      [
        {
          "label": "select 1",
          "value": 1,
          "children": [
            {
              "label": "child 1",
              "value": 11,
              "children": [
                {
                  "label": "child 1",
                  "value": 11
                }
              ]
            }
          ]
        },
        {
          "label": "select 2",
          "value": 2
        },
        {
          "label": "select 3",
          "value": 3
        }
      ]
      `,
      showDataSource: false,
      filterDataSource: "",
      checkedDataSource: {},
      DataSourceExpandedKeys: [],
      echoDataSource: "暂未选择数据来源",
      oldVal: "",
      eventEditorRef: '',
      eventEditorRef2: '',
    };
  },
  components: {
    EventEditor,
    VueJsonPretty,
    ModelQuery,
  },
  watch: {
    optionModel: {
      handler(newName, oldName) {
        if (this.options.length !== 0) {
          this.calculation();
        } else {
          this.initApis();
        }
        this.dataFormat = "未配置标准数据格式";
      },
      immediate: true,
      deep: true,
    },
    filterDataSource(val) {
      this.$refs.dataSourceTree.filter(val);
    },
  },
  computed: {
    dataFormatParse() {
      try {
        return JSON.parse(this.dataFormat);
      } catch (e) { }
      return {};
    },
  },
  created() {
    if (!this.optionModel.hasOwnProperty("autoLoadData")) {
      this.$set(this.optionModel, "autoLoadData", true);
    }
    if (!this.optionModel.hasOwnProperty("configData")) {
      this.$set(this.optionModel, "configData", false);
    }
  },
  methods: {
    saveEventHandler(code) {
      this.$set(this.optionModel, "onSuccessCallback", code);
    },
    saveEventHandler2(code) {
      this.$set(this.optionModel, "onFailCallback", code);
    },
    handleVisibleChange(e) {
      if (e) {
        this.oldVal = this.optionModel.api;
      }
    },
    handleChange() {
      this.checkMutex('api', this.optionModel.api, this.oldVal)
    },
    platGetEcho() {
      this.echoDataSource = "";
      let platList = this.platFn(this.options);
      this.apiComp.forEach((e, index) => {
        this.echoDataSource +=
          platList.find((item) => item.interfaceId == e).name +
          [index + 1 == this.apiComp.length ? "" : "-"];
      });
    },
    platFn(list) {
      //树形数据扁平化
      let res = [];
      res = list.concat(
        ...list
          .map((item) => {
            if (item.children instanceof Array && item.children.length > 0) {
              return this.platFn(item.children);
            }
            return null;
          })
          .filter((o) => o instanceof Array && o.length > 0)
      );
      return res;
    },
    showDataSourceFn() {
      //显示数据来源弹窗
      this.filterDataSource = "";
      this.showDataSource = true;
      if (this.apiComp.length === 0) return;
      this.DataSourceExpandedKeys = [this.apiComp[this.apiComp.length - 1]];
      this.$nextTick(() => {
        this.$refs.dataSourceTree.setCurrentKey(
          this.apiComp[this.apiComp.length - 1]
        );
      });
      // if(this.echoDataSource.length){
      //   this.$nextTick(()=>{
      //     let arr=this.echoDataSource.split('-')
      //     this.$refs.dataSourceTree.filter(arr[arr.length-1])
      //   })
      // }
    },
    handlerDataSource(data, node, oneself) {
      //选中节点
      this.checkedDataSource = data;
    },
    confirmApi(type) {
      //数据来源dialog确认
      if (type === "dialog") {
        if (this.checkedDataSource.children !== null)
          return this.$message({
            message: "请选择最后一级接口！",
            type: "warning",
          });
        this.oldVal = this.optionModel.api;
        this.optionModel.api = this.checkedDataSource.interfaceId;
      } else if (type === "close") {
        this.oldVal = this.optionModel.api;
        this.optionModel.api = undefined;
        this.$message({
          message: "取消选中数据来源成功！",
          type: "success",
        });
      }
      if ("paramConfig" in this.optionModel)
        this.optionModel["paramConfig"] = "";
      if ("optionItems" in this.optionModel)
        this.optionModel["optionItems"] = [];
      if ("popupSelect" in this.optionModel)
        this.optionModel["popupSelect"] = [];
      if ("data" in this.optionModel) this.optionModel["data"] = [];
      this.showDataSource = false;
      [
        this.checkedDataSource,
        this.checkedDataSource,
        this.DataSourceExpandedKeys,
        this.echoDataSource,
      ] = ["", {}, [], ""];
    },
    filterDataSourceNode(value, data) {
      //数据来源树过滤查询
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    // 刷新接口
    refresh() {
      this.initApis();
      this.$notify({
        title: "成功",
        message: "成功刷新接口",
        type: "success",
      });
    },
    editFormEventHandler(eventName) {
      if (eventName === "onSuccess") {
        this.$refs.eventEditorRef.open(true);
        this.codeEditorKey = new Date().getTime() + 1;
        this.$nextTick(()=> {
          this.$refs.eventEditorRef.open(true);
        })
      } else {
        this.eventEditorRef2 = new Date().getTime() + 1;
        this.$nextTick(()=> {
          this.$refs.eventEditorRef2.open(true);
        })
      }
    },
    getFormat() {
      if (this.optionModel.name.indexOf("customtree") != -1) {
        this.dataFormat = this.customTree;
      } else if (this.optionModel.name.indexOf("datatable") != -1) {
        this.dataFormat = this.dataTable;
      } else if (this.optionModel.name.indexOf("cascader") != -1) {
        this.dataFormat = this.cascader;
      } else {
        this.dataFormat = "{}";
      }
      this.dialogVisible = true;
      this.$forceUpdate();
    },
    // apiChange(e) {
    //   this.optionModel.api = this.apiComp[this.apiComp.length - 1];
    //   console.log(this.optionModel.api)
    //   if ('paramConfig' in this.optionModel)
    //     this.optionModel['paramConfig'] = '';
    //   if ('optionItems' in this.optionModel)
    //     this.optionModel['optionItems'] = [];
    //   if ('popupSelect' in this.optionModel)
    //     this.optionModel['popupSelect'] = [];
    //   if ('data' in this.optionModel) this.optionModel['data'] = [];
    // },
    //  @throttle(500)
    initApis() {
      let interfacesInfos = this.$store.state.design.interfacesInfos;
      let apiList = []
      interfacesInfos.forEach(item => {
        let method = item.interfacesContent.method
        apiList.push({
          methodType: method ? method : "",
          interfaceType: "api",
          responseBody: item.interfacesContent.responseBody ? item.interfacesContent.responseBody : "",
          name: item.interfacesContent.name ? item.interfacesContent.name : "",
          parentId: this.$store.state.design.formId,
          interfaceId: item.interfacesId,
          parameters: method == 'GET' ? JSON.stringify(item.interfacesContent.parameters) : "",
          requestBody: method == 'POST' ? item.interfacesContent.requestBody : '',
          interfacesUid: item.interfacesUid
        })
      })
      this.options = [{
        parentId: 0,
        interfaceType: "group",
        interfaceId: this.$store.state.design.formId,
        name: this.$store.state.design.businessName,
        children: apiList
      }]
      this.$nextTick(() => {
        if (this.$store.state.design.refBus) {
          let simple = this.$store.state["refBusDataList"]
          if (simple.length > 0) {
            let apiTree = simple.filter(item => item.apiTree !== undefined);
            if(apiTree && apiTree.length > 0){
              let interfaces = apiTree.flatMap(item => item.apiTree);
              if (interfaces && interfaces[0]) {
                this.options = this.options.concat(interfaces)
              }
            }
          }
        }
      })
      this.calculation();
      this.platGetEcho();
      // getInterfaceTree({ businessId: this.$store.state.design.formId }).then((res) => {
      //   this.options = res.data;
      //   this.calculation();
      //   this.platGetEcho();
      // });
    },
    calculation() {
      function recursion(ls, target, datas) {
        var is = false;
        for (var i = 0; i < datas.length; i++) {
          var item = datas[i];
          if (item.interfaceId === target) {
            ls.unshift(item.interfaceId);
            is = true;
            break;
          } else {
            if (item.children != null) {
              var childResult = recursion(ls, target, item.children);
              if (childResult) {
                ls.unshift(item.interfaceId);
                is = true;
                break;
              }
              is = childResult;
            } else {
              is = false;
            }
          }
        }
        return is;
      }
      this.apiComp = [];
      if (this.optionModel && this.optionModel.api) {
        recursion(this.apiComp, this.optionModel.api, this.options);
      }
      this.platGetEcho();
    },
  },
};
</script>

<style lang="scss" scoped>
i {
  cursor: pointer;
}
</style>
