<template>
  <div>
    <el-form-item label="工艺配置">
      <el-button type="primary" icon="el-icon-edit" plain round @click="showConfig">工艺配置</el-button>
    </el-form-item>
    <el-form-item label="显示保存">
      <el-switch v-model="optionModel.showSave"></el-switch>
    </el-form-item>
    <el-form-item label="显示应用">
      <el-switch v-model="optionModel.showUse"></el-switch>
    </el-form-item>
    <el-dialog title="工艺配置" :visible.sync="visible" width="45%">
      <div class="block">基础配置</div>
      <el-form ref="form" label-width="120px">
        <el-form-item label="左侧列表数据源">
          <el-cascader :filterable="true" v-model="optionModel.processConfig.menuList.api" style="width: 100%" :options="options"
            :props="{ value:'interfacesUid', label: 'name', children: 'children',emitPath:false }"
            clearable></el-cascader>
        </el-form-item>
        <el-form-item label="加号配置PC视图">
          <el-select v-model="optionModel.processConfig.menuList.templateId" style="width: 100%" placeholder="请选择PC视图" clearable
            filterable>
            <el-option v-for="bus in formIdList" :key="bus.templateId" :label="bus.templateName"
              :value="bus.templateId">
              <span style="float: left">{{ bus.templateName }}</span>
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div class="block">节点类型配置</div>
      <div>
        <div class="list">
          <div style="width: 65px;">序号</div>
          <div style="width: 160px;">类型名称</div>
          <div style="width: 160px;">类型值</div>
          <div style="width: 160px;">PC视图</div>
          <el-button type="primary" size="small" icon="el-icon-plus" round @click="handleAddTypeList"></el-button>
        </div>
        <draggable tag="ul" animation="300" :list="optionModel.processConfig.typeList">
          <div v-for="(item, index) in optionModel.processConfig.typeList" :key="index" class="list">
            <div style="width: 65px;">
              <i class="el-icon-rank drag-option mr-r10" />
              <span>{{ index + 1 }}</span>
            </div>
            <el-input v-model="item.label" :disabled="item.disabled" placeholder="请输入类型名称" size="small"
              style="width: 160px" class="mr-r10"></el-input>
            <el-input v-model="item.type" :disabled="item.disabled" placeholder="请输入类型值" size="small"
              style="width: 160px" class="mr-r10"></el-input>
            <el-select v-model="item.value" style="width: 160px" placeholder="请选择PC视图" clearable filterable
              class="mr-r10">
              <el-option v-for="bus in formIdList" :key="bus.templateId" :label="bus.templateName"
                :value="bus.templateId">
                <span style="float: left">{{ bus.templateName }}</span>
              </el-option>
            </el-select>
            <el-button :disabled="item.disabled" type="danger" size="small" icon="el-icon-delete" round
              @click="optionModel.processConfig.typeList.splice(index, 1)"></el-button>
          </div>
        </draggable>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="visible = false">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
  import i18n from "@/utils/i18n"
  export default {
    name: "processConfig-editor",
    mixins: [i18n],
    props: {
      designer: Object,
      selectedWidget: Object,
      optionModel: Object,
    },
    data() {
      return {
        visible: false,
        options: [],
        formIdList: [],
      }
    },
    mounted() {
      this.loadPCList();
      this.initApis();
    },
    methods: {
      // 显示工艺配置
      showConfig() {
        this.visible = true;
      },
      initApis() {
        let interfacesInfos = this.$store.state.design.interfacesInfos;
        let apiList = []
        interfacesInfos.forEach(item => {
          let method = item.interfacesContent.method
          apiList.push({
            methodType: method ? method : "",
            interfaceType: "api",
            responseBody: item.interfacesContent.responseBody ? item.interfacesContent.responseBody : "",
            name: item.interfacesContent.name ? item.interfacesContent.name : "",
            parentId: this.$store.state.design.formId,
            interfaceId: item.interfacesId,
            parameters: method == 'GET' ? JSON.stringify(item.interfacesContent.parameters) : "",
            requestBody: method == 'POST' ? item.interfacesContent.requestBody : '',
            interfacesUid: item.interfacesUid
          })
        })
        this.options = [{
          parentId: 0,
          interfaceType: "group",
          interfaceId: this.$store.state.design.formId,
          name: this.$store.state.design.businessName,
          children: apiList
        }]
        this.$nextTick(() => {
          if (this.$store.state.design.refBus) {
            let simple = this.$store.state["refBusDataList"]
            if (simple.length > 0) {
              let apiTree = simple.filter(item => item.apiTree !== undefined);
              if(apiTree && apiTree.length > 0){
                let interfaces = apiTree.flatMap(item => item.apiTree);
                if (interfaces && interfaces[0]) {
                  this.options = this.options.concat(interfaces)
                }
              }
            }
          }
        })
      },
      loadPCList() {
        this.formIdList = []
        // 获取当前应用的视图模版
        this.$store.state?.design?.forms.forEach(item => {
          if (item.templateType == 2) {
            this.formIdList.push({
              formId: item.formId,
              templateId: item.templateId,
              templateName: item.templateName,
            })
          }
        });
        // 勾选了外部应用
        if (this.$store.state.design.refBus) {
          this.$store.state?.refBusDataList.forEach(item => {
            item?.forms.forEach(e => {
              if (e.templateType == 2) {
                this.formIdList.push({
                  formId: e.formId,
                  templateId: e.templateId,
                  templateName: e.templateName,
                })
              }
            })
          })
        }    
      },
      // 添加类型列表
      handleAddTypeList() {
        this.optionModel.processConfig.typeList.push({ label: '', value: '', type: '', disabled: false });
      },
    }
  }
</script>

<style lang="scss" scoped>
.list {
  display: flex;
  justify-content: space-around;
  align-items: center;
  margin-bottom: 18px;
}
.block{
  width: 100%;
  height: 32px;
  padding-left: 8px;
  background: linear-gradient(65deg, rgba(49,119,255,0.15) 0%, rgba(49,119,255,0.01) 100%);
  border-left: 4px solid var(--primary-color);
  color: var(--primary-color);
  font-size: 18px;
  line-height: 32px;
  font-weight: bold;
  margin-bottom: 18px;
}
</style>
