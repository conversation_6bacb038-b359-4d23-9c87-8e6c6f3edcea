<template>
  <div class="container">
    <el-form-item label="开启轮询" v-if="optionModel.api">
      <el-switch v-model="optionModel.polling"></el-switch>
    </el-form-item>
    <el-form-item label="轮询时间(ms)" v-if="optionModel.polling">
      <el-input-number v-model="optionModel.pollTime" :min="500" :precision="0"></el-input-number>
    </el-form-item>
  </div>
</template>

<script>
  export default {
    name: "polling-editor",
    props: {
      designer: Object,
      selectedWidget: Object,
      optionModel: Object,
    },
    data() {
      return {
        
      }
    },
    methods: {

    },
    watch: {
      optionModel: {
        immediate: true,
        handler(val) {
          // 如果没有这个字段则添加这个字段
          if (!('pollTime' in val)) {
            this.$set(this.optionModel, 'pollTime', '500');
          }
        },
        deep: true,
      },
    },
  }
</script>
<style lang="scss" scoped>

</style>
