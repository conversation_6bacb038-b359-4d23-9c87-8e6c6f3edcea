<template>
  <div>
    <el-form-item label="单元格选中">
      <el-switch v-model="optionModel.tableCellSelect"></el-switch>
    </el-form-item>
    <el-form-item label="选中前置事件" title="preOnCellClick" v-if="optionModel.tableCellSelect">
      <el-button type="primary" icon="el-icon-edit" plain round @click="editEventHandler('preOnCellClick', eventParams)">
        {{i18nt('designer.setting.addEventHandler')}}</el-button>
    </el-form-item>
    <el-form-item v-if="optionModel.tableCellSelect">
      <template #label>
        行互斥模式
        <el-tooltip
          effect="light"
          content="开启后每一行只能选中一个单元格"
        >
          <i class="el-icon-info" />
        </el-tooltip>
      </template>
      <el-switch v-model="optionModel.mutualExclusion" />
    </el-form-item>
    <el-form-item label="选中背景颜色" v-if="optionModel.tableCellSelect">
      <el-color-picker v-model="optionModel.cellSelectBgColor" show-alpha
            :predefine="predefineColors"></el-color-picker>
    </el-form-item>
    <el-form-item label="选中文字颜色" v-if="optionModel.tableCellSelect">
      <el-color-picker v-model="optionModel.cellSelectColor" show-alpha
            :predefine="predefineColors"></el-color-picker>
    </el-form-item>
  </div>

</template>

<script>
  import i18n from "@/utils/i18n"
  import propertyMixin from "@/components/form-designer/setting-panel/property-editor/propertyMixin"
  import eventMixin from "@/components/form-designer/setting-panel/property-editor/event-handler/eventMixin"

  export default {
    name: "tableCellSelect-editor",
    mixins: [i18n, propertyMixin, eventMixin],
    props: {
      designer: Object,
      selectedWidget: Object,
      optionModel: Object,
    },
    data() {
    return {
      eventParams: ['row', 'column','cell','event',"rowIndex","columnIndex"],
    };
  },
  computed: {
    predefineColors() {
      return [
        this.$store.state.settings.theme,
        '#67C23A',
        '#E6A23C',
        '#F56C6C',
        '#909399',
      ]
    }
  },
  mounted(){
    if(!this.optionModel.hasOwnProperty('tableCellSelect')){
      this.$set(this.optionModel, 'tableCellSelect', false)
    }
    if(!this.optionModel.hasOwnProperty('cellSelectBgColor')){
      this.$set(this.optionModel, 'cellSelectBgColor', this.$store.state.settings.theme,)
    }
    if(!this.optionModel.hasOwnProperty('cellSelectColor')){
      this.$set(this.optionModel, 'cellSelectColor', '#fff')
    }
    if(!this.optionModel.hasOwnProperty('preOnCellClick')){
      this.$set(this.optionModel, 'preOnCellClick', '')
    }
  }
  }
</script>

<style scoped>

</style>
