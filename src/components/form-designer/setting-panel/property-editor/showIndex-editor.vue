<template>
  <div>
    <el-form-item
      label="显示序号"
    >
      <el-switch
        v-model="optionModel.showIndex">
      </el-switch>
    </el-form-item>
    <el-form-item
      v-if="optionModel.showIndex"
      label="对齐方式"
    >
      <el-select
        v-model="optionModel.indexAlign">
        <el-option label="左" value="left"></el-option>
        <el-option label="中" value="center"></el-option>
        <el-option label="右" value="right"></el-option>
      </el-select>
    </el-form-item>
  </div>

</template>

<script>
import i18n from '@/utils/i18n'
export default {
  name: 'showIndex-editor',
  mixins: [i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  mounted(){
    if(!this.optionModel.hasOwnProperty('indexAlign')){
      this.$set(this.optionModel, 'indexAlign', 'left')
    }
  }
}
</script>
