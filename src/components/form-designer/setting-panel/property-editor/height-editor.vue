<template>
  <el-form-item v-if="!optionModel.minHeight && !optionModel.heightFit && !optionModel.minimumHeight" :label="selectedWidget.type=='data-table'?'最大高度':'高度'">
    <template #label>
      <span> {{ selectedWidget.type=='data-table'?'最大高度':'高度' }} </span>
      <el-tooltip
        v-if="selectedWidget.type=='data-table'"
        effect="light"
        content="最大高度为空时，可设置固定高度或最小高度"
      >
        <i class="el-icon-info" />
      </el-tooltip>
    </template>
    <el-input type="number" v-model="optionModel.height"></el-input>
  </el-form-item>
</template>

<script>
  import i18n from "@/utils/i18n"

  export default {
    name: "height-editor",
    mixins: [i18n],
    props: {
      designer: Object,
      selectedWidget: Object,
      optionModel: Object,
    },
  }
</script>

<style scoped>

</style>
