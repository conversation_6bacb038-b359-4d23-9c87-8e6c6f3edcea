<template>
  <div>
    <el-form-item label="左侧列配置">
      <el-button
        type="info"
        icon="el-icon-edit"
        plain
        round
        @click="dialogVisible = true"
      >
        配置</el-button
      >
    </el-form-item>
    <el-form-item label="允许拖动">
      <el-switch v-model="optionModel.allowDrag"></el-switch>
    </el-form-item>
    <el-form-item label-width="70px" label="默认范围">
      <el-date-picker
        style="width: 100%"
        v-model="optionModel.defaultDateRange"
        type="datetimerange"
        size="mini"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        format="yyyy-MM-dd HH:mm:ss"
        value-format="yyyy-MM-dd HH:mm:ss"
      >
      </el-date-picker>
    </el-form-item>
    <el-form-item label="默认粒度">
      <el-select v-model="optionModel.defaultGrain">
        <el-option
          v-for="item in grainOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
        </el-option>
      </el-select>
    </el-form-item>
    <el-dialog
      title="按钮配置"
      width="80%"
      :visible="dialogVisible"
      :show-close="false"
    >
      <lt-sort-table
        :isEnable="true"
        row-key="id"
        v-model="optionModel.ganttCols"
        max-height="500"
      >
        <lt-table-column-edit prop="label" label="列名" />
        <lt-table-column-edit prop="name" label="字段" />
        <lt-table-column-edit prop="width" label="宽度" />
        <lt-table-column-edit
          prop="align"
          label="对齐方式"
          state="select"
          :select-opt="{ options: $enum.alignFix }"
        />
        <lt-table-column-edit label="树列" state="switch" prop="tree" />
        <lt-table-column-edit label="自定义HTML" prop="template" editDisable>
          <template #preview="{ row }">
            <el-button
              type="info"
              icon="el-icon-edit"
              plain
              round
              size="mini"
              @click="editHtml(row)"
            >
              编写代码</el-button
            >
          </template>
        </lt-table-column-edit>
        <lt-table-column-operation
          ref="operationRef"
          width="160"
          v-model="optionModel.ganttCols"
          :row-data="rowObj"
          :unshift="false"
          primary-key="id"
          validateProp="label,name"
          unlimitedAdd
          @addBefore="addBefore"
          @save="custSave"
          @delete="custDelete"
        />
      </lt-sort-table>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="checkDialog"> 确认</el-button>
      </div>
    </el-dialog>

    <EventEditor
      ref="eventEditorRef"
      :designer="designer"
      title="自定义HTML"
      eventHeader="customHtml(row,$index,text){"
      :eventHandlerCode="codeSave"
      @saveEventHandler="saveHtml"
      :key="codeEditorKey"
    >
    </EventEditor>
  </div>
</template>

<script>
import i18n from '@/utils/i18n';
import EventEditor from '@/components/EventEditor/index';
export default {
  name: 'ganttCols-editor',
  mixins: [i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  components: { EventEditor },
  data() {
    return {
      codeEditorKey: "",
      dialogVisible: false,
      currentRow: {},
      rowObj: {
        id: '',
        label: '', //列名
        name: '', // 字段
        width: '', // 宽度
        align: '', //对齐方式
        tree: false, // 树的展开列
        template: '', // 自定义渲染
      },
      codeSave: null,
      grainOptions:[{
        label: '月',
        value: 'month'
      },{
        label: '周',
        value: 'week'
      },{
        label: '日',
        value: 'day'
      },{
        label: '12小时',
        value: 12
      },{
        label: '8小时',
        value: 8
      },{
        label: '6小时',
        value: 6
      },{
        label: '4小时',
        value: 4
      },{
        label: '2小时',
        value: 2
      },{
        label: '1小时',
        value: 1
      },]
    };
  },
  watch: {
    optionModel: {
      immediate: true,
      handler(val) {
        // 如果没有这个字段则添加这个字段
        if (!val.ganttCols) this.$set(val, 'ganttCols', []);
        if (!val.defaultDateRange) this.$set(val, 'defaultDateRange', []);
        if (!val.defaultGrain) this.$set(val, 'defaultGrain', 'day');
        if (!val.hasOwnproperty('allowDrag')) this.$set(val, 'allowDrag', true);
      },
      deep: true,
    },
  },
  methods: {
    editHtml(row) {
      this.currentRow = row;
      this.codeSave = row.template;
      this.codeEditorKey = new Date().getTime() + 1;
      this.$nextTick(() => {
        this.$refs.eventEditorRef.open(true);
      });
    },
    saveHtml(eventCode) {
      this.codeSave = eventCode;
      this.currentRow.template = this.codeSave;
    },
    addBefore() {
      this.rowObj['id'] = this.$string.getUUID(7);
    },
    custSave({ data }) {
      this.$notify.success({ message: '保存成功' });
      this.$refs.operationRef.saveComplete(data);
    },
    custDelete({ index }) {
      this.optionModel.ganttCols.splice(index, 1);
      this.$notify.success({ message: '删除成功' });
    },

    checkDialog() {
      for (let i = 0; i < this.optionModel.ganttCols.length; i++) {
        console.log(this.optionModel.ganttCols[i].isEdit);
        if (this.optionModel.ganttCols[i].isEdit) {
          this.$message({ message: `请保存第${i + 1}条数据` });
          return;
        }
      }
      this.dialogVisible = false;
    },
  },
};
</script>

<style lang="scss" scoped></style>
