<template>
  <el-form-item label="选择条码">
    <el-select v-model="optionModel.codeId" clearable  filterable>
      <el-option-group
      v-for="group in options"
      :key="group.label"
      :label="group.label">
      <el-option
        v-for="item in group.options"
        :key="item.uid"
        :label="item.codeName"
        :value="item.uid">
      </el-option>
    </el-option-group>

      <!-- <el-option v-for="(item, index) in options"
                 :key="index" :label="item.codeName"
                 :value="item.uid"></el-option> -->
    </el-select>
  </el-form-item>
</template>

<script>
 import { getBarcodeInfoByExternalList } from '@/api/ldf/ldfInfo.js'
  export default {
    name: "codeManage-editor",
    props: {
      designer: Object,
      selectedWidget: Object,
      optionModel: Object
    },
    data(){
      return {
        options: []
      }
    },
    methods:{

    },
    mounted() {
      if (this.options.length == 0) {
        this.options = [{ label: "内部配置", options: this.$store.state.design.barcodeTags }]
      }

      // 获取外部配置的条码信息 商定获取全部的条码信息，后续可以根据需求修改，比如分页，搜索等，目前先不做优化，后续优化后再优化这个
      // 这里需要注意，因为外部配置的条码信息是在其他页面配置的，所以需要在其他页面配置完后，再刷新当前页面，才能看到外部配置的条码信息
      getBarcodeInfoByExternalList().then(res => {
        this.options.push({ label: "外部配置", options: res.data })
      })
      
    },
  }
</script>

<style lang="scss" scoped>

</style>
