<template>
  <el-form-item >
    <template slot="label">
      虚拟列表
       <el-tooltip
        effect="light"
        content="适用于数据量大且不分页的场景，开启虚拟列表后，列表将只渲染可视区域内的数据，数据表格的序号和选择功能将失效"
      >
        <i class="el-icon-info" />
      </el-tooltip>
    </template>
    <el-switch type="text" v-model="optionModel.isVirtual"></el-switch>
  </el-form-item>
</template>

<script>

export default {
  name: 'isVirtual-editor',
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  watch:{
    optionModel: {
      immediate: true,
      handler(val) {
        // 如果没有这个字段则添加这个字段
        if (val.isVirtual == null) {
          this.$set(this.optionModel, 'isVirtual', false)
        }
      },
      deep: true
    }
  },
}
</script>

<style scoped>

</style>

