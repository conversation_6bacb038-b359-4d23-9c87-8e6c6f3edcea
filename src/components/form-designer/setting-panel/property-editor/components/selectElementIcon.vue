<template>
  <el-dialog title="Icon选择" :visible.sync="dialogVisible" width="70%">
    <div class="selectElementIcon">
      <ul class="icon-list">
        <li
          v-for="item in $enum.elIconList"
          :key="item"
          @click="handleClick(item)"
          :class="{ active: item === activeIcon }"
        >
          <i :class="{ [item]: true }" />
        </li>
      </ul>
    </div>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      activeIcon: '',
      dialogVisible: false,
    };
  },
  methods: {
    open(icon) {
      this.activeIcon = icon;
      this.dialogVisible = true;
    },
    handleClick(icon) {
      this.activeIcon = icon === this.activeIcon ? '' : icon;
      this.$emit('iconSelect', this.activeIcon);
      this.dialogVisible = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.selectElementIcon {
  height: 600px;
  overflow: auto;
}
.icon-list {
  margin: 10px 0;
  padding: 0 0 0 20px;
  font-size: 14px;
  color: #5e6d82;
  line-height: 2em;
  overflow: hidden;
  list-style: none;
  padding: 0 !important;
  border: 1px solid #eaeefb;
  border-radius: 4px;
  li {
    float: left;
    width: 6.25%;
    text-align: center;
    height: 80px;
    line-height: 80px;
    color: #666;
    font-size: 13px;
    border-right: 1px solid #eee;
    border-bottom: 1px solid #eee;
    cursor: pointer;
    &:hover {
      background-color: #eee;
    }
    &.active {
      background-color: #eee;
    }
    i {
      font-size: 26px;
    }
  }
}
</style>
