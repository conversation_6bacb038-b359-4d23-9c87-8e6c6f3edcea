<template>
  <el-dialog
    :visible.sync="dialogVisible"
    title="参数配置"
    center
    width="70vw"
    :close-on-click-modal="false"
    append-to-body
    @close="close"
  >
    <div class="d-flex">
      <el-scrollbar
        wrap-style="overflow-x:hidden;"
        style="height: 360px"
        class="flex-1"
      >
        <vue-json-pretty
          path="jsonData"
          :data="jsonData"
          @click="jsonClick"
          @node-click="nodeClick"
        />
      </el-scrollbar>
      <div class="flex-1">
        <template v-if="mode">
          <el-form label-width="90px">
            <el-form-item label="属性:" style="font-weight: bold">{{
              activeKey
            }}</el-form-item>
            <el-form-item label="表单项:">
              <el-cascader
                v-model="selectRole.value"
                :options="optionData"
                :props="cProps || {
                  expandTrigger: 'hover',
                  value: 'elementKey',
                  label: 'elementTitle',
                  children: 'items',
                }"
                placeholder="请选择"
                clearable
                filterable
                @change="cascaderChange"
              />
            </el-form-item>
          </el-form>
        </template>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import VueJsonPretty from 'vue-json-pretty';
export default {
  components: {
    VueJsonPretty,
  },
  props: ['allFormItem','cProps'],
  data() {
    return {
      dialogVisible: false, // 关闭开启标识
      mode: '', // 模式 keyValue || arr
      activeKey: '', // 当前点击的key
      activeForm: '', // 当前点击节点对应的formId
      jsonData: {},
      index: 0,
      cascaderValue: [],
      relation: [], // 关联关系数组
      cascaderFromTemplate: {
        // 接口参数配置基本对象格式
        type: '',
        key: '',
        value: [],
        subset: [],
      },
      // allFormItem:[],
      selectRole: {}, // 当前选中的字段的配置数据
      role: [], // 当前接口的参数配置
      optionData: [], // 选择框数据
    };
  },
  methods: {
    nodeClick(e) {
      this.jsonClick(e.key, e.path);
    },
    options() {
      var elementKey = null;
      if (this.selectRole.key.indexOf('.') != -1) {
        // 当前选择的是数组下的key 需要先获取数组选择的elementKey
        let keySplit = this.selectRole.key.split('.');
        let patientIndex = this.role.findIndex(
          (item) => item.key == keySplit[0],
        );
        if (patientIndex == -1) {
          this.optionData = [];
          return;
        } else {
          // 这里赋值筛选子表单中的子项目
          elementKey = this.role[patientIndex].value;
          if (elementKey == null || elementKey.length == 0) {
            this.optionData = [];
            return;
          }
        }
      }
      let temp = JSON.parse(JSON.stringify(this.allFormItem));
      temp = temp.filter((item) => {
        if (elementKey != null && item.templateId != elementKey[0]) {
          return false;
        } else {
          item.items = item.items.filter((item2) => {
            if (elementKey != null) {
              return (
                item2.elementKey.indexOf(elementKey[1]) != -1 &&
                item2.elementKey != elementKey[1]
              );
            } else {
              return this.mode === 'keyValue'
                ? item2.elementType !== 'sub-form'
                : item2.elementType === 'sub-form' ||
                    item2.elementType === 'custom-condition';
            }
          });
          return item.items != 0;
        }
      });
      this.optionData = temp;
    },
    cascaderChange(e) {
      this.selectRole.value = e;
      if (this.selectRole.key.indexOf('.') != -1) {
        var keySplit = this.selectRole.key.split('.');
        // 当前是数组下的元素
        let arrIndex = this.role.findIndex((item) => {
          return item.key === keySplit[0];
        });
        let subSetIndex = this.role[arrIndex].subset.findIndex((item) => {
          return item.key === this.selectRole.key;
        });
        this.role[arrIndex].subset.splice(
          subSetIndex,
          1,
          JSON.parse(JSON.stringify(this.selectRole)),
        );
      } else {
        // 当前不是数组下的元素
        let arrIndex = this.role.findIndex((item) => {
          return item.key === this.selectRole.key;
        });
        if (arrIndex != -1) {
          this.role.splice(
            arrIndex,
            1,
            JSON.parse(JSON.stringify(this.selectRole)),
          );
        } else {
          this.role.push(JSON.parse(JSON.stringify(this.selectRole)));
        }
      }
    },
    /**
     * 打开
     * @param {Object} data
     * @param {Object} index
     */
    open(data, role, index = 0) {
      this.dialogVisible = true;
      this.role = JSON.parse(JSON.stringify(role));
      this.jsonData = JSON.parse(data);
      this.index = index;
    },
    /**
     * json串的点击
     * @param {Object} key
     * @param {Object} value
     */
    jsonClick(key, value) {
      // console.log(key, value);
      let mateKey = key.replaceAll('jsonData.', '');
      if (!['{', '}'].includes(value)) {
        key = key.split('.').pop();
        this.activeKey = key;
        // 只需要判断两种类型
        if (['[', ']'].includes(value)) {
          // 数组
          this.mode = 'arr';
        } else {
          // 键值对
          this.mode = 'keyValue';
        }
        // 如果是数组的形势
        if (mateKey.indexOf('[') != -1) {
          let keySplit = mateKey.split('[');
          let keySplitSpot = mateKey.split('.');
          // 匹配是否已经有父级了
          let patientRole = this.role.filter((e) => e.key === keySplit[0]);
          if (patientRole.length == 0) {
            // 没有父级 则添加父级
            var patientObj = JSON.parse(
              JSON.stringify(this.cascaderFromTemplate),
            );
            patientObj.type = 'arr';
            patientObj.key = keySplit[0];
            // 创建当前对象
            var thisObj = JSON.parse(JSON.stringify(this.cascaderFromTemplate));
            thisObj.type = 'keyValue';
            thisObj.key = keySplit[0] + '.' + keySplitSpot[1];
            // 并且将当前点击对象放置到父级的子数组中
            patientObj.subset.push(thisObj);
            this.role.push(patientObj);
            this.selectRole = JSON.parse(JSON.stringify(thisObj));
          } else {
            // 如果已经存在父级 则找出当前父级下的子集对应的数据
            let arrIndex = patientRole[0].subset.findIndex((item) => {
              return item.key === keySplit[0] + '.' + keySplitSpot[1];
            });
            if (arrIndex == -1) {
              // 如果当前父级没有当前子集 则创建一个空的
              // 创建当前对象
              var thisObj = JSON.parse(
                JSON.stringify(this.cascaderFromTemplate),
              );
              thisObj.type = 'keyValue';
              thisObj.key = keySplit[0] + '.' + keySplitSpot[1];
              this.selectRole = JSON.parse(JSON.stringify(thisObj));
              // 将新增的这个对象 添加的列表中
              let paIndex = this.role.findIndex((e) => e.key === keySplit[0]);

              this.role[paIndex].subset.push(
                JSON.parse(JSON.stringify(this.selectRole)),
              );
            } else {
              this.selectRole = JSON.parse(
                JSON.stringify(patientRole[0].subset[arrIndex]),
              );
            }
          }
        } else {
          // 不是数组下面的key
          let hisRole = this.role.filter((e) => e.key === mateKey);
          if (hisRole && hisRole.length != 0) {
            // 匹配已经有的参数配置
            this.selectRole = JSON.parse(JSON.stringify(hisRole[0]));
          } else {
            this.selectRole = JSON.parse(
              JSON.stringify(this.cascaderFromTemplate),
            );
            this.selectRole.type = this.mode;
            this.selectRole.key = mateKey;
          }
        }
        this.options();
        return;
      }
      this.mode = '';
    },
    /**
     * 关闭
     */
    close() {
      this.$emit('close', {
        data: this.role,
        index: this.index,
      });
      (this.mode = ''), (this.activeKey = ''), (this.optionData = []);
    },
  },
};
</script>

<style lang="scss" scoped>

</style>
