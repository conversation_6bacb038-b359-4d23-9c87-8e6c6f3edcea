<template>
  <el-form-item label="只能选择最子级节点">
    <el-switch v-model="optionModel.disableBranchNodes" />
  </el-form-item>
</template>

<script>
import i18n from '@/utils/i18n';
import propertyMixin from '@/components/form-designer/setting-panel/property-editor/propertyMixin';

export default {
  name: 'disableBranchNodes-editor',
  mixins: [i18n, propertyMixin],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  watch: {
    optionModel: {
      immediate: true,
      handler(val) {
        // 如果没有这个字段则添加这个字段
        if (val.disableBranchNodes == null) {
          this.$set(this.optionModel, 'disableBranchNodes', false);
        }
      },
      deep: true,
    },
  },
};
</script>
