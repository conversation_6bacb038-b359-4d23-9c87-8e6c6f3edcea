<template>
  <el-form-item label="布局模式">
    <div class="imgBox" :class="{ active: optionModel.layoutModel === 1 }">
      <img
        src="@/assets/images/1710917335244.jpg"
        @click="handleClick(1)"
      />
    </div>
    <div class="imgBox" :class="{ active: optionModel.layoutModel === 2 }">
      <img
        src="@/assets/images/1710917386996.jpg"
        @click="handleClick(2)"
      />
    </div>
  </el-form-item>
</template>

<script>
import i18n from '@/utils/i18n';
import propertyMixin from '@/components/form-designer/setting-panel/property-editor/propertyMixin';

export default {
  name: 'layoutModel-editor',
  mixins: [i18n, propertyMixin],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  methods: {
    handleClick(val) {
      this.optionModel.layoutModel = val
    }
  }
};
</script>

<style scoped lang="scss">
.imgBox {
  padding: 5px;
  // display: inline-block;
  width: 100px;
  height: 100px;
  // box-sizing: border-box;
  img {
    width: 100%;
    height: 100%;
  }
  &.active {
    border: 1px solid var(--primary-color);
  }
}
</style>
