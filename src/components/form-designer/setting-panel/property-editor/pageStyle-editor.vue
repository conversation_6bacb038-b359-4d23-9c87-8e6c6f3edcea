<template>
  <el-form-item>
    <template #label>
      分页样式
      <el-tooltip effect="light" content="分页样式">
        <i class="el-icon-info" />
      </el-tooltip>
    </template>
    <el-select v-model="optionModel.pageStyle" multiple>
      <el-option label="总数" value="total" />
      <el-option label="单页显示量" value="sizes" />
      <el-option label="上一页" value="prev" />
      <el-option label="按钮页码" value="pager" />
      <el-option label="下一页" value="next" />
      <el-option label="页码跳转" value="jumper" />
      <!-- <el-option label="->" value="->" /> -->
      <!-- <el-option label="slot" value="slot" /> -->
    </el-select>
  </el-form-item>
</template>

<script>
import i18n from '@/utils/i18n';
import propertyMixin from '@/components/form-designer/setting-panel/property-editor/propertyMixin';
import CodeEditor from '@/components/code-editor/index';
export default {
  name: 'pageStyle-editor',
  mixins: [i18n, propertyMixin],
  components: {
    CodeEditor,
  },
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  watch: {
    optionModel: {
      immediate: true,
      handler(val) {
        // 如果没有这个字段则添加这个字段
        if (!val.pageStyle || !val.pageStyle.length) {
          this.$set(this.optionModel, 'pageStyle', [
            'total',
            'sizes',
            'prev',
            'pager',
            'next',
            'jumper',
          ]);
        }
      },
      deep: true,
    },
  },
  data() {
    return {
      dialogFlag: false,
    };
  },
};
</script>

<style scoped></style>
