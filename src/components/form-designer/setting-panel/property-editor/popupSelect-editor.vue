<template>
  <el-form-item label-width="0">
    <el-divider class="custom-divider-margin-top">{{ i18nt('designer.setting.popupSelect') }}</el-divider>
    <el-button type="text" @click="addOption">{{ i18nt('designer.setting.addOption') }}</el-button>
    <div style="display: flex;">回显字段：<el-select v-model="optionModel.popupSelect" size="mini" style="width: 130px;" @change="handleCurrentChangeLabel">
      <el-option
        v-for="(item,index) in columnData"
        :key="index"
        :label="item"
        :value="item"
      />
    </el-select></div>

    <div style="display: flex;">传值字段：<el-select v-model="optionModel.popupSelectValue" size="mini" style="width: 130px;" @change="handleCurrentChangeValue">
      <el-option
        v-for="(item,index) in columnData"
        :key="index"
        :label="item"
        :value="item"
      />
    </el-select></div>
    <draggable
      tag="ul"
      style="padding: 0;"
      :list="optionModel.optionItems"
      v-bind="{group:'optionsGroup', ghostClass: 'ghost', handle: '.drag-option'}"
    >
      <li v-for="(option, idx) in optionModel.optionItems" :key="idx">
        <el-input v-model="option.label" size="mini" style="width: 100px" />
        <el-select v-model="option.value" size="mini" style="width: 100px">
          <el-option
            v-for="(item,index) in columnData"
            :key="index"
            :label="item"
            :value="item"
          />
        </el-select>
        <i class="iconfont icon-drag drag-option" />
        <el-button
          circle
          plain
          size="mini"
          type="danger"
          icon="el-icon-minus"
          class="col-delete-button"
          @click="deleteOption(option, idx)"
        />
      </li>
    </draggable>
  </el-form-item>
</template>

<script>
import i18n from '@/utils/i18n'
import propertyMixin from '@/components/form-designer/setting-panel/property-editor/propertyMixin'
import Draggable from 'vuedraggable'
import { getInterfaceTree } from '@/api/interfaces/interfaces'
export default {
  name: 'PopupSelectEditor',
  components: {
    Draggable
  },
  mixins: [i18n, propertyMixin],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object
  },
  data() {
    return {
      columnData: {},
      popupSelectValue: ''
    }
  },
  watch: {
    'optionModel.api'() {
      this.getInterfaceTreelist(this.optionModel.api)
      this.columnData = {}
      this.optionModel.popupSelect = ''
      this.optionModel.popupSelectValue = ''
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.getInterfaceTreelist(this.optionModel.api)
    })
  },
  methods: {
    handleCurrentChangeLabel(val) {
      this.optionModel.popupSelect = val
    },
    handleCurrentChangeValue(val) {
      this.optionModel.popupSelectValue = val
    },
    getInterfaceTreelist(val) {
      let options = this.$parent.$children.find(item => item.$options.name === 'ApiEditor').options
      if (options.length) {

        let dfs = (root) => {
          if (root.interfaceId === this.selectedWidget.options.api) {
            this.$nextTick(() => {
              this.columnData = Object.keys(JSON.parse(root.responseBody).data.records[0])
            })
            return
          }
          root.children && root.children.forEach(item => dfs(item))
        }
        dfs({ children: options })
      } else {
        getInterfaceTree().then(res => {
          let options = res.data
          let dfs = (root) => {
            if (root.interfaceId === this.selectedWidget.options.api) {
              this.$nextTick(() => {
                this.columnData = Object.keys(JSON.parse(root.responseBody).data.records[0])
              })
              return
            }
            root.children && root.children.forEach(item => dfs(item))
          }
          dfs({ children: options })
        })
      }
    },

    deleteOption(option, index) {
      this.optionModel.optionItems.splice(index, 1)
    },

    addOption() {
      let newValue = this.optionModel.optionItems.length + 1
      this.optionModel.optionItems.push({
        value: this.columnData[0],
        label: 'new option'
      })
    }
  }
}
</script>

<style scoped>
.option-items-pane ul {
  padding-inline-start: 6px;
  padding-left: 6px; /* 重置IE11默认样式 */
}
li.ghost {
  background: #fff;
  border: 2px dotted var(--primary-color);
}
li {
  white-space: nowrap;

  list-style: outside none none;
}

.drag-option {
  cursor: move;
}

.small-padding-dialog ::v-deep .el-dialog__body {
  padding: 10px 15px;
}

.dialog-footer .el-button {
  width: 100px;
}
</style>
