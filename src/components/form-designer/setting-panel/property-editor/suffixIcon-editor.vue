<template>
  <el-form-item :label="i18nt('designer.setting.suffixIcon')">
    <el-input type="text" v-model="optionModel.suffixIcon" clearable>
      <div slot="append" @click="$refs.selectElementIconRef.open(optionModel.suffixIcon)">
        选择图标
      </div>
    </el-input>
    <selectElementIcon ref="selectElementIconRef" @iconSelect="iconSelect" />
  </el-form-item>
</template>

<script>
import i18n from '@/utils/i18n';
import selectElementIcon from './components/selectElementIcon.vue';
export default {
  name: 'suffixIcon-editor',
  mixins: [i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  watch: {
    optionModel: {
      immediate: true,
      handler(val) {
        if (val.suffixIcon == null) {
          this.$set(this.optionModel, "suffixIcon", '')
        }
      },
      deep: true,
    },
  },
  components: {
    selectElementIcon,
  },
  methods: {
    iconSelect(e) {
      this.optionModel.suffixIcon = e;
    },
  },
};
</script>

<style scoped></style>
