<template>
  <el-form-item label="开启右下角缩略控件">
    <el-switch v-model="optionModel.abbreviation" />
  </el-form-item>
</template>

<script>
import i18n from '@/utils/i18n';
import propertyMixin from '@/components/form-designer/setting-panel/property-editor/propertyMixin';

export default {
  name: 'abbreviation-editor',
  mixins: [i18n, propertyMixin],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  watch: {
    optionModel: {
      immediate: true,
      handler(val) {
        // 如果没有这个字段则添加这个字段
        if (val.abbreviation == null) {
          this.$set(this.optionModel, 'abbreviation', false);
        }
      },
      deep: true,
    },
  },
};
</script>
