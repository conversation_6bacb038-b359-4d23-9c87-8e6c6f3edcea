<template>
  <el-form-item label="允许粘贴数据">
    <el-switch v-model="optionModel.allowCopy" ></el-switch>
  </el-form-item>
</template>

<script>
  import i18n from "@/utils/i18n"

  export default {
    name: "allowCopy-editor",
    mixins: [i18n,],
    props: {
      designer: Object,
      selectedWidget: Object,
      optionModel: Object,
    },
    watch: {
    optionModel: {
      immediate: true,
      handler(val) {
        // 如果没有这个字段则添加这个字段
        if (val.allowCopy == null) {
          this.$set(this.optionModel, 'allowCopy', false);
        }
      },
      deep: true,
    },
  },
  }
</script>

<style scoped>

</style>
