<template>
  <div>
   <el-form-item label="自定义"> 
              <el-switch v-model="optionModel.customHtml">自定义html</el-switch>
    </el-form-item>
    
    <el-form-item label="编写" v-if="optionModel.customHtml">
              <el-button size="mini" type="primary" @click="showDialog">编写代码</el-button>
    </el-form-item>
    <EventEditor
      ref="eventEditorRef"
      :designer="designer"
      title="编写html"
      eventHeader="customHtml(date,data(type, isSelected, day)){"
      :eventHandlerCode="fun"
      @saveEventHandler="confirm"
      :key="codeEditorKey"
    >
    </EventEditor>
  </div>
</template>

<script>
  import i18n from "@/utils/i18n"
  import EventEditor from "@/components/EventEditor/index"
  export default {
    name: "slot-editor",
    mixins: [i18n],
    props: {
      designer: Object,
      selectedWidget: Object,
      optionModel: Object,
    },
    components: {
      EventEditor,
    },
    data(){
      return{
        codeEditorKey: 0,
        fun:'',
      }
    },
    methods: {
      showDialog(){
        this.fun=this.optionModel.slot
        this.codeEditorKey = new Date().getTime() + 1
        this.$nextTick(() => {
          this.$refs.eventEditorRef.open(true);
        });
      },
      confirm(eventCode){
        this.fun = eventCode
        this.optionModel.slot=this.fun
      }

    }
  }
</script>

<style lang="scss" scoped>
  li.col-item {
    list-style: none;

    margin: 5px 0;
  }

  ::v-deep .el-button--mini.is-circle{
    padding: 5px;
  }

</style>
