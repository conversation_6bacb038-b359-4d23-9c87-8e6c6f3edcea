<template>
  <el-form-item :label="i18nt('designer.setting.bandDataIdsApi')">
    <el-cascader
      v-model="apiComp"
      :options="options"
      filterable
      clearable
      :show-all-levels="false"
      :props="{ value: 'interfaceId', label: 'name' }"
      @change="optionModel.bandDataIdsApi = apiComp[apiComp.length-1]"
    />
  </el-form-item>
</template>

<script>
import i18n from '@/utils/i18n'
import { getInterfaceTree } from '@/api/interfaces/interfaces'
import {createDesigner} from "@/components/form-designer/designer";
export default {
  name: 'BandDataIdsApiEditor',
  mixins: [i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object
  },
  data() {
    return {
      options: [],
      apiComp: []
    }
  },
  watch:{
    optionModel: {
      immediate: true,
      handler(val) {
        if (this.options.length != 0) {
          this.calculation();
        } else {
          this.initApis();
        }
      },
      deep: true
    }
  },
  methods: {
    initApis(){
      getInterfaceTree().then(res => {
        this.options = res.data
        this.calculation()
      })
    },
    calculation() {
      function recursion(ls, target, datas) {
        var is = false
        for (var i = 0; i < datas.length; i++) {
          var item = datas[i]
          if (item.interfacesUid === target) {
            ls.unshift(item.interfacesUid)
            is = true
            break
          } else {
            if (item.children != null) {
              var childResult = recursion(ls, target, item.children)
              if (childResult) {
                ls.unshift(item.interfacesUid)
                is = true
                break
              }
              is = childResult
            } else {
              is = false
            }
          }
        }
        return is
      }
      this.apiComp=[]
      if (this.optionModel && this.optionModel.bandDataIdsApi) {
        recursion(this.apiComp, this.optionModel.bandDataIdsApi, this.options)
      }
    }
  }
}
</script>

<style scoped>

</style>
