<template>
  <div>
    <el-form-item label="表格列配置">
      <el-button
        type="info"
        icon="el-icon-edit"
        plain
        round
        @click="dialogShow = true"
      >
        配置</el-button
      >
    </el-form-item>
    <el-dialog
      v-dialog-drag
      title="表格列配置"
      width="95%"
      :visible="dialogShow"
      :show-close="false"
    >
      <lt-sort-table
        v-model="optionModel.cols"
        :isEnable="isEnable"
        max-height="500"
        row-key="id"
        default-expand-all
      >
        <lt-table-column-edit prop="label" label="列名" show-overflow-tooltip />
        <lt-table-column-edit prop="prop" label="字段" show-overflow-tooltip />
        <lt-table-column-edit prop="width" label="宽度" />
        <lt-table-column-edit
          prop="align"
          state="select"
          :select-opt="{ options: $enum.alignFix }"
        >
          <template slot="header">
            <div style="display: flex;align-items: center;">
              <span style="margin-right: 10px;">对齐方式</span>
              <svg-icon
                icon-class="左对齐"
                style="cursor: pointer; height: 32px; width: 16px; margin-right: 6px;"
                @click="handleAllAlign('left')"
              ></svg-icon>
              <svg-icon
                icon-class="居中对齐"
                style="cursor: pointer; height: 32px; width: 16px; margin-right: 6px;"
                @click="handleAllAlign('center')"
              ></svg-icon>
              <svg-icon
                icon-class="右对齐"
                style="cursor: pointer; height: 32px; width: 16px;"
                @click="handleAllAlign('right')"
              ></svg-icon>
            </div>
          </template>
        </lt-table-column-edit>
        <lt-table-column-edit label="缩略显示" state="switch" prop="overflow" />
        <lt-table-column-edit label="是否隐藏" state="switch" prop="hidden" />

        <lt-table-column-operation
          width="340"
          v-model="optionModel.cols"
          :row-data="rowObj"
          :unshift="false"
          fixed="right"
          ref="operationRef"
          @save="save"
          @delete="del"
          @addBefore="addBefore"
          :unlimitedAdd="true"
          :validateProp="false"
          primary-key="prop"
          :layout="['delete', 'edit', 'save']"
        >
          <template #after="{ row }">
            <el-link
              :underline="false"
              type="primary"
              style="margin-left: 5px"
              @click="showEdit(row)"
            >
              其他配置
            </el-link>
            <el-link
              :underline="false"
              type="primary"
              style="margin-left: 5px"
              @click="colHeadConfig(row)"
            >
              表头配置
            </el-link>
            <el-link
              :underline="false"
              type="primary"
              style="margin-left: 5px"
              v-if="!row.isEdit"
              @click="addChildren(row)"
            >
              添加子集
            </el-link>
            <template v-if="!isEnable">
              <el-link
                :underline="false"
                type="success"
                style="margin-left: 5px"
                v-if="!row.isEdit && isShowMove(row, 'top')"
                @click="handleMove(row, 'top')"
              >
                上移
              </el-link>
              <el-link
                :underline="false"
                type="success"
                style="margin-left: 5px"
                v-if="!row.isEdit && isShowMove(row, 'bottom')"
                @click="handleMove(row, 'bottom')"
              >
                下移
              </el-link>
            </template>
          </template>
        </lt-table-column-operation>
      </lt-sort-table>
      <div slot="footer" class="dialog-footer">
        <!-- <el-button @click="dialogShow = false"> 取消</el-button> -->
        <el-button type="primary" @click="saveCheck"> 确认</el-button>
      </div>
    </el-dialog>

    <EventEditor
        ref="eventEditorRef"
        :designer="designer"
        title="列点击事件"
        eventHeader="columnClick(row,$index){"
        :eventHandlerCode="clickCode"
        @saveEventHandler="saveClick"
        :key="codeEditorKey"
      >
    </EventEditor>
    <EventEditor
        ref="eventEditorRef2"
        :designer="designer"
        title="自定义HTML"
        eventHeader="customHtml(row,$index,text){"
        :eventHandlerCode="codeSave"
        @saveEventHandler="saveHtml"
        :key="codeEditorKey2"
      >
    </EventEditor>
    <el-dialog
      v-dialog-drag
      title="select配置"
      :visible="selectDialog"
      show-close
      class="small-padding-dialog"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      destroy-on-close
      @close="selectDialog = false"
    >
      <template v-if="row.selectConfig">
        <OptionDict
          :optionModel="row.selectConfig"
        >
        </OptionDict>
        <el-form-item label="选择模型">
          <el-switch v-model="row.selectConfig.isModel"></el-switch>
        </el-form-item>

        <OptionModel
          v-if="row.selectConfig.isModel"
          :optionModel="row.selectConfig"
        />
        <template v-else>
          <ApiEditor ref="apiEditorRef" :optionModel="row.selectConfig" />
          <el-form-item label="参数配置">
            <!-- <el-switch v-model="optionModel.paramConfig" /> -->
            <el-button
              size="mini"
              type="primary"
              round
              plain
              @click="openConfig"
              >配置</el-button
            >
            <paramDialog
              ref="paramDialogRef"
              @close="close"
              :keysArr="optionModel.cols.map((e) => e.prop)"
            />
          </el-form-item>
          <SelectLabel :optionModel="row.selectConfig" />
      <SelectValue :optionModel="row.selectConfig" />
        </template>
      </template>

      <template v-if="row.editType == 'cascader'">
        <SelectChildren :optionModel="row.selectConfig" />
        <!-- <ShowAllLevels :optionModel="row.selectConfig" />
        <CheckStrictly :optionModel="row.selectConfig" /> -->
      </template>
      <template v-if="row.editType == 'select'">
        <OptionItemsDditor
          :selectedWidget="{ ...row.selectConfig, showDefault: false }"
        />
        <el-alert title="数据来源的优先级会比自定义选项高" type="warning" />
        <AllowCreateEditor :optionModel="row.selectConfig" />
        <el-form-item label="多选" v-if="row.selectConfig">
          <el-switch v-model="row.selectConfig.multiple"></el-switch>
        </el-form-item>
      </template>
      <div slot="footer" class="dialog-footer">
        <el-button @click="selectDialog = false"> 取消</el-button>
        <el-button type="primary" @click="saveSelect">确认</el-button>
      </div>
    </el-dialog>
    <el-dialog
      v-dialog-drag
      width="60%"
      :title="row.label + '列配置'"
      :visible="editVisible"
      class="small-padding-dialog ov-a"
      :close-on-click-modal="false"
      @close="editVisible = false"
    >
      <el-form ref="form" :model="row" label-width="70px">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="列固定">
              <el-select v-model="row.fix">
                <el-option label="左" value="left"></el-option>
                <el-option label="右" value="right"></el-option>
                <el-option label="无" value=""></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="合计" label-width="50px">
              <el-switch v-model="row.total" @change="totalChange" />
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="row.total">
            <el-form-item label-width="100px">
              <template #label>
                合计占位符
                <el-tooltip effect="light">
                  <div slot="content">
                    默认是对列进行合计计算<br/>
                    <span style="color: red;">当列不需要进行合计计算时</span>, 此处可配置合计列所显示的文字<br/>
                  </div>
                  <i class="el-icon-info" />
                </el-tooltip>
              </template>
              <el-input v-model="row.totalPlaceholder" placeholder="请输入合计占位符" style="width: 200px;" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="10" v-if="row.total">
            <el-form-item label-width="120px" label="合计保留位数">
              <template #label>
                合计保留位数
                <el-tooltip effect="light">
                  <div slot="content">
                    <span style="color: red;">默认2位</span>
                  </div>
                  <i class="el-icon-info" />
                </el-tooltip>
              </template>
              <el-input v-model="row.totalFixed" type="number" placeholder="请输入合计占位符" style="width: 200px;" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="图标选择">
              <el-popover
                placement="bottom-start"
                width="460"
                trigger="click"
                @show="$refs['iconSelect'].reset()"
              >
                <IconSelect ref="iconSelect" @selected="selected($event, row)" />
                <el-input
                  slot="reference"
                  v-model="row.icon"
                  placeholder="点击选择图标"
                  readonly
                >
                  <svg-icon
                    v-if="row.icon"
                    slot="prefix"
                    :icon-class="row.icon"
                    class="el-input__icon"
                    style="height: 32px; width: 16px"
                  />
                  <i v-else slot="prefix" class="el-icon-search el-input__icon" />
                </el-input>
              </el-popover>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="长类型">
              <el-switch v-model="row.longType" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="显示类型">
          <el-radio-group v-model="row.other" @input="radioChange">
            <el-radio label="hnbj">行内编辑</el-radio>
            <el-radio label="nzlx">内置类型</el-radio>
            <el-radio label="html">自定义html</el-radio>
          </el-radio-group>
        </el-form-item>
        <template v-if="row.other == 'hnbj'">
          <el-form-item label="可编辑">
            <el-switch v-model="row.editable" />
          </el-form-item>
          <el-form-item label="编辑类型">
            <el-select v-model="row.editType">
              <el-option label="输入框" value="input"></el-option>
              <el-option label="日期" value="date"></el-option>
              <el-option label="时间" value="time"></el-option>
              <el-option label="日期时间" value="datetime"></el-option>
              <el-option label="数字" value="number"></el-option>
              <el-option label="下拉框" value="select"></el-option>
              <el-option label="级联选择" value="cascader"></el-option>
              <el-option label="开关" value="switch"></el-option>
              <el-option label="自定义" value="custom"></el-option>
              <el-option label="编码" value="code"></el-option>
            </el-select>
          </el-form-item>
          <template v-if="row.other == 'hnbj'">
            <el-form-item
              v-if="['select', 'cascader'].includes(row.editType)"
              :label="row.editType == 'select' ? '下拉框配置' : '级联配置'"
            >
              <el-button
                type="primary"
                icon="el-icon-edit"
                plain
                size="mini"
                round
                @click="editSelect(row)"
                >配置</el-button
              >
            </el-form-item>
            <el-form-item label="精度"  v-else-if="row.editType=='number' && row.inputConfig">
              <el-input-number v-model="row.inputConfig.precision" :min="0"></el-input-number>
            </el-form-item>
          </template>

          <ruleDefinition-editor
            v-if="row.other == 'hnbj' && row.editType == 'code'"
            :designer="designer"
            :optionModel="row"
          />
        </template>
        <template v-else-if="row.other == 'nzlx'">
          <el-form-item label="内置类型">
            <el-select v-model="row.colType">
              <el-option label="用户名称" value="user"></el-option>
              <el-option label="开关" value="switch"></el-option>
              <el-option label="链接" value="link"></el-option>
              <el-option label="标签" value="tag"></el-option>
              <el-option label="时间" value="time"></el-option>
              <el-option label="文件" value="file"></el-option>
              <el-option label="长文本" value="longText"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="链接事件" v-if="['link', 'switch'].includes(row.colType)">
            <el-tooltip
              effect="light"
              content="仅用于内置类型为链接或开关时，其他类型请使用表格单击单元格事件"
            >
              <el-button
                type="primary"
                icon="el-icon-edit"
                size="mini"
                plain
                round
                @click="editClickCode(row)"
                >编写代码</el-button
              >
            </el-tooltip>
          </el-form-item>
          <el-form-item label="文件为空时的占位符" label-width="130px" v-if="row.colType === 'file'">
            <el-input v-model="row.filePlaceholder" placeholder="当fildId为空时所显示的文字"></el-input>
          </el-form-item>
        </template>

        <el-form-item v-else-if="row.other == 'html'" label="自定义html">
          <el-button
            type="primary"
            icon="el-icon-edit"
            plain
            size="mini"
            round
            @click="editHtml(row)"
            >编写代码</el-button
          >
        </el-form-item>
        <el-form-item
          v-if="row.other == 'nzlx' && row.colType == 'tag'"
          label="标签类型"
        >
          <el-button type="primary" @click="addTagType(row)"
            ><i class="el-icon-plus"></i>添加</el-button
          >
          <el-form
            :inline="true"
            class="demo-form-inline mr-10"
            v-for="(item, index) in row.tagTypes"
            :key="index"
          >
            <el-form-item label="值">
              <el-input
                v-model="item.value"
                placeholder="输入字段值"
                size="small"
                style="width: 160px"
              ></el-input>
            </el-form-item>
            <el-form-item label="标签显示标题">
              <el-input
                v-model="item.label"
                placeholder="标签显示标题"
                size="small"
                style="width: 160px"
              ></el-input>
            </el-form-item>
            <el-form-item label="显示类型">
              <el-select
                v-model="item.type"
                placeholder="显示类型"
                size="small"
                style="width: 160px"
              >
                <el-option label="默认" value="default"></el-option>
                <el-option label="主要" value="primary"></el-option>
                <el-option label="成功" value="success"></el-option>
                <el-option label="信息" value="info"></el-option>
                <el-option label="警告" value="warning"></el-option>
                <el-option label="危险" value="danger"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button
                type="danger"
                size="mini"
                plain
                round
                @click="row.tagTypes.splice(index, 1)"
                >删除</el-button
              >
            </el-form-item>
          </el-form>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="editVisible = false"> 取消</el-button>
        <el-button type="primary" @click="confirm">确认</el-button>
      </div>
    </el-dialog>

    <el-dialog
      v-dialog-drag
      width="40%"
      :title="row.label + '表头配置'"
      :visible="headerVisible"
      class="small-padding-dialog ov-a"
      :close-on-click-modal="false"
      @close="headerVisible = false"
    >
      <el-form ref="form" :model="row" label-width="70px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="显示排序">
              <el-switch v-model="row.showSort">
              </el-switch>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="显示已填">
              <el-switch v-model="row.showBlank">
              </el-switch>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="显示搜索">
              <el-switch v-model="row.showSearch">
              </el-switch>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="row.showSearch">
            <el-form-item label="搜索类型">
              <el-select v-model="row.searchType" clearable>
                <el-option label="日期" value="date"></el-option>
                <el-option label="数字" value="number"></el-option>
                <!-- <el-option label="选择器" value="select"></el-option> -->
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="headerVisible = false"> 取消</el-button>
        <el-button type="primary" @click="confirm">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import i18n from "@/utils/i18n";
import EventEditor from "@/components/EventEditor/index";
import IconSelect from "@/components/IconSelect";
import ApiEditor from "@/components/form-designer/setting-panel/property-editor/api-editor.vue";
import OptionModel from "@/components/form-designer/setting-panel/property-editor/optionsModel-editor.vue";
import OptionDict from "@/components/form-designer/setting-panel/property-editor/optionsDict-editor.vue";
import paramDialog from "../components/param-dialog.vue";
import SelectLabel from "@/components/form-designer/setting-panel/property-editor/selectLabel-editor.vue";
import SelectValue from "@/components/form-designer/setting-panel/property-editor/selectValue-editor.vue";
import SelectChildren from "@/components/form-designer/setting-panel/property-editor/selectChildren-editor.vue";
import ShowAllLevels from "@/components/form-designer/setting-panel/property-editor/showAllLevels-editor.vue";
import CheckStrictly from "@/components/form-designer/setting-panel/property-editor/checkStrictly-editor.vue";
import OptionItemsDditor from "@/components/form-designer/setting-panel/property-editor/optionItems-editor.vue";
import AllowCreateEditor from "@/components/form-designer/setting-panel/property-editor/allowCreate-editor.vue";
import ruleDefinitionEditor from "@/components/form-designer/setting-panel/property-editor/ruleDefinition-editor.vue";
import Template from "../../../../views/tool/messageComponents/template.vue";

const selectConfig = {
  api: "",
  onSuccessCallback: "",
  onFailCallback: "",
  selectLabel: "label",
  selectValue: "value",
  selectChildren: "children",
  multiple: false,
  showAllLevels: true,
  checkStrictly: false,
  isModel: false,
  optionsDict: undefined,
  optionsModel: undefined,
  modelLabel: undefined,
  modelValue: undefined,
  options: {
    optionItems: [
      {
        label: "select 1",
        value: "1",
      },
      {
        label: "select 2",
        value: "2",
      },
      {
        label: "select 3",
        value: "3",
      },
    ],
  },
  type: "select",
  allowCreate: false,
  paramConfig: {},
};
const inputConfig={
  precision:0
}
export default {
  name: "cols-editor",
  mixins: [i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  components: {
    EventEditor,
    IconSelect,
    ApiEditor,
    OptionModel,
    paramDialog,
    SelectLabel,
    SelectValue,
    SelectChildren,
    ShowAllLevels,
    CheckStrictly,
    OptionItemsDditor,
    AllowCreateEditor,
    ruleDefinitionEditor,
    Template,
    OptionDict,
  },
  data() {
    return {
      dialogShow: false,
      row: {},
      codeSave: "",
      clickCode: "",
      rowObj: {
        label: "",
        prop: "",
        fix: "",
        align: "",
        width: "",
        editable: true,
        editType: "input",
        icon: "",
        code: ``,
        sortable: false,
        overflow: true,
        link: false,
        tag: false,
        total: false,
        totalPlaceholder: '',   // 合计占位符
        totalFixed: 2, // 合计保留位数
        hidden: false,
        isEdit: true,
        other: "",
        inputConfig,
        selectConfig: selectConfig,
        ruleDefinition: [
          {
            context: "4 位数",
            placeholder: "自动计数",
            isDel: false,
            ruleType: 4,
            numberLength: 4,
            openLength: true,
            resetCycle: 4,
            initialValue: 1,
          },
        ],
        filePlaceholder: ''     // 文件占位符
      },
      selectDialog: false,
      editVisible: false,
      headerVisible: false,
      currentObj: {},
      oldOther: "",
      showSort:false,
      showBlank:false,
      showSearch:false,
      searchType:null,
      codeEditorKey: "",
      codeEditorKey2: "",
    };
  },
  computed: {
    isEnable() {
      return this.optionModel.cols.every(
        (item) => !item.children || item.children.length == 0
      );
    },
  },
  mounted() {
    this.$array.handleDFS(this.optionModel.cols, 'children', (item) => {
      if (!('inputConfig' in item)) {
        this.$set(item, 'inputConfig', { ...inputConfig });
      }
      if (!item.id) {
        this.$set(item, "id", +new Date().getTime());
      }
      if (!item.align) {
        this.$set(item, "align", "left");
      }

      if (!item.hasOwnProperty("editable")) {
        this.$set(item, "editable", true);
      }
      if (!item.hasOwnProperty("editType")) {
        this.$set(item, "editType", "input");
      }
      if (!item.hasOwnProperty("showSort")) {
        this.$set(item, "showSort", false);
      }
      if (!item.hasOwnProperty("showBlank")) {
        this.$set(item, "showBlank", false);
      }
      if (!item.hasOwnProperty("showSearch")) {
        this.$set(item, "showSearch", false);
      }
      if (!item.hasOwnProperty("searchType")) {
        this.$set(item, "searchType", null);
      }
      if (!item.hasOwnProperty("filePlaceholder")) {
        this.$set(item, "filePlaceholder", null);
      }
    })
  },

  methods: {
    saveCheck(){
      let colMap = {}
      let isSuccess = true

      this.$array.handleDFS(this.optionModel.cols, 'children', (item) => {
        if (isSuccess) {
          if (this.$enum.keywordList.includes(item.prop)) {
            isSuccess = false
            this.$message.warning("无法创建关键字 " + item.prop)
          }

          if (item.prop) {
            if (!colMap[item.prop]) {
              colMap[item.prop] = true
            }
            else {
              isSuccess = false
              this.$message.warning("字段重复请重新查验 " + item.prop)
            }
          }

        }
      })

      if(isSuccess) this.dialogShow = false;
    },
    colHeadConfig(row){

      this.headerVisible=true
      this.currentObj = row;
      this.row = JSON.parse(JSON.stringify(row));
    },
    openConfig() {
      if (!this.row.selectConfig.api) {
        this.$message.error("请先选择接口");
        return;
      }
      let interfacesInfos = this.$store.state.design.interfacesInfos;
      let apiList = [];
      interfacesInfos.forEach((item) => {
        let method = item.interfacesContent.method;
        apiList.push({
          methodType: method ? method : "",
          interfaceType: "api",
          responseBody: item.interfacesContent.responseBody
            ? item.interfacesContent.responseBody
            : "",
          name: item.interfacesContent.name ? item.interfacesContent.name : "",
          parentId: this.$store.state.design.formId,
          interfacesUid: item.interfacesUid,
          parameters:
            method == "GET"
              ? JSON.stringify(item.interfacesContent.parameters)
              : "",
          requestBody:
            method == "POST" ? item.interfacesContent.requestBody : "",
        });
      });

      let itemObj = {};
      let dfs = (root) => {
        if (root.interfacesUid === this.row.selectConfig.api) {
          itemObj = root;
          return;
        }
        root.children && root.children.forEach((item) => dfs(item));
      };
      dfs({ children: this.$refs.apiEditorRef.options });


      if (
        (itemObj.methodType === "GET" && !itemObj.parameters) ||
        (itemObj.methodType !== "GET" && !itemObj.requestBody)
      ) {
        this.$message.error("接口不存在入参");
        return;
      }
      let apiParam = {};
      if (itemObj.methodType === 'GET' && itemObj.parameters != '{}' && itemObj.parameters != '') {
        let par = JSON.parse(itemObj.parameters);
        apiParam = par.reduce((obj, item) => {
          obj[item.name] = ""; // 将 name 属性作为对象的属性，初始值设为空字符串
          return obj;
        }, {});
      } else if (itemObj.methodType !== 'GET' && itemObj.requestBody != null || itemObj.requestBody != '') {
        apiParam = JSON.parse(itemObj.requestBody);
      }

      // 去除掉一些固定参数 ，比如：分页的pageSize和pageNum
      ['pageSize', 'pageNum', 'choiceId'].map((item) => {
        delete apiParam[item];
      });

      if (!this.row.selectConfig.paramConfig) {
        this.row.selectConfig.paramConfig = {};
      }
      for (let pam in apiParam) {
        if (!(pam in this.row.selectConfig.paramConfig)) {
          this.row.selectConfig.paramConfig[pam] = "";
        }
      }
      this.$refs.paramDialogRef.open(
        apiParam,
        this.row.selectConfig.paramConfig
      );
    },

    close(e) {
      this.row.selectConfig.paramConfig = e;
    },
    addTagType(row) {
      if (!row.tagTypes) {
        this.$set(row, "tagTypes", [{ value: "", label: "", type: "" }]);
      } else {
        row.tagTypes.push({ value: "", label: "", type: "" });
      }
    },
    showEdit(row) {
      this.editVisible = true;
      if(!('totalPlaceholder' in row)) {
        this.$set(row, 'totalPlaceholder', false)
      }
      if(!('totalFixed' in row)) {
        this.$set(row, 'totalFixed', 2)
      }

      this.currentObj = row;
      this.oldOther = row.other
      this.row = JSON.parse(JSON.stringify(row));
    },
    confirm() {
      for (let i in this.row) {
        this.currentObj[i] = this.row[i];
      }
      this.editVisible = false;
      this.headerVisible=false
    },
    selected(e, row) {
      row.icon = e;
    },
    saveHtml(eventCode) {
      this.codeSave = eventCode;
      this.row.code = this.codeSave;
    },
    saveClick(eventCode) {
      this.clickCode = eventCode;
      this.row.clickCode = this.clickCode;
    },
    editHtml(row) {
      this.codeSave = row.code;
      this.codeEditorKey2 = new Date().getTime() + 1;
      this.$nextTick(() => {
        this.$refs.eventEditorRef2.open(true);
      });
    },
    editClickCode(row) {
      this.clickCode = row.clickCode || "";
      this.codeEditorKey = new Date().getTime() + 1;
      this.$nextTick(() => {
        this.$refs.eventEditorRef.open(true);
      })
    },
    addChildren(row) {
      this.$refs.operationRef.addChildren(row);
    },
    addBefore() {
      this.rowObj.id = +new Date().getTime();
    },
    save({ data }) {
      this.$notify.success({ message: "保存成功" });
      this.$refs.operationRef.saveComplete(data);
    },
    del({ data, index }) {
      function getItem(arr, id) {
        arr.forEach((item, index2) => {
          if (item.id && item.id === id) {
            arr.splice(index2, 1);
          } else {
            item.children && getItem(item.children, id);
          }
        });
      }
      getItem(this.optionModel.cols, data.id);
      this.$forceUpdate();
    },
    editSelect(row) {
      if (!row.editable) {
        this.$message("请启用可编辑");
        return;
      }
      if (!("selectConfig" in this.row)) {
        this.$set(this.row, "selectConfig", { ...selectConfig });
      }
      // 字段比较,判断是否有某个字段,没有则追加
      for (let key in selectConfig) {
        if (!(key in this.row.selectConfig)) {
          this.$set(this.row.selectConfig, key, selectConfig[key]);
        }
      }
      this.selectDialog = true;
    },
    saveSelect() {
      this.selectDialog = false;
    },
    isShowMove(row, moveFlag) {
      const { id } = row;
      if (id) {
        let isShow = null;
        // 找到id对应的节点
        const dfs = (root) => {
          let index = root.findIndex((item) => item.id === id);
          if (index !== -1) {
            if (moveFlag === "top") {
              isShow = index !== 0;
            } else {
              isShow = index !== root.length - 1;
            }
          } else {
            root.forEach((item) => {
              item.children && dfs(item.children);
            });
          }
        };
        dfs(this.optionModel.cols);

        return isShow;
      }
      return false;
    },
    handleMove(row, moveFlag) {
      let dataSource = {
        id: 99999999,
        children: JSON.parse(JSON.stringify(this.optionModel.cols)),
      };
      let isComplete = false;
      const dfs = (root) => {
        if (!isComplete) {
          let i =
            root.children &&
            root.children.findIndex((item) => item.id === row.id);
          if (i > -1) {
            let positionalIndex = moveFlag === "top" ? i - 1 : i + 1;

            let temp = root.children[i];
            root.children[i] = root.children[positionalIndex];
            root.children[positionalIndex] = temp;
            isComplete = true;
          }
          root.children && root.children.forEach((item) => dfs(item));
        }
      };

      dfs(dataSource);

      this.optionModel.cols = dataSource.children;
    },
    radioChange(val) {
      if (val && this.oldOther) {
        const checkData = [
          { key: "hnbj", label: "行内编辑" },
          { key: "nzlx", label: "内置类型", value: this.row.colType },
          { key: "html", label: "自定义html", value: this.row.code },
        ];

        if (
          ["nzlx", "html"].includes(this.oldOther) &&
          (this.row.colType || this.row.code)
        ) {
          let start = checkData.find(
            (item) => item.key === this.oldOther
          ).label;
          let end = checkData.find((item) => item.key === val).label;

          this.$confirm(
            `当前操作正在从'${start}'切换至'${end}', 将会清空关于'${start}'的配置, 是否继续?`,
            "提示",
            {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            }
          )
            .then(() => {
              if (this.oldOther === "nzlx") this.row.colType = undefined;
              if (this.oldOther === "html") this.row.code = null;
              this.oldOther = val;
            })
            .catch(() => {
              this.row.other = this.oldOther;
            });
        } else {
          this.oldOther = val;
        }
      } else {
        this.oldOther = val;
      }
    },
    totalChange() {
      this.row.totalPlaceholder = ''
    },
    handleAllAlign(val) {
      this.optionModel.cols.forEach(item => {
        item.align = val
      })
    }
  },
};
</script>

<style lang="scss" scoped>
li.col-item {
  list-style: none;

  margin: 5px 0;
}

::v-deep .cell {
  display: flex;
}

::v-deep .el-button--mini.is-circle {
  padding: 5px;
}

::v-deep .el-radio-button--mini .el-radio-button__inner {
  padding: 7px 10px;
}
.ov-a {
  ::v-deep .el-dialog__body {
    overflow-y: auto;
  }
}
</style>
