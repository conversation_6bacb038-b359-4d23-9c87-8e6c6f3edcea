<template>
  <div>
    <el-form-item label="配置数据源">
      <el-button class="DataSource" type="primary" round plain @click="showDataSource = true">配置</el-button>
    </el-form-item>
    <el-dialog title="配置数据源" :visible.sync="showDataSource" width="80%" :show-close="true" class="small-padding-dialog" v-dialog-drag :close-on-click-modal="false" :close-on-press-escape="false" :destroy-on-close="true">
      <el-form-item label="主数据">
        <div class="d-flex a-center" style="width: 300px">
          <el-select v-model="sqlQuery.sourceTable" @change="sourceTableChange" filterable>
            <el-option v-for="item in tables" :key="item.tableName" :label="item.tableComment" :value="item.tableName">
            </el-option>
          </el-select>
          <FieldsSelect v-model="sqlQuery.fields" :fieldArr="getFields(sqlQuery.sourceTable)"></FieldsSelect>
        </div>
      </el-form-item>
      <template v-if="sqlQuery.joins.length">
        <el-form-item label="关联" v-for="(item, index) in sqlQuery.joins" :key="index">
          <div class="d-flex a-center">
            <i class="el-icon-delete font-16" @click="deleteJoin(index)" style="cursor: pointer"></i>
            <el-select v-model="item.method" class="mr-l10 mr-r5" placeholder="请选择关联方式" style="width: 150px">
              <el-option v-for="method in joinMethod" :key="method.value" :label="method.label" :value="method.value"></el-option>
            </el-select>

            <div class="d-flex a-center" style="width: 400px">
              <el-select v-model="item.tableAlias" @change="joinTableChange($event, item, index)" placeholder="请选择关联表">
                <el-option v-for="item in filterTables(index)" :key="item.tableName" :label="item.tableComment" :value="item.tableName"></el-option>
              </el-select>
              <FieldsSelect v-model="item.fields" :fieldArr="getFields(item.originTable || item.tableAlias)"></FieldsSelect>
            </div>
            <span class="mr-l5 mr-r5">当</span>
            <el-cascader style="width: 300px" placeholder="请选择关联字段" v-model="item.field" :options="filterJoinTable(index)" :props="{
                emitPath: false,
              }" filterable></el-cascader>
            <span class="mr-l5 mr-r5">=</span>
            <el-select style="width: 300px" v-model="item.joinField" placeholder="请选择被关联字段" filterable>
              <el-option v-for="field in getFields(
                  item.originTable || item.tableAlias
                ).map((e) => ({
                  ...e,
                  columnName: `${item.tableAlias}.${e.columnName}`,
                }))" :key="field.columnName" :label="`${field.columnComment}(${field.columnName})`" :value="field.columnName"></el-option>
            </el-select>
          </div>
        </el-form-item>
      </template>
      <el-form-item label="排序" v-if="showMap.orders">
        <div class="d-flex a-center flex-wrap">
          <i class="el-icon-delete font-16" @click="deleteRow('orders')" style="cursor: pointer"></i>
          <el-tag class="mr-l10" :key="item.field" v-for="(item, index) in sqlQuery.orders" closable :disable-transitions="false" @close="sqlQuery.orders.splice(index, 1)">
            {{ item.label }}
            <i :class="item.order == 'ASC' ? 'el-icon-top' : 'el-icon-bottom'" class="mr-l5"></i>
          </el-tag>
          <el-popover placement="bottom" title="新增排序" width="200" trigger="click">
            <el-cascader v-model="orderObj.field" :options="fieldTree" :props="{
                emitPath: false,
              }" filterable></el-cascader>

            <el-radio-group class="mr-t10 mr-b10" v-model="orderObj.order">
              <el-radio label="ASC">升序</el-radio>
              <el-radio label="DESC">降序</el-radio>
            </el-radio-group>

            <el-form-item label-width="40">
              <el-button type="primary" @click="addOrder">确定</el-button>
            </el-form-item>
            <el-button slot="reference" type="primary" class="font-18 mr-l10" size="mini" plain>+</el-button>
          </el-popover>
        </div>
      </el-form-item>

      <el-form-item label="过滤" v-if="showMap.filters">
        <div class="d-flex a-center flex-wrap">
          <i class="el-icon-delete font-16" @click="deleteRow('filters')" style="cursor: pointer"></i>
          <div>
            <el-popover placement="bottom" title="修改过滤" width="300" trigger="click" v-for="(item, index) in sqlQuery.filters" :key="index">
              <div class="mr-b20">
                <el-radio-group v-model="item.type">
                  <el-radio-button label="field">字段</el-radio-button>
                  <el-radio-button label="symbol">符号</el-radio-button>
                  <el-radio-button label="relation">关系</el-radio-button>
                </el-radio-group>
              </div>

              <el-radio-group v-if="item.type == 'symbol'" v-model="item.value">
                <el-radio-button label="("></el-radio-button>
                <el-radio-button label=")"></el-radio-button>
              </el-radio-group>

              <el-radio-group v-else-if="item.type == 'relation'" v-model="item.value">
                <el-radio-button label="or">或</el-radio-button>
                <el-radio-button label="and">且</el-radio-button>
              </el-radio-group>

              <div v-else>
                <el-cascader v-model="item.field" placeholder="请选择字段" :options="fieldTree" :props="{
                    emitPath: false,
                  }" filterable></el-cascader>

                <el-select v-model="item.operator" placeholder="请选择关系" class="mr-t10 mr-b10" filterable>
                  <el-option v-for="operator in operatorMap" :key="operator.value" :label="operator.label" :value="operator.value"></el-option>
                </el-select>

                <el-radio-group v-model="item.valueType" @change="(item.value = ''), (item.widgetId = null)" class="mr-b10">
                  <el-radio label="normal">常量</el-radio>
                  <el-radio label="variable">变量</el-radio>
                </el-radio-group>
                <el-input v-if="item.valueType == 'normal'" placeholder="请输入常量值" v-model="item.value"></el-input>
                <el-select v-else placeholder="请选择组件" v-model="item.widgetId" filterable>
                  <el-option v-for="item in formTree" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </div>
              <el-tag class="mr-l10" slot="reference" closable draggable="true" :data-index="index" :disable-transitions="false" @close="sqlQuery.filters.splice(index, 1)">
                {{
                  item.type == "field"
                    ? `${item.label}${item.operator}${
                        item.valueType == "normal"
                          ? item.value
                          : `#(${item.widgetId})`
                      }`
                    : item.value
                }}
              </el-tag>
            </el-popover>
          </div>

          <el-popover placement="bottom" title="新增过滤" width="300" trigger="click">
            <div class="mr-b20">
              <el-radio-group v-model="filterObj.type">
                <el-radio-button label="field">字段</el-radio-button>
                <el-radio-button label="symbol">符号</el-radio-button>
                <el-radio-button label="relation">关系</el-radio-button>
              </el-radio-group>
            </div>

            <el-radio-group v-if="filterObj.type == 'symbol'" v-model="filterObj.value">
              <el-radio-button label="("></el-radio-button>
              <el-radio-button label=")"></el-radio-button>
            </el-radio-group>

            <el-radio-group v-else-if="filterObj.type == 'relation'" v-model="filterObj.value">
              <el-radio-button label="or">或</el-radio-button>
              <el-radio-button label="and">且</el-radio-button>
            </el-radio-group>

            <div v-else>
              <el-cascader v-model="filterObj.field" placeholder="请选择字段" :options="fieldTree" :props="{
                  emitPath: false,
                }" filterable></el-cascader>

              <el-select v-model="filterObj.operator" placeholder="请选择关系" class="mr-t10 mr-b10" filterable>
                <el-option v-for="operator in operatorMap" :key="operator.value" :label="operator.label" :value="operator.value"></el-option>
              </el-select>

              <el-radio-group v-model="filterObj.valueType" @change="(filterObj.value = ''), (filterObj.widgetId = null)" class="mr-b10">
                <el-radio label="normal">常量</el-radio>
                <el-radio label="variable">变量</el-radio>
              </el-radio-group>
              <el-input v-if="filterObj.valueType == 'normal'" placeholder="请输入常量值" v-model="filterObj.value"></el-input>
              <el-select v-else placeholder="请选择组件" v-model="filterObj.widgetId" filterable>
                <el-option v-for="item in formTree" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </div>

            <el-form-item label-width="40" class="mr-t10">
              <el-button type="primary" @click="addFliter">确定</el-button>
            </el-form-item>
            <el-button slot="reference" type="primary" class="font-18 mr-l10" size="mini" plain>+</el-button>
          </el-popover>
        </div>
      </el-form-item>
      <el-form-item label="聚合" v-if="showMap.expression">
        <div class="d-flex a-center flex-wrap">
          <i class="el-icon-delete font-16" @click="deleteRow('expression')" style="cursor: pointer"></i>
          <el-tag class="mr-l10" :key="index" v-for="(item, index) in sqlQuery.expression" closable :disable-transitions="false" @close="sqlQuery.expression.splice(index, 1)">
            {{ `${item.method} ${item.field ? `(${item.field})` : ""} ` }}
          </el-tag>
          <el-popover placement="bottom" title="新增聚合" width="250" trigger="click">
            <el-select v-model="expressionObj.method" placeholder="请选择函数" class="mr-b10" filterable>
              <el-option v-for="method in expressionMethod" :key="method.value" :label="method.label" :value="method.value"></el-option>
            </el-select>
            <el-cascader v-if="expressionObj.method !== 'COUNT'" v-model="expressionObj.field" placeholder="请选择字段" :options="fieldTree" :props="{
                emitPath: false,
              }" filterable></el-cascader>
            <el-form-item label-width="40" class="mr-t10">
              <el-button type="primary" @click="addExpression">确定</el-button>
            </el-form-item>
            <el-button slot="reference" type="primary" class="font-18 mr-l10" size="mini" plain>+</el-button>
          </el-popover>
        </div>
      </el-form-item>
      <el-form-item label="分组" v-if="showMap.group">
        <div class="d-flex a-center">
          <i class="el-icon-delete font-16 mr-r10" @click="deleteRow('group')" style="cursor: pointer"></i>
          <el-cascader style="width: 300px" v-model="sqlQuery.group" placeholder="请选择字段" :options="fieldTree" :props="{
              emitPath: false,
              multiple: true,
            }" filterable></el-cascader>
        </div>
      </el-form-item>
      <el-form-item label="行数" v-if="showMap.pageSize">
        <div class="d-flex a-center">
          <i class="el-icon-delete font-16 mr-r10" @click="deleteRow('pageSize')" style="cursor: pointer"></i>
          <el-input-number :min="0" v-model="sqlQuery.pageSize"></el-input-number>
        </div>
      </el-form-item>
      <div class="btn-bar">
        <el-button size="mini" type="primary" v-if="showJoinButton" @click="addJoin">
          关联
        </el-button>
        <el-button size="mini" type="primary" v-if="!showMap.filters" @click="showMap.filters = true">
          过滤
        </el-button>
        <!-- <el-button
          size="mini"
          type="primary"
        >
          自定义列
        </el-button> -->
        <el-button v-if="!showMap.expression" size="mini" type="primary" @click="showMap.expression = true">
          聚合
        </el-button>

        <el-button v-if="!showMap.orders" size="mini" type="primary" @click="showMap.orders = true">
          排序
        </el-button>
        <el-button v-if="!showMap.group" size="mini" type="primary" @click="showMap.group = true">
          分组
        </el-button>
        <el-button v-if="!showMap.pageSize" size="mini" type="primary" @click="showMap.pageSize = true">
          行数
        </el-button>
        <el-button size="mini" type="success" @click="getSql">
          查看SQL
        </el-button>
        <!-- <el-button size="mini" type="success"> 执行 </el-button> -->
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="showDataSource = false">取 消</el-button>
        <el-button type="primary" @click="confirm">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog :visible="sqlVisble" @close="sqlVisble = false" title="查看Sql">
      <codemirror ref="targetInSql" v-model="sqlStr" :options="cOptions" style="height: 600px; overflow: auto" />
    </el-dialog>
  </div>
</template>

<script>
import i18n from "@/utils/i18n";
import FieldsSelect from "./fieldsSelect";
import { traverseAllWidgets } from "@/utils/util";
import { jsonToSql } from "@/api/tool/form.js";
import { codemirror } from "vue-codemirror";
import "codemirror/mode/sql/sql.js";
import "codemirror/theme/nord.css";
import "codemirror/lib/codemirror.css";
import sqlFormatter from "sql-formatter";
export default {
  components: { FieldsSelect, codemirror },
  name: "modelQuery-editor",
  mixins: [i18n],
  props: {
    designer: Object,
    selectedWidget: Object,
    optionModel: Object,
  },
  data() {
    return {
      sqlStr: "",
      sqlVisble: false,
      cOptions: {
        mode: "text/x-mysql",
        lineNumbers: true,
        lineWrapping: true,

        hintOptions: {
          completeSingle: true,
        },
        readOnly: true,
      },
      showDataSource: false,
      checkAll: false,
      showMap: {
        orders: false,
        joins: false,
        filters: false,
        pageSize: false,
        group: false,
        expression: false,
      }, // 控制表单显示
      sqlQuery: {
        sourceTable: "",
        fields: [],
        orders: [],
        joins: [],
        filters: [],
        expression: [],
        group: [],
        pageSize: null,
        pageNum: 1,
      }, // 查询json
      orderObj: {
        field: "",
        label: "",
        order: "ASC",
      }, // 排序对象
      filterObj: {
        field: "",
        operator: null,
        value: "",
        valueType: "variable",
        fieldType: "",
        widgetId: null,
        label: "",
        type: "field",
      }, // 过滤对象
      expressionObj: {
        field: "",
        method: null,
      },
      joinMethod: [
        {
          label: "左联",
          value: "left",
        },
        {
          label: "右联",
          value: "right",
        },
        {
          label: "内联",
          value: "inner",
        },
        {
          label: "全联",
          value: "all",
        },
      ],
      operatorMap: [
        {
          label: "等于",
          value: "=",
        },
        {
          label: "不等于",
          value: "!=",
        },
        {
          label: "大于",
          value: ">",
        },
        {
          label: "大于等于",
          value: ">=",
        },
        {
          label: "小于",
          value: "<",
        },
        {
          label: "小于等于",
          value: "<=",
        },
        {
          label: "包含",
          value: "LIKE",
        },
        {
          label: "不包含",
          value: "NOT LIKE",
        },
        {
          label: "为空",
          value: "IS NULL",
        },
        {
          label: "不为空",
          value: "IS NOT NULL",
        },
      ],
      expressionMethod: [
        {
          label: "计数",
          value: "COUNT",
        },
        {
          label: "求和",
          value: "SUM",
        },
        {
          label: "平均",
          value: "AVG",
        },
        {
          label: "最大",
          value: "MAX",
        },
        {
          label: "最小",
          value: "MIN",
        },
      ],
      dragged: null,
    };
  },
  mounted() {
    let self = this;

    // 当拖拽开始的时候，拖拽目标变成半透明
    document.addEventListener(
      "dragstart",
      function (event) {
        // 保存拖动元素的引用(ref.)
        self.dragged = event.target;
        // 使其半透明
        event.target.style.opacity = 0.5;
      },
      false
    );

    document.addEventListener(
      "dragend",
      function (event) {
        // 重置透明度
        event.target.style.opacity = "";
      },
      false
    );

    /* 放置目标元素时触发事件 */
    document.addEventListener(
      "dragover",
      function (event) {
        // 阻止默认动作以启用drop
        event.preventDefault();
      },
      false
    );

    document.addEventListener(
      "drop",
      function (event) {
        // 阻止默认动作（如打开一些元素的链接）
        event.preventDefault();
        // 将拖动的元素到所选择的放置目标节点中

        // 要被替换元素的索引
        let targetIndex = event.target.dataset.index;
        // 拖拽元素的目标索引
        let draggedIndex = self.dragged.dataset.index;
        // 拖拽元素对应的数组中的数据
        if (targetIndex && draggedIndex && targetIndex !== draggedIndex) {
          let arrItem = self.sqlQuery.filters[draggedIndex];
          // 删除原位置的拖拽元素对应的数据
          self.sqlQuery.filters.splice(draggedIndex, 1);
          // 添加到指定位置
          self.sqlQuery.filters.splice(targetIndex, 0, arrItem);
        }
      },
      false
    );
  },
  watch: {
    "optionModel.sqlJson": {
      handler(val) {
        if (!val) return;
        let copyObj = JSON.parse(JSON.stringify(val));
        copyObj.fields = copyObj.fields.map((item) => item.columnName);
        if (copyObj.joins.length > 0) {
          copyObj.joins.forEach((item) => {
            item.fields = item.fields.map((item) => item.columnName);
          });
        }
        this.sqlQuery = copyObj;
        for (let key in this.showMap) {
          if (key == "pageSize") {
            this.showMap.pageSize = Boolean(copyObj.pageSize);
          } else {
            this.showMap[key] = copyObj[key].length > 0;
          }
        }
      },
      deep: true,
      immediate: true,
    },
  },
  computed: {
    formTree() {
      let arr = [];
      traverseAllWidgets(this.designer.widgetList, (item) => {
        if (
          [
            "input",
            "select",
            "cascader",
            "steps",
            "date-range",
            "select-page",
            "tab",
            "time-range",
            "borad",
            "textarea",
            "radio",
            "custom-condition",
            "transfer",
            "descriptions",
            "rich-editor",
            "progress",
            "checkbox",
            "static-text",
            "pictureupload",
            "fileupload",
            "map",
            "number",
            "tree",
            "time",
            "date",
            "date-time",
            "switch",
            "rate",
            "color",
            "slider",
            "html-text",
            "codeConfig",
            "image",
            "timeline",
            "carousel",
            "qrcode",
            "barCode",
            "data",
          ].includes(item.type)
        ) {
          arr.push({
            label: item.options.label
              ? `${item.options.label}(${item.options.name})`
              : item.options.name,
            value: item.id,
          });
        }
      });
      return arr;
    },
    showJoinButton() {
      return (
        this.sqlQuery.joins.length == 0 ||
        this.sqlQuery.joins.every(
          (item) => item.field && item.joinField && item.tableAlias
        )
      );
    },
    setup() {
      return this.$store.state.design;
    },
    tables() {
      let tables = JSON.parse(JSON.stringify(this.setup.tables));
      if (this.setup.refBus) {
        let model = tables.concat(this.$store.state["refModel"])
        return model
      } else {
        return tables
      }
    },
    fieldTree() {
      let arr = this.tables
        .filter((item) => {
          let tableNames = this.sqlQuery.joins.map((item) => item.tableAlias);
          return (
            item.tableName == this.sqlQuery.sourceTable ||
            tableNames.includes(item.tableName)
          );
        })
        .map((item) => ({
          label: item.tableComment,
          tableName: item.tableName,
          children: item.columns.map((item2) => ({
            label: `${item2.columnComment}(${item2.columnName})`,
            value: `${item.tableName}.${item2.columnName}`,
            fieldType: item2.columnType,
          })),
        }));
      let tableNames = arr.map((item) => item.tableName);
      this.sqlQuery.joins.forEach((item) => {
        if (
          item.field &&
          item.joinField &&
          item.tableAlias &&
          !tableNames.includes(item.tableAlias)
        ) {

          let obj = JSON.parse(JSON.stringify(this.tables.find((item2) => item2.tableName == item.originTable)));
          arr.push({
            label: obj.tableComment + "_" + item.tableAlias.split("_").at(-1),
            tableName: item.tableAlias,
            children: obj.columns.map((e) => ({
              label: `${e.columnComment}(${e.columnName})`,
              value: `${item.tableAlias}.${e.columnName}`,
              fieldType: e.columnType,
            })),
          });
        }
      });
      return arr;
    },
  },
  methods: {
    sourceTableChange(val) {
      let obj = this.tables.find((item) => item.tableName == val);
      this.sqlQuery.fields = obj.columns.map((item) => item.columnName);
      this.sqlQuery = {
        ...this.sqlQuery,
        orders: [],
        joins: [],
        filters: [],
        expression: [],
        group: [],
        pageSize: null,
      };
    },
    joinTableChange(val, item, index) {
      let obj = this.filterTables(index).find((e) => e.tableName == val);
      this.$set(item, "originTable", obj.originTable);
      let columns = this.getFields(obj.originTable).map(
        (item) => item.columnName
      );
      item.fields = columns;
      item.joinField = "";
    },
    filterTables(index) {
      let arr = this.tables.filter(
        (item) => item.tableName !== this.sqlQuery.sourceTable
      );

      arr = arr.map((item) => {
        let flag =
          index == 0 ||
          !this.sqlQuery.joins
            .slice(0, index)
            .map((item) => item.tableAlias)
            .includes(item.tableName);
        return {
          ...item,
          tableName: flag ? item.tableName : item.tableName + "_" + index,
          originTable: item.tableName,
        };
      });

      return arr;
    },
    filterJoinTable(index) {
      let arr = JSON.parse(JSON.stringify(this.tables));
      let tableNames = arr.map((item) => item.tableName);
      this.sqlQuery.joins.forEach((item) => {
        if (
          item.field &&
          item.joinField &&
          item.tableAlias &&
          !tableNames.includes(item.tableAlias)
        ) {
          let obj = JSON.parse(JSON.stringify(
            this.tables.find((item2) => item2.tableName == item.originTable)
          ));
          arr.push({
            ...obj,
            tableComment:
              obj.tableComment + "_" + item.tableAlias.split("_").at(-1),
            tableName: item.tableAlias,
          });
        }
      });

      arr = arr
        .filter((item) => {
          if (index == 0) {
            return item.tableName == this.sqlQuery.sourceTable;
          } else {
            let arr = this.sqlQuery.joins
              .slice(0, index)
              .map((e) => e.tableAlias);
            return (
              item.tableName == this.sqlQuery.sourceTable ||
              arr.includes(item.tableName)
            );
          }
        })
        .map((item) => ({
          label: item.tableComment,
          tableName: item.tableName,
          children: item.columns.map((item2) => ({
            label: `${item2.columnComment}(${item2.columnName})`,
            value: `${item.tableName}.${item2.columnName}`,
          })),
        }));
      return arr;
    },
    handlerJson() {
      let columns = this.tables.find(
        (item) => item.tableName === this.sqlQuery.sourceTable
      ).columns;
      let copyObj = JSON.parse(JSON.stringify(this.sqlQuery));
      console.log(copyObj.fields)
      copyObj.fields = copyObj.fields.map((item) => {
        let obj = columns.find((item2) => item2.columnName === item);
        return {
          fieldName: obj.javaField,
          columnName: obj.columnName,
          fieldComment: obj.columnComment,
          fieldType: obj.htmlType,
          tableName: copyObj.sourceTable,
        };
      });
      if (copyObj.joins.length > 0) {
        copyObj.joins.forEach((item) => {
          let columns = this.tables.find(
            (item2) =>
              item.tableAlias == item2.tableName ||
              item.originTable == item2.tableName
          ).columns;
          item.fields = item.fields.map((e) => {
            let obj = columns.find((item2) => item2.columnName === e);
            return {
              fieldName:
                item.tableAlias == item.originTable
                  ? obj.javaField
                  : obj.javaField + item.tableAlias.split("_").at(-1),
              columnName: obj.columnName,
              fieldComment: obj.columnComment,
              fieldType: obj.htmlType,
              tableName: item.tableAlias,
            };
          });
        });
      }
      return copyObj;
    },
    confirm() {
      if (!this.sqlQuery.sourceTable) {
        this.$message.warning("请选择主表");
        return;
      }

      jsonToSql(this.handlerJson(this.sqlQuery)).then((res) => {
        let fun = () => {
          let arr = [];
          res.data.fieldsList.forEach((item, index) => {
            arr.push({
              id: +new Date().getTime() + index,
              label: item.fieldComment,
              prop: item.columnName,
              fix: "",
              align: "",
              width: "",
              icon: "",
              code: ``,
              sortable: false,
              overflow: false,
              link: false,
              tag: false,
              editable: true,
              editType: "input",
              total: 0,
              isEdit: false,
              other: "",
              selectConfig: {
                api: "",
                onSuccessCallback: "",
                onFailCallback: "",
                selectLabel: "label",
                selectValue: "value",
                options: {
                  optionItems: [
                    {
                      label: "select 1",
                      value: "1",
                    },
                    {
                      label: "select 2",
                      value: "2",
                    },
                    {
                      label: "select 3",
                      value: "3",
                    },
                  ],
                },
                type: "select",
                allowCreate: false,
                multiple: false,
              },
            });
          });

          this.$set(this.optionModel, "cols", arr);
        };
        if (this.optionModel.cols.length == 0) {
          fun();
        } else {
          this.$confirm("是否要替换当前列配置？", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          })
            .then(() => {
              fun();
            })
            .catch(() => { });
        }
        this.$set(this.optionModel, "fieldsList", res.data.fieldsList);
      });

      this.$set(this.optionModel, "sqlJson", this.handlerJson(this.sqlQuery));
      this.showDataSource = false;
    },
    addExpression() {
      if (!this.expressionObj.method) {
        this.$message.warning("请选择函数");
        return;
      }
      if (this.expressionObj.method !== "COUNT" && !this.expressionObj.field) {
        this.$message.warning("请选择字段");
        return;
      }
      this.sqlQuery.expression.push(this.expressionObj);
      this.expressionObj = {
        field: "",
        method: null,
      };
    },
    addFliter() {
      if (this.filterObj.type == "field") {
        if (!this.filterObj.field) {
          this.$message.warning("请选择字段");
          return;
        }
        if (!this.filterObj.operator) {
          this.$message.warning("请选择关系");
          return;
        }
        if (
          this.filterObj.valueType == "normal" &&
          this.filterObj.operator !== "IS NULL" &&
          this.filterObj.operator !== "IS NOT NULL" &&
          !this.filterObj.value
        ) {
          this.$message.warning("请输入值");
          return;
        }
        if (
          this.filterObj.valueType == "variable" &&
          !this.filterObj.widgetId
        ) {
          this.$message.warning("请选择组件");
          return;
        }
      } else {
        if (!this.filterObj.value) {
          this.$message.warning("请选择过滤项");
        }
      }

      this.fieldTree.forEach((item) => [
        item.children.forEach((item2) => {
          if (item2.value == this.filterObj.field) {
            this.filterObj.fieldType = item2.fieldType;
          }
        }),
      ]);
      var label = "";

      this.fieldTree.forEach((item) => {
        item.children.forEach((e) => {
          if (e.value == this.filterObj.field) {
            label += item.label + "-" + e.label;
          }
        });
      });

      this.filterObj.label = label;
      this.sqlQuery.filters.push(this.filterObj);

      this.filterObj = {
        field: "",
        operator: null,
        label: "",
        value: "",
        valueType: "variable",
        fieldType: "",
        widgetId: null,
        type: "field",
      };
    },
    addJoin() {
      this.sqlQuery.joins.push({
        field: null,
        joinField: null,
        tableAlias: null,
        method: "left",
        fields: [],
      });
    },
    getFields(tableName) {
      let table = this.tables.filter((e) => e.tableName == tableName);
      if (table != null && table.length > 0) {
        return table[0].columns;
      }
      return [];
    },
    handleCheckAllChange(val) {
      this.sqlQuery.fields = val
        ? this.mainCols.map((item) => item.columnName)
        : [];
    },
    handleCheckedCitiesChange(val) {
      this.checkAll = val.length === this.mainCols.length;
    },
    getSql() {
      let json = this.handlerJson(this.sqlQuery);
      jsonToSql(json).then((res) => {
        console.log(res)
        this.sqlStr = sqlFormatter.format(res.data.sql);
        this.sqlVisble = true;
      });
    },
    deleteRow(name) {
      this.showMap[name] = false;
      if (name == "pageSize") {
        this.sqlQuery[name] = null;
      } else {
        this.sqlQuery[name] = [];
      }
    },
    deleteJoin(index) {
      this.sqlQuery.joins.splice(index, 1);
    },
    addOrder() {
      if (!this.orderObj.field) {
        this.$message.warning("请选择字段");
        return;
      }
      if (
        this.sqlQuery.orders.some((item) => item.field == this.orderObj.field)
      ) {
        this.$message.warning("已经存在该字段");
        return;
      }
      var label = "";

      this.fieldTree.forEach((item) => {
        item.children.forEach((e) => {
          if (e.value == this.orderObj.field) {
            label += item.label + "-" + e.label;
          }
        });
      });

      this.orderObj.label = label;
      this.sqlQuery.orders.push(this.orderObj);
      this.orderObj = {
        field: "",
        order: "ASC",
        label: "",
      };
    },
  },
};
</script>

<style lang="scss" scoped>
.btn-bar {
  border-top: 1px solid #d9d9d9;
  padding-top: 10px;
}
::v-deep .check-box {
  display: block;
  margin: 5px 0;
}
::v-deep .CodeMirror{
  height: auto;
}
</style>
