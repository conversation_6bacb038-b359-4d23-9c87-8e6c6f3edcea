<template>
  <el-popover placement="bottom" title="" width="350" trigger="hover">
    <el-checkbox v-model="checkAll" @change="handleCheckAllChange"
      >全选</el-checkbox
    >
    <div class="mr-t5 mr-b5" style="border-top: 1px solid #d9d9d9"></div>
    <el-checkbox-group v-model="fields" @change="handleCheckedChange">
      <el-checkbox
        v-for="item in fieldArr"
        :key="item.columnName"
        :label="item.columnName"
        >{{ `${item.columnComment}(${item.columnName})` }}</el-checkbox
      >
    </el-checkbox-group>
    <el-button slot="reference" type="primary"
      >字段<i class="el-icon-caret-bottom mr-l5"></i
    ></el-button>
  </el-popover>
</template>

<script>
export default {
  name: "fieldSelect",
  props: {
    value: {
      type: Array,
      default: () => [],
    },
    fieldArr: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      fields: [],
      checkAll: true,
    };
  },
  watch:{
    value:{
        handler(val){
            this.fields = val;
            this.checkAll = (val.length === this.fieldArr.length) && val.length > 0
        },
        deep:true,
        immediate:true
    },
    // fieldArr:{
    //     handler(val){
    //         this.fields=[]
    //     },
    //     deep:true
    // }
  },
  methods: {
    handleCheckAllChange(val) {
      this.$emit(
        "input",
        val ? this.fieldArr.map((item) => item.columnName) : []
      );
    },
    handleCheckedChange(val) {
      this.checkAll = (val.length === this.fieldArr.length)&&val.length>0;
      this.$emit("input", val);
    },
  },
};
</script>

<style>
</style>