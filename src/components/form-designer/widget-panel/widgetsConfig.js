import store from '@/store';

export const containers = [
  {
    type: 'grid',
    category: 'container',
    icon: 'grid',
    cols: [],
    options: {
      name: '',
      label: '',
      margin: {
        mt: '',
        mb: '',
        ml: '',
        mr: '',
      },
      padding: {
        pt: '',
        pb: '',
        pl: '',
        pr: '',
      },
      border2: {
        width: '',
        type: 'solid',
        color: '',
      },
      authority: false,
      hidden: false,
      tableShow: false,
      tableOrder: false,
      gutter: 12,
      customClass: '', // 自定义css类名
    },
  },
  {
    type: 'dialog',
    icon: 'text-field',
    category: 'container',
    widgetList: [],
    options: {
      name: '',
      label: '弹出层',
      mark: '',
      dialogWidth: 60,
      dialogWidthPad: 100,
      closeOnClickModal: false,
      destroyOnClose: false,
      fullscreen: false,
      showClose: true,
      isCustom: false,
      leftBtns: [],
      hasCloseTips: false,
      isDrag: false,
      hasCloseTipsContent: '确认关闭？',
      // -------------------
      customClass: '', // 自定义css类名
      // -------------------
      onCreated: '',
      onDialogClose: '',
    },
  },
  {
    type: 'card',
    category: 'container',
    icon: 'card',
    widgetList: [],
    options: {
      name: '',
      label: '卡片',
      authority: false,
      hidden: false,
      tableShow: false,
      tableOrder: false,
      tableShowWeight: 0,
      folded: false,
      showFold: true,
      cardWidth: '100%',
      shadow: 'never',
      subtitle: '',
      customClass: '',
      onMounted: '',
    },
  },
  {
    type: 'condition-container',
    category: 'container',
    icon: 'grid',
    widgetList: [],
    options: {
      name: '',
      hidden: false,
      customClass: '',
      onSearchClick: '',
      onResetClick: '',
    },
  },
  {
    type: 'tab',
    category: 'container',
    icon: 'tab',
    // displayType: 'border-card',
    tabs: [],
    options: {
      name: '',
      label: '',
      authority: false,
      hidden: false,
      icon: '',
      tabType: 'border-card',
      tabPosition: 'top',
      stretch: false,
      tabChange: '',
      customClass: '', // 自定义css类名
    },
  },

  {
    type: 'condition',
    category: 'container',
    icon: 'grid',
    widgetList: [],
    options: {
      name: '',
      hidden: false,
      customClass: '',
      onQuery: '',
    },
  },
  {
    type: 'custom-condition',
    icon: 'select-field',
    formItemFlag: true,
    options: {
      name: '',
      alias: '',
      authority: false,
      api: '',
      isPolling: false,
      pollTimes: 10,
      sqlJson: {
        sourceTable: '',
        fields: [],
        orders: [],
        joins: [],
        filters: [],
        expression: [],
        group: [],
        pageSize: null,
        pageNum: 1,
      },
      configData: false,
      paramConfig: '',
      autoLoadData: true,
      hidden: false,
      optionItems: [
        { label: 'select 1', value: 1 },
        { label: 'select 2', value: 2 },
        { label: 'select 3', value: 3 },
      ],
      selectValue: 'value',
      selectLabel: 'label',
      onCreated: '',
      onMounted: '',
    },
  },
  {
    type: 'drawer',
    icon: 'text-field',
    category: 'container',
    widgetList: [],
    options: {
      name: '',
      label: '抽屉',
      hidden: true,
      dialogWidth: 60,
      destroyOnClose: false,
      drawerDirection: 'ltr',
      customClass: '', // 自定义css类名
      onCreated: '',
      onDialogClose: '',
    },
  },
  {
    type: 'collapse',
    category: 'container',
    icon: 'grid',
    items: [],
    options: {
      name: '',
      label: '',
      hidden: false,
      accordion: false,
      collapse: '',
      customClass: '', // 自定义css类名
    },
  },
  {
    type: 'table',
    category: 'container',
    icon: 'table',
    rows: [],
    options: {
      name: '',
      label: '',
      authority: false,
      hidden: false,
      tableShow: false,
      tableOrder: false,
      customClass: '', // 自定义css类名
    },
  },
  {
    type: 'iteration',
    icon: 'text-field',
    category: 'container',
    widgetList: [],
    options: {
      name: '',
      onMounted: '',
      hidden: false,
      customClass: '',
    },
  },

  {
    type: 'sub-form',
    category: 'container',
    icon: 'sub-form',
    widgetList: [],
    options: {
      name: '',
      label: '',
      bandKey: '',
      alias: '',
      authority: false,
      samePage: true,
      groupPage: false,
      showBlankRow: true,
      showRowNumber: true,
      labelAlign: 'label-center-align',
      required: false,
      readonly: false,
      hasRole: true,
      addAble: false,
      deleteAble: false,
      rowEdit: false,
      validationHint: '',
      subFormCols: [],
      hidden: false,
      tableShow: false,
      tableOrder: false,
      customClass: [],
      onSubFormRowAdd: '',
      onSubFormRowInsert: '',
      onSubFormRowDelete: '',
      onSubFormRowChange: '',
      onSubSave: '',
    },
  },
  {
    type: 'grid-col',
    category: 'container',
    icon: 'grid-col',
    internal: true,
    widgetList: [],
    options: {
      name: '',
      label: '',
      authority: false,
      hidden: false,
      tableShow: false,
      tableOrder: false,
      span: 12,
      offset: 0,
      push: 0,
      pull: 0,
      border2: {
        width: '',
        type: 'solid',
        color: '',
      },
      responsive: false, // 是否开启响应式布局
      md: 12,
      sm: 12,
      xs: 12,
      customClass: '', // 自定义css类名
      margin: {
        mt: '',
        mb: '',
        ml: '',
        mr: '',
      },
      padding: {
        pt: '10',
        pb: '10',
        pl: '5',
        pr: '5',
      },
    },
  },
  {
    type: 'table-cell',
    category: 'container',
    icon: 'table-cell',
    internal: true,
    widgetList: [],
    merged: false,
    options: {
      name: '',
      label: '',
      authority: false,
      cellWidth: '',
      cellHeight: '',
      colspan: 1,
      rowspan: 1,
      tableShow: false,
      tableOrder: false,
      customClass: '', // 自定义css类名
    },
  },

  {
    type: 'tab-pane',
    category: 'container',
    icon: 'tab-pane',
    internal: true,
    widgetList: [],
    options: {
      name: '',
      label: '',
      authority: false,
      hidden: false,
      tableShow: false,
      tableOrder: false,
      active: false,
      disabled: false,
      customClass: '', // 自定义css类名
    },
  },

  // {
  //   type: "trends-tab",
  //   category: "container",
  //   icon: "tab",
  //   // displayType: 'border-card',
  //   widgetList: [],
  //   options: {
  //     name: "",
  //     label: "动态选项卡",
  //     tabType: "border-card",

  //     authority: false,
  //     // required: false,
  //     // validationHint: '',
  //     hidden: false,
  //     readonly: false,
  //     icon: "",
  //     // tableShow: false,
  //     // tableOrder: false,
  //     // tableShowWeight: 0,
  //     tabPosition: "top",
  //     stretch: false,
  //     customClass: "", // 自定义css类名
  //   },
  // },
  {
    type: 'dialog-body',
    icon: 'text-field',
    category: 'container',
    internal: true,
    widgetList: [],
    options: {
      name: '',
      label: '',
      labelAlign: 'label-center-align',
      type: 'text',

      size: '',
      hidden: false,
      // -------------------
      customClass: '', // 自定义css类名
      // -------------------
      onCreated: '',
      onMounted: '',
    },
  },
  {
    type: 'dialog-footer',
    icon: 'text-field',
    category: 'container',
    internal: true,
    widgetList: [],
    options: {
      name: '',
      label: '',
      labelAlign: 'label-center-align',
      type: 'text',

      size: '',
      hidden: false,
      // -------------------
      customClass: '', // 自定义css类名
      // -------------------
      onCreated: '',
      onMounted: '',
    },
  },
  {
    type: 'condition-container-footer',
    icon: 'text-field',
    category: 'container',
    internal: true,
    widgetList: [],
    options: {
      name: '',
      label: '',
      labelAlign: 'label-center-align',
      type: 'text',

      size: '',
      hidden: false,
      // -------------------
      customClass: '', // 自定义css类名
      // -------------------
      onCreated: '',
      onMounted: '',
    },
  },
  {
    type: 'condition-container-body',
    icon: 'text-field',
    category: 'container',
    internal: true,
    widgetList: [],
    options: {
      name: '',
      label: '',
      labelAlign: 'label-center-align',
      type: 'text',

      size: '',
      hidden: false,
      // -------------------
      customClass: '', // 自定义css类名
      // -------------------
      onCreated: '',
      onMounted: '',
    },
  },
  {
    type: 'collapse-item',
    category: 'container',
    icon: 'grid-col',
    internal: true,
    widgetList: [],
    options: {
      name: '',
      label: '',
      hidden: false,
      tableShow: false,
      tableOrder: false,
      customClass: '', // 自定义css类名
    },
  },
  {
    type: 'layout',
    icon: 'text-field',
    category: 'container',
    widgetList: [],
    options: {
      name: '',
      label: '布局容器',
      layoutModel: 1,
      customClass: '', // 自定义css类名
    },
  },
  {
    type: 'layout-header',
    icon: 'text-field',
    category: 'container',
    internal: true,
    widgetList: [],
    options: {
      border: true,
    },
  },
  {
    type: 'layout-left',
    icon: 'text-field',
    category: 'container',
    internal: true,
    widgetList: [],
    options: {
      width: 200,
      shrink: true,
      border: true,
    },
  },
  {
    type: 'layout-body',
    icon: 'text-field',
    category: 'container',
    internal: true,
    widgetList: [],
    options: {
      border: false,
    },
  },
];

export const basicFields = [
  {
    type: 'input',
    icon: 'text-field',
    formItemFlag: true,
    options: {
      name: '',
      label: '输入框',
      value: '',
      alias: '',
      labelAlign: 'label-center-align',
      type: 'text',
      authority: false,
      bandKey: '',
      prepend: '',
      append: '',
      indexes: '',
      defaultValue: '',
      placeholder: '',
      size: '',
      color: '',
      labelColor: '',
      labelWidth: 100,
      labelHidden: false,
      readonly: false,
      disabled: false,
      hidden: false,
      tableShow: false,
      tableOrder: false,
      tableShowWeight: 0,
      clearable: true,
      showPassword: false,
      required: false,
      validation: '',
      validationHint: '',
      clearEndZero: true,
      // -------------------
      customClass: '', // 自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      minLength: null,
      maxLength: null,
      showWordLimit: false,
      prefixIcon: '',
      suffixIcon: '',
      query: true,
      // -------------------
      onCreated: '',
      onMounted: '',
      onInput: '',
      onChange: '',
      onFocus: '',
      onBlur: '',
      onValidate: '',
      onPrependClick: '',
      onAppendClick: '',
      onEnter: '',
    },
  },
  {
    type: 'select',
    icon: 'select-field',
    formItemFlag: true,
    options: {
      name: '',
      label: '下拉选择框',
      alias: '',
      bandKey: '',
      authority: false,
      api: '',
      isPolling: false,
      pollTimes: 10,
      sqlJson: {
        sourceTable: '',
        fields: [],
        orders: [],
        joins: [],
        filters: [],
        expression: [],
        group: [],
        pageSize: null,
        pageNum: 1,
      },
      configData: false,
      paramConfig: '',
      autoLoadData: true,
      labelAlign: 'label-center-align',
      color: '',
      labelColor: '',
      value: '',
      placeholder: '',
      size: '',
      labelWidth: 100,
      labelHidden: false,
      disabled: false,
      hidden: false,
      tableShow: false,
      tableOrder: false,
      tableShowWeight: 0,
      clearable: true,
      filterable: true,
      collapseTag: false,
      allowCreate: false,
      suffixIcon:'',
      remote: false,
      automaticDropdown: false, // 自动下拉
      multiple: false,
      multipleLimit: 0,
      optionItems: [
        { label: 'select 1', value: 1 },
        { label: 'select 2', value: 2 },
        { label: 'select 3', value: 3 },
      ],
      selectValue: 'value',
      selectLabel: 'label',
      required: false,
      validation: '',
      validationHint: '',
      // -------------------
      customClass: '', // 自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      query: true,
      // -------------------
      onCreated: '',
      onMounted: '',
      onRemoteQuery: '',
      onChange: '',
      allowCreateFn: '',
      onFocus: '',
      onBlur: '',
      onValidate: '',
      bandDataIdsApi: '',
      optionsDict: '',
      optionsModel: '',
      modelLabel: '',
      modelValue: '',
      onAppendClick:''
    },
  },
  {
    type: 'order-input',
    icon: 'text-field',
    formItemFlag: true,
    options: {
      name: '',
      label: '编码生成组件',
      alias: '',
      labelAlign: 'label-center-align',
      type: 'text',
      bandKey: '',
      authority: false,
      defaultValue: '',
      placeholder: '',
      ruleDefinition: [
        {
          context: '4 位数',
          placeholder: '自动计数',
          isDel: false,
          ruleType: 4,
          numberLength: 4,
          openLength: true,
          resetCycle: 4,
          initialValue: 1,
        },
      ],
      size: '',
      labelWidth: 100,
      labelHidden: false,
      hidden: false,
      tableShow: false,
      tableOrder: false,
      tableShowWeight: 0,
      clearable: true,
      showPassword: false,
      required: false,
      validation: '',
      validationHint: '',
      // -------------------
      customClass: '', // 自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      minLength: null,
      maxLength: null,
      showWordLimit: false,
      prefixIcon: '',
      suffixIcon: '',
      appendButton: false,
      appendButtonDisabled: false,
      query: true,
      // -------------------
      onCreated: '',
      onMounted: '',
      onInput: '',
      onChange: '',
      onFocus: '',
      onBlur: '',
      onValidate: '',
    },
  },
  {
    type: 'textarea',
    icon: 'textarea-field',
    formItemFlag: true,
    options: {
      name: '',
      label: '多行输入框',
      alias: '',
      bandKey: '',
      value: '',
      authority: false,
      // indexes: '',
      labelAlign: 'label-center-align',
      rows: 3,
      defaultValue: '',
      placeholder: '',
      showReply: false,
      defaultReply: ['同意', '驳回', '已核实'],
      custom: false,
      size: '',
      labelWidth: 100,
      labelHidden: false,
      readonly: false,
      disabled: false,
      hidden: false,
      tableShow: false,
      tableOrder: false,
      tableShowWeight: 0,
      required: false,
      validation: '',
      validationHint: '',
      // -------------------
      customClass: '', // 自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      minLength: null,
      maxLength: null,
      showWordLimit: false,
      query: true,
      // -------------------
      onCreated: '',
      onMounted: '',
      onInput: '',
      onChange: '',
      onFocus: '',
      onBlur: '',
      onValidate: '',
    },
  },

  {
    type: 'number',
    icon: 'number-field',
    formItemFlag: true,
    options: {
      name: '',
      label: '数字框',
      alias: '',
      bandKey: '',
      authority: false,
      labelAlign: 'label-center-align',
      value: '',
      defaultValue: 0,
      placeholder: '',
      size: '',
      labelWidth: 100,
      labelHidden: false,
      disabled: false,
      readonly: false,
      hidden: false,
      tableShow: false,
      tableOrder: false,
      tableShowWeight: 0,
      required: false,
      validationHint: '',
      // -------------------
      customClass: '', // 自定义css类名
      min: -100000000000,
      max: 100000000000,
      precision: 5,
      maxLength: 15,
      step: 1,
      controlsPosition: 'right',
      // -------------------
      onCreated: '',
      onMounted: '',
      onInput: '',
      onChange: '',
      onFocus: '',
      onBlur: '',
      onValidate: '',
    },
  },

  {
    type: 'radio',
    icon: 'radio-field',
    formItemFlag: true,
    options: {
      name: '',
      label: '单选框',
      alias: '',
      bandKey: '',
      authority: false,
      labelAlign: 'label-center-align',
      defaultValue: null,
      size: '',
      displayStyle: 'inline',
      buttonStyle: false,
      border: false,
      labelWidth: 100,
      labelHidden: false,
      disabled: false,
      hidden: false,
      tableShow: false,
      tableOrder: false,
      tableShowWeight: 0,
      optionItems: [
        { value: '1', label: 'new option' },
        { value: '2', label: 'new option' },
      ],
      required: false,
      validation: '',
      validationHint: '',
      paramConfig: '',
      autoLoadData: true,
      // -------------------
      customClass: '', // 自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      // -------------------
      onCreated: '',
      onMounted: '',
      onChange: '',
      onValidate: '',
      api: '',
      isPolling: false,
      pollTimes: 10,
      sqlJson: {
        sourceTable: '',
        fields: [],
        orders: [],
        joins: [],
        filters: [],
        expression: [],
        group: [],
        pageSize: null,
        pageNum: 1,
      },
      configData: false,
      optionsModel: '',
      optionsDict: '',
      modelLabel: '',
      modelValue: '',
    },
  },

  {
    type: 'checkbox',
    icon: 'checkbox-field',
    formItemFlag: true,
    options: {
      name: '',
      label: '复选框',
      alias: '',
      bandKey: '',
      authority: false,
      showCheckAll: false,
      labelAlign: 'label-center-align',
      defaultValue: [],
      paramConfig: '',
      autoLoadData: true,
      size: '',
      displayStyle: 'inline',
      buttonStyle: false,
      border: false,
      labelWidth: 100,
      labelHidden: false,
      disabled: false,
      hidden: false,
      tableShow: false,
      tableOrder: false,
      tableShowWeight: 0,
      optionItems: [
        { value: '1', label: 'new option' },
        { value: '2', label: 'new option' },
      ],
      required: false,
      validation: '',
      validationHint: '',
      // -------------------
      customClass: '', // 自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      // -------------------
      onCreated: '',
      onMounted: '',
      onChange: '',
      onValidate: '',
      api: '',
      isPolling: false,
      pollTimes: 10,
      sqlJson: {
        sourceTable: '',
        fields: [],
        orders: [],
        joins: [],
        filters: [],
        expression: [],
        group: [],
        pageSize: null,
        pageNum: 1,
      },
      configData: false,
      optionsDict: '',
      optionsModel: '',
      modelLabel: '',
      modelValue: '',
    },
  },

  {
    type: 'tree',
    icon: 'select-field',
    formItemFlag: true,
    options: {
      name: '',
      label: '树下拉框',
      alias: '',
      authority: false,
      labelAlign: 'label-center-align',
      defaultValue: null,
      placeholder: '请选择数据',
      selectValue: 'id',
      selectLabel: 'label',
      size: '',
      labelWidth: 100,
      labelHidden: false,
      disabled: false,
      hidden: false,
      tableShow: false,
      tableOrder: false,
      tableShowWeight: 0,

      clearable: true,
      filterable: true,
      // allowCreate: false,
      // remote: false,
      // automaticDropdown: false, // 自动下拉
      multiple: false,
      // multipleLimit: 0,
      optionItems: [
        { label: 'select 1', value: 1 },
        { label: 'select 2', value: 2 },
        { label: 'select 3', value: 3 },
      ],
      required: false,
      validation: '',
      validationHint: '',
      // -------------------
      customClass: '', // 自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      // -------------------
      onCreated: '',
      onMounted: '',
      onRemoteQuery: '',
      onChange: '',
      onFocus: '',
      onBlur: '',
      onValidate: '',
      api: '',
      isPolling: false,
      pollTimes: 10,
      sqlJson: {
        sourceTable: '',
        fields: [],
        orders: [],
        joins: [],
        filters: [],
        expression: [],
        group: [],
        pageSize: null,
        pageNum: 1,
      },
      configData: false,
      autoLoadData: true,
      paramConfig: '',
      // bandDataIdsApi: '',
      // bandKey: '',
      selectTreeFlat: false,
      disableBranchNodes: false,
    },
  },
  {
    type: 'picture-upload',
    icon: 'picture-upload-field',
    formItemFlag: true,
    options: {
      name: '',
      label: '图片上传',
      alias: '',
      // bandKey: '',
      authority: false,
      labelWidth: 100,
      labelHidden: false,
      disabled: false,
      hidden: false,
      defaultValue: null,
      labelAlign: 'label-center-align',
      tableShow: false,
      tableOrder: false,
      tableShowWeight: 0,
      required: false,
      requiredHint: '',
      customRule: '',
      customRuleHint: '',
      onlyCamera:false,
      // -------------------
      uploadTip: '',
      multipleSelect: false,
      showFileList: true,
      limit: 3,
      fileMaxSize: 5, // MB
      fileTypes: ['jpeg', 'png'],
      // headers: [],
      // -------------------
      customClass: '', // 自定义css类名
      // -------------------
      onCreated: '',
      onMounted: '',
      onBeforeUpload: '',
      onUploadSuccess: '',
      onUploadError: '',
      onValidate: '',
      // onFileChange: '',
    },
  },
  {
    type: 'cascader',
    icon: 'cascader-field',
    formItemFlag: true,
    options: {
      name: '',
      label: '级联选择框',
      alias: '',
      bandKey: '',
      query: true,
      authority: false,
      labelAlign: 'label-center-align',
      defaultValue: '',
      placeholder: '',
      size: '',
      labelWidth: 100,
      labelHidden: false,
      disabled: false,
      hidden: false,
      tableShow: false,
      showAllLevels: true,
      clearable: true,
      filterable: false,
      collapseTag: false,
      api: '',
      isPolling: false,
      pollTimes: 10,
      sqlJson: {
        sourceTable: '',
        fields: [],
        orders: [],
        joins: [],
        filters: [],
        expression: [],
        group: [],
        pageSize: null,
        pageNum: 1,
      },
      configData: false,
      multiple: false,
      checkStrictly: false,
      paramConfig: '',
      autoLoadData: true,
      selectValue: 'value',
      selectLabel: 'label',
      selectChildren: 'children',
      optionItems: [
        {
          label: 'select 1',
          value: 1,
          children: [{ label: 'child 1', value: 11 }],
        },
        { label: 'select 2', value: 2 },
        { label: 'select 3', value: 3 },
      ],
      required: false,
      requiredHint: '',
      validationHint: '',
      customRule: '',
      customRuleHint: '',
      // -------------------
      customClass: '', // 自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      // -------------------
      onCreated: '',
      onMounted: '',
      onChange: '',
      onFocus: '',
      onBlur: '',
      onValidate: '',
      optionsDict: '',
      optionsModel: '',
      modelLabel: '',
      modelValue: '',
    },
  },
  {
    type: 'file-upload',
    icon: 'file-upload-field',
    formItemFlag: true,
    options: {
      name: '',
      label: '文件上传',
      alias: '',
      authority: false,
      bandKey: '',
      labelAlign: 'label-center-align',
      labelWidth: 100,
      labelHidden: false,
      disabled: false,
      readonly: false,
      hidden: false,
      showDown: true,
      showDel: true,
      fileUploadType: 'drag',
      fileUploadButtonLabel: '选取文件',
      tableShow: false,
      tableOrder: false,
      tableShowWeight: 0,
      required: false,
      requiredHint: '',
      customRule: '',
      customRuleHint: '',
      uploadURL: '',
      // -------------------
      isShowTip: true,
      multipleSelect: false,
      showFileList: true,
      limit: 3,
      fileMaxSize: 5, // MB
      fileTypes: ['doc', 'docx', 'xls', 'xlsx'],

      // -------------------
      customClass: '', // 自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      // -------------------
      onCreated: '',
      onMounted: '',
      onBeforeUpload: '',
      onUploadSuccess: '',
      onUploadError: '',
      onValidate: '',
      // onFileChange: '',
    },
  },
  // {
  //   type: 'select-page',
  //   icon: 'select-field',
  //   formItemFlag: true,
  //   options: {
  //     name: '',
  //     label: '分页下拉框',
  //     alias: '',
  //     bandKey: '',
  //     authority: false,
  //     labelAlign: 'label-center-align',
  //     defaultValue: '',
  //     placeholder: '',
  //     size: '',
  //     labelWidth: 100,
  //     dialogtableShow: false,
  //     tableOrder: false,
  //     labelHidden: false,
  //     disabled: false,
  //     hidden: false,
  //     tableShow: false,
  //     clearable: true,
  //     multiple: false,
  //     isUser: false,
  //     optionItems: [
  //       { label: 'select 1', value: '1' },
  //       { label: 'select 2', value: '2' },
  //       { label: 'select 3', value: '3' },
  //     ],
  //     required: false,
  //     validation: '',
  //     validationHint: '',
  //     // -------------------
  //     customClass: '', // 自定义css类名
  //     labelIconClass: null,
  //     labelIconPosition: 'rear',
  //     labelTooltip: null,
  //     // -------------------
  //     onCreated: '',
  //     onMounted: '',
  //     onRemoteQuery: '',
  //     onChange: '',
  //     onFocus: '',
  //     onBlur: '',
  //     onValidate: '',
  //     api: '',
  //     isPolling: false,
  //     pollTimes: 10,
  //     sqlJson: {
  //       sourceTable: '',
  //       fields: [],
  //       orders: [],
  //       joins: [],
  //       filters: [],
  //       expression: [],
  //       group: [],
  //       pageSize: null,
  //       pageNum: 1,
  //     },
  //     configData: false,
  //     autoLoadData: true,
  //     paramConfig: '',
  //     // bandDataIdsApi: '',
  //   },
  // },
  {
    type: 'time',
    icon: 'time-field',
    formItemFlag: true,
    options: {
      name: '',
      label: '时间选择框',
      alias: '',
      bandKey: '',
      authority: false,
      labelAlign: 'label-center-align',
      defaultValue: null,
      placeholder: '',
      query: true,
      size: '',
      value: '',
      labelWidth: 100,
      labelHidden: false,
      readonly: false,
      disabled: false,
      hidden: false,
      tableShow: false,
      tableOrder: false,
      tableShowWeight: 0,
      clearable: true,
      editable: false,
      format: 'HH:mm:ss', // 时间格式
      required: false,
      validation: '',
      validationHint: '',
      // -------------------
      customClass: '', // 自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      // -------------------
      onCreated: '',
      onMounted: '',
      onChange: '',
      onFocus: '',
      onBlur: '',
      onValidate: '',
    },
  },

  {
    type: 'time-range',
    icon: 'time-range-field',
    formItemFlag: true,
    options: {
      name: '',
      label: '时间范围选择框',
      alias: '',
      bandKey: '',
      authority: false,
      labelAlign: 'label-center-align',
      defaultValue: null,
      startPlaceholder: '',
      endPlaceholder: '',
      query: true,
      size: '',
      value: '',
      labelWidth: 100,
      labelHidden: false,
      readonly: false,
      disabled: false,
      hidden: false,
      tableShow: false,
      tableOrder: false,
      tableShowWeight: 0,
      clearable: true,
      editable: false,
      format: 'HH:mm:ss', // 时间格式
      required: false,
      validation: '',
      validationHint: '',
      // -------------------
      customClass: '', // 自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      // -------------------
      onCreated: '',
      onMounted: '',
      onChange: '',
      onFocus: '',
      onBlur: '',
      onValidate: '',
    },
  },

  {
    type: 'date',
    icon: 'date-field',
    formItemFlag: true,
    options: {
      name: '',
      label: '日期选择框',
      alias: '',
      bandKey: '',
      authority: false,
      labelAlign: 'label-center-align',
      type: 'date',
      defaultValue: null,
      defaultCurrent: false,
      query: true,
      placeholder: '',
      size: '',
      value: '',
      labelWidth: 100,
      labelHidden: false,
      readonly: false,
      disabled: false,
      hidden: false,
      tableShow: false,
      tableOrder: false,
      tableShowWeight: 0,
      clearable: true,
      format: 'yyyy-MM-dd HH:mm:ss', // 日期显示格式
      valueFormat: 'yyyy-MM-dd', // 日期对象格式
      required: false,
      validation: '',
      validationHint: '',
      // -------------------
      customClass: '', // 自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      // -------------------
      onCreated: '',
      onMounted: '',
      onChange: '',
      onFocus: '',
      onBlur: '',
      onValidate: '',
      onDisabledDate: '',
    },
  },
  {
    type: 'date-time',
    icon: 'date-field',
    formItemFlag: true,
    options: {
      name: '',
      label: '日期时间选择框',
      alias: '',
      bandKey: '',
      authority: false,
      labelAlign: 'label-center-align',
      type: 'datetime',
      defaultValue: '',
      placeholder: '',
      query: true,
      size: '',
      value: '',
      labelWidth: 100,
      labelHidden: false,
      defaultTime: false,
      autoRefresh: false,
      // readonly: true,
      disabled: false,
      hidden: false,
      tableShow: false,
      tableOrder: false,
      tableShowWeight: 0,
      required: false,
      validationHint: '',
      // -------------------
      customClass: '', // 自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      // -------------------
      onCreated: '',
      onMounted: '',
      onChange: '',
      onFocus: '',
      onBlur: '',
      onValidate: '',
    },
  },
  {
    type: 'date-range',
    icon: 'date-range-field',
    formItemFlag: true,
    options: {
      name: '',
      label: '日期范围选择框',
      alias: '',
      bandKey: '',
      authority: false,
      labelAlign: 'label-center-align',
      type: 'daterange',
      // defaultValue: null,
      startPlaceholder: '',
      endPlaceholder: '',
      shortcuts: true,
      query: true,
      size: '',
      value: '',
      labelWidth: 100,
      labelHidden: false,
      readonly: false,
      disabled: false,
      hidden: false,
      tableShow: false,
      tableOrder: false,
      tableShowWeight: 0,
      clearable: true,
      editable: false,
      format: 'yyyy-MM-dd', // 日期显示格式
      valueFormat: 'yyyy-MM-dd', // 日期对象格式
      required: false,
      validation: '',
      validationHint: '',
      // -------------------
      customClass: '', // 自定义css类名
      defaultRange: '',
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      // -------------------
      onCreated: '',
      onMounted: '',
      onChange: '',
      onFocus: '',
      onBlur: '',
      onValidate: '',
      onDisabledDate:''
    },
  },

  {
    type: 'switch',
    icon: 'switch-field',
    formItemFlag: true,
    options: {
      name: '',
      label: '开关',
      alias: '',
      bandKey: '',
      authority: false,
      labelAlign: 'label-center-align',
      defaultValue: false,

      labelWidth: 100,
      labelHidden: false,
      disabled: false,
      hidden: false,
      tableShow: false,
      tableOrder: false,
      tableShowWeight: 0,
      // -------------------
      customClass: '', // 自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      switchWidth: 40,
      activeText: '',
      inactiveText: '',
      activeColor: null,
      inactiveColor: null,
      // -------------------
      onCreated: '',
      onMounted: '',
      onChange: '',
      onValidate: '',
    },
  },

  {
    type: 'rate',
    icon: 'rate-field',
    formItemFlag: true,
    options: {
      name: '',
      label: '评分',
      alias: '',
      bandKey: '',
      authority: false,
      labelAlign: 'label-center-align',
      defaultValue: null,
      query: true,
      labelWidth: 100,
      labelHidden: false,
      disabled: false,
      hidden: false,
      tableShow: false,
      tableOrder: false,
      tableShowWeight: 0,
      required: false,
      validation: '',
      validationHint: '',
      // -------------------
      customClass: '', // 自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      max: 5,
      lowThreshold: 2,
      highThreshold: 4,
      allowHalf: false,
      showText: false,
      showScore: false,
      // -------------------
      onCreated: '',
      onMounted: '',
      onChange: '',
      onValidate: '',
    },
  },

  {
    type: 'color',
    icon: 'color-field',
    formItemFlag: true,
    options: {
      name: '',
      label: '颜色选择框',
      alias: '',
      bandKey: '',
      authority: false,
      labelAlign: 'label-center-align',
      defaultValue: null,

      size: '',
      labelWidth: 100,
      labelHidden: false,
      disabled: false,
      hidden: false,
      tableShow: false,
      tableOrder: false,
      tableShowWeight: 0,
      required: false,
      validation: '',
      validationHint: '',
      // -------------------
      customClass: '', // 自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      // -------------------
      onCreated: '',
      onMounted: '',
      onChange: '',
      onValidate: '',
    },
  },

  {
    type: 'slider',
    icon: 'slider-field',
    formItemFlag: true,
    options: {
      name: '',
      label: '滑块',
      alias: '',
      bandKey: '',
      authority: false,
      labelAlign: 'label-center-align',

      showStops: true,
      size: '',
      labelWidth: 100,
      labelHidden: false,
      disabled: false,
      hidden: false,
      tableShow: false,
      tableOrder: false,
      tableShowWeight: 0,
      required: false,
      validation: '',
      validationHint: '',
      // -------------------
      customClass: '', // 自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      min: 0,
      max: 100,
      step: 10,
      range: false,
      // vertical: false,
      height: null,
      // -------------------
      onCreated: '',
      onMounted: '',
      onChange: '',
      onValidate: '',
    },
  },

  {
    type: 'userinput',
    icon: 'text-field',
    formItemFlag: true,
    options: {
      name: '',
      label: '当前登录人',
      labelAlign: 'label-center-align',
      authority: false,
      alias: '',
      type: 'text',
      bandKey: '',
      hidden: false,
      // defaultValue: '',
      placeholder: '',
      required: false,
      size: '',
      tableShow: false,
      tableOrder: false,
      tableShowWeight: 0,
      labelWidth: 100,
      customClass: '', // 自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      minLength: null,
      maxLength: null,
      showWordLimit: false,
      prefixIcon: '',
      suffixIcon: '',
      appendButton: false,
      appendButtonDisabled: false,
      // bandDataIdsApi: '',

      onCreated: '',
      onMounted: '',
    },
  },
  {
    type: 'deptinput',
    icon: 'text-field',
    formItemFlag: true,
    options: {
      name: '',
      label: '当前部门',
      alias: '',
      labelAlign: 'label-center-align',
      type: 'text',
      authority: false,
      bandKey: '',
      tableShow: false,
      tableOrder: false,
      tableShowWeight: 0,
      // defaultValue: '',
      placeholder: '',

      size: '',
      labelWidth: 100,
      customClass: '', // 自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      minLength: null,
      maxLength: null,
      showWordLimit: false,
      prefixIcon: '',
      suffixIcon: '',
      appendButton: false,
      appendButtonDisabled: false,
      // bandDataIdsApi: '',

      onCreated: '',
      onMounted: '',
    },
  },
  {
    type: 'borad',
    icon: 'static-text',
    formItemFlag: true,
    options: {
      name: '',
      label: '写字板',
      color: '#000',
      labelHidden: false,
      bgColor: '#fff',
      writeWidth: 5,
      required: false,
      customClass: '', // 自定义css类名
      onMounted: '',
      width: 500,
      height: 250,
    },
  },

  {
    type: 'rich-editor',
    icon: 'rich-editor-field',
    formItemFlag: true,
    options: {
      name: '',
      label: '富文本',
      alias: '',
      bandKey: '',
      authority: false,
      content: '',
      placeholder: '',
      labelWidth: 100,
      labelHidden: false,
      query: true,
      disabled: false,
      hidden: false,
      labelAlign: 'label-center-align',
      tableShow: false,
      tableOrder: false,
      tableShowWeight: 0,
      required: false,
      requiredHint: '',
      customRule: '',
      customRuleHint: '',
      // -------------------
      customClass: '', // 自定义css类名
      labelIconClass: null,
      labelIconPosition: 'rear',
      labelTooltip: null,
      minLength: null,
      maxLength: null,
      showWordLimit: false,
      // -------------------
      onCreated: '',
      onMounted: '',
      onValidate: '',
    },
  },
  {
    type: 'expression',
    icon: 'text-field',
    formItemFlag: true,
    options: {
      name: '',
      label: '表达式',
      alias: '',
      labelHidden: false,
      onMounted: '',
    },
  },
];

export const viewFields = [
  {
    type: 'data-table',
    icon: 'table',
    formItemFlag: true,
    widgetList: [],
    options: {
      name: '',
      label: '',
      labelHidden: true,
      authority: false,
      bandKey: '',
      alias: '',
      // operation: false,
      data: [],
      hidden: false,
      height: '',
      isLazy: false,
      isVirtual: false,
      minHeight: '',
      minimumHeight: '200',
      api: '',
      isPolling: false,
      pollTimes: 10,
      sqlJson: {
        sourceTable: '',
        fields: [],
        orders: [],
        joins: [],
        filters: [],
        expression: [],
        group: [],
        pageSize: null,
        pageNum: 1,
      },
      configData: false,
      paramConfig: '',
      disabled: false,
      readonly: false,
      hasFrame: true,
      hasPrint: true,
      hasExpand: false,
      hasExport: true,
      mouseFlag: true,
      cols: [],
      btns: [],
      leftBtns: [],
      highlightCurrentRow: false,
      treeTable: false,
      subsetField: 'children',
      autoLoadData: true,
      hasScrollBar: true,
      showIndex: false,
      showSearch: false,
      isShowTableField: true,
      tableStripe: false,
      showSummary: false,
      allowRowSelect: false,
      reserveSelection: true,
      opeFix: true,
      pageStyle: '',
      pagerCount: 7,
      rowKey: '',
      isEdit: false,
      defaultEdit: false,
      dbClickEdit: false,
      allowCopy: false,
      onCurrentChange: '',
      customClass: '', // 自定义css类名
      onCreated: '',
      onMounted: '',
      heightFit: true,
      onClickPrint: '',
      onClickExport: '',
      onClickSearch: '',
      onCellDblclick: '',
      onExpandChange: '',
      onEditChange: '',
      onBlurChange: '',
      onFocusChange: '',
      onTableEnter: '',
      linkAble: false,
      defaultExpandAll: false,
      selectionLimit: undefined,
      hasSelection: false,
      onSelection: '',
      onRowStyle: '',
      onRowClass: '',
      onCellStyle: '',
      onSpanMethod: '',
      onCellClick: '',
      onRowClick: '',
      onSortChange: '',
      onLazy: '',
      isEnable: false,
      modelQuery: {},
      checkIsCustom: false,
      checkField: 'allowCheck',
      tableCellSelect: false,
      preOnCellClick: '',
      mutualExclusion: false,
      cellSelectBgColor: store.state.settings.theme,
      cellSelectColor: '#fff',
      // abbreviation: false,
      onPaginationChange: '',
      onCopyCallback: '',
    },
  },
  {
    type: 'button',
    icon: 'button',
    formItemFlag: true,
    options: {
      name: '',
      label: '按钮',
      alias: '',
      alreadyClickLable: '',
      bandKey: '',
      authority: false,

      size: '',
      disabled: false,
      hidden: false,
      tableShow: false,
      tableOrder: false,
      tableShowWeight: 0,
      type: '',
      plain: false,
      round: false,
      circle: false,
      icon: null,
      iconColor: undefined,
      // -------------------
      customClass: '', // 自定义css类名
      // -------------------
      onCreated: '',
      onMounted: '',
      onClick: '',
    },
  },
  {
    type: 'custom-tree',
    icon: 'text-field',
    formItemFlag: true,
    options: {
      name: '',
      hidden: false,
      placeholder: '',
      customClass: '', // 自定义css类名
      onCreated: '',
      onMounted: '',
      onLoad: '', // 加载子树数据的方法，仅当 lazy 属性为true 时生效 function(node, resolve)
      onRenderContent: '', //树节点的内容区的渲染 Function	Function(h, { node, data, store }
      onDefaultExpandedKeys: '', // 默认展开的节点数组 Function(data) return array
      onDefaultCheckedKeys: '', // 默认勾选的节点的 key 的数组 默认展开的节点数组 Function(data) return array
      onFilterNodeMethod: '', // 对树节点进行筛选时执行的方法，返回 true 表示这个节点可以显示，返回 false 则表示这个节点会被隐藏 Function(value, data, node)
      onAllowDrag: '', // 判断节点能否被拖拽  Function(node)
      onAllowDrop: '', // 拖拽时判定目标节点能否被放置。type 参数有三种情况：'prev'、'inner' 和 'next'，分别表示放置在目标节点前、插入至目标节点和放置在目标节点后 Function(draggingNode, dropNode, type)
      onNodeClick: '',
      onNodeCheck: '',
      indent: 16, //相邻级节点间的水平缩进，单位为像素
      iconClass: '', // 自定义树节点的图标
      isLazy: false,
      heightFit: true,
      api: '',
      isPolling: false,
      pollTimes: 10,
      sqlJson: {
        sourceTable: '',
        fields: [],
        orders: [],
        joins: [],
        filters: [],
        expression: [],
        group: [],
        pageSize: null,
        pageNum: 1,
      },
      configData: false,
      paramConfig: '',
      autoLoadData: true,
      hasSearch: true,
      onLazy: '',
      expandAll: false,
      highlightCurrent: false, // 是否高亮当前选中节点，默认值是 false
      defaultExpandAll: false, // 是否默认展开所有节点
      expandOnClickNode: true, //是否在点击节点的时候展开或者收缩节点， 默认值为 true，如果为 false，则只有点箭头图标的时候才会展开或者收缩节点。
      checkOnClickNode: false, // 是否在点击节点的时候选中节点，默认值为 false，即只有在点击复选框时才会选中节点
      autoExpandParent: true, // 展开子节点的时候是否自动展开父节点
      showCheckbox: false, // 节点是否可被选择
      checkStrictly: false, // 在显示复选框的情况下，是否严格的遵循父子不互相关联的做法，默认为 false
      emptyText: '暂无数据', //内容为空的时候展示的文本
      nodeKey: '', // 每个树节点用来作为唯一标识的属性，整棵树应该是唯一的
      draggable: false, //是否开启拖拽节点功能
      onScrollEnd: '',
      onNodeStyle: '',
    },
  },
  {
    type: 'spcAnalysis',
    icon: 'text-field',
    formItemFlag: true,
    options: {
      name: '',
      // -------------------
      customClass: '', // 自定义css类名
      spcConfig: {},
      // -------------------
      onCreated: '',
      onMounted: '',
      onFormChange: '',
    },
  },
  {
    type: 'static-text',
    icon: 'static-text',
    formItemFlag: true,
    options: {
      label: '静态文字',
      name: '',
      hidden: false,
      authority: false,
      tableShow: false,
      tableOrder: false,
      tableShowWeight: 0,
      textContent: 'static text',
      showType: 'text',
      textIcon: '',
      tooltip: '',
      effect: 'dark',
      placement: 'bottom',
      color: '',
      fontSize: 16,
      // -------------------
      customClass: '', // 自定义css类名
      // -------------------
      onCreated: '',
      onMounted: '',
    },
  },

  {
    type: 'html-text',
    icon: 'html-text',
    formItemFlag: false,
    options: {
      name: '',

      hidden: false,
      tableShow: false,
      authority: false,
      tableOrder: false,
      tableShowWeight: 0,
      htmlContent: '<b>html text</b>',
      // -------------------
      customClass: '', // 自定义css类名
      // -------------------
      onCreated: '',
      onMounted: '',
    },
  },

  {
    type: 'divider',
    icon: 'divider',
    formItemFlag: false,
    options: {
      name: '',
      label: '分割线',
      alias: '',
      bandKey: '',
      authority: false,

      direction: 'vertical',
      contentPosition: 'center',
      hidden: false,
      tableShow: false,
      tableOrder: false,
      tableShowWeight: 0,
      // -------------------
      customClass: '', // 自定义css类名
      // -------------------
      onCreated: '',
      onMounted: '',
    },
  },
  {
    type: 'organizational-structure',
    icon: 'select-field',
    formItemFlag: true,
    options: {
      name: '',
      label: '',
      placeholder: '请输入搜索内容',
      showNodeType: [],
      organizationalList: {},
      multiple: true,
      hidden: false,
      checkStrictly: true,
      customClass: '', // 自定义css类名
      onCreated: '',
      onMounted: '',
      onNodeClick: '',
      onNodeCheck: '',
    },
  },
  {
    type: 'gantt',
    icon: 'table',
    formItemFlag: true,
    options: {
      name: '',
      hidden: false,
      readonly: false,
      dateWidth: 80,
      minHeight: 400,
      ganttCols: [],
      onCreated: '',
      onMounted: '',
      defaultDateRange: [],
      defaultGrain: 'day',
      onTaskDblClick: '',
      onAfterTaskDrag: '',
      onGanttTooltip: '',
    },
  },
  {
    type: 'map',
    icon: 'text-field',
    formItemFlag: true,
    options: {
      name: '',
      readonly: false,
      // -------------------
      customClass: '', // 自定义css类名
      width: 300,
      height: 300,
      // -------------------
      onCreated: '',
      onMounted: '',
    },
  },
  {
    type: 'position',
    icon: 'text-field',
    formItemFlag: true,
    options: {
      name: '',
      readonly: false,
      // -------------------
      customClass: '', // 自定义css类名
      hidden: true,
      // -------------------
      onCreated: '',
      onMounted: '',
    },
  },
  {
    type: 'qrcode',
    icon: 'text-field',
    formItemFlag: true,
    options: {
      name: '',
      width: 100,
      textarea: '',
      upload: '',
      customClass: '', // 自定义css类名
      // -------------------
      onCreated: '',
      onMounted: '',
    },
  },

  {
    type: 'vfrom-quote',
    icon: 'text-field',
    formItemFlag: true,
    options: {
      name: '',
      hidden: false,
      formId: undefined,
      label: '',
      labelHidden: false,
      customClass: '', // 自定义css类名
      onCreated: '',
      onMounted: '',
    },
  },

  {
    type: 'data',
    icon: 'table',
    formItemFlag: true,
    options: {
      name: '',
      label: '',
      hidden: true,
      api: '',
      isPolling: false,
      pollTimes: 10,
      sqlJson: {
        sourceTable: '',
        fields: [],
        orders: [],
        joins: [],
        filters: [],
        expression: [],
        group: [],
        pageSize: null,
        pageNum: 1,
      },
      configData: false,
      alias: '',
      paramConfig: '',
      onCreated: '',
      onMounted: '',
      onBeforeDestroy: '',
    },
  },
  {
    type: 'carousel',
    icon: 'text-field',
    formItemFlag: true,
    options: {
      name: '',
      hidden: false,
      height: '',
      interval: 3000,
      direction: 'horizontal',
      isCard: false,
      autoplay: true,
      // -------------------
      customClass: '', // 自定义css类名
      // -------------------
      onCreated: '',
      onMounted: '',
    },
  },
  {
    type: 'timeline',
    icon: 'text-field',
    formItemFlag: true,
    options: {
      name: '',
      hidden: false,
      activities: [],
      reverse: false,
      // -------------------
      customClass: '', // 自定义css类名
      // -------------------
      onCreated: '',
      onMounted: '',
    },
  },
  {
    type: 'calendar',
    icon: 'text-field',
    formItemFlag: true,
    options: {
      name: '',
      hidden: false,
      customHtml: false,
      slot: ``,
      // -------------------
      customClass: '', // 自定义css类名
      // -------------------
      onCreated: '',
      onMounted: '',
      onChange: '',
    },
  },
  {
    type: 'barCode',
    icon: 'text-field',
    formItemFlag: true,
    options: {
      name: '',
      barCode: '1',
      onCreated: '',
      onMounted: '',
    },
  },
  {
    type: 'progress', //进度条
    icon: 'text-field',
    formItemFlag: true,
    options: {
      name: '',
      LineWidth: 126,
      progressType: 'line',
      strokeWidth: 6, //默认 6
      progressTextInside: false,
      progressStatus: null,
      progressShowText: true,
      progressTextColor: '',
      progressDefineBackColor: '',
      progressColor: '',
      onCreated: '',
      onMounted: '',
      onProgressFormat: '',
    },
  },
  {
    type: 'transfer',
    icon: 'tab',
    formItemFlag: true,
    options: {
      name: '',
      label: '',
      filterable: false,
      placeholder: '请输入搜索内容',
      selectValue: 'value',
      selectLabel: 'label',
      api: '',
      isPolling: false,
      pollTimes: 10,
      sqlJson: {
        sourceTable: '',
        fields: [],
        orders: [],
        joins: [],
        filters: [],
        expression: [],
        group: [],
        pageSize: null,
        pageNum: 1,
      },
      configData: false,
      paramConfig: '',
      autoLoadData: true,
      subtitle: '目标',
      targetOrder: 'push',
      title: '数据源',
      height: '',
      onChange: '',
      customClass: '', // 自定义css类名
      onMounted: '',
    },
  },
  {
    type: 'image',
    icon: 'picture-upload-field',
    formItemFlag: true,
    options: {
      name: '',
      alias: '',
      label: '',
      width: 100,
      height: 100,
      fit: 'fill',
      maxShow: undefined,
      showIndex: undefined,
      direction: 'horizontal',
      customClass: '', // 自定义css类名
      onMounted: '',
      onImageClick: '',
    },
  },
  {
    type: 'codeConfig',
    icon: 'static-text',
    formItemFlag: true,
    options: {
      name: '',
      label: '',
      codeManage: '',
      direction: 'horizontal',
      onMounted: '',
      codeId: null,
    },
  },
  {
    type: 'steps',
    icon: 'slider-field',
    formItemFlag: true,
    options: {
      name: '',
      label: '',
      steps: [],
      processStatus: 'process',
      finishStatus: 'process',
      direction: 'horizontal',
      alignCenter: false,
      customClass: '',
      simple: false,
      space: '',
      onMounted: '',
      onChange: '',
    },
  },
  {
    type: 'custom-steps',
    icon: 'slider-field',
    formItemFlag: true,
    options: {
      name: '',
      label: '',
      alias: '',
      customSteps: [],
      direction: 'horizontal',
      highColor: 'rgba(64, 158, 255, 1)',
      textColor: 'rgba(255, 255, 255, 1)',
      customStyle: '',
      // -- 网络属性 --
      api: '',
      autoLoadData: true,
      paramConfig: '',
      isPolling: false,
      pollTimes: 10,
      // -- 事件 --
      onMounted: '',
      onChange: '',
      onRowStyle: '',
    },
  },
  {
    type: 'onlineComponents',
    icon: 'text-field',
    formItemFlag: true,
    options: {
      name: '',
      hidden: false,
      onlineRelevance: '',
      onlineCode: '',
      // -------------------
      onCreated: '',
      onMounted: '',
      onHandleEmit: `/** 接收在线组件通过'$emitUp'方法传过来的参数
 * 其它组件中可通过this.$emitUp(event, params)触发此处函数
 * event 事件名称 String 用于区分处理不同的事件
 * params 参数 类型和其它组件传值一致
 */`,
    },
  },
  {
    type: 'webSocket',
    icon: 'text-field',
    formItemFlag: true,
    options: {
      name: '',
      hidden: true,
      onCreated: '',
      onMounted: '',
      onMessage: '',
      rowKey: '',
    },
  },
  {
    type: 'reportPrint',
    icon: 'text-field',
    formItemFlag: true,
    options: {
      name: '',
      hidden: true,
      reportUid: '',
      // isShowExport: true,
      // isShowPrint: true,
      isShowQuery: true,
      minHeight: '',
      // -------------------
      onCreated: '',
      onMounted: '',
    },
  },
  {
    type: 'tablePrint',
    icon: 'report_preview',
    formItemFlag: true,
    printConfig: {
      copies: 1, // 份数
      paperSizeType: 9, // 纸张大小
      printLayoutType: 1, //布局类型 0 默认 1居中 2垂直
      printFooterPageNum: false, // 是否显示页码
      zoom: 100, // 缩放比列`
      printMode: 0, // 单双面打印
      orientation: 1, // 打印方向
      printGridLines: false, // 是否显示网格线
      hasHomePageCover: false, // 首页是否封面
      isDialogPreview: false, // 弹窗预览
      isJumpPreview: false, // 跳转预览
    },
    options: {
      name: '',
      hidden: true,
      reportUid: '',
      // -------------------
      onCreated: '',
      onMounted: '',
    },
  },
  {
    type: 'dropdown',
    icon: 'select-field',
    formItemFlag: true,
    options: {
      name: '',
      label: '',
      dropItems: [
        {
          id: 1,
          label: '选项',
          command: 'test',
          disabled: false,
          divided: false,
          icon: 'el-icon-setting',
        },
      ],
      isButton: false,
      placement: 'bottom-end',
      dropType: 'primary',
      dropSize: 'medium',
      disabled: false,
      hideOnClick: true,
      trigger: 'hover',
      api: '',
      isPolling: false,
      pollTimes: 10,
      sqlJson: {
        sourceTable: '',
        fields: [],
        orders: [],
        joins: [],
        filters: [],
        expression: [],
        group: [],
        pageSize: null,
        pageNum: 1,
      },
      configData: false,
      paramConfig: '',
      autoLoadData: true,
      // -------------------
      onCreated: '',
      onMounted: '',
      onClick: '',
      onCommand: '',
    },
  },
  {
    type: 'pointer',
    icon: 'picture-upload-field',
    formItemFlag: true,
    options: {
      name: '',
      markers: [],
      imageUrl: '',
      fileId: '',
      imageType: 'fileId',
      imageWidth: null,
      imageHeight: 400,
      // -------------------
      onCreated: '',
      onMounted: '',
      onNodeClick: '',
      onPointerClick: '',
    },
  },
  {
    type: 'scoringCard',
    icon: 'text-field',
    formItemFlag: true,
    options: {
      name: '',
      hideDemo: false,
      hideField: false,
      // -- 网络属性--
      onMounted: '',
      // api: '',
      // isPolling: false,
      // pollTimes: 10,
      // autoLoadData: true,
      // paramConfig: ''
    },
  },
  {
    type: 'ruleEngine',
    icon: 'text-field',
    formItemFlag: true,
    options: {
      name: '',
      // -- 网络属性--
      // api: '',
      // isPolling: false,
      // pollTimes: 10,
      // autoLoadData: true,
      // paramConfig: '',
      onMounted: '',
      leftBtns: [],
    },
  },
  {
    type: 'processRoute',
    icon: 'text-field',
    formItemFlag: true,
    options: {
      name: '',
      processConfig: { // 工艺配置对象
        menuList: { 
          api: '', // 左侧列表数据来源
          templateId: null, // 左侧 + 号绑定的pc视图
        },
        typeList: [ // 节点类型配置
          { label: '工序', value: '', type: '1', disabled: false },
          { label: '连接线', value: '', type: '2', disabled: true },
          { label: '群组', value: '', type: '3', disabled: true },
        ]
      },
      showSave: true,
      showUse: true,
      jsonData: null,
      onMounted: '',
      onSubSave: '',
      onUseData: '',
    }
  }
];


// 自定义组件配置，还有templateName和templateId数据需要从服务端获取
export const customContentFields = [
  {
    type: 'custom-component',
    icon: 'tab',
    formItemFlag: true,
    options: {
      name: '',
      labelHidden: true,
      loadingType: 'skeleton',
      onCreated: '',
      onMounted: '',
      onReceive: `/** 接收并处理其它组件通过'$emitUp'方法传过来的参数
 * 其它组件中可通过this.$emitUp(event, params)触发此处函数
 * event 事件名称 String 用于区分处理不同的事件
 * params 参数 类型和其它组件传值一致
 */`
    },
  },
];

// 统计组件配置
export const statisticalFields = [
  {
    type: 'chart',
    icon: 'text-field',
    formItemFlag: true,
    options: {
      name: '',
      // -------------------
      customClass: '', // 自定义css类名
      // -------------------
      onCreated: '',
      onMounted: '',
      theme: 'macarons',
      height: 300,
    },
  },
  {
    type: 'descriptions',
    icon: 'slider-field',
    formItemFlag: true,
    options: {
      name: '',
      label: '',
      title: '',
      rows: 3,
      hasFrame: true,
      widgetSize: 'medium',
      direction: 'horizontal',
      customClass: '',
      api: '',
      isPolling: false,
      pollTimes: 10,
      sqlJson: {
        sourceTable: '',
        fields: [],
        orders: [],
        joins: [],
        filters: [],
        expression: [],
        group: [],
        pageSize: null,
        pageNum: 1,
      },
      configData: false,
      paramConfig: '',
      autoLoadData: true,
      selectLabel: 'label',
      selectValue: 'value',
      onMounted: '',
      onValueClick: '',
    },
  },
  {
    type: 'statisticCard',
    icon: 'select-field',
    formItemFlag: true,
    options: {
      name: '',
      statisticCard: '',
      cardWidth: '100%',
      mode: 1,
      gradientColor0: 'rgba(54,198,160,1)',
      gradientColor1: 'rgba(0,122,255,1)',
      gradientDirection: 'to right',
      unit: '',
      unitSize: '18',
      unitColor: 'rgba(255,255,255,1)',
      distance: '10',
      lineDistance: '10',
      fontFamily: '',
      firstLine: '项目总数',
      firstSize: '20',
      firstColor: 'rgba(255,255,255,1)',
      firstWeight: '500',
      secondLine: '80',
      secondSize: '16',
      secondColor: 'rgba(255,255,255,1)',
      secondWeight: '500',
      statisticIcon: null,
      iconSize: '32',
      iconColor: 'rgba(255,255,255,1)',
      iconBackColor: 'rgba(56, 187, 229, 1)',
      // -- 网络属性--
      api: '',
      autoLoadData: true,
      paramConfig: '',
      polling: false,
      pollTime: '500',
      isPolling: false,
      pollTimes: 10,
      // -- 事件 --
      onCreated: '',
      onMounted: '',
    },
  },
  {
    type: 'statistics',
    icon: 'select-field',
    formItemFlag: true,
    options: {
      name: '',
      statistics: '',
      titleContent: '标题',
      titleSize: '20',
      labelSize: '14',
      valueSize: '18',
      statisticsColor: 'rgba(255, 255, 255, 1)',
      backgdColor: 'rgba(135, 175, 234, 1)',
      titleColor: 'rgba(255, 255, 255, 1)',
      textColor: 'rgba(255, 255, 255, 1)',
      lineColor: 'rgba(255, 255, 255, 1)',
      optionItems: [
        { label: 'label 1', value: 'value 1' },
        { label: 'label 2', value: 'value 2' },
      ],
      selectValue: 'value',
      selectLabel: 'label',
      // -- 网络属性--
      api: '',
      isPolling: false,
      pollTimes: 10,
      autoLoadData: true,
      paramConfig: '',
      // -- 事件 --
      onCreated: '',
      onMounted: '',
    },
  },
  {
    type: 'dashboard',
    icon: 'select-field',
    formItemFlag: true,
    options: {
      name: '',
      dashboard: '',
      bandColor: 'rgba(64,158,255,1)',
      optionItems: {
        label: '待处理问题',
        value: '12',
        desc: '较上周下降10%',
      },
      descColor: 'rgba(103,194,58,1)',
      selectLabel: 'label',
      selectValue: 'value',
      selectDesc: 'desc',
      // -- 网络属性--
      api: '',
      autoLoadData: true,
      paramConfig: '',
      polling: false,
      pollTime: '500',
      isPolling: false,
      pollTimes: 10,
      // -- 事件 --
      onCreated: '',
      onMounted: '',
    },
  },
];

export function addContainerWidgetSchema(containerSchema) {
  containers.push(containerSchema);
}

export function addBasicFieldSchema(fieldSchema) {
  basicFields.push(fieldSchema);
}

export function addAdvancedFieldSchema(fieldSchema) {
  advancedFields.push(fieldSchema);
}

export function addCustomWidgetSchema(widgetSchema) {
  customFields.push(widgetSchema);
}

export function addViewFieldsWidgetSchema(widgetSchema) {
  viewFields.push(widgetSchema);
}
