<template>
  <el-container class="full-height">
    <el-container v-if="designer" style="position: relative; overflow: hidden">
      <el-aside class="side-panel" :style="{ width: leftShow ? '300px' : '0px' }">
        <widget-panel :designer="designer" ref="widgetRef" />
      </el-aside>
      <div class="toggle-bar-left" :style="{ left: leftShow ? '255px' : '0' }" @click="clickIcon('left')">
        <Transition name="leftIconSpin">
          <i v-if="leftIconShow" :class="{ 'el-icon-caret-left': leftShow }"></i>
        </Transition>
        <Transition name="leftIconSpin">
          <i v-if="!leftIconShow" :class="{ 'el-icon-caret-right': !leftShow }"></i>
        </Transition>
      </div>
      <el-container class="center-layout-container">
        <el-header class="toolbar-header">
          <toolbar-panel :designer="designer" :templateId="templateId" ref="toolbal"></toolbar-panel>
        </el-header>
        <el-main class="form-widget-main" style="height: 90vh;">
          <div class="custom-tabs">
            <div class="tab-content" style="height:94%;overflow: hidden">
              <div v-show="divActiveTab === 'page'" style="height:100%;overflow: auto">
                <v-form-widget :designer="designer" :form-config="designer.formConfig" />
              </div>
              <div v-show="divActiveTab === 'code'" style="height:100%;overflow: auto">
                <v-event-code ref="eventCode" :designer="designer" />
              </div>
            </div>

            <div class="tab-header">
              <div  class="tab-item" title="视图" :class="{ 'active': divActiveTab === 'page' }" @click="divActiveTab = 'page'">
                <svg class="icon" width="20px" height="20px" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"><path d="M704.8704 950.016H319.0784a25.6 25.6 0 0 1 0-51.2h385.792a25.6 25.6 0 0 1 0 51.2zM656.2304 610.3552a25.6 25.6 0 0 1-25.6-25.6V315.2896a25.6 25.6 0 1 1 51.2 0v269.4656a25.6 25.6 0 0 1-25.6 25.6zM425.3184 612.7104h-47.2576a84.6336 84.6336 0 0 1-84.5312-84.5312V315.2896a25.6 25.6 0 0 1 51.2 0v212.8896a33.3824 33.3824 0 0 0 33.3312 33.3312h47.2576a33.3824 33.3824 0 0 0 33.3312-33.3312V315.2896a25.6 25.6 0 0 1 51.2 0v212.8896a84.6336 84.6336 0 0 1-84.5312 84.5312z" fill="#888888" /><path d="M897.792 817.9712H126.208A122.1632 122.1632 0 0 1 4.1472 695.9616V213.7088A122.2144 122.2144 0 0 1 126.208 91.648h771.584a122.2144 122.2144 0 0 1 122.0608 122.0608v482.2528a122.1632 122.1632 0 0 1-122.0608 122.0096zM126.208 142.848a70.9632 70.9632 0 0 0-70.8608 70.8608v482.2528a70.912 70.912 0 0 0 70.8608 70.8608h771.584a70.912 70.912 0 0 0 70.8608-70.8608V213.7088a70.912 70.912 0 0 0-70.8608-70.8608z" fill="#888888" /></svg>
              </div>
              <div  class="tab-item" title="脚本" :class="{ 'active': divActiveTab === 'code' }" @click="divActiveTab = 'code'">
                <svg class="icon" width="20px" height="20px" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"><path d="M856 128H168a40 40 0 0 0-40 40v172a4 4 0 0 0 4 4h760a4 4 0 0 0 4-4V168a40 40 0 0 0-40-40zM236 272a36 36 0 1 1 36-36 36 36 0 0 1-36 36z m114 0a36 36 0 1 1 36-36 36 36 0 0 1-36 36z m114 0a36 36 0 1 1 36-36 36 36 0 0 1-36 36z m428 144H132a4 4 0 0 0-4 4v436a40 40 0 0 0 40 40h688a40 40 0 0 0 40-40V420a4 4 0 0 0-4-4zM444.5 726.7a4.2 4.2 0 0 1 0 5.7l-34 33.9a3.9 3.9 0 0 1-5.6 0l-70.7-70.7-36.8-36.8a3.9 3.9 0 0 1 0-5.6l36.8-36.8 70.7-70.7a3.9 3.9 0 0 1 5.6 0l34 33.9a4.2 4.2 0 0 1 0 5.7L373.8 656z m118.4-205.6l-49.3 279.5a3.9 3.9 0 0 1-4.6 3.2l-47.3-8.3a4 4 0 0 1-3.2-4.6l49.3-279.5a3.9 3.9 0 0 1 4.6-3.2l47.3 8.3a4 4 0 0 1 3.2 4.6z m125.3 174.5l-70.7 70.7a4 4 0 0 1-5.7 0l-33.9-33.9a4 4 0 0 1 0-5.7l70.7-70.7-70.7-70.7a4 4 0 0 1 0-5.7l33.9-33.9a4 4 0 0 1 5.7 0L727.8 656z" /></svg>
              </div>
            </div>
          </div>
        </el-main>
      </el-container>
      <div class="toggle-bar-right" :style="{ right: rightShow ? '280px' : '0' }" @click="clickIcon('right')">
        <Transition name="rightIconSpin">
          <i v-if="rightIconShow" :class="{ 'el-icon-caret-right': rightShow }"></i>
        </Transition>
        <Transition name="rightIconSpin">
          <i v-if="!rightIconShow" :class="{ 'el-icon-caret-left': !rightShow }"></i>
        </Transition>
      </div>
      <el-aside style="overflow: hidden" :style="{ width: rightShow ? '280px' : '0px' }">
        <setting-panel :designer="designer" :selected-widget="designer.selectedWidget" :form-config="designer.formConfig" ref="settingPanelRef"/>
      </el-aside>
    </el-container>
<!--    <vfDialog ref="linkDialog" />-->
    <!-- <model-operation ref="modeOperation"/> -->
  </el-container>
</template>

<script>
import WidgetPanel from './widget-panel/index';
import ToolbarPanel from './toolbar-panel/index';
import SettingPanel from './setting-panel/index';
import VFormWidget from './form-widget/index';
import { createDesigner } from '@/components/form-designer/designer';
import { addWindowResizeHandler, deepClone, getQueryParam } from '@/utils/util';
import { MOCK_CASE_URL, VARIANT_FORM_VERSION } from '@/utils/config';
import i18n, { changeLocale } from '@/utils/i18n';
import bus from '@/magic-editor/scripts/bus';
import vfDialog from '@/views/tool/variantform/vfDialog.vue';
// import modelOperation from '@/views/tool/variantform/modelOperation.vue';

import { getInfo } from '@/api/tool/form';

import VEventCode from './form-widget/event-code';
export default {
  name: 'VFormDesigner',
  componentName: 'VFormDesigner',
  mixins: [i18n],
  components: {
    WidgetPanel,
    ToolbarPanel,
    SettingPanel,
    VFormWidget,
    vfDialog,
    // modelOperation,
    VEventCode,
  },
  props: {
    inDesigner: {
      type: Object,
      default:()=>{},
    },
    fieldListApi: {
      type: Object,
      default: null,
    },
    templateId:{
      type: String,
    },
    designInfo: {
      type: Object,
      require: false
    },

  },

  data() {
    return {
      vFormVersion: VARIANT_FORM_VERSION,
      curLangName: '',

      vsCodeFlag: false,
      caseName: '',
      scrollerHeight: 0,
      designer: null,
      leftShow: true,
      rightShow: true,
      fieldList: [],
      leftIconShow: true,
      rightIconShow: true,
      divActiveTab: 'page', 
    }
  },
  provide() {
    return {
      serverFieldList: this.fieldList,
      previewDialogClose: this.previewDialogClose,
      flowNode: '',
      hasRead: false,
      eventUid: '',
    }
  },
  created() {
    this.vsCodeFlag = getQueryParam('vscode') == 1
    this.caseName = getQueryParam('case')
  },
  mounted() {
    this.designer = createDesigner(this)
    this.$set(this.designer,'reportInfos',this.designInfo.reportInfos)

    this.initLocale()

    this.scrollerHeight = window.innerHeight - 56 - 36 + 'px'
    addWindowResizeHandler(() => {
      this.$nextTick(() => {
        this.scrollerHeight = window.innerHeight - 56 - 36 + 'px'
      })
    })

    this.loadCase()

    this.loadFieldListFromServer()
    // 监听任务
    bus.$on('onOpenVfDialog', this.openVfDialog)
    // 监听任务
    // bus.$on('onOpenModelOperation', this.openModelOperation)

    bus.$on('callEditEventHandler', (eventType, params) =>{
      this.$refs.settingPanelRef.editEventHandler(eventType,params)
    })

    // 存在初始化没有值 但是马上就赋值的操作
    setTimeout(() => {
      this.$nextTick(() => {
        if (this.designer.widgetList.length == 0) {
          this.$refs.widgetRef.open()
        }
      })
    }, 100)
  },
  methods: {
    previewDialogClose(){},
    clickIcon(type) {
      if (type === 'left') {
        this.leftShow = !this.leftShow;
        setTimeout(() => {
          this.leftIconShow = !this.leftIconShow;
        }, 1)
      } else {
        this.rightShow = !this.rightShow;
        setTimeout(() => {
          this.rightIconShow = !this.rightIconShow;
        }, 1)
      }
    },
    // openVfDialog(hasOpen, formId, data) {
    //   let dataKey = 'openVfDialog-' + formId;
    //   localStorage.setItem(dataKey, JSON.stringify(data|| {}));
    //   this.$refs.linkDialog.open(hasOpen, formId, dataKey);
    // },
    // openModelOperation(model, data) {
    //   this.$refs.modeOperation.open(model, data);
    // },
    test(){
      alert(111)
    },
    openUrl(event, url) {
      if (!!this.vsCodeFlag) {
        const msgObj = {
          cmd: 'openUrl',
          data: {
            url
          }
        }
        window.parent.postMessage(msgObj, '*')
      } else {
        let aDom = event.currentTarget
        aDom.href = url
        //window.open(url, '_blank') //直接打开新窗口，会被浏览器拦截
      }
    },

    loadCase() {
      if (!this.caseName) {
        return
      }

      axios
        .get(MOCK_CASE_URL + this.caseName + '.txt')
        .then((res) => {
          if (!!res.data.code) {
            this.$message.error(this.i18nt('designer.hint.sampleLoadedFail'));
            return;
          }

          this.setFormJson(res.data);
          this.$message.success(
            this.i18nt('designer.hint.sampleLoadedSuccess'),
          );
        })
        .catch((error) => {
          this.$message.error(
            this.i18nt('designer.hint.sampleLoadedFail') + ':' + error,
          );
        });
    },

    initLocale() {
      let curLocale = localStorage.getItem('v_form_locale')
      if (!!this.vsCodeFlag) {
        curLocale = curLocale || 'en-US'
      } else {
        curLocale = curLocale || 'zh-CN'
      }
      this.curLangName = this.i18nt('application.' + curLocale)
      this.changeLanguage(curLocale)
    },

    loadFieldListFromServer() {
      if (!this.fieldListApi) {
        return
      }

      axios.get(this.fieldListApi.URL).then(res => {
        let labelKey = this.fieldListApi.labelKey || 'label'
        let nameKey = this.fieldListApi.nameKey || 'name'

        res.data.forEach(fieldItem => {
          this.fieldList.push({
            label: fieldItem[labelKey],
            name: fieldItem[nameKey]
          })
        })
      }).catch(error => {
        this.$message.error(error)
      })
    },

    handleLanguageChanged(command) {
      this.changeLanguage(command)
      this.curLangName = this.i18nt('application.' + command)
    },

    changeLanguage(langName) {
      changeLocale(langName)
    },
    clearFormWidget() {
      this.designer.clearDesigner();
    },
    setFormJson(formJson) {
      let modifiedFlag = false
      if (!!formJson) {
        if (typeof formJson === 'string') {
          modifiedFlag = this.designer.loadFormJson(JSON.parse(formJson))
        } else if (formJson.constructor === Object) {
          modifiedFlag = this.designer.loadFormJson(formJson)
        }

        if (modifiedFlag) {
          this.designer.emitHistoryChange()
        }
      }
    },

    getFormJson() {
      return {
        widgetList: deepClone(this.designer.widgetList),
        formConfig: deepClone(this.designer.formConfig)
      }
    },

    //TODO: 增加更多方法！！

  },
  watch: {
    // inDesigner: {
    //   handler(val) {
    //     if (val) {
    //       window.localStorage.setItem('widget__list__backup', JSON.stringify(val.widgetList))
    //       window.localStorage.setItem('form__config__backup', JSON.stringify(val.formConfig))
    //     }
    //   },
    //   deep: true
    // }
  }
}
</script>

<style lang="scss" scoped>

::v-deep .el-aside {
  transition: 0.3s;
}

.rightIconSpin-enter-active,
.leftIconSpin-enter-active {
  animation: leftIcon 0.3s;
  animation-timing-function: linear;
}

.rightIconSpin-leave-active,
.leftIconSpin-leave-active {
  animation: leftIcon 0.3s;
  animation-timing-function: linear;
}

@keyframes leftIcon {
  from {
    transform: rotate(180deg);
  }

  to {
    transform: rotate(0);
  }
}

.el-container.full-height {
  height: 100%;
  overflow-y: hidden;
}

.toggle-bar-left {
  display: block;
  cursor: pointer;
  height: 52px;
  width: 10px;
  position: absolute;
  top: calc(50% - 6px);
  border-radius: 0 8px 8px 0;
  background: #fff;
  z-index: 8;
  padding-top: 16px;
  transition: left 0.3s;
  transition-timing-function: linear;

  &:hover {
    background: #cdc6c6;
  }

  i {
    font-size: 16px;
    margin-left: -5px;
    color: #333;
  }
}

.toggle-bar-right {
  display: block;
  cursor: pointer;
  height: 52px;
  width: 10px;
  position: absolute;
  top: calc(50% - 6px);
  border-radius: 8px 0 0 8px;
  background: #fff;
  z-index: 8;
  padding-top: 16px;
  transition: right 0.3s;
  transition-timing-function: linear;

  &:hover {
    background: #cdc6c6;
  }

  i {
    font-size: 16px;
    color: #333;
  }
}

.el-container.center-layout-container {
  border-left: 2px dotted #ebeef5;
  border-right: 2px dotted #ebeef5;
}

.el-header.main-header {
  border-bottom: 2px dotted #ebeef5;
  height: 48px !important;
  line-height: 48px !important;
  min-width: 800px;
}

div.main-title {
  font-size: 18px;
  color: #242424;
  display: flex;
  align-items: center;
  justify-items: center;

  img {
    cursor: pointer;
    width: 36px;
    height: 36px;
  }

  span.bold {
    font-size: 20px;
    font-weight: bold;
    margin: 0 6px 0 6px;
  }

  span.version-span {
    font-size: 14px;
    color: #101f1c;
    margin-left: 6px;
  }
}

.float-left {
  float: left;
}

.float-right {
  float: right;
}

.el-dropdown-link {
  margin-right: 12px;
  cursor: pointer;
}

div.external-link a {
  font-size: 13px;
  text-decoration: none;
  margin-right: 10px;
  color: #606266;
}

.el-header.toolbar-header {
  font-size: 14px;
  border-bottom: 1px dotted #cccccc;
  height: 40px !important;
  //line-height: 42px !important;
}

.el-aside.side-panel {
  max-width: 255px !important;
  overflow-y: hidden;
}

.el-main.form-widget-main {
  padding: 0;

  position: relative;
  overflow-x: hidden;
}

.container-scroll-bar {
  ::v-deep .el-scrollbar__wrap,
  ::v-deep .el-scrollbar__view {
    overflow-x: hidden;
  }
}

::v-deep .el-form-item__content .el-input--medium .el-input-group__append {
  height: 34px !important;
}

::v-deep .el-select {
  width: 100%;
}


.custom-tabs {
  height: 100%;

  .tab-header {
    display: flex;
    border-bottom: 1px solid #e4e7ed;

    .tab-item {
      padding: 0 20px;
      height: 40px;
      line-height: 40px;
      cursor: pointer;
      font-size: 14px;
      color: #303133;
      position: relative;

      &.active {
        color: #409eff;
        font-weight: 500;

        &::after {
          content: '';
          position: absolute;
          bottom: -1px;
          left: 0;
          right: 0;
          height: 2px;
          background-color: #409eff;
        }
      }

      &:hover {
        color: #409eff;
      }
    }
  }

  .tab-content {
    padding: 10px;
    overflow: auto;
  }
}
</style>
