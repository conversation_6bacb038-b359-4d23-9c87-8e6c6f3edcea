<template>
  <container-wrapper :designer="designer" :widget="widget" :parent-widget="parentWidget" :parent-list="parentList"
                     :index-of-parent-list="indexOfParentList">

    <el-collapse :key="widget.id"
           class="padding"
           :accordion="widget.options.accordion"
            :class="[selected ? 'selected' : '', customClass]"
            @click.native.stop="selectWidget(widget)">
        <collapse-item-widget v-for="(itemWidget, index) in widget.items" :title="widget.title"  :key="itemWidget.id" :widget="itemWidget" :designer="designer"  :parent-list="widget.items"
                         :index-of-parent-list="index" :parent-widget="widget"></collapse-item-widget>
    </el-collapse>

  </container-wrapper>
</template>

<script>
  import i18n from "@/utils/i18n"
  import CollapseItemWidget from "@/components/form-designer/form-widget/container-widget/collapse-item-widget"
  import containerMixin from "@/components/form-designer/form-widget/container-widget/containerMixin";
  import ContainerWrapper from "@/components/form-designer/form-widget/container-widget/container-wrapper";

  export default {
    name: "collapse-widget",
    componentName: 'ContainerWidget',
    mixins: [i18n, containerMixin],
    components: {
      ContainerWrapper,
     CollapseItemWidget
    },
    props: {
      widget: Object,
      parentWidget: Object,
      parentList: Array,
      indexOfParentList: Number,
      designer: Object,
    },
    computed: {
      selected() {
        return this.widget.id === this.designer.selectedId
      },

      customClass() {
        return this.widget.options.customClass || ''
      },

    },
    watch: {
      //
    },
    mounted() {
      console.log(this.widget)
      //
    },
    methods: {


    }
  }
</script>

<style lang="scss" scoped>
 .selected {
    outline: 2px solid  var(--primary-color) !important;
  }
  .padding{
    padding: 5px;
  }

</style>
