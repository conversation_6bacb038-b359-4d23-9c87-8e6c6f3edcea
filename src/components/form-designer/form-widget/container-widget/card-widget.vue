<template>
  <container-wrapper
    :designer="designer"
    :widget="widget"
    :parent-widget="parentWidget"
    :parent-list="parentList"
    :index-of-parent-list="indexOfParentList"
  >
    <div
      :key="widget.id"
      :class="{ selected: selected }"
      @click.stop="selectWidget(widget)"
    >
      <el-card class="box-card">
        <div slot="header" class="clearfix">
          <span
            style="
              font-size: 16px;
              color: rgb(18, 19, 21);
              font-weight: 600;
              background-color: inherit;
            "
            >{{ widget.options.label }}</span
          >
          <span
            style="
              font-size: 12px;
              color: #999;
              background-color: inherit;
              display: block;
              margin-top: 5px;
            "
            v-if="widget.options.subtitle"
            >{{ widget.options.subtitle }}</span
          >
        </div>
        <div class="text item">
          <div
            style="width: 100%"
            :style="{ height: widget.widgetList.length ? 'auto' : '68px' }"
          >
            <draggable
              :list="widget.widgetList"
              v-bind="{
                group: 'dragGroup',
                ghostClass: 'ghost',
                animation: 200,
              }"
              handle=".drag-handler"
              @add="(evt) => onContainerDragAdd(evt, widget.widgetList)"
              @update="onContainerDragUpdate"
              :move="checkContainerMove"
            >
              <transition-group
                name="fade"
                tag="div"
                class="form-widget-list"
                :style="{ height: widget.widgetList.length ? 'auto' : '68px' }"
              >
                <template v-for="(subWidget, swIdx) in widget.widgetList">
                  <template v-if="'container' === subWidget.category">
                    <component
                      :is="subWidget.type + '-widget'"
                      :widget="subWidget"
                      :designer="designer"
                      :key="subWidget.id"
                      :parent-list="widget.widgetList"
                      :index-of-parent-list="swIdx"
                      :parent-widget="widget"
                    ></component>
                  </template>
                  <template v-else>
                    <component
                      :is="subWidget.type + '-widget'"
                      :field="subWidget"
                      :designer="designer"
                      :key="subWidget.id"
                      :parent-list="widget.widgetList"
                      :index-of-parent-list="swIdx"
                      :parent-widget="widget"
                      :design-state="true"
                    ></component>
                  </template>
                </template>
              </transition-group>
            </draggable>
          </div>
        </div>
      </el-card>
    </div>
  </container-wrapper>
</template>

<script>
import Draggable from 'vuedraggable';
import i18n from '@/utils/i18n';
// import fieldMixin from '@/components/form-designer/form-widget/field-widget/fieldMixin';
import containerMixin from '@/components/form-designer/form-widget/container-widget/containerMixin';
import ContainerWrapper from '@/components/form-designer/form-widget/container-widget/container-wrapper';
import FieldComponents from '@/components/form-designer/form-widget/field-widget/index';

export default {
  name: 'card-widget',
  componentName: 'ContainerWidget',
  mixins: [i18n,  containerMixin],
  components: {
    ContainerWrapper,
    Draggable,

    ...FieldComponents,
  },
  props: {
    widget: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,
  },
  data() {
    return {
      activeTab: 'tab1',
      //
    };
  },
  computed: {
    selected() {
      return this.widget.id === this.designer.selectedId;
    },

    customClass() {
      return this.widget.options.customClass || '';
    },
  },
  watch: {
    //
  },
  mounted() {
  },
  methods: {
    onTabClick(evt) {
      console.log('onTabClick', evt);
      let paneName = evt.name;
      this.widget.tabs.forEach((tp) => {
        tp.options.active = tp.options.name === paneName;
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.selected {
  outline: 2px solid var(--primary-color) !important;
}
</style>
