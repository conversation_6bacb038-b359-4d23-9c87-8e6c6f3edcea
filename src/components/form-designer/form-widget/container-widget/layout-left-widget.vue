<template>
  <container-wrapper
    :designer="designer"
    :widget="widget"
    :parent-widget="parentWidget"
    :parent-list="parentList"
    :index-of-parent-list="indexOfParentList"
  >
    <div
      style=""
      :class="[selected ? 'selected' : '', customClass]"
      class="layout-left-container"
      :style="{
        width: leftWidth,
        marginRight: isOpen && widget.options.shrink ? '25px' : '15px',
      }"
      @click.stop="selectWidget(widget)"
    >
      <!-- <div v-if="widget.widgetList.length === 0" class="no-widget-hint">
        左侧内容
      </div> -->
      <draggable
        :list="widget.widgetList"
        v-bind="{ group: 'dragGroup', ghostClass: 'ghost', animation: 200 }"
        handle=".drag-handler"
        @add="(evt) => onContainerDragAdd(evt, widget.widgetList)"
        @update="onContainerDragUpdate"
        :move="checkContainerMove"
      >
        <transition-group
          name="fade"
          tag="div"
          :style="{
            minHeight: widget.widgetList.length ? 'auto' : '500px',
            padding: '6px',
            overflow: 'hidden'
          }"
        >
          <template v-for="(subWidget, swIdx) in widget.widgetList">
            <template v-if="'container' === subWidget.category">
              <component
                :is="subWidget.type + '-widget'"
                :widget="subWidget"
                :designer="designer"
                :key="subWidget.id"
                :parent-list="widget.widgetList"
                :index-of-parent-list="swIdx"
                :parent-widget="widget"
              ></component>
            </template>
            <template v-else>
              <component
                :is="subWidget.type + '-widget'"
                :field="subWidget"
                :designer="designer"
                :key="subWidget.id"
                :parent-list="widget.widgetList"
                :index-of-parent-list="swIdx"
                :parent-widget="widget"
                :design-state="true"
              ></component>
            </template>
          </template>
        </transition-group>
      </draggable>

      <div
        v-if="widget.options.shrink"
        :style="{ right: isOpen ? '-16px' : '-16px' }"
        class="bd-rd d-flex a-center j-center pointer"
        @click="isOpen = !isOpen"
      >
        <i
          class="el-icon-arrow-left"
          :class="{ clear: !isOpen }"
          style="color: #ffffff"
        />
      </div>
    </div>
  </container-wrapper>
</template>

<script>
import Draggable from 'vuedraggable';
import i18n from '@/utils/i18n';
import containerMixin from '@/components/form-designer/form-widget/container-widget/containerMixin';
import ContainerWrapper from '@/components/form-designer/form-widget/container-widget/container-wrapper';
import FieldComponents from '@/components/form-designer/form-widget/field-widget/index';

export default {
  name: 'layout-left-widget',
  componentName: 'ContainerWidget', //必须固定为FieldWidget，用于接收父级组件的broadcast事件
  mixins: [i18n, containerMixin],
  components: {
    ContainerWrapper,
    Draggable,
    ...FieldComponents,
  },
  props: {
    widget: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,
  },
  data() {
    return {
      isOpen: true,
    };
  },

  computed: {
    selected() {
      return this.widget.id === this.designer.selectedId;
    },

    customClass() {
      return this.widget.options.customClass || '';
    },
    leftWidth() {
      let width = this.widget.options.width ? this.widget.options.width + 'px' : '200px'
      return this.isOpen ? width : '0px'
    }
  },
  watch: {
    //
  },
  mounted() {
    //
    if (!this.widget.widgetList) {
    }
  },
  methods: {
    checkContainerMove(evt) {
      return this.designer.checkWidgetMove(evt);
    },
  },
};
</script>

<style lang="scss" scoped>
.layout-left-container {
  outline: 1px dashed #336699;
  min-height: 500px;
  position: relative;
  margin-right: 15px;
  transition: 0.5s linear;
  &.selected {
    outline: 2px solid var(--primary-color) !important;
  }
  .bd-rd {
    border-radius: 0 30px 30px 0;
    width: 16px;
    height: 30px;
    background-color: #e6ebf5;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    right: -16px;
    transition: 0.5s linear;
    .clear {
      transition: 0.5s linear;
      transform: rotate(180deg); //旋转180°
    }
  }
}

.no-widget-hint {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  font-size: 18px;
  color: #999999;
}
</style>
