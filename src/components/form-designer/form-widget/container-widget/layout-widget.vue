<template>
  <container-wrapper
    :designer="designer"
    :widget="widget"
    :parent-widget="parentWidget"
    :parent-list="parentList"
    :index-of-parent-list="indexOfParentList"
  >
    <div
      :class="[selected ? 'selected' : '', customClass]"
      class="layout-container"
      @click.stop="selectWidget(widget)"
    >
      <component
        :is="'layout-header-widget'"
        :widget="widget.widgetList[0]"
        :designer="designer"
        :parent-list="widget.widgetList"
        :index-of-parent-list="0"
        :parent-widget="widget"
      />

      <div class="d-flex">
        <component
          v-if="widget.options.layoutModel === 2"
          :is="'layout-left-widget'"
          :widget="widget.widgetList[1]"
          :designer="designer"
          :parent-list="widget.widgetList"
          :index-of-parent-list="1"
          :parent-widget="widget"
        />
        <component
          :is="'layout-body-widget'"
          :widget="widget.widgetList[2]"
          :designer="designer"
          :parent-list="widget.widgetList"
          :index-of-parent-list="2"
          :parent-widget="widget"
          class="flex-1"
        />
      </div>
    </div>
  </container-wrapper>
</template>

<script>
import Draggable from 'vuedraggable';
import i18n from '@/utils/i18n';
import containerMixin from '@/components/form-designer/form-widget/container-widget/containerMixin';
import ContainerWrapper from '@/components/form-designer/form-widget/container-widget/container-wrapper';
import FieldComponents from '@/components/form-designer/form-widget/field-widget/index';

export default {
  name: 'layout-widget',
  componentName: 'ContainerWidget', //必须固定为FieldWidget，用于接收父级组件的broadcast事件
  mixins: [i18n, containerMixin],
  components: {
    ContainerWrapper,
    Draggable,

    ...FieldComponents,
  },
  props: {
    widget: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,
  },
  data() {
    return {
      //
    };
  },
  computed: {
    selected() {
      return this.widget.id === this.designer.selectedId;
    },

    customClass() {
      return this.widget.options.customClass || '';
    },
  },
  watch: {},
  mounted() {},
  methods: {},
};
</script>

<style lang="scss" scoped>
.layout-container {
  padding: 6px;
  outline: 1px dashed #336699;
  &.selected {
    outline: 2px solid var(--primary-color) !important;
  }
  .form-widget-list {
    min-height: 28px;
  }
}
</style>
