<template>
  <container-wrapper :designer="designer" :widget="widget" :parent-widget="parentWidget" :parent-list="parentList"
                     :index-of-parent-list="indexOfParentList">

    <div style="min-height: 100px" :class="[selected ? 'selected' : '', customClass]"
         class="dialog-container"
         @click.stop="selectWidget(widget)">
        <template v-for="(subWidget, swIdx) in widget.widgetList">

          <template v-if="'container' === subWidget.category">
            <component  v-if="subWidget.type!=='dialog-footer' || widget .options.isCustom" :is="subWidget.type + '-widget'" :widget="subWidget" :designer="designer" :key="subWidget.type" :parent-list="widget.widgetList"
                       :index-of-parent-list="swIdx" :parent-widget="widget"></component>
          </template>
          <template v-else>
            <component :is="subWidget.type + '-widget'" :field="subWidget" :designer="designer" :key="subWidget.type" :parent-list="widget.widgetList"
                       :index-of-parent-list="swIdx" :parent-widget="widget" :design-state="true"></component>
          </template>

        </template>
    </div>
  </container-wrapper>
</template>

<script>
import Draggable from 'vuedraggable'
import i18n from "@/utils/i18n"
import containerMixin from "@/components/form-designer/form-widget/container-widget/containerMixin"
import ContainerWrapper from "@/components/form-designer/form-widget/container-widget/container-wrapper"
import FieldComponents from '@/components/form-designer/form-widget/field-widget/index'

export default {
  name: "dialog-widget",
  componentName: 'ContainerWidget',  //必须固定为FieldWidget，用于接收父级组件的broadcast事件
  mixins: [i18n, containerMixin],
  components: {
    ContainerWrapper,
    Draggable,

    ...FieldComponents,
  },
  props: {
    widget: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,
  },
  data() {
    return {
      //
    }
  },
  computed: {
    selected() {
      return this.widget.id === this.designer.selectedId
    },

    customClass() {
      return this.widget.options.customClass || ''
    },

  },
  watch: {
  },
  mounted() {
   if(!this.widget.options.hasOwnProperty('isCustom')){
      this.$set(this.widget.options,'isCustom',true)
    }
  },
  methods: {
  }
}
</script>

<style lang="scss" scoped>
.dialog-container {
  min-height: 50px;
  //line-height: 48px;
  padding: 6px;
  outline: 1px dashed #336699;

  .form-widget-list {
    min-height: 28px;
  }

}

.dialog-container.selected {
  outline: 2px solid var(--primary-color) !important;
}
</style>
