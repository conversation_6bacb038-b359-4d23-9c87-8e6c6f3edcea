<template>
  <el-collapse-item  :title="widget.options.title"  :class="[selected ? 'selected' : '', customClass]"
          :key="widget.id" @click.native.stop="selectWidget(widget)">
    <draggable :list="widget.widgetList" v-bind="{group:'dragGroup', ghostClass: 'ghost',animation: 200}"
               handle=".drag-handler"
               @add="(evt) => onContainerDragAdd(evt, widget.widgetList)"
              @update="onContainerDragUpdate" :move="checkContainerMove">
      <transition-group name="fade" tag="div" class="form-widget-list">
        <template v-for="(subWidget, swIdx) in widget.widgetList">
          <template v-if="'container' === subWidget.category">
            <component :is="subWidget.type + '-widget'" :widget="subWidget" :designer="designer" :key="subWidget.id" :parent-list="widget.widgetList"
                              :index-of-parent-list="swIdx" :parent-widget="widget"></component>
          </template>
          <template v-else>
            <component :is="subWidget.type + '-widget'" :field="subWidget" :designer="designer" :key="subWidget.id" :parent-list="widget.widgetList"
                          :index-of-parent-list="swIdx" :parent-widget="widget" :design-state="true"></component>
          </template>
        </template>
      </transition-group>
    </draggable>
  </el-collapse-item>
</template>

<script>
  import Draggable from 'vuedraggable'
  import containerMixin from "@/components/form-designer/form-widget/container-widget/containerMixin"
  import i18n from "@/utils/i18n";
  import FieldComponents from '@/components/form-designer/form-widget/field-widget/index'

  export default {
    name: "CollapseItemWidget",
    componentName: "CollapseItemWidget",
    mixins: [i18n, containerMixin],
    components: {
      Draggable,
      ...FieldComponents,
    },
    props: {
      widget: Object,
      parentWidget: Object,
      parentList: Array,
      indexOfParentList: Number,
      designer: Object,
      title:String

    },
    data() {
      return {
      }
    },
    computed: {
      selected() {
        return this.widget.id === this.designer.selectedId
      },

      customClass() {
        return this.widget.options.customClass || ''
      },

    },
    created() {
    },
    methods: {
      checkContainerMove(evt) {
        return this.designer.checkWidgetMove(evt)
      },
       selectWidget(widget) {
        this.designer.setSelected(widget)
      },
    }
  }
</script>

<style lang="scss" scoped>
.selected {
    outline: 2px solid  var(--primary-color) !important;
  }
</style>
