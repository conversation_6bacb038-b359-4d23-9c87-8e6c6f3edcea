<template>
  <container-wrapper
    :designer="designer"
    :widget="widget"
    :parent-widget="parentWidget"
    :parent-list="parentList"
    :index-of-parent-list="indexOfParentList"
  >
    <div :key="widget.id" class="pd-b5 d-flex flex-wrap">
      <div
        :key="widget.id"
        class="container d-flex a-center pd-b10 pd-r10"
        style="flex: 1"
        :class="[selected ? 'isselected' : '']"
        @click.stop="selectWidget(widget)"
      >
        <div class="d-flex flex-wrap" style="width: 100%">
          <template v-for="(subWidget, swIdx) in widget.widgetList">
            <template v-if="'condition-container-body' === subWidget.type">
              <component
                style="width: 100%"
                :is="subWidget.type + '-widget'"
                :widget="subWidget"
                :designer="designer"
                :key="subWidget.id"
                :parent-list="widget.widgetList"
                :index-of-parent-list="swIdx"
                :parent-widget="widget"
              ></component>
            </template>
            <template
              v-if="'condition-container-footer' === subWidget.type && moreFlag"
            >
              <component
                style="width: 100%"
                :is="subWidget.type + '-widget'"
                :widget="subWidget"
                :designer="designer"
                :key="subWidget.id"
                :parent-list="widget.widgetList"
                :index-of-parent-list="swIdx"
                :parent-widget="widget"
              ></component>
            </template>
          </template>
        </div>
      </div>
      <div>
        <el-button type="primary" icon="el-icon-search" class="mr-l5"
          >查询</el-button
        >
        <el-button class="mr-l10" icon="el-icon-refresh-left" plain
          >重置</el-button
        >
        <span class="el-dropdown-link" @click="more">
          {{ !moreFlag ? '展开' : '收缩'
          }}<i
            class="el-icon-arrow-down"
            :style="{ transform: moreFlag ? 'rotate(180deg)' : 'rotate(0)' }"
          ></i>
        </span>
      </div>
    </div>
  </container-wrapper>
</template>

<script>
import Draggable from 'vuedraggable';
import i18n from '@/utils/i18n';
import containerMixin from '@/components/form-designer/form-widget/container-widget/containerMixin';
import ContainerWrapper from '@/components/form-designer/form-widget/container-widget/container-wrapper';
import FieldComponents from '@/components/form-designer/form-widget/field-widget/index';

export default {
  name: 'condition-container-widget',
  componentName: 'ContainerWidget',
  mixins: [i18n, containerMixin],
  components: {
    ContainerWrapper,
    Draggable,
    ...FieldComponents,
  },
  props: {
    widget: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,
  },
  data() {
    return {
      moreFlag: false,
    };
  },
  computed: {
    selected() {
      return this.widget.id === this.designer.selectedId;
    },

    customClass() {
      return this.widget.options.customClass || '';
    },

    listCustomClass() {
      return this.widget.options.listCustomClass || '';
    },
  },
  watch: {
    //
  },
  mounted() {
    //
  },
  methods: {
    more() {
      this.moreFlag = !this.moreFlag;
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  border: 1px dashed;
}
.isselected {
  outline: 2px solid var(--primary-color) !important;
}
.el-dropdown-link {
  cursor: pointer;
  color: var(--primary-color);
  font-size: 12px;
  margin-left: 10px;
}
.el-icon-arrow-down {
  font-size: 12px;
  transition: 0.5s;
}
</style>
