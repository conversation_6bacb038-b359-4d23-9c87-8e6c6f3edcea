<template>
  <view-wrapper
    :designer="designer"
    :field="field"
    :rules="rules"
    :design-state="designState"
    :parent-widget="parentWidget"
    :parent-list="parentList"
    :index-of-parent-list="indexOfParentList"
    :sub-form-row-index="subFormRowIndex"
    :sub-form-col-index="subFormColIndex"
    :sub-form-row-id="subFormRowId"
    :echo-field-data="echoFieldData"
    :sub-edit-flag="subEditFlag"
  >
    <printForm
      :printConfig="field.printConfig"
      :margin="margin"
      :isDesign=true
    />
    <printDialog
    ref="printDialogRef"/>
  </view-wrapper>
</template>

<script>
import viewWrapper from './view-wrapper'
import i18n, { translate } from '@/utils/i18n'
import fieldMixin from '@/components/form-designer/form-widget/field-widget/fieldMixin'
import emitter from 'element-ui/lib/mixins/emitter'
import printForm from '@/views/tool/excelreport/viewer/printForm.vue'
import printDialog from '@/views/tool/excelreport/viewer/printDialog.vue'
import { getUniCode } from '@/utils/print-util'

export default {
  name: 'tablePrint-widget',
  componentName: 'FieldWidget', //必须固定为FieldWidget，用于接收父级组件的broadcast事件
  components: {
    printDialog,
    viewWrapper,
    printForm,
  },
  mixins: [emitter, fieldMixin, i18n],
  props: {
    field: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,

    designState: {
      type: Boolean,
      default: false
    },

    subFormRowIndex: {
      /* 子表单组件行索引，从0开始计数 */ type: Number,
      default: -1
    },
    subFormColIndex: {
      /* 子表单组件列索引，从0开始计数 */ type: Number,
      default: -1
    },
    subFormRowId: {
      /* 子表单组件行Id，唯一id且不可变 */ type: String,
      default: ''
    },
    subEditFlag: {
      type: Object,
      default: null
    },
    echoFieldData: {
      type: Object,
      default: null
    }
  },
  inject: ['refList', 'formConfig', 'globalOptionData', 'globalModel'],
  data() {
    return {
      rules: [],
      queryDefault: {},
      printConfig: {
        copies: 1, // 份数
        paperSizeType: 9, // 纸张大小
        printLayoutType: 1, //布局类型 0 默认 1居中 2垂直
        printFooterPageNum: false, // 是否显示页码
        zoom: 100, // 缩放比列`
        printMode: 0, // 单双面打印
        orientation: 1, // 打印方向
        printGridLines: false, // 是否显示网格线
        hasHomePageCover: false, // 首页是否封面
        isDialogPreview: false, // 弹窗预览
        isJumpPreview: false // 跳转预览
      },
      margin: [1, 0.75, 1, 0.75],
    }
  },
  created() {
    /* 注意：子组件mounted在父组件created之后、父组件mounted之前触发，故子组件mounted需要用到的prop
       需要在父组件created中初始化！！ */
    this.registerToRefList()
    this.initEventHandler()
    this.handleOnCreated()
  },
  mounted() {
    this.handleOnMounted()
  },

  beforeDestroy() {
    this.unregisterFromRefList()
  },
  methods: {

    printReport(params = {}) {
      if (this.field.options.reportUid === undefined || this.field.options.reportUid === '') {
        this.$message.warning("请先选择需要打印的报表")
        return;
      }

      let printParam = {
        body: {...params},
        reportUid: this.field.options.reportUid,
      }

      printParam['reportExcelDto'] = {
        ...this.field.printConfig,
        unicode: getUniCode(),
        reportUid: this.field.options.reportUid
      }

      this.$refs.printDialogRef.report2Pdf(printParam);
    },

  }
}
</script>

<style scoped></style>
