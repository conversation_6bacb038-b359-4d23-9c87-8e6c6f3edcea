<template>
  <view-wrapper
    :designer="designer"
    :field="field"
    :rules="rules"
    :design-state="designState"
    :parent-widget="parentWidget"
    :parent-list="parentList"
    :index-of-parent-list="indexOfParentList"
    :sub-form-row-index="subFormRowIndex"
    :sub-form-col-index="subFormColIndex"
    :sub-form-row-id="subFormRowId"
    :echo-field-data="echoFieldData"
    :sub-edit-flag="subEditFlag"
    class="table-view table-container"
  >
    <div
      class="tableBox"
      v-loading="loading"
    >
      <div class="d-flex no-print j-center titleBox">
        <div class="d-flex a-center leftBox">
          <template v-if="getLeftBtns.length">
            <template v-for="(item, index) in getLeftBtns">
              <!-- <template v-if="item.hidden === undefined || item.hidden === false"> -->
              <el-link
                :underline="false"
                :key="index"
                :type="item.type"
                :style="{
                  backgroundColor: item.bgColor,
                  borderColor: item.bgColor,
                }"
                @click="handleLeftClick(item.fun, index)"
                v-if="
                  handleLeftDisplay(item.displayFun) &&
                  (field.options.authority
                    ? checkButtonPermission(
                        `${field.options.name}-leftBtn-${item['cust-id']}`,
                        field,
                      )
                    : true)
                "
              >
                <template v-if="item.icon">
                  <i
                    v-if="item.icon && item.icon.includes('el-icon')"
                    :style="{ color: item.color }"
                    :class="item.icon"
                  ></i>
                  <svg-icon
                    :class="[
                      !!!item.color && item.type
                        ? 'el-button--' + item.type
                        : '',
                    ]"
                    v-else
                    :icon-class="item.icon"
                    :style="{ color: item.color, margin: 0 }"
                  />
                </template>
                {{ item.name }}
                <el-divider
                  v-if="index != field.options.leftBtns.length - 1"
                  direction="vertical"
                ></el-divider>
              </el-link>
              <!-- </template> -->
            </template>
          </template>
          <el-input
            v-if="field.options.showSearch"
            :style="{ marginLeft: getLeftBtns.length ? '10px' : 0 }"
            class="searchBox"
            v-model="searchKey"
            prefix-icon="el-icon-search"
            placeholder="按enter键搜索"
            @change="doSearch"
          />
        </div>
        <div
          class="tableTitle"
          style="font-weight: 600"
          v-if="!field.options.labelHidden"
        >
          {{ field.options.label }}
        </div>
        <div class="rightBox">
          <template>
            <el-button
              type="primary"
              v-if="isShowPrint && !total"
              size="mini"
              class="mr-r5"
              icon="el-icon-printer"
              @click="doPrint()"
            >
              打印
            </el-button>
            <el-dropdown
              trigger="click"
              v-else-if="isShowPrint && total"
              @command="doPrint"
            >
              <el-button
                type="primary"
                size="mini"
                class="mr-r5"
                icon="el-icon-printer"
              >
                打印
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="page">当前页</el-dropdown-item>
                <el-dropdown-item divided command="all" style="color: #ffbf40"
                  >全部</el-dropdown-item
                >
              </el-dropdown-menu>
            </el-dropdown>

            <el-button
              type="primary"
              icon="el-icon-download"
              size="mini"
              class="mr-r5 mr-l0"
              @click="doExport"
              v-if="isShowExport && !total"
            >
              导出
            </el-button>

            <el-dropdown
              v-else-if="isShowExport && total"
              trigger="click"
              @command="doExport"
            >
              <el-button
                type="primary"
                icon="el-icon-download"
                size="mini"
                class="mr-r5"
              >
                导出
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="page">当前页</el-dropdown-item>
                <el-dropdown-item divided command="all" style="color: #ffbf40"
                  >全部</el-dropdown-item
                >
              </el-dropdown-menu>
            </el-dropdown>
          </template>

          <el-popover placement="bottom-start" trigger="click">
            <lt-sort-table
              v-model="copyCol"
              isEnable
              row-key="id"
              height="400"
              @change="colChange"
            >
              <el-table-column label="名称" prop="label" />
              <el-table-column label="隐藏" prop="hidden">
                <template slot-scope="{ row }">
                  <el-switch
                    v-model="row.hidden"
                    @change="colChange"
                    :disabled="disabledFieldSelect(row)"
                  />
                </template>
              </el-table-column>
            </lt-sort-table>
            <el-button
              type="primary"
              size="mini"
              slot="reference"
              icon="el-icon-setting"
              v-if="field.options.isShowTableField"
              >字段管理</el-button
            >
          </el-popover>
        </div>
      </div>
      <div style="padding: 5px 10px 0px 10px">
        <div class="orderBox">
          <div
            class="orderItem"
            v-for="(item, index) in copyColumnQuery.filter(
              (e) => e.order && e.order !== '',
            )"
            :key="index"
          >
            <span>{{ item.label }}: </span>
            <span>{{
              (item.order || item.multiOrder) === 'ascending' ? '升序' : '降序'
            }}</span>
            <i class="el-icon-close" @click="deleteOrder(item.prop)" />
          </div>
          <div
            class="orderItem"
            v-for="(item, index) in copyColumnQuery.filter(
              (e) => e.input && e.input !== '',
            )"
            :key="index"
          >
            <span>{{ item.label }}: </span>
            <span v-if="item.type === 'date'">{{
              item.input[0] + '至' + item.input[1]
            }}</span>
            <span v-else>{{ item.input }}</span>
            <i class="el-icon-close" @click="deleteInput(item.prop)" />
          </div>
          <div
            class="orderItem"
            v-for="(item, index) in copyColumnQuery.filter(
              (e) => e.min || e.max,
            )"
            :key="index"
          >
            <span>{{ item.label }}: </span>
            <span v-if="item.type === 'number'">
              <template v-if="item.min"> 大于等于{{ item.min }} </template>
              <template v-if="item.min && item.max">并且</template>
              <template v-if="item.max"> 小于等于{{ item.max }} </template>
            </span>
            <span v-else>{{ item.input }}</span>
            <i class="el-icon-close" @click="deleteInputNumber(item.prop)" />
          </div>
          <div
            class="orderItem"
            v-for="(item, index) in copyColumnQuery.filter(
              (e) => e.select && e.select.length > 0,
            )"
            :key="index"
          >
            <span>{{ item.label }}: </span>
            <span
              v-if="
                item.type === 'select' ||
                item.type === 'checkbox' ||
                item.type === 'radio'
              "
            >
              {{ queryValueToLabel(item) }}
            </span>
            <span v-else>{{ item.input }}</span>
            <i class="el-icon-close" @click="deleteSelect(item.prop)" />
          </div>
          <div
            class="orderItem"
            v-for="(item, index) in copyColumnQuery.filter(
              (e) => e.blank && e.blank !== '' && e.blank !== 1,
            )"
            :key="index"
          >
            <span>{{ item.label }}: </span>
            <span v-if="item.blank === 2">已填</span>
            <span v-if="item.blank === 3">未填</span>
            <i class="el-icon-close" @click="deleteBlank(item.prop)" />
          </div>
        </div>
        <lt-sort-table
          :id="`data-table-${stringToIntHash(field.id)}`"
          :class="{
            'hide-checkbox': field.options.selectionLimit,
          }"
          :style="{ minHeight: field.options.minimumHeight + 'px' || '200px' }"
          v-model="tableData"
          ref="sortTableRef"
          v-if="flag"
          :height="
            computedHeight ||
            (field.options.minHeight
              ? (total ? field.options.minHeight-50:field.options.minHeight) + 'px'
              : field.options.showSummary
              ? 'auto'
              : 'auto')
          "
          :max-height="
            field.options.height || field.options.showSummary ? 'auto' : 'auto'
          "
          :highlight-current-row="field.options.highlightCurrentRow"
          :custKey="custKey"
          :lazy="field.options.isLazy"
          :load="lazyLoad"
          :expand-row-keys="expandRowKeys"
          :show-summary="
            copyData.length > 0 ? field.options.showSummary : false
          "
          :summary-method="getSummaries"
          :row-key="getRowKey"
          :tree-props="{
            children: field.options.subsetField || 'children',
            hasChildren: field.options.isLazy ? 'hasChildren' : '',
          }"
          :row-style="tableRowStyle"
          :cell-style="tableCellStyle"
          :border="field.options.hasFrame"
          :stripe="field.options.tableStripe"
          :isEnable="field.options.isEnable && !field.options.treeTable"
          :span-method="spanMethod"
          :header-cell-style="rowClass"
          :default-expand-all="field.options.defaultExpandAll"
          @row-click="onRowClick"
          @paste.native="pasteInfo($event)"
          @row-dblclick="onRowDbClick"
          @sort-change="sortChange"
          @current-change="currentChange"
          @cell-dblclick="cellDblclick"
          @cell-click="cellClick"
          @select="select"
          @select-all="selectAll"
          @change="change"
          @selection-change="handleSelectionChange"
          @expand-change="handleExpandChange"
          @header-dragend="handleHeaderDragend"
          @mousedown.native="mouseDownHandler"
          @mouseup.native="mouseUpHandler"
          @mousemove.native="mouseMoveHandler"
          @touchstart.native="touchstartHandler"
          @touchend.native="touchendHandler"
          @touchmove.native="touchmoveHandler"
        >
          <el-table-column
            v-if="field.options.hasSelection"
            :reserve-selection="'reserveSelection' in field.options ? field.options.reserveSelection : true"
            type="selection"
            :selectable="checkStatus"
            width="50"
            fixed="left"
          >
          </el-table-column>
          <el-table-column
            type="expand"
            v-if="
              field.options.hasExpand &&
              field.widgetList &&
              field.widgetList.length > 0
            "
          >
            <template slot-scope="{ $index }">
              <div
                style="min-height: 100px; padding: 20px"
                :style="{ zIndex: zCIndex }"
              >
                <template v-for="(subWidget, swIdx) in field.widgetList">
                  <template v-if="'container' === subWidget.category">
                    <component
                      :is="subWidget.type + '-item'"
                      :widget="subWidget"
                      :key="swIdx"
                      :parent-list="field.widgetList"
                      :index-of-parent-list="swIdx"
                      :parent-widget="field"
                    ></component>
                  </template>
                  <template v-else>
                    <component
                      :is="subWidget.type + '-widget'"
                      :field="subWidget"
                      :designer="null"
                      :key="swIdx"
                      :parent-list="field.widgetList"
                      :index-of-parent-list="swIdx"
                      :sub-form-row-index="$index"
                      :sub-form-row-id="$index"
                      :parent-widget="field"
                    ></component>
                  </template>
                </template>
              </div>
            </template>
          </el-table-column>

          <el-table-column
            label="序号"
            v-if="field.options.showIndex"
            width="55"
            type="index"
            :align="field.options.indexAlign || 'left'"
          >
          </el-table-column>
          <template v-for="(col, index) in copyCol.filter((item) => item)">
            <Column
              v-if="!col.hidden"
              :col="col"
              :key="col.id"
              :index="index"
              :field="field"
              :dict-map="dictMap"
              :user-map="userMap"
              :selectData="selectData"
              :selectDict="selectDict"
              :disableRelation="disableRelation"
              @userChange="userChange"
              :columnQuery="columnQuery"
              @confirmPop="confirmPop"
              @closePop="closePop"
              @columnShowQuery="columnShowQuery"
            />
          </template>
          <el-table-column
            class-name="noExl"
            align="center"
            :fixed="field.options.opeFix ? 'right' : false"
            label="操作"
            v-if="
              getBtns.length > 0 &&
              (!field.options.disabled || !field.options.readonly)
            "
            :width="getOperColumnWidth()"
          >
            <template slot-scope="{ row, $index }">
              <template v-for="(btn, index) in getBtns">
                <!-- <template v-if="btn.hidden === undefined || btn.hidden === false" > -->
                <span
                  :key="index"
                  v-if="
                    hasButDisplay(btn, row, index) &&
                    (field.options.authority
                      ? checkButtonPermission(
                          `${field.options.name}-btn-${btn['cust-id']}`,
                          field,
                        )
                      : true)
                  "
                >
                  <el-tooltip
                    v-if="btn.tip && btn.type == 2"
                    class="item"
                    effect="dark"
                    :content="btn.tip"
                    placement="top"
                  >
                    <table-cells
                      :btn="btn"
                      @click.native.stop="click(row, $index, btn.fun, index)"
                    />
                  </el-tooltip>
                  <table-cells
                    v-else
                    :btn="btn"
                    @click.native.stop="click(row, $index, btn.fun, index)"
                  />
                </span>
                <!-- </template> -->
              </template>
            </template>
          </el-table-column>
          <template #append>
            <div :style="{height:`${totalHeight}px`}"></div>
          </template>
        </lt-sort-table>
      </div>

      <div v-if="designState && field.options.hasExpand">
        <div
          style="min-height: 100px; padding: 20px"
          class="dialog-body-container field-action"
          @click.stop="selectWidget(field)"
        >
          <draggable
            style="border: 1px solid var(--primary-color)"
            :list="field.widgetList"
            v-bind="{ group: 'dragGroup', ghostClass: 'ghost', animation: 200 }"
            handle=".drag-handler"
            @add="(evt) => onContainerDragAdd(evt, field.widgetList)"
            @update="onContainerDragUpdate"
            :move="checkContainerMove"
          >
            <transition-group
              name="fade"
              tag="div"
              class="form-widget-list"
              :style="{ height: field.widgetList.length ? 'auto' : '68px' }"
            >
              <template v-for="(subWidget, swIdx) in field.widgetList">
                <template v-if="'container' === subWidget.category">
                  <component
                    :is="subWidget.type + '-widget'"
                    :widget="subWidget"
                    :designer="designer"
                    :key="subWidget.id"
                    :parent-list="field.widgetList"
                    :index-of-parent-list="swIdx"
                    :parent-widget="field"
                  ></component>
                </template>
                <template v-else>
                  <component
                    :is="subWidget.type + '-widget'"
                    :field="subWidget"
                    :designer="designer"
                    :key="subWidget.id"
                    :parent-list="field.widgetList"
                    :index-of-parent-list="swIdx"
                    :parent-widget="field"
                    :design-state="true"
                  ></component>
                </template>
              </template>
            </transition-group>
          </draggable>
        </div>
      </div>
      <div style="height: auto; width: 100%; min-height: 5px" v-if="total" >
        <lt-pagination
          :total="total"
          :page.sync="currentPage"
          :limit.sync="size"
          :pagerCount="Number(field.options.pagerCount) || 7"
          :auto="false"
          :pageSizes="
            pageSizes.length == 0 || !pageSizes ? [20, 30, 50] : pageSizes
          "
          @pagination="paginationChange"
          :layout="
            field.options.pageStyle && field.options.pageStyle instanceof Array
              ? field.options.pageStyle.join()
              : 'total, sizes, prev, pager, next, jumper'
          "
        />
      </div>
    </div>
    <!-- <lt-table-abbreviation
      v-if="abbreviationShow"
      :table-ref="getTableRef()"
      :table-data="copyData"
      :offsetWidth="
        $nextTick(() => {
          getTableRef().$el.offsetWidth;
        })
      "
      @scrollChange="scrollChange"
    /> -->
  </view-wrapper>
</template>

<script>
import i18n, { translate } from '@/utils/i18n';
import viewWrapper from './view-wrapper';
import fieldMixin from '@/components/form-designer/form-widget/field-widget/fieldMixin';
import emitter from 'element-ui/lib/mixins/emitter';
import {
  newExecuteInterface,
  excel2PdfByTable,
} from '@/api/interfaces/interfaces';
import Column from './column.vue';
import bus from '@/magic-editor/scripts/bus';

import allBus from '@/magic-editor/scripts/bus.js';
import { getUserAvatars } from '@/api/system/user';
import containerMixin from '@/components/form-designer/form-widget/container-widget/containerMixin';
import ContainerItemWrapper from '@/components/form-render/container-item/container-item-wrapper';
import FieldComponents from '@/components/form-designer/form-widget/field-widget/index';
import Draggable from 'vuedraggable';
import { colsUpdate, executeSql } from '@/api/tool/form';
import TableCells from '../components/table-cells.vue';
import { getSelectTableList } from '@/api/tool/gen';
import {
  checkBusinessPermission,
  checkFieldBusinessPermission,
} from '@/utils/permission';
import { getDicts } from '@/api/system/dict/data.js';
const _popupManager = require('element-ui/lib/utils/popup/popup-manager');
import user from "@/store/modules/user";

export default {
  name: 'data-table-widget',
  componentName: 'FieldWidget', //必须固定为FieldWidget，用于接收父级组件的broadcast事件
  components: {
    viewWrapper,
    Column,
    Draggable,
    ContainerItemWrapper,
    ...FieldComponents,
    TableCells,
  },
  computed: {
    isShowPrint() {
      const { options } = this.field;
      let authCode = `${options.name}-print`;
      return (
        ('hasPrint' in options ? options.hasPrint : true) &&
        (options.authority
          ? this.checkButtonPermission(authCode, this.field)
          : true)
      );
    },
    isShowExport() {
      const { options } = this.field;
      let authCode = `${options.name}-export`;
      return (
        ('hasExport' in options ? options.hasExport : true) &&
        (options.authority
          ? this.checkButtonPermission(authCode, this.field)
          : true)
      );
    },
    widthComputed() {
      const {
        copyData,
        field: {
          options: { btns, authority, name },
        },
      } = this;
      return {
        copyData,
        btns,
        authority,
        name,
      };
    },
    getLeftBtns() {
      return this.field.options.leftBtns.filter((item) => !item.hidden) || [];
    },
    getBtns() {
      return this.field.options.btns.filter((item) => !item.hidden) || [];
    },
    tableData() {
      return this.field.options.isVirtual ? this.sliceTableData : this.copyData;
    }
    // abbreviationShow() {
    //   return (
    //     this.copyData.length > 0 &&
    //     // !this.isFieldList &&
    //     !this.field.options.treeTable &&
    //     this.field.options.abbreviation
    //   );
    // },
  },

  mixins: [emitter, fieldMixin, containerMixin, i18n],
  props: {
    field: Object,
    isFieldList: Boolean,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,
    designState: {
      type: Boolean,
      default: false,
    },
    subFormRowIndex: {
      /* 子表单组件行索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormColIndex: {
      /* 子表单组件列索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormRowId: {
      /* 子表单组件行Id，唯一id且不可变 */ type: String | Number,
      default: '',
    },
    subEditFlag: {
      type: Object,
      default: null,
    },
    echoFieldData: {
      type: Array,
      default: null,
    },
  },
  inject: [
    'refList',
    'formConfig',
    'globalOptionData',
    'globalModel',
    'busUid',
    'status',
    'allInterfaceData'
  ],
  watch: {
    'field.options.api': {
      handler(val, oldVal) {
        if (this.field.options.autoLoadData) {
          this.loadData();
        }
      },
      deep: true,
    },
    'field.options.sqlJson': {
      handler(val, oldVal) {
        if (this.field.options.autoLoadData) {
          this.loadData();
        }
      },
      deep: true,
    },
    'field.options.configData': {
      handler(val, oldVal) {
        if (val && this.field.options.cols.length) {
          this.$confirm(
            '此操作将清空当前表格的列配置以及表格数据, 是否继续?',
            '提示',
            {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning',
            },
          )
            .then(() => {
              this.field.options.cols = [];
              this.updateTableData([]);
            })
            .catch(() => {
              this.field.options.configData = false;
            });
        }
      },
    },
    copyData: {
      handler(val, oldVal) {
        if (val != this.formModel) {
          if (this.field.options.rowKey && val.length && this.designState) {
            if (
              val.some(
                (item) => !item.hasOwnProperty(this.field.options.rowKey),
              )
            ) {
              this.$message.error('请检查主键是否正确');
              return;
            }
            let ids = [];
            let fun = (arr) => {
              try {
                arr.forEach((item) => {
                  if (ids.includes(item[this.field.options.rowKey])) {
                    this.$message.error('主键值重复');
                    throw new Error('主键重复');
                  } else {
                    ids.push(item[this.field.options.rowKey]);
                  }
                  if (
                    item[this.field.options.subsetField] &&
                    item[this.field.options.subsetField].length
                  ) {
                    fun(item[this.field.options.subsetField]);
                  }
                });
              } catch (err) {}
            };
            fun(val);
          }
          this.setValue(val);
        }
        if (this.field.options.heightFit) {
          this.resizeHeight();
        }
        this.$nextTick(() => {
          let userCol = []
          this.$array.handleDFS(this.copyCol, 'children', item => {
            if(item.colType == 'user') {
              userCol.push(item)
            }
          })

          // 用户名称请求
          if (userCol.length) {
            let userIds = [];
            userCol.forEach((colItem) => {
              this.$array.handleDFS(
                this.copyData,
                this.field.options.subsetField,
                (item) => {
                  if (item[colItem.prop]) {
                    item[colItem.prop] += '';
                    if (item[colItem.prop].includes(',')) {
                      userIds.push(...item[colItem.prop].split(','));
                    } else {
                      userIds.push(item[colItem.prop]);
                    }
                  }
                },
              );
            });

            if (userIds && userIds.length > 0) {
              console.log('getUserAvatars')
              getUserAvatars({ userIds: [...new Set(userIds)] }).then(
                (data) => {
                  this.userMap = data.data;
                },
              );
            }
          }
        });
        // 判断是否在子表格中
        let that = this;
        let flag = false;
        while (that) {
          if (that?.field?.options?.hasExpand) {
            flag = true;
            break;
          }
          that = that?.$parent;
        }
        $('.el-table__body-wrapper').css('z-index', flag ? 'auto' : 1);
      },
      deep: true,
    },
    'field.options.cols': {
      handler(val, oldVal) {
        this.copyCol = JSON.parse(JSON.stringify(val));
      },
      deep: true,
      immediate: true,
    },
    copyCol: {
      handler(n, o) {
        this.flag = false;
        this.$nextTick(() => {
          this.flag = true;
        });
      },
      deep: true,
    },
    widthComputed() {
      this.getOperColumnWidth();
    },
    size() {
      this.computedHeight = '';
    },
    // 不能删,删了的话lt-sort-table组件中的isEnable会失效
    'field.options.isEnable': function () {},
    allInterfaceData(data) {
      this.initInterfaces()
    },
    tableScrollTop: {
      immediate: true,
      handler(val) {
        this.$nextTick(() => {
          if(this.field.options.isVirtual) {
            const start = Math.floor(val / this.itemHeight);
            this.sliceTableData = this.copyData.slice(start, start + 20);
            let top = start * this.itemHeight;
            let bottom = (this.copyData.length - start - 20) * this.itemHeight;
            this.getTableRef().$el.getElementsByClassName("el-table__body")[0].style.padding = `${top}px 0 ${bottom}px 0`;
          }
        })
      }
    }
  },
  data() {
    return {
      oldFieldValue: null, //field组件change之前的值
      fieldModel: null,
      rules: [],
      pageSizes: [],
      currentPage: 1,
      size: 20,
      total: 0,
      flag: true,
      loading: false,
      searchKey: '',
      queryData: [],
      ordersList: [],
      copyCol: [],
      copyData: [],
      sortArr: [],
      sort: { prop: '', order: '' },
      currentSelectData: [],
      dictMap: {}, // 字典
      userMap: {}, // 用户名称头像
      custKey: '', // 用于表格的刷新
      computedHeight: '',
      setCurrentSelectDataUnfinished: [], //初始化表格的时候设置选中内容的时候，由于数据表的数据还没有，则选中不了，将设置进入的加载到这里，等到数据加载完成在重新调用选中
      expandRowKeys: [],
      selectData: {}, // 下拉选项的接口数据源
      selectDict: {}, // 下拉选择的自定义字典
      zCIndex: 2001,
      maskZIndex: 2001,
      disableRelation: {},
      cellSelectData: [],
      tableSelectData: [],
      mouseFlag: false,
      mouseOffset: 0,
      offsetLeft: 0,
      scrollLeft: 0,
      offsetTop: 0,
      scrollTop: 0,
      copyRowIndex: null,
      copyColIndex: null,
      columnQuery: [],
      copyColumnQuery: [],
      rowSelectFlag: false,
      totalHeight: 0,
      itemHeight: 40,
      tableScrollTop: 0,
      debounce: false,
      sliceTableData: [],
    };
  },
  created() {
    /* 注意：子组件mounted在父组件created之后、父组件mounted之前触发，故子组件mounted需要用到的prop
       需要在父组件created中初始化！！ */
    this.initFieldModel();
    this.registerToRefList();
    this.initEventHandler();
    this.handleOnCreated();
    // 由于新增了widgetList 以前没有的在这里补全
    if (this.designState) {
      if (!this.field.widgetList) this.field['widgetList'] = [];
    }
  },
  mounted() {
    this.handleOnMounted();
    if (!this.field.options.hasOwnProperty('showSearch')) {
      this.$set(this.field.options, 'showSearch', false);
    }
    if (!this.field.options.hasOwnProperty('opeFix')) {
      this.$set(this.field.options, 'opeFix', true);
    }
    if (this.field.options.autoLoadData) {
      this.loadData();
    }

    // 将 this.fieldModel 引用关系链接到copyData
    if (this.fieldModel) {
      this.loading = true;

      if (this.field.options.treeTable) {
        // 树表循环子集看是否需要转换未json 流程中保存树表问题 子集成了转义字符
        let tbData =
          typeof this.fieldModel == 'string'
            ? JSON.parse(this.fieldModel)
            : this.fieldModel;
        let inter = (list) => {
          let tbData = typeof list == 'string' ? JSON.parse(list) : list;
          for (let i = 0; i < tbData.length; i++) {
            if (tbData[i][this.field.options.subsetField]) {
              tbData[i][this.field.options.subsetField] = inter(
                tbData[i][this.field.options.subsetField],
              );
            }
          }
          return tbData;
        };
        this.copyData = inter(tbData);
      } else {
        // this.updateTableData(typeof this.fieldModel == 'string'
        //     ? JSON.parse(this.fieldModel)
        //     : this.fieldModel)
        this.copyData =
          typeof this.fieldModel == 'string'
            ? JSON.parse(this.fieldModel)
            : this.fieldModel;
      }
      this.field.options.cols.forEach((item) => {
        let selectConfig = item.selectConfig;
        if (
          selectConfig?.paramConfig &&
          Object.keys(selectConfig.paramConfig).length
        ) {
          this.getColApiByParam(item.prop);
        }
      });
      setTimeout(() => {
        this.loading = false;
      }, 300);
    }
    bus.$on('showTableAbb', (flag) => {
      this.isShow = flag;
    });

    if (this.field.options.heightFit) {
      bus.$on('resizeHeight', this.resizeHeight);
      // if (this.field.options.heightFit && !this.computedHeight) {
      //   this.resizeHeight();
      // }
    } else {
      bus.$on('resizeHeight', () => {});
    }

    this.initInterfaces()

    this.createColsQuery();

    if(this.field.options.isVirtual) {
      setTimeout(() => {
      this.getTableRef().bodyWrapper.addEventListener('scroll',(e) => {
          this.tableScrollTop = e.target.scrollTop
        })
      }, 300);
    }
  },
  beforeDestroy() {
    this.unregisterFromRefList();
    bus.$off('resizeHeight');
  },
  methods: {
    initInterfaces() {
      if(this.allInterfaceData && Object.keys(this.allInterfaceData)) {
        for(let key in this.allInterfaceData) {
          if(key.includes(this.field.id)) {
            if(key === this.field.id) {
              this.handleSuccess(this.allInterfaceData[key], true)
            }
            let prop = key.split('<--->')[1]
            this.$array.handleDFS(this.field.options?.cols, 'children', item => {
              if(prop === item.prop) {
                let selectConfig = item?.selectConfig;
                if(selectConfig) {
                  const { isModel, optionsModel, optionsDict, api, paramConfig, onSuccessCallback } = selectConfig
                  if(isModel && optionsModel && !(`${prop}-${optionsModel}` in this.selectData)) {
                    this.$set(this.selectData, `${prop}-${optionsModel}`, this.allInterfaceData[key].data);
                  }
                  else if (optionsDict && !(`${prop}-${optionsDict}` in this.selectData)) {
                    let selectData = this.allInterfaceData[key].data.map((d) => {
                      return { value: d.dictValue, label: d.dictLabel };
                    });
                    this.$set(this.selectData, `${prop}-${optionsDict}`, selectData);
                  }
                  else if (api && (paramConfig ? !Object.keys(paramConfig).length : true) && !(`${prop}-${api}` in this.selectData)) {
                    this.$set(this.selectData, `${prop}-${api}`, this.allInterfaceData[key].data);
                    if (onSuccessCallback) {
                      let customFunc = new Function('res', onSuccessCallback);
                      customFunc.call(this, res);
                    }
                  }
                }
              }
            })
          }
        }
      }
    },
    setSelectDict(data) {
      this.selectDict = data;
    },
    confirmPop() {
      document.body.click();
      this.columnQueryChange();
    },
    closePop() {
      this.columnQuery = this.copyColumnQuery;
      document.body.click();
    },
    columnShowQuery() {
      this.copyColumnQuery = JSON.parse(JSON.stringify(this.columnQuery));
    },
    columnQueryChange() {
      this.copyColumnQuery = JSON.parse(JSON.stringify(this.columnQuery));
      this.queryData = [];
      this.ordersList = [];
      for (let i = 0; i < this.field.options.cols.length; i++) {
        let cq = this.columnQuery[i];
        //判断是否为空
        if (cq.blank !== 1 || cq.blank !== null) {
          if (cq.blank === 2) {
            this.queryData.push({
              columnName: cq.prop,
              queryMode: 'LT_NOT_NULL',
              type: cq.type,
            });
          }
          if (cq.blank === 3) {
            this.queryData.push({
              columnName: cq.prop,
              queryMode: 'LT_NULL',
              type: cq.type,
            });
          }
        }
        if (cq.input) {
          if (cq.type === 'date') {
            this.queryData.push({
              columnName: cq.prop,
              value: cq.input[0],
              queryMode: '>=',
              type: cq.type,
            });
            this.queryData.push({
              columnName: cq.prop,
              value: cq.input[1],
              queryMode: '<=',
              type: cq.type,
            });
          } else {
            this.queryData.push({
              columnName: cq.prop,
              value: cq.input,
              queryMode: 'LIKE',
              type: cq.type,
            });
          }
        }
        // 数字类型 上下限判断
        if (cq.type === 'number' && (cq.max || cq.min)) {
          if (cq.min) {
            this.queryData.push({
              columnName: cq.prop,
              value: cq.min,
              queryMode: '>=',
              type: cq.type,
            });
          }
          if (cq.max) {
            this.queryData.push({
              columnName: cq.prop,
              value: cq.max,
              queryMode: '<=',
              type: cq.type,
            });
          }
        }
        // 下拉判断
        if (cq.select && cq.select.length > 0) {
          if (
            cq.type === 'select' ||
            cq.type === 'checkbox' ||
            cq.type === 'radio'
          ) {
            this.queryData.push({
              columnName: cq.prop,
              value: cq.select.join(','),
              queryMode: 'LT_SELECT_IN',
              type: cq.type,
            });
          }
        }
        if (cq && cq.order) {
          this.ordersList.push({
            label: cq.label,
            property: cq.prop,
            multiOrder: cq.order,
          });
        }
      }

      // 刷新数据
      this.loadData();
    },
    createColsQuery() {
      this.columnQuery = [];
      this.copyCol
        .filter((item) => item)
        .forEach((item) => {
          this.columnQuery.push({
            input: null,
            prop: item.prop,
            label: item.label,
            type: item.searchType,
            select: [],
            selectItems: {},
            blank: 1,
          });
        });
    },
    deleteOrder(prop) {
      let c = this.columnQuery.findIndex((e) => e.prop === prop);
      if (c !== -1) {
        this.columnQuery[c].order = null;
        this.copyColumnQuery[c].order = null;
      }
      // 调接口查询
      this.columnQueryChange();
    },
    deleteInput(prop) {
      let c = this.columnQuery.findIndex((e) => e.prop === prop);
      if (c !== -1) {
        this.columnQuery[c].input = null;
        this.copyColumnQuery[c].input = null;
      }
      // 调接口查询
      this.columnQueryChange();
    },
    deleteInputNumber(prop) {
      let c = this.columnQuery.findIndex((e) => e.prop === prop);
      if (c !== -1) {
        this.columnQuery[c].min = undefined;
        this.columnQuery[c].max = undefined;
        this.copyColumnQuery[c].min = undefined;
        this.copyColumnQuery[c].max = undefined;
      }
      // 调接口查询
      this.columnQueryChange();
    },
    deleteSelect(prop) {
      let c = this.columnQuery.findIndex((e) => e.prop === prop);
      if (c !== -1) {
        this.columnQuery[c].select = [];
        this.copyColumnQuery[c].select = [];
      }
      // 调接口查询
      this.columnQueryChange();
    },
    deleteBlank(prop) {
      let c = this.columnQuery.findIndex((e) => e.prop === prop);
      if (c !== -1) {
        this.columnQuery[c].blank = null;
        this.copyColumnQuery[c].blank = null;
      }
      // 调接口查询
      this.columnQueryChange();
    },
    queryValueToLabel(item) {
      let labels = ''; // 初始化空字符串来收集标签

      // 检查item是否有select属性且为数组
      if (item.select && Array.isArray(item.select)) {
        // 检查selectItems是否为数组
        if (item.selectItems && Array.isArray(item.selectItems)) {
          // 遍历每个选中的值
          for (let i = 0; i < item.select.length; i++) {
            // 获取选中项的值
            const value = item.select[i];
            // 在selectItems数组中找到匹配的项
            const selectedItem = item.selectItems.find(
              (im) => im.value === value,
            );
            // 如果找到匹配的项，将label添加到labels字符串中
            if (selectedItem) {
              labels += selectedItem.label;
              // 如果不是最后一个选项，添加分隔符，例如逗号
              if (i < item.select.length - 1) {
                labels += ', ';
              }
            }
          }
        }
      }

      return labels; // 返回拼接好的标签字符串
    },
    async getColApiByParam(prop) {
      let col = this.field.options.cols.find((item) => item.prop == prop);
      let { selectConfig } = col;
      if (
        !selectConfig.paramConfig ||
        !Object.keys(selectConfig.paramConfig).length
      ) {
        return;
      }
      let map = {};
      for (let i = 0; i < this.copyData.length; i++) {
        let row = this.copyData[i];
        let json = JSON.parse(JSON.stringify(selectConfig.paramConfig));
        for (let key in json) {
          json[key] = row[json[key]];
        }

        if (!map[JSON.stringify(json)]) {
          let res = await newExecuteInterface({
            apiId: selectConfig.api,
            body: json,
          });
          if (res) {
            map[JSON.stringify(json)] = res.data;
            if (selectConfig.onSuccessCallback) {
              let customFunc = new Function(
                'res',
                selectConfig.onSuccessCallback,
              );
              customFunc.call(this, res);
            }
          }
        }
      }
      this.$set(this.selectData, `${prop}-${selectConfig.api}`, map);
    },
    touchstartHandler(event) {
      if (!this.field.options.mouseFlag) {
        return;
      }
      let divData = this.getTableRef().bodyWrapper;
      this.mouseFlag = true;
      this.offsetLeft = event.touches[0].pageX - divData.offsetLeft;
      this.scrollLeft = divData.scrollLeft;
      this.offsetTop = event.touches[0].pageY - divData.offsetTop;
      this.scrollTop = divData.scrollTop;
    },
    touchendHandler(e) {
      this.mouseFlag = false;
    },
    touchmoveHandler(e) {
      if (!this.field.options.mouseFlag) {
        return;
      }
      // 这里面需要注意，通过ref需要那个那个包含table元素的父元素
      let divData = this.getTableRef().bodyWrapper;
      if (!this.mouseFlag) return;
      e.preventDefault();
      const x = e.touches[0].pageX - divData.offsetLeft;
      const y = e.touches[0].pageY - divData.offsetTop;
      const walkX = (x - this.offsetLeft) * 2; // scroll-fast
      const walkY = (y - this.offsetTop) * 2; // scroll-fast
      divData.scrollLeft = this.scrollLeft - walkX;
      divData.scrollTop = this.scrollTop - walkY;
    },
    mouseDownHandler(e) {
      if (!this.field.options.mouseFlag) {
        return;
      }
      this.mouseOffset = e.clientX;
      this.mouseFlag = true;
    },
    mouseUpHandler(e) {
      this.mouseFlag = false;
    },
    mouseMoveHandler(e) {
      if (!this.field.options.mouseFlag) {
        return;
      }
      // 这里面需要注意，通过ref需要那个那个包含table元素的父元素
      let divData = this.getTableRef()?.bodyWrapper;

      if (this.mouseFlag && divData) {
        // 设置水平方向的元素的位置
        divData.scrollLeft -=
          -this.mouseOffset + (this.mouseOffset = e.clientX);
      }
    },
    checkButtonPermission(authCode, field) {
      let isDesign = this.designer != null ? this.designer.isDesign : false;
      if (isDesign === true) {
        return true;
      }
      // let formPermission = field.formPermission;
      let formPermission = (field.formPermission && field.formPermission.length> 0) ? field.formPermission : user.state.permissions;
      let authority = field.options.authority;
      return checkBusinessPermission(authority, authCode, formPermission);
    },
    /**
     * 分页变化时的处理函数。
     * 当分页发生变化时，此方法会被触发，重新加载数据，并根据配置执行自定义的分页变化回调函数。
     * @param {number} currentPage - 当前页码
     * @param {number} size - 每页显示的条目数量
     * @param {number} total - 数据总数
     */
    paginationChange() {
      this.loadData(false);

      if (this.field.options.onPaginationChange) {
        let customFunc = new Function(
          'current',
          'size',
          'total',
          this.field.options.onPaginationChange,
        );
        customFunc.call(this, this.currentPage, this.size, this.total);
      }
    },
    getRowKey(row) {
      let field;
      if (this.field.options.rowKey) {
        field = this.field.options.rowKey;
      } else if (row.hasOwnProperty('id')) {
        field = 'id';
      } else {
        field = Object.keys(row)[0];
      }
      return row[field];
    },
    rowClass({ row, column, rowIndex, columnIndex }) {
      //表头单元格的 style 的回调方法
      if (this.field.options.onRowClass) {
        let JS = new Function(
          'row',
          'column',
          'rowIndex',
          'columnIndex',
          this.field.options.onRowClass,
        );
        return JS.call(this, row, column, rowIndex, columnIndex);
      }
      return null;
    },
    change(oldData, data) {
      if (this.field.options.onSortChange) {
        let JS = new Function(
          'oldData',
          'data',
          this.field.options.onSortChange,
        );
        return JS.call(this, oldData, data);
      }
    },
    setRowEditable(row, flag, callback) {
      let id = null;
      if (typeof row === 'object') {
        id = row[this.field.options.rowKey];
      } else {
        // 兼容,之前传的不是对象,传的是主键
        id = row;
      }

      if (!id && !row['addFlag']) {
        this.$message.error(
          `所传入的主键并不是一个有效值,请检查 主键${this.field.options.rowKey}设置是否正确  或者  数据中是否带有主键`,
        );
        return;
      }

      let obj = null;

      let bfs = (arr) => {
        arr.forEach((item) => {
          if (item[this.field.options.rowKey] == id) {
            obj = item;
          }
          let children = item[this.field.options.subsetField || 'children'];
          children && bfs(children);
        });
      };
      bfs(this.copyData);
      if (obj) {
        if (callback && callback(obj) === false) return;
        this.$set(obj, 'isEdit', flag);
      }
    },
    setExpandRow(arr) {
      this.expandRowKeys = arr;
    },
    addSubFormFirstRow(subData) {
      this.addSubFormRow(subData, 0);
      // return new Promise((resolve, reject) => {
      //   resolve(this.rowIdData[0]);
      // });
    },
    addSubFormLastRow(subData) {
      let oldSubFormData = this.getData() || [];
      this.addSubFormRow(subData, oldSubFormData.length);
      // return new Promise((resolve, reject) => {
      //   resolve(this.getData().length);
      // });
    },
    delSubFormRow(formRowIndex) {
      let oldSubFormData = this.getData() || [];
      oldSubFormData.splice(
        formRowIndex == null ? oldSubFormData.length : formRowIndex,
        1,
      );
      this.updateTableData(oldSubFormData);
    },
    addSubFormRow(subData, index) {
      let oldSubFormData = this.getData() || [];
      // 新增下标位置
      index = index == null ? oldSubFormData.length : index;
      let keys = this.field.options.cols.map((item) => item.prop);
      keys.forEach((key) => {
        if (!subData.hasOwnProperty(key)) {
          subData[key] = null;
        }
      });
      subData['addFlag'] = true;
      oldSubFormData.splice(
        index == null ? oldSubFormData.length : index,
        0,
        subData,
      );
      this.updateTableData(oldSubFormData);
      return new Promise((resolve, reject) => {
        resolve(index);
      });
    },
    setSubFormRow(index, data) {
      let oldSubFormData = this.getData() || [];
      // 判断列表长度
      if (index <= oldSubFormData.length - 1) {
        oldSubFormData.splice(
          index == null ? oldSubFormData.length : index,
          1,
          data,
        );
        this.updateTableData(oldSubFormData);
      }

      return new Promise((resolve, reject) => {
        resolve(index);
      });
    },
    getSubTable(index, name) {
      if (!name) {
        name = this.field.widgetList[0].id;
      }
      let foundRef = this.refList[name + '@row' + index];
      if (!foundRef) {
        return null;
      }
      return foundRef;
    },
    setColHidden(prop, flag = true) {
      for (let i = 0; i < this.copyCol.length; i++) {
        if (this.copyCol[i].prop == prop) {
          this.copyCol[i]['hidden'] = flag;
        }
      }
    },
    /**
     * 操作列宽度的计算
     */
    getOperColumnWidth() {
      let arr = [];
      // 可能是树表格
      let subsetField = this.field.options.subsetField || 'children';
      this.$array.handleDFS(this.copyData, subsetField, (row, index) => {
        let btns = this.getBtns.filter((btn, btnIndex) => {
          return (
            this.hasButDisplay(btn, row, index) &&
            (this.field.options.authority
              ? this.checkButtonPermission(
                  `${this.field.options.name}-btn-${btn['cust-id']}`,
                  this.field,
                )
              : true)
          );
        });
        if (btns && btns.length) {
          // 文字的宽度计算
          let temp = btns
            .filter((item) => item.type === 1 || !('type' in item))
            .map((item) => item.name.length);
          let width1 = temp.length ? temp.reduce((n, m) => n + m) * 22 : 0;
          // 图标的宽度计算
          let width2 = btns.filter((item) => item.type === 2).length * 40;
          arr.push(width1 + width2 + 20);
        } else {
          arr.push(25);
        }
      });
      let num = Math.max.apply(null, arr);
      return num > 60 ? num : 60;
    },
    // 合并行列事件
    spanMethod({ row, column, rowIndex, columnIndex }) {
      if (this.field.options.onSpanMethod) {
        let JS = new Function(
          'row',
          'column',
          'rowIndex',
          'columnIndex',
          this.field.options.onSpanMethod,
        );
        return JS.call(this, row, column, rowIndex, columnIndex);
      }
      // 默认一行一列
      return { rowspan: 1, colspan: 1 };
    },
    checkStatus(row) {
      if (this.field.options.checkIsCustom && this.field.options.checkField) {
        return row[this.field.options.checkField];
      }
      if (!this.field.options.selectionLimit) {
        return true;
      }
      const state = this.currentSelectData.some(
        (item) =>
          item[this.field.options.rowKey || 'id'] ===
          row[this.field.options.rowKey || 'id'],
      );
      return (
        this.currentSelectData.length < this.field.options.selectionLimit ||
        state
      );
    },
    setPageSizes(pageSizes) {
      if (!Array.isArray(pageSizes)) {
        return;
      }
      this.pageSizes = pageSizes;
      this.size = pageSizes[0];
    },
    getData() {
      return this.copyData;
    },
    getCol() {
      return this.copyCol;
    },
    lazyLoad(tree, treeNode, resolve) {
      if (this.field.options.onLazy) {
        let js = new Function(
          'tree',
          'treeNode',
          'resolve',
          this.field.options.onLazy,
        );
        return js.call(this, tree, treeNode, resolve);
      }
    },
    setDictMap(dictMap) {
      this.dictMap = dictMap;
    },
    // 行样式设置
    tableRowStyle({ row, rowIndex }) {
      if (this.field.options.onRowStyle) {
        let JS = new Function('row', 'rowIndex', this.field.options.onRowStyle);
        return JS.call(this, row, rowIndex);
      }

      return null;
    },
    tableCellStyle({ row, column, rowIndex, columnIndex }) {
      if (
        this.field.options.onCellClick ||
        this.field.options.onCellDblclick ||
        this.field.options.tableCellSelect ||
        this.field.options.allowCopy
      ) {
        row.rowIndex = rowIndex;
        column.columnIndex = columnIndex;
      }
      if (
        this.field.options.allowCopy &&
        this.copyRowIndex == rowIndex &&
        this.copyColIndex == columnIndex
      ) {
        return {
          background: '#eee',
        };
      }
      if (
        this.field.options.tableCellSelect &&
        this.cellSelectData.length > 0
      ) {
        let obj = this.cellSelectData.find(
          (item) =>
            item.rowIndex == rowIndex &&
            item.columnIndex == columnIndex &&
            item.prop == column.property &&
            item.value == row[column.property],
        );
        if (obj) {
          return {
            backgroundColor: this.field.options.cellSelectBgColor,
            color: this.field.options.cellSelectColor,
          };
        }
      }

      if (this.field.options.onCellStyle) {
        let JS = new Function(
          'row',
          'column',
          'rowIndex',
          'columnIndex',
          this.field.options.onCellStyle,
        );
        return JS.call(this, row, column, rowIndex, columnIndex);
      }

      return null;
    },
    getCellSelectData() {
      return this.cellSelectData;
    },
    setCellSelectData(data) {
      this.cellSelectData = data;
    },
    getSearchKey() {
      return this.searchKey;
    },
    setSearchKey(key) {
      this.searchKey = key;
    },
    handleSelectionChange(val) {
      this.tableSelectData[this.currentPage - 1] = val;
      this.currentSelectData = val;
      // 通过方法触发的选择设置不在触发选中回调
      if (this.rowSelectFlag) return
      if (this.field.options.onSelection) {
        let JS = new Function('val', this.field.options.onSelection);
        JS.call(this, val);
      }
    },
    setRowSelected() {
      this.$nextTick(() => {
        this.copyData.forEach((row) => {
          this.getTableRef() &&
            this.getTableRef().toggleRowSelection(row, false);
        });
      });
    },
    currentChange(currentRow, oldCurrentRow) {
      if (this.field.options.onCurrentChange) {
        let JS = new Function(
          'currentRow',
          'oldCurrentRow',
          this.field.options.onCurrentChange,
        );
        JS.call(this, currentRow, oldCurrentRow);
      }
    },
    onRowClick(row, column) {
      if (this.designState) {
        // 设计状态不触发点击事件
        return;
      }
      if (this.field.options.allowRowSelect && this.checkStatus(row)) {
        let arr = this.tableSelectData[this.currentPage - 1] || [];
        let index = arr.findIndex(
          (item) => JSON.stringify(item) == JSON.stringify(row),
        );
        if (index != -1) {
          this.getTableRef().toggleRowSelection(row, false);
          arr.splice(index, 1);
        } else {
          this.getTableRef().toggleRowSelection(row, true);
          arr.push(row);
        }
        this.tableSelectData[this.currentPage - 1] = arr;
      }

      if (this.field.options.onRowClick) {
        let JS = new Function('row', 'column', this.field.options.onRowClick);
        JS.call(this, row, column);
      }
    },
    onRowDbClick(row) {
      if (this.field.options.isEdit && this.field.options.dbClickEdit) {
        this.$set(row, 'isEdit', true);
      }
    },
    cellDblclick(row, column, cell, event) {
      let rowIndex = this.copyData.indexOf(row)
      if (this.designState) {
        // 设计状态不触发点击事件
        return;
      }
      if (this.field.options.onCellDblclick) {
        let JS = new Function(
          'row',
          'column',
          'cell',
          'event',
          'rowIndex',
          'columnIndex',
          this.field.options.onCellDblclick,
        );
        JS.call(
          this,
          row,
          column,
          cell,
          event,
          rowIndex,
          column.columnIndex,
        );
      }
    },
    cellClick(row, column, cell, event) {
      let rowIndex = this.copyData.indexOf(row)
      if (this.designState) {
        // 设计状态不触发点击事件
        return;
      }
      if (this.field.options.allowCopy) {
        this.copyRowIndex = rowIndex;
        this.copyColIndex = column.columnIndex;
      }

      if (this.field.options.tableCellSelect) {
        if (this.field.options.preOnCellClick) {
          let JS = new Function(
            'row',
            'column',
            'cell',
            'event',
            'rowIndex',
            'columnIndex',
            this.field.options.preOnCellClick,
          );
          let preTableSelectValue = JS.call(
            this,
            row,
            column,
            cell,
            event,
            rowIndex,
            column.columnIndex,
          );
          if (!preTableSelectValue) return;
        }
        if (this.field.options.mutualExclusion) {
          let rowExisted = this.cellSelectData.findIndex(
            (e) => e.rowIndex == rowIndex,
          );
          if (rowExisted != -1) {
            this.cellSelectData.splice(rowExisted, 1);
          }
        }
        let index = this.cellSelectData.findIndex(
          (e) =>
            e.rowIndex == rowIndex && e.columnIndex == column.columnIndex,
        );
        if (index != -1) {
          this.cellSelectData.splice(index, 1);
        } else {
          this.cellSelectData.push({
            rowIndex: rowIndex,
            columnIndex: column.columnIndex,
            prop: column.property,
            value: row[column.property],
          });
        }
      }
      if (this.field.options.onCellClick) {
        let JS = new Function(
          'row',
          'column',
          'cell',
          'event',
          'rowIndex',
          'columnIndex',
          this.field.options.onCellClick,
        );
        JS.call(
          this,
          row,
          column,
          cell,
          event,
          rowIndex,
          column.columnIndex,
        );
      }
    },
    pasteInfo(e) {
      if (this.field.options.allowCopy) {
        try {
          e.preventDefault(); //阻止默认粘贴事件
          e.stopPropagation(); //阻止事件冒泡

          var data = null;
          var clipboardData = e.clipboardData || window.clipboardData; // IE
          if (!clipboardData) {
            //chrome
            clipboardData = e.originalEvent.clipboardData;
          }
          data = clipboardData.getData('Text'); //复制过来的内容
          //首先对源头进行解析
          if (data && !data.includes('\r\n')) {
            // 单独复制文本，不是复制单个单元格
            data = data + '\r\n';
          }

          var rowStrArray = data.split('\r\n'); //拆成多行
          let rows = [];
          for (var i = 0; i < rowStrArray.length - 1; i++) {
            var row = [];
            var tdStrArray = rowStrArray[i].split('\t'); //按列拆分
            for (var j = 0; j < tdStrArray.length; j++) {
              row.push(tdStrArray[j]);
            }
            rows.push(row);
          }
          // console.log(rows, '---------rows')
          this.$confirm('确认要粘贴数据?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }).then(() => {
            let emptyObj = {};
            this.field.options.cols
              .filter((item) => !item.hidden)
              .forEach((item) => {
                emptyObj[item.prop] = null;
              });
            for (var j = 0; j < rows.length; j++) {
              if (this.copyRowIndex + j > this.copyData.length - 1) {
                break;
              }
              let item = {};
              item = JSON.parse(
                JSON.stringify(this.copyData[this.copyRowIndex + j]),
              );
              let num = 0;
              let numFlag = 0; //从哪一列开始粘贴：全部列都可以粘贴(即从第0列可以粘贴)
              for (var key in emptyObj) {
                if (!rows[j][num]) {
                  break;
                }
                // console.log('numFlag--', numFlag, 'this.columnIndex--', this.columnIndex, 'num-', num);
                if (this.copyColIndex <= numFlag) {
                  // 针对不能修改的列字段做处理，可以复制粘贴的列才做赋值。根据需求加下面的if判断
                  // if (key !== 'jg' && key !== 'gz' && key !== 'xz') {
                  item[key] = rows[j][num];
                  // }
                  num = num + 1;
                }
                numFlag = numFlag + 1;
              }
              this.$set(this.copyData, this.copyRowIndex + j, item);
            }
            if (this.field.options.onCopyCallback) {
              let changeFn = new Function(
                'data',
                this.field.options.onCopyCallback,
              );
              changeFn.call(this, this.copyData);
              return;
            }
          });
        } catch (err) {
          this.$message.error('请选择粘贴位置');
        }
      }
    },
    hasButDisplay(btn, row, index) {
      try {
        if (!btn.displayFun) {
          return true;
        } else {
          let cell = new Function('row', 'index', btn.displayFun);
          return cell.call(this, row, index);
        }
      } catch (e) {
        // console.error(e, btn.displayFun, '函数执行失败');
        return true;
      }
    },
    doSearch() {
      if (this.field.options.onClickSearch) {
        let changeFn = new Function(this.field.options.onClickSearch);
        changeFn.call(this);
      }
      this.loadData();
    },
    doPrint(type) {
      if (this.designState) {
        // 设计状态不触发点击事件
        return;
      }
      if (this.field.options.onClickPrint) {
        let changeFn = new Function(this.field.options.onClickPrint);
        changeFn.call(this);
        return;
      }

      let apiId = this.field.options.api;
      if (apiId) {
        // 获取参数配置
        let param = this.getRequestParam();
        // 分页设置
        let baseParam = {
          pageNum: type == 'all' ? 1 : this.currentPage,
          pageSize: type == 'all' ? 99999 : this.size,
        };
        let cols = JSON.parse(JSON.stringify(this.copyCol));
        cols.forEach((item) => {
          if (!('hidden' in item)) {
            item.hidden = false;
          }
        });

        let excel2PdfParam = {
          body: {
            ...param,
            ...baseParam,
            searchKey: this.searchKey,
            searchParams: this.queryData,
            ordersList: this.ordersList,
          },
          apiId: apiId,
          cols: cols,
        };
        this.$ltPrintDialog.table2Pdf(excel2PdfParam);
      } else {
        this.$print.print(this.getTableRef());
      }
    },
    doExport(type) {
      if (this.designState) {
        // 设计状态不触发点击事件
        return;
      }

      if (this.field.options.onClickExport) {
        let changeFn = new Function(this.field.options.onClickExport);
        changeFn.call(this);
        return;
      }

      let apiId = this.field.options.api;
      if (apiId) {
        // 获取参数配置
        let param = this.getRequestParam();
        // 分页设置
        let baseParam = {
          pageNum: type == 'all' ? 1 : this.currentPage,
          pageSize: type == 'all' ? 99999 : this.size,
        };

        let cols = JSON.parse(JSON.stringify(this.copyCol));
        cols.forEach((item) => {
          if (!('hidden' in item)) {
            item.hidden = false;
          }
        });

        let excel2PdfParam = {
          body: {
            ...param,
            ...baseParam,
            searchKey: this.searchKey,
            searchParams: this.queryData,
            ordersList: this.ordersList,
          },
          apiId: apiId,
          cols,
          isOutExcel: true,
        };
        const loading = this.$loading({
          lock: true,
          text: '正在导出',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)',
        });
        excel2PdfByTable(excel2PdfParam)
          .then((res) => {
            const fileName = type == 'all' ? '全部数据.xlsx' : '当页数据.xlsx';
            const blob = new Blob([res], {
              type: `'application/vnd.ms-excel';charset=utf-8`,
            });
            const downloadElement = document.createElement('a');
            const href = window.URL.createObjectURL(blob);
            downloadElement.href = href;
            downloadElement.download = fileName;
            document.body.appendChild(downloadElement);
            downloadElement.click();
            document.body.removeChild(downloadElement);
            window.URL.revokeObjectURL(href);
            loading.close();
          })
          .catch((e) => {
            loading.close();
          });
      } else {
        // eslint-disable-next-line no-undef
        $(`div[cust-sort-table-id=${this.$refs.sortTableRef.id}]`).table2excel({
          exclude: '.noExl',
          filename: this.field.options.label,
        });
      }
    },
    tableExport() {
      if (this.designState) {
        // 设计状态不触发点击事件
        return;
      }
      if (this.copyData.length > 0) {
        return this.copyData;
      }
    },
    getHeader() {
      let obj = {};
      this.copyCol.forEach((item) => {
        if (item.label && item.prop) {
          if (
            this.copyData.some(
              (e) =>
                typeof e[item.prop] == Number ||
                !Number.isNaN(Number(e[item.prop])),
            )
          ) {
            obj[item.label] = {
              field: item.prop,
              callback: (value) => {
                return '&nbsp;' + value;
              },
            };
          } else {
            obj[item.label] = item.prop;
          }
        }
      });

      return obj;
    },
    setLoading(flag) {
      this.loading = flag;
    },
    click(row, index, fun, bIndex) {
      // 触发全局规则引擎
      allBus.$emit(this.busUid, [
        this,
        this.field.id + '-' + this.getBtns[bIndex]['cust-id'],
        'event',
        'dataTableClick',
        { row: row, rowIndex: index, bIndex: bIndex },
      ]);
      if (fun) {
        let JS = new Function('row', 'index', fun);
        JS.call(this, row, index);
      }
    },
    handleLeftClick(fun, index) {
      allBus.$emit(this.busUid, [
        this,
        this.field.id + '-' + this.getLeftBtns[index]['cust-id'],
        'event',
        'dataTableClick',
        { bIndex: index },
      ]);
      if (fun) {
        let JS = new Function(fun);
        JS.call(this);
      }
    },
    handleLeftDisplay(displayFun) {
      try {
        if (!displayFun) {
          return true;
        } else {
          let cell = new Function(displayFun);
          return cell.call(this);
        }
      } catch (e) {
        return true;
      }
    },
    sortChange({ column, prop, order }) {
      if (this.field.options.configData) {
        let fieldsList = this.field.options.fieldsList;
        if (fieldsList && fieldsList.length) {
          fieldsList.forEach((item) => {
            if (item.fieldName == prop) {
              let field = item.tableName + '.' + item.columnName;
              let obj = this.sortArr.find((e) => e.field == field);
              if (obj) {
                if (order) {
                  obj.order = order == 'descending' ? 'DESC' : 'ASC';
                } else {
                  this.sortArr.splice(
                    this.sortArr.findIndex((e) => e.field == field),
                    1,
                  );
                }
              } else {
                this.sortArr.push({
                  field: field,
                  order: order == 'descending' ? 'DESC' : 'ASC',
                });
              }
            }
          });
        }
      } else {
        this.sort.prop = prop;
        this.sort.order = order;
      }

      this.loadData();
    },
    loadData(flag = true) {
      if (this.status !== 'authority') {
        try {
          if (this.field.options.api && !this.field.options.configData) {
            // 获取参数配置
            let param = this.getRequestParam();
            if (this.total < this.pageSize) {
              this.currentPage = 1;
            }
            let baseParam = {
              pageNum: this.currentPage,
              pageSize: this.size,
              sort: this.sort,
              searchKey: this.searchKey,
              searchParams: this.queryData,
              ordersList: this.ordersList,
            };
            this.loading = true;

            newExecuteInterface({
              apiId: this.field.options.api,
              body: { ...baseParam, ...param },
            })
              .then((res) => {
                this.handleSuccess(res, flag);
              })
              .catch((err) => {
                this.handleError(err);
              });
          } else if (this.field.options.configData) {
            let sqlJson = JSON.parse(
              JSON.stringify(this.field.options.sqlJson),
            );
            sqlJson.filters.forEach((item) => {
              if (item.type == 'field' && item.valueType == 'variable') {
                item.value = this.$getValue(item.widgetId);
              }
            });
            if (this.total == 0 && sqlJson.pageSize) {
              this.pageSizes = [sqlJson.pageSize, 20, 30, 50];
              this.size = sqlJson.pageSize;
            }
            if (this.sortArr.length) {
              this.sortArr.forEach((item) => {
                let obj = sqlJson.orders.find((e) => e.field == item.field);
                if (obj) {
                  obj.order = item.order;
                } else {
                  sqlJson.orders.push({
                    field: item.field,
                    order: item.order,
                  });
                }
              });
            }
            this.loading = true;
            executeSql({
              ...sqlJson,
              formId: this.formConfig.formId,
              tableId: this.field.id,
              pageNum: this.total > 0 ? this.currentPage : sqlJson.pageNum,
              pageSize: this.total > 0 ? this.size : sqlJson.pageSize,
            })
              .then((res) => {
                this.handleSuccess(res);
                this.loading = false;
              })
              .catch((err) => {
                this.handleError(err);
                this.loading = false;
              });
          }
        } catch (error) {
          this.loading = false;
          console.log(error);
        }
      }
    },
    handleSuccess(res, flag) {
      if (res.data) {
        let temp = (Object.prototype.toString.call(res.data) === '[object Object]' && 'records' in res.data ? res.data.records : res.data) || [];
        if (Array.isArray(temp)) {
          this.updateTableData(temp);
        }
        if (flag) {
          this.setRowSelected();
          this.clearCurrentSelectData();
        }
        if (this.expandRowKeys.length) {
          this.setRowsExpansion(this.expandRowKeys, false);
        }
        this.total = res.data?.total || 0;
        if (res.data && res.data.tableHeader != null) {
          this.updateTableRow(res.data.tableHeader);
        }
        if (res.data.data) {
          this.updateTableData(res.data.data);
          if (res.data && res.data.tableHeader != null) {
            this.updateTableRow(res.data.tableHeader);
          }
        }
        //编辑状态下自动填充列
        if (
          this.copyData.length > 0 &&
          this.designState &&
          this.copyCol.length == 0 &&
          !this.field.options.configData
        ) {
          var cols = [];

          let keys = Object.keys(this.copyData[0]);
          // TODO 修改数据表格列配置时候，这边也同步修改
          keys.forEach((key, index) => {
            if (key) {
              let icon = '';
              // if (key.includes("time")) {
              //   icon = "time";
              // } else if (key.includes("date")) {
              //   icon = "date";
              // }
              cols.push({
                id: +new Date().getTime() + index,
                label: key,
                prop: key,
                fix: '',
                align: '',
                width: '',
                icon: icon,
                code: ``,
                sortable: false,
                overflow: false,
                link: false,
                tag: false,
                editable: true,
                editType: 'input',
                total: 0,
                isEdit: false,
                other: '',
                selectConfig: {
                  api: '',
                  onSuccessCallback: '',
                  onFailCallback: '',
                  selectLabel: 'label',
                  selectValue: 'value',
                  selectChildren: 'children',
                  multiple: false,
                  showAllLevels: true,
                  checkStrictly: false,
                  isModel: false,
                  optionsModel: undefined,
                  modelLabel: undefined,
                  modelValue: undefined,
                  options: {
                    optionItems: [
                      {
                        label: 'select 1',
                        value: '1',
                      },
                      {
                        label: 'select 2',
                        value: '2',
                      },
                      {
                        label: 'select 3',
                        value: '3',
                      },
                    ],
                  },
                  type: 'select',
                  allowCreate: false,
                  paramConfig: {},
                },
              });
            }
          });

          this.field.options.cols = cols;
        }

        // 字典
        if (Object.prototype.toString.call(res.data) === '[object Object]' && 'dictMap' in res.data) {
          this.dictMap = res.data.dictMap;
        }
      }

      this.loading = false;

      setTimeout(() => {
        this.$nextTick(() => {
          if (this.field.options.onSuccessCallback) {
            let customFunc = new Function(
              'res',
              this.field.options.onSuccessCallback,
            );
            customFunc.call(this, res);
          }
          if (this.tableSelectData.length > 0) {
            const selectDataArr = this.tableSelectData[this.currentPage - 1];
            if (selectDataArr) {
              let rowKey = this.field.options.rowKey || 'id';
              selectDataArr.forEach((row) => {
                this.copyData.forEach((item) => {
                  if (row[rowKey] == item[rowKey]) {
                    this.getTableRef().toggleRowSelection(item, true);
                  }
                });
              });
            }
          }
        });
      }, 220);
    },
    handleError(err) {
      this.loading = false;
      // this.$message.error(err.message);
      if (this.field.options.onFailCallback) {
        let customFunc = new Function('e', this.field.options.onFailCallback);
        customFunc.call(this, err);
      }
    },
    resizeHeight() {
      // 有高度则不重新计算，避免切换分页的时候一直重新计算高度，如果父标签有滚动条的话可能表格就高度就越来越高
      if (this.computedHeight) {
        return;
      }
      let hasHidden = $(
        '#data-table-' + this.stringToIntHash(this.field.id),
      ).is(':visible');
      if (!hasHidden) {
        setTimeout(() => {
          this.resizeHeight();
        }, 200);
      } else {
        let h =
          $('#data-table-' + this.stringToIntHash(this.field.id)).offset().top +
          10;
        //分页器高度
        if (this.total > 0) {
          h += 50;
        }
        this.computedHeight = `calc(100vh - ${h + 16}px)`;
      }
    },
    /**
     * 刷新表格数据
     * */
    updateRefresh(page = 1) {
      if (page) this.currentPage = page;
      this.loadData();
    },
    // 返回当前分页参数
    getPage() {
      return {
        pageNum: this.currentPage,
        pageSize: this.size,
        sort: this.sort,
      };
    },
    // 新增通过主键获取表格数据
    getByPrimaryData(id) {
      // 如果没有配置主键 或者表格没有数据则返回null
      if (!this.field.options.rowKey && id && !this.copyData) {
        return null;
      }
      // 需要保持引用状态 通过find找到下标 然后返回list[i]保持引用
      let index = this.copyData.findIndex(
        (e) => e[this.field.options.rowKey] === id,
      );
      if (index !== -1) {
        return this.copyData[index];
      } else {
        return null;
      }
    },
    // 新增通过主键更新表格数据
    updateByPrimaryData(data) {
      // 如果没有配置主键 或者表格没有数据则返回null
      if (
        !this.field.options.rowKey &&
        !data &&
        !data[this.field.options.rowKey] &&
        !this.copyData
      ) {
        return null;
      }
      // 需要保持引用状态 通过find找到下标 然后返回list[i]保持引用
      let index = this.copyData.findIndex(
        (e) => e[this.field.options.rowKey] === data[this.field.options.rowKey],
      );
      if (index !== -1) {
        this.copyData.splice(index, 1, data);
      } else {
        console.error('修改失败，没有找到对应主键的数据');
      }
    },
    // 新增通过主键删除表格数据
    delByPrimaryData(id) {
      // 如果没有配置主键 或者表格没有数据则返回null
      if (!this.field.options.rowKey && !id && !this.copyData) {
        return null;
      }
      // 需要保持引用状态 通过find找到下标 然后返回list[i]保持引用
      let index = this.copyData.findIndex(
        (e) => e[this.field.options.rowKey] === id,
      );
      if (index !== -1) {
        this.copyData.splice(index, 1);
      } else {
        console.error('删除失败，没有找到对应主键的数据');
      }
      this.getTableRef().doLayout();
    },

    updateTableRow(cols) {
      // 1. 过滤掉空对象
      cols = cols.filter((item) => Object.keys(item).length);

      // 2. 继承之前的hidden字段
      let dict = {};
      this.copyCol.forEach((item) => {
        dict[`${item?.label}-${item?.prop}`] = item?.hidden || false;
      });
      cols.forEach((item) => {
        item.hidden = dict[`${item?.label}-${item?.prop}`];
      });

      this.copyCol = cols;
    },
    updateTableData(data, isPagination = false) {
      let arr;
      if (isPagination) {
        arr = JSON.parse(JSON.stringify(data.records));
        data.current && (this.currentPage = data.current);
        data.size && (this.size = data.size);
        // this.pageSizes.unshift(data.size)
        data.total && (this.total = data.total);
      } else {
        arr = JSON.parse(JSON.stringify(data));
      }

      if (this.field.options.isEdit && this.field.options.defaultEdit) {
        arr.forEach((item) => {
          this.$set(item, 'isEdit', true);
        });
      }
      this.loading = true;
      if(arr === JSON.stringify(this.copyData)) {
        setTimeout(() => {
          this.loading = false;
        }, 300);
        return
      }
      // 更新表格内容不能直接进行赋值，需要保存原有的对象进行增改，保证对象引用不变
      this.copyData.splice(0, this.copyData.length);
      this.copyData.push(...arr);
      this.field.options.cols.forEach((item) => {
        let selectConfig = item.selectConfig;
        if (
          selectConfig?.paramConfig &&
          Object.keys(selectConfig.paramConfig).length
        ) {
          this.getColApiByParam(item.prop);
        }
      });

      if (
        this.field.options.isEnable &&
        !this.field.options.treeTable &&
        this.copyData.length &&
        !this.copyData[0].sortId
      ) {
        this.copyData.forEach((item, index) => {
          this.$set(item, 'sortId', index + 1);
        });
      }
      this.custKey = Math.random();
      this.getTableRef()?.doLayout();

      setTimeout(() => {
        this.loading = false;
      }, 300);
    },
    silenceUpdateTableData(data) {
      let arr = JSON.parse(JSON.stringify(data));
      this.copyData.splice(0, this.copyData.length);
      this.copyData.push(...arr);
      // this.custKey = Math.random();
    },
    getCurrentSelectData() {
      let selectData = [];
      this.tableSelectData.forEach((item) => {
        if (item.length > 0) {
          item.forEach((item) => {
            selectData.push(item);
          });
        }
      });

      // if(!this.field.options.rowKey && (selectData.length ? !selectData[0].hasOwnProperty('id') : true)) {
      //   throw new Error('数据表格使用多选功能时需要配置主键才能使用');
      // }
      return selectData;
    },
    setChildren(children, type) {
      // 编辑多个子层级
      children.map((j) => {
        this.toggleSelection(j, type);
        if (j.children) {
          this.setChildren(j.children, type);
        }
      });
    },
    // 选中父节点时，子节点一起选中取消
    select(selection, row) {
      if (!this.field.options.linkAble) {
        return;
      }
      const hasSelect = selection.some((el) => {
        return (
          row[this.field.options.rowKey || 'id'] ===
          el[this.field.options.rowKey || 'id']
        );
      });
      if (hasSelect) {
        if (row.children) {
          // 解决子组件没有被勾选到
          this.setChildren(row.children, true);
        }
      } else {
        if (row.children) {
          this.setChildren(row.children, false);
        }
      }
    },
    toggleSelection(row, select) {
      if (row) {
        this.$nextTick(() => {
          this.getTableRef() &&
            this.getTableRef().toggleRowSelection(row, select);
        });
      }
    },
    setRowsExpansion(row, flag = false, expanded = true) {
      if (row) {
        let rowKey = this.field.options.rowKey || 'id';
        const dfs = (root) => {
          root.forEach((item) => {
            if (flag) {
              this.$nextTick(() => {
                this.getTableRef() &&
                  this.getTableRef().toggleRowExpansion(item, expanded);
              });
              item.children && dfs(item.children);
            } else {
              if (row.includes(item[rowKey])) {
                this.$nextTick(() => {
                  this.getTableRef() &&
                    this.getTableRef().toggleRowExpansion(item, expanded);
                });
              }
              item.children && dfs(item.children);
            }
          });
        };
        if (flag) {
          dfs(this.copyData.filter((e) => row.includes(e[rowKey])));
        } else {
          dfs(this.copyData);
        }
      }
    },
    // 选择全部
    selectAll(selection) {
      if (!this.field.options.linkAble) {
        return;
      }
      // tabledata第一层只要有在selection里面就是全选
      const isSelect = selection.some((el) => {
        const tableDataIds = this.copyData.map(
          (j) => j[this.field.options.rowKey || 'id'],
        );
        return tableDataIds.includes(el[this.field.options.rowKey || 'id']);
      });
      // tableDate第一层只要有不在selection里面就是全不选
      const isCancel = !this.copyData.every((el) => {
        const selectIds = selection.map(
          (j) => j[this.field.options.rowKey || 'id'],
        );
        return selectIds.includes(el[this.field.options.rowKey || 'id']);
      });
      if (isSelect) {
        selection.map((el) => {
          if (el.children) {
            // 解决子组件没有被勾选到
            this.setChildren(el.children, true);
          }
        });
      }
      if (isCancel) {
        this.copyData.map((el) => {
          if (el.children) {
            // 解决子组件没有被勾选到
            this.setChildren(el.children, false);
          }
        });
      }
    },
    // 设置表格选中的值
    setCurrentSelectData(rows, isSelectionChange = true) {
      const { rowKey } = this.field.options;
      if (!rowKey) {
        this.$message({ message: '请设置数据表格的主键' });
        return;
      }

      if (!this.copyData || this.copyData.length == 0) {
        this.setCurrentSelectDataUnfinished.push(...rows);
      } else {
        setTimeout(() => {
          if(!isSelectionChange) this.rowSelectFlag = true
          this.copyData.forEach((item) => {
            if (rows.includes(item[rowKey])) {
              this.getTableRef().toggleRowSelection(item, true);
            } else {
              this.getTableRef().toggleRowSelection(item, false);
            }
          });
          if(!isSelectionChange) this.rowSelectFlag = false
        },0)
      }
    },
    // 消除设置初始化的时候未完成的选中项
    eliminateUnfinishedCheck() {
      if (
        this.setCurrentSelectDataUnfinished &&
        this.setCurrentSelectDataUnfinished.length > 0
      ) {
        const { rowKey } = this.field.options;
        this.copyData.forEach((item) => {
          if (setCurrentSelectDataUnfinished.includes(item[rowKey])) {
            this.getTableRef().toggleRowSelection(item, true);
          } else {
            this.getTableRef().toggleRowSelection(item, false);
          }
        });
      }
    },
    clearCurrentSelectData() {
      this.currentSelectData = [];
      this.getTableRef().clearSelection();
    },
    // 因为套了层sort-table,所以需要这样调用才能拿到 el-table表格 的ref
    getTableRef() {
      return this.$refs.sortTableRef && this.$refs.sortTableRef.$refs.tableRef;
    },
    getPageNum() {
      return this.currentPage;
    },
    getPageSize() {
      return this.size;
    },
    stringToIntHash(str, upperbound, lowerbound) {
      let result = 0;
      for (let i = 0; i < str.length; i++) {
        result = result + str.charCodeAt(i);
      }

      if (!lowerbound) lowerbound = 0;
      if (!upperbound) upperbound = 500;

      return (result % (upperbound - lowerbound)) + lowerbound;
    },
    getSummaries(param) {
      const { columns, data } = param;
      const sums = [];
      const { showIndex, hasSelection, subsetField, hasExpand, treeTable } =
        this.field.options;
      let colData = [];
      this.$array.handleDFS(this.copyCol, 'children', (item) => {
        if (!item.hidden && !item.children) {
          colData.push(item);
        }
      });

      // data转成扁平化的数据,以兼容树表格
      // let tableData = this.$array.treeToArray(data, subsetField);
      columns.forEach((column, index) => {
        if (column.label === '序号' && showIndex) {
          sums[index] = '';
        } else if (column.type === 'selection' && hasSelection) {
          sums[index] = '';
        } else {
          const values = data.map((item) => Number(item[column.property]));
          // 考虑序号和多选的导致,索引进行后移
          let realIndex = index;
          if (hasSelection) --realIndex;
          if (showIndex) --realIndex;
          if (hasExpand && this.field.widgetList.length) --realIndex;
          if (
            !values.every((value) => isNaN(value)) &&
            colData[realIndex]?.total &&
            !colData[realIndex]?.totalPlaceholder
          ) {
            sums[index] = values.reduce((prev, curr) => {
              const value = Number(curr);
              if (!isNaN(value)) {
                return prev + curr;
              } else {
                return prev;
              }
            }, 0);
            sums[index] = sums[index].toFixed(colData[realIndex]?.totalFixed || 2);
          } else {
            sums[index] = colData[realIndex]?.totalPlaceholder || '';
          }
        }
      });

      return sums;
    },
    scrollChange(e) {
      this.getTableRef().bodyWrapper.scrollLeft = e;
    },
    handleExpandChange(row, expandedRows) {
      // 保存选中的项的rowKey
      if (Array.isArray(expandedRows)) {
        this.expandRowKeys = expandedRows.map(
          (item) => item[this.field.options.rowKey],
        );
      } else {
        if (expandedRows) {
          this.expandRowKeys.push(row[this.field.options.rowKey]);
        } else {
          this.expandRowKeys = this.expandRowKeys.filter(
            (key) => key !== row[this.field.options.rowKey],
          );
        }
      }

      // element ui 累加z-index
      // 弹窗
      this.zCIndex = _popupManager.default.nextZIndex();
      if (this.designState) {
        // 设计状态不触发点击事件
        return;
      }
      if (expandedRows && expandedRows.length) {
        $('.el-table__body-wrapper').css('z-index', 'auto');
      } else {
        $('.el-table__body-wrapper').css('z-index', 1);
      }
      if (this.field.options.onExpandChange) {
        let JS = new Function(
          'row',
          'expandedRows',
          this.field.options.onExpandChange,
        );
        JS.call(this, row, expandedRows);
      }
    },
    handleHeaderDragend(newWidth, oldWidth, column, event) {
      if (
        this.getBtns.length > 0 &&
        this.copyCol.findIndex((item) => item.fix) !== -1
      ) {
        this.copyCol.forEach((item) => {
          if (item.prop === column.property && item.label === column.label) {
            item.width = newWidth;
          }
        });
      }
      this.copyCol.forEach((item) => {
        if (item.prop === column.property && item.label === column.label) {
          item.width = newWidth;
        }
      });
      // 设计模式时，拖拽表格改变表格列宽度 --- 设计模式之前已经设置过宽度不足的话就全被置为空了
      if (this.designState) {
        this.field.options.cols.forEach((item) => {
          if (item.prop === column.property && item.label === column.label) {
            item.width = newWidth;
          }
        });
      } else {
        // 如果全部设置了宽度，并且宽度小于表格宽度，最后一列的宽度改为空即自适应
        if (this.copyCol.every((item) => item.width)) {
          let totalWidth = 0;
          this.copyCol.forEach((item) => {
            totalWidth += parseInt(item.width);
          });
          let tableWidth = document.querySelector(
            `#data-table-${this.stringToIntHash(this.field.id)}`,
          ).offsetWidth;
          if (totalWidth < tableWidth) {
            this.copyCol[this.copyCol.length - 1].width = '';
          }
        }
        this.custKey = Math.random();
      }
    },
    colChange() {
      // let formDesign = this.$route.path;
      // if (formDesign.indexOf("/formDesign") === -1) {
      //   let index = formDesign.lastIndexOf("/");
      //   let templateId = formDesign.slice(index + 1);
      //   let data = {
      //     templateId: templateId,
      //     tableId: this.field.id,
      //     cols: this.copyCol,
      //   };
      //   colsUpdate(data);
      // }
    },
    /**
     * 主动触发editChange事件
     * @param {*} val
     * @param {*} fieldName
     * @param {*} rowIndex
     * @param {*} colIndex
     * @param {*} rowData
     * @param {*} colData
     * @param {*} extra
     */
    triggerEditChange(
      val,
      fieldName,
      rowIndex,
      colIndex,
      rowData,
      colData,
      extra,
    ) {
      if (this.field.options.onEditChange) {
        let JS = new Function(
          'val',
          'fieldName',
          'rowIndex',
          'colIndex',
          'rowData',
          'colData',
          'extra',
          this.field.options.onEditChange,
        );
        if (this.copyData && this.copyData.length && this.copyData[rowIndex]) {
          rowData = this.copyData[rowIndex];
        }

        JS.call(
          this,
          val,
          fieldName,
          rowIndex,
          colIndex,
          rowData,
          colData,
          extra,
        );
      }
    },
    triggerBlurChange(
      val,
      fieldName,
      rowIndex,
      colIndex,
      rowData,
      colData,
      extra,
    ) {
      if (this.field.options.onBlurChange) {
        let JS = new Function(
          'val',
          'fieldName',
          'rowIndex',
          'colIndex',
          'rowData',
          'colData',
          'extra',
          this.field.options.onBlurChange,
        );
        if (this.copyData && this.copyData.length && this.copyData[rowIndex]) {
          rowData = this.copyData[rowIndex];
        }

        JS.call(
          this,
          val,
          fieldName,
          rowIndex,
          colIndex,
          rowData,
          colData,
          extra,
        );
      }
    },
    /**
     * 设置编辑列不可编辑关系
     * @param {*} id
     * @param {*} relation
     */
    setDisableRelation(id, relation) {
      this.$set(this.disableRelation, id, relation);
    },
    /**
     * 数字处理
     * @param {*} val    值
     * @param {*} digit  保留几位小数
     * @param {*} round  是否四舍五入
     */
    handleNumber(val, digit, round) {
      if (/^[\+-]?(\d+\.?\d*|\.\d+|\d\.\d+e\+\d+)$/.test(val)) {
        let num = val;
        num = num.toString();
        // 小数后截取
        let index = num.indexOf('.');
        if (index !== -1) {
          num = num.substring(0, digit + index + 1);
        } else {
          num = num.substring(0);
        }

        // 去掉最后一个.
        if (num.at(-1) === '.') {
          num = num.substring(0, num.length - 1);
        }
        // 四舍五入
        if (digit && round) {
          num = Math.round(num * Math.pow(10, digit)) / Math.pow(10, digit);
        }
        return num;
      }
      return val;
    },

    disabledFieldSelect(row) {
      if (this.copyCol && this.copyCol.length) {
        // 截断现象的处理(列全给了宽度, 但是累加小于总的宽度)
        if (this.copyCol.every((item) => item.width)) {
          setTimeout(() => {
            let totalWidth = 0;
            this.copyCol.forEach((item) => {
              if (!item?.hidden) totalWidth += parseInt(item.width);
            });
            let tableWidth = document.querySelector(
              `#data-table-${this.stringToIntHash(this.field.id)}`,
            ).offsetWidth;

            if (totalWidth < tableWidth) {
              for (let i = this.copyCol.length - 1; i >= 0; i--) {
                if (!this.copyCol[i]?.hidden) {
                  this.copyCol[i].width = '';
                }
              }
            }
          }, 100);
        }

        let len = this.copyCol.filter((item) => !item.hidden);
        if (len.length == 1 && len[0].id == row.id) {
          return !row.hidden;
        }
        return false;
      }
      return false;
    },
    userChange(data) {
      // 主要考虑懒加载时,内置类型用户名称的显示名称问题
      this.userMap = { ...this.userMap, ...data };
    },
  },
};
</script>
<style>
@page {
  size: auto;
  margin: 0mm;
}
</style>
<style lang="scss" scoped>
::v-deep .hide-checkbox {
  th .el-checkbox {
    display: none;
  }
}

.searchBox {
  width: 200px;

  ::v-deep .el-input__inner {
    height: 28px;
    line-height: 28px;
  }
}

.orderBox {
  display: flex;
  flex-wrap: wrap;
  align-items: center;

  .orderItem {
    max-width: 400px;
    white-space: normal;
    height: 24px;
    padding: 2px 4px;
    display: inline-flex;
    align-items: center;
    height: auto;
    margin-right: 6px;
    margin-bottom: 5px;
    font-size: 13px;
    line-height: 20px;
    white-space: nowrap;
    background: #f4f6f9;
    border-radius: 4px;
    opacity: 1;
    transition: all 0.3s;
    color: #494f57;
    cursor: pointer;

    &:hover {
      background: #e6eaf0;
    }

    & > span:nth-of-type(2) {
      margin: 0 5px;
    }
  }
}

::v-deep .el-dropdown-menu__item {
  color: var(--primary-color);
  text-align: center;
}

.titleBox {
  position: relative;
  height: 36px;

  .leftBox {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
  }

  .rightBox {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);

    ::v-deep .el-button--mini {
      padding: 7px 10px;
      background-color: #eaf1ff;
      border-color: #eaf1ff;
      color: var(--primary-color);
    }
  }
}

::v-deep .fixed-pre-first-field {
  z-index: 4;
}

::v-deep .el-table--scrollable-x {
  .fixed-pre-first-field {
    z-index: 0;
  }
}

::v-deep .el-table__body-wrapper {
  z-index: 1;
}

.table-container {
  background-color: #ffffff;
  .tableBox {
    border: 1px solid RGB(228, 231, 237);
    border-radius: 5px
  }
  .el-table__body-wrapper {
    overflow-y: auto;
    .el-table__body {
      position: absolute;
    }
  }
  .el-table__row {
    height: 40px!important;
    line-height: 40px!important;
  }
}
</style>
