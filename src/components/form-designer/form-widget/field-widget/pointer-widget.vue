<template>
  <view-wrapper
    :designer="designer"
    :field="field"
    :design-state="designState"
    :parent-widget="parentWidget"
    :parent-list="parentList"
    :index-of-parent-list="indexOfParentList"
  >
    <lt-empty v-if="showEmpty" description="请上传图片" />
    <div 
      v-else
      :style="{
        width: field.options.imageWidth ? `${field.options.imageWidth}px` : '100%',
        height: field.options.imageHeight +'px',
        backgroundImage: `url(${imageUrl})`,
        backgroundSize: '100% 100%',
        backgroundPosition: 'center',
        position: 'relative'
      }"
      @click="getCoordinates"
    >
      <template v-if="field.options.markers">
        <template v-for="(item, index) in field.options.markers">
          <!-- 圆形/正方形标记 -->
          <div 
            v-if="item.type === 'circle' || item.type === 'square'"
            :key="`marker-${index}`"
            @click="onNodeClick(item)"
            :style="{
              position: 'absolute',
              top: `${item.top}px`,
              left: item.left.includes('%') ? item.left : `${item.left}px`,
              width: `${item.size || 10}px`,
              height: `${item.size || 10}px`,
              border: `2px solid ${item.color}`,
              borderRadius: item.type === 'circle' ? '50%' : '2px',
              backgroundColor: item.solid ? item.color : 'transparent',
              cursor: 'pointer',
              transform: 'translate(-5px, -5px)',
              boxShadow: '0 3px 6px rgba(0,0,0,0.3)',
              transition: 'all 0.2s'
            }"
            class="marker-point"
          ></div>

          <!-- 三角形标记 -->
          <div 
            v-else-if="item.type === 'triangle'"
            :key="`triangle-${index}`"
            @click="onNodeClick(item)"
            :style="{
              position: 'absolute',
              top: `${item.top}px`,
              left: item.left.includes('%') ? item.left : `${item.left}px`,
              transform: 'translate(-50%, -50%)',
              cursor: 'pointer',
              transition: 'all 0.2s cubic-bezier(0.18, 0.89, 0.32, 1.28)',
            }"
          >
            <svg 
              :width="item.size || 10" 
              :height="(item.size || 10) * 0.866"
              viewBox="0 0 100 86.6"
              style="overflow: visible;"
            >
              <path
                :fill="item.solid ? item.color : 'transparent'"
                :stroke="item.solid ? 'none' : item.color"
                :stroke-width="item.solid ? 0 : 2"
                d="M50 0 L100 86.6 L0 86.6 Z"
                class="triangle-path"
              />
            </svg>
          </div>

          <!-- 自定义HTML标记 -->
          <div 
            v-else-if="item.type === 'customHtml'"
            :key="`custom-${index}`"
            @click="onNodeClick(item)"
            :style="{
              position: 'absolute',
              top: `${item.top}px`,
              left: item.left.includes('%') ? item.left : `${item.left}px`,
              transform: 'translate(-50%, -50%)',
              cursor: 'pointer'
            }"
            v-html="renderCustomHtml(item)"
          ></div>
        </template>
      </template>
    </div>
  </view-wrapper>
</template>

<script>
import viewWrapper from "./view-wrapper";
import i18n, { translate } from "@/utils/i18n";
import fieldMixin from "@/components/form-designer/form-widget/field-widget/fieldMixin";
import emitter from "element-ui/lib/mixins/emitter";
import { getFile } from '@/api/file/file.js';
export default {
  name: "pointer-widget",
  componentName: "FieldWidget", //必须固定为FieldWidget，用于接收父级组件的broadcast事件
  components: {
    viewWrapper,
  },
  mixins: [emitter, fieldMixin, i18n],
  props: {
    field: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,
    designState: {
      type: Boolean,
      default: false,
    },
  },
  inject: ["refList", "formConfig", "globalOptionData", "globalModel"],
  data() {
    return {
      oldFieldValue: null, //field组件change之前的值
      imageUrl: '',
      showEmpty: false,
    };
  },
  watch: {
    'field.options.fileId': {
      handler(n) {
        this.getUrl();
      },
    },
    'field.options.imageUrl': {
      handler() {
        this.getUrl();
      },
    },
  },
  created() {
    this.registerToRefList();
    this.initEventHandler();
    this.handleOnCreated();
  },
  mounted() {
    this.handleOnMounted();
    this.getUrl();
  },
  beforeDestroy() {
    this.unregisterFromRefList();
  },
  methods: {
    getUrl() {
      if(this.field.options.imageType === 'fileId' && this.field.options.fileId) {

        getFile({
          fileId: this.field.options.fileId,
        }).then((res) => {
          
            let item = res.data[0];
            this.imageUrl = this.$store.state.app.uploadUrl + item.pathName
            
            this.showEmpty = false;
        });
      } else if(this.field.options.imageType === 'http'&&this.field.options.imageUrl) {
        this.imageUrl = this.field.options.imageUrl;
        this.showEmpty = false;
      } else{
        this.showEmpty = true;
      }

    },
    setValue(value) {
        if(!value.startsWith('http')){
            this.field.options.imageType = 'fileId'
            this.field.options.fileId=value
        }else{
            this.field.options.imageType = 'http'
            this.field.options.imageUrl=value
        }
        this.getUrl()

    },

    onNodeClick(node){
      if (this.field.options.onNodeClick) {
        let changeFn = new Function('node', this.field.options.onNodeClick);
        changeFn.call(this, node);
      }
    },
    renderCustomHtml(item) {
      try {
        const htmlFunction = new Function('item', item.html || 'return "";')
        console.log(htmlFunction(item))
        return htmlFunction(item) || ''
      } catch (e) {
        console.error('Custom HTML render error:', e)
        return ''
      }

    },
    setMarkers(value) {
      if (Array.isArray(value)) {
        this.$set(this.field.options, 'markers', [...value])
      } else if (typeof value === 'object' && value !== null) {
        if (!this.field.options.markers) {
          this.$set(this.field.options, 'markers', [])
        }
        this.field.options.markers.push({...value})
      }
      
      // 触发视图更新
      this.$forceUpdate()
    },
    getCoordinates(event) {
      const rect = event.currentTarget.getBoundingClientRect();
      const xPercent = ((event.clientX - rect.left) / rect.width) * 100; // 计算横坐标百分比
      const yPx = event.clientY - rect.top; // 计算纵坐标像素值
      if (this.field.options.onPointerClick) {
        let changeFn = new Function('left', 'top', this.field.options.onPointerClick);
        changeFn.call(this, `${xPercent.toFixed(2)}%`, yPx);
      }
    },
  },
};
</script>

<style scoped>

.marker-point:hover {
  transform: translate(-5px, -5px) scale(1.1) !important;
  z-index: 1;
}

/* 添加SVG样式 */
.marker-point.triangle svg {
  filter: drop-shadow(0 3px 6px rgba(0,0,0,0.3));
}

.triangle-path {
  transition: all 0.2s ease-in-out;
}


.marker-point.triangle .triangle-path {
  --original-fill: inherit;
  --original-stroke: inherit;
  will-change: transform, filter; /* 优化渲染性能 */
}

</style>
