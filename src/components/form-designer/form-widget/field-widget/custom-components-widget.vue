<template>
  <form-item-wrapper
    :designer="designer"
    :field="field"
    :rules="rules"
    :design-state="designState"
    :parent-widget="parentWidget"
    :parent-list="parentList"
    :index-of-parent-list="indexOfParentList"
    :sub-form-row-index="subFormRowIndex"
    :sub-form-col-index="subFormColIndex"
    :sub-form-row-id="subFormRowId"
    :echo-field-data="echoFieldData"
    :sub-edit-flag="subEditFlag"
  >
    <VFormRender
      v-if="!loading && templateJson"
      ref="customComponentLast"
      :form-json="templateJson"
      :form-data="initData"
      :writeState="false"
      :key="randomKey"
      :class="designState ? 'minHeight' : ''"
    />
    <div v-else>
      <el-skeleton :rows="6" animated v-if="field.options.loadingType == 'skeleton'" />
      <div v-loading="pageloading" class="minHeight" v-else-if="field.options.loadingType == 'load'" ></div>
    </div>
  </form-item-wrapper>
</template>

<script>
import FormItemWrapper from './form-item-wrapper';
import emitter from 'element-ui/lib/mixins/emitter';
import i18n from '@/utils/i18n';
import fieldMixin from '@/components/form-designer/form-widget/field-widget/fieldMixin';
import { getByTemplateIdInfo, getInfo } from '@/api/tool/form';
export default {
  name: 'custom-component-widget',
  componentName: 'FieldWidget', //必须固定为FieldWidget，用于接收父级组件的broadcast事件
  mixins: [emitter, fieldMixin, i18n],
  props: {
    field: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,
    designState: {
      type: Boolean,
      default: false,
    },

    subFormRowIndex: {
      /* 子表单组件行索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormColIndex: {
      /* 子表单组件列索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormRowId: {
      /* 子表单组件行Id，唯一id且不可变 */ type: String,
      default: '',
    },
    subEditFlag: {
      type: Object,
      default: null,
    },
    echoFieldData: {
      type: Array,
      default: () => [],
    },
  },
  components: {
    FormItemWrapper,
    VFormRender: () => import("@/components/form-render/index"),
  },
  inject: ['refList', 'globalOptionData', 'globalModel'],
  data() {
    return {
      oldFieldValue: null, //field组件change之前的值
      fieldModel: null,
      rules: [],
      templateJson: null,
      formId: null,
      templateId: null,
      initData: {},
      keyToMap: {},
      aliasToMap: {},
      randomKey: '',
      loading: false,
      pageloading: true,
    };
  },
  beforeCreate() {
    /* 这里不能访问方法和属性！！ */
  },

  created() {
    /* 注意：子组件mounted在父组件created之后、父组件mounted之前触发，故子组件mounted需要用到的prop
         需要在父组件created中初始化！！ */
    this.initFieldModel();
    this.initAsData();
    this.registerToRefList();
    this.initEventHandler();
    this.buildFieldRules();

    this.handleOnCreated();
  },

  mounted() {
    this.handleOnMounted();
    this.setFormId(this.field.formId, {})
    // this.$bus.$off('onReceive')
    this.$bus.$on('onReceive', this.onReceive);
  },

  beforeDestroy() {
    this.$bus.$off('onReceive')
    this.unregisterFromRefList();
  },

  methods: {
    initView() {
      this.loading = true
      this.templateJson = null;
      if (this.templateId) {
        getByTemplateIdInfo(this.templateId)
          .then((res) => {
            let templateJson = {};
            if (res.data.templateJson) {
              templateJson = JSON.parse(res.data.templateJson);
            }
            func(templateJson);
            this.$nextTick(() => {
              this.randomKey = Math.random();
            });
            this.loading = false;
            this.pageloading = false;
          })
          .catch((err) => {
            this.loading = false;
            this.pageloading = false;
          });
      } else {
        getInfo(this.formId, {})
          .then((res) => {
            let templateJson = {};
            if (res.data.templateJson) {
              templateJson = JSON.parse(res.data.templateJson);
            }
            func(templateJson);
            this.$nextTick(() => {
              this.randomKey = Math.random();
            });
            this.loading = false;
            this.pageloading = false;
          })
          .catch((err) => {
            this.loading = false;
            this.pageloading = false;
          });
      }
      var func = (templateJson) => {
        // 迭代出别名和elementkey
        let cr = (arr) => {
          arr.forEach((item) => {
            // 如果有别名 设置别名
            if (item.options.alias) {
              this.keyToMap[item.options.alias] = item.id;
              this.aliasToMap[item.id] = item.options.alias;
            }
            if (item.cols && item.cols.length > 0) {
              cr(item.cols);
            }
            if (item.tabs && item.tabs.length > 0) {
              cr(item.tabs);
            }
            if (item.widgetList && item.widgetList.length > 0) {
              cr(item.widgetList);
            }
            if (item.type == 'table' && item.rows && item.rows.length > 0) {
              item.rows.forEach((e) => {
                cr(e.cols);
              });
            }
          });
        };
        //迭代
        cr(templateJson.widgetList);

        Object.getOwnPropertyNames(this.initData)
          .filter((item) => item != '__ob__')
          .forEach((name) => {
            if (
              name.includes('subform') ||
              (this.keyToMap[name] && this.keyToMap[name].includes('subform'))
            ) {
              this.initData[name].forEach((it) => {
                for (let k in it) {
                  if (this.keyToMap[k]) {
                    it[this.keyToMap[k]] = it[k];
                    delete it[k];
                  }
                }
              });
            }
            if (this.keyToMap[name]) {
              this.initData[this.keyToMap[name]] = this.initData[name];
              delete this.initData[name];
            }
          });
        this.templateJson = templateJson;
      };
    },

    setFormId(formId, initData) {
      this.formId = formId;
      this.initData = initData;
      this.initView();
    },
    setTemplateId(templateId, initData) {
      this.templateId = templateId;
      this.initData = initData;
      this.initView();
    },
    getCustomComponentRef(name) {
      return new Promise((resolve, reject) => {
        let timer = setInterval(() => {
          const dfs = (root) => {
            if (
              (root?.field?.id &&
                root.field.id === name) ||
              (root?.widget?.id &&
                root.widget.id === name)
            ) {
              clearInterval(timer);
              resolve(root);
            }
            root.$children && root.$children.forEach(dfs);
          };
          dfs(this);
        }, 0);
        setTimeout(() => {
          clearInterval(timer);
          reject(
            '获取子组件‘' + name + '’失败,请检查所传入的‘唯一名称’与子组件的‘唯一名称’值是否一致',
          );
        }, 6000);
      });
    },
    getCustomComponentRefs(names) {
      return new Promise((resolve, reject) => {
        let widget = {};
        let timeout = null;
        let timer = null;
        timer = setInterval(()=>{
          const dfs = (root) => {
            if (root?.field?.id && names.includes(root?.field?.id))
            {
              widget[root.field.id] = root
            }
            if (root?.widget?.id && names.includes(root.widget.id)) {
              widget[root.widget.id] = root
            }
            if (Object.keys(widget).length === names.length) {
              clearInterval(timer)
              clearTimeout(timeout)
              resolve(widget)
            }
            root.$children && root.$children.forEach(dfs);
          };
          dfs(this);
        }, 0)
        timeout = setTimeout(() => {
          clearInterval(timer);
          let notFound = [];
          if (Object.keys(widget).length > 0) {
            names.forEach((item) => {
              if (!(Object.keys(widget).includes(item))) {
                notFound.push(item)
              }
            })
          } else {
            notFound = names
          }
          notFound = notFound.join(',')
          reject(
            '未获取到子组件‘' + notFound + '’,请检查所传入的‘唯一名称’与子组件的‘唯一名称’值是否一致',
          );
        }, 6000);
      })
    },
    onReceive(e) {
      if (this.field.options.onReceive) {
        let JS = new Function('event', 'params', this.field.options.onReceive);
        JS.call(this, e.event, e.params);
      }
    },
  },
};
</script>

<style lang="scss">
.color {
  .el-input__inner {
    color: var(--color);
  }
}
.minHeight {
  min-height: 100px;
}
</style>
