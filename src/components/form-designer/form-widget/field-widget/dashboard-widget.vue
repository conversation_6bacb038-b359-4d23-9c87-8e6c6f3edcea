<template>
  <view-wrapper :designer="designer" :field="field" :design-state="designState" :parent-widget="parentWidget"
    :parent-list="parentList" :index-of-parent-list="indexOfParentList">
    <div class="dashboard-box">
      <div class="content" :style="{
        'border-left': '4px solid ' + field.options.bandColor + ''
      }">
        <div class="content-first">
          {{ field.options.optionItems[getLabelField] }}
        </div>
        <div class="content-second">
          {{ field.options.optionItems[getValueField] }}
        </div>
        <div :style="{
          color: field.options.descColor,
        }" class="content-three">
          {{ field.options.optionItems[getDescField] }}
        </div>
      </div>
    </div>
  </view-wrapper>
</template>
<script>
import viewWrapper from './view-wrapper';
import fieldMixin from '@/components/form-designer/form-widget/field-widget/fieldMixin';
import { newExecuteInterface } from '@/api/interfaces/interfaces';
export default {
  name: 'dashboard-widget',
  componentName: 'FieldWidget', //必须固定为FieldWidget，用于接收父级组件的broadcast事件
  components: {
    viewWrapper,
  },
  mixins: [fieldMixin],
  props: {
    field: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,
    designState: {
      type: Boolean,
      default: false,
    },
  },
  created() {
    /* 注意：子组件mounted在父组件created之后、父组件mounted之前触发，故子组件mounted需要用到的prop
       需要在父组件created中初始化！！ */
    this.registerToRefList();
    this.initEventHandler();
    this.handleOnCreated();
  },
  computed: {
    getLabelField() {
      return this.field.options.selectLabel || 'label';
    },
    getValueField() {
      return this.field.options.selectValue || 'value';
    },
    getDescField() {
      return this.field.options.selectDesc || 'desc';
    },
  },
  mounted() {
    this.handleOnMounted();
    if (this.field.options.autoLoadData) {
      this.sendApi();
    }
  },
  methods: {
    sendApi() {
      if (this.field.options.api) {
        // 获取参数配置
        let param = this.getRequestParam();
        newExecuteInterface({
          apiId: this.field.options.api,
          body: param,
        })
          .then((res) => {
            this.field.options.optionItems = res.data
            let customFunc = new Function(
              'res',
              this.field.options.onSuccessCallback,
            );
            customFunc.call(this, res);
          })
          .catch((err) => {
            if (this.field.options.onFailCallback) {
              let customFunc = new Function(
                'e',
                this.field.options.onFailCallback,
              );
              customFunc.call(this, err);
            }
          });
      }
    },
    setDashboardData(data) {
      this.field.options.optionItems = data
    }
  },
};
</script>
<style lang="scss" scoped>
.dashboard-box {
  height: 100%;
  width: 100%;
}
.content {
  border-radius: 10px;
  background: #fff;
  padding: 10px 20px;
  box-shadow: 0px 0px 12px rgba(0, 0, 0, .12)
}
.content-first {
  font-size: 14px;
  height: 30px;
  line-height: 30px;
  color: #999;
}
.content-second {
  font-size: 28px;
  height: 30px;
  line-height: 30px;
  color: #000;
  font-weight: bold;
}
.content-three {
  font-size: 14px;
  height: 30px;
  line-height: 30px;
}
</style>
