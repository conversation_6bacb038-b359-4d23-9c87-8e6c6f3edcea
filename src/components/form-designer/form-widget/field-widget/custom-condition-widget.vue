<template>
  <view-wrapper
    :designer="designer"
    :field="field"
    :rules="rules"
    :design-state="designState"
    :parent-widget="parentWidget"
    :parent-list="parentList"
    :index-of-parent-list="indexOfParentList"
    :sub-form-row-index="subFormRowIndex"
    :sub-form-col-index="subFormColIndex"
    :sub-form-row-id="subFormRowId"
    :echo-field-data="echoFieldData"
    :sub-edit-flag="subEditFlag"
  >
    <div v-for="(conditions, i) in screenList" :key="i">
      <div class="d-flex" style="flex-wrap: wrap !important">
        <div
          v-for="(item, index) in conditions"
          :key="index"
          class="d-flex a-center pd-b10 pd-r10"
        >
          <!-- 筛选项 -->
          <el-select
            v-model="item.elementKey"
            placeholder="请选择项目"
            style="width: 120px"
            clearable
          >
            <el-option
              v-for="(item, index) in items"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          <div style="width: 80px; margin-left: 15px">
            <!-- 筛选项 -->
            <el-select
              v-model="item.condition"
              style="width: 80px"
              placeholder="条件"
            >
              <el-option
                v-for="(item, index) in crList"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </div>

          <!-- 筛选项目对应的输入框/选择框... -->
          <div class="mr-lr15" style="width: 150px">
            <el-input
              v-model="item.value"
              clearable
              :placeholder="'请输入' + getScreenLabel(item.elementKey)"
            />
          </div>
          <!-- 关系 -->
          <el-select
            v-model="item.relation"
            style="width: 80px"
            placeholder="或者/并且"
          >
            <el-option
              v-for="(item, index) in relationList"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          <i
            v-if="conditions.length > 1 || screenList.length > 1"
            class="el-icon-remove font-20 mr-l10 pointer"
            style="color: #ff4949"
            @click="deleteCondition(i, index)"
          />
          <template
            v-if="
              i === screenList.length - 1 && index === conditions.length - 1
            "
          >
            <i
              class="el-icon-circle-plus font-20 mr-l10 pointer"
              style="color: var(--primary-color)"
              @click="addCondition"
            />
          </template>
        </div>
      </div>
    </div>
  </view-wrapper>
</template>

<script>
import emitter from 'element-ui/lib/mixins/emitter';
import i18n from '@/utils/i18n';
import fieldMixin from '@/components/form-designer/form-widget/field-widget/fieldMixin';
import ViewWrapper from '@/components/form-designer/form-widget/field-widget/view-wrapper.vue';

export default {
  name: 'custom-condition-widget',
  componentName: 'FieldWidget', //必须固定为FieldWidget，用于接收父级组件的broadcast事件
  mixins: [emitter, fieldMixin, i18n],
  props: {
    field: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,

    designState: {
      type: Boolean,
      default: false,
    },

    subFormRowIndex: {
      /* 子表单组件行索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormColIndex: {
      /* 子表单组件列索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormRowId: {
      /* 子表单组件行Id，唯一id且不可变 */ type: String,
      default: '',
    },
    subEditFlag: {
      type: Object,
      default: null,
    },
    echoFieldData: {
      type: Array,
      default: () => [],
    },
  },
  components: {
    ViewWrapper,
  },
  inject: ['refList', 'formConfig', 'globalOptionData', 'globalModel'],
  data() {
    return {
      screenList: [],
      oldFieldValue: null, //field组件change之前的值
      fieldModel: null,
      selectItem: null,
      selectData: [],
      copySelectData: [],
      rules: [],
      conditions: {
        elementKey: '', // 选中的项目key
        value: undefined, // 选中的项目对应输入的值
        relation: 'or', // 关系(或/且)
        condition: 'eq',
      },
      relationList: [
        { label: '或者', value: 'or' },
        { label: '并且', value: 'and' },
      ],
      crList: [
        { label: '等于', value: 'eq' },
        { label: '包含', value: 'like' },
        { label: '不等于', value: 'ne' },
        { label: '大于', value: 'dy' },
        { label: '小于', value: 'xy' },
        { label: '大于等于', value: 'edy' },
        { label: '小于等于', value: 'exy' },
      ],
    };
  },
  created() {
    /* 注意：子组件mounted在父组件created之后、父组件mounted之前触发，故子组件mounted需要用到的prop
        需要在父组件created中初始化！！ */
    this.initOptionItems();
    this.initFieldModel();
    this.registerToRefList();
    this.initEventHandler();
    this.buildFieldRules();
    this.handleOnCreated();
  },
  beforeDestroy() {
    this.unregisterFromRefList();
  },
  computed: {
    items() {
      return this.selectData;
    },
  },
  watch: {
    screenList: {
      handler(data) {
        let arr = data
          .flat()
          .filter((item) => item.elementKey && item.condition && item.value);
        this.setValue(arr);
      },
      deep: true,
    },
  },
  mounted() {
    this.handleOnMounted();
    if(this.field.options.autoLoadData){
      this.sendApi();
    }
  },
  methods: {
    sendApi() {
      if (this.field.options.api) {
        this.newExecuteInterface()
          .then((res) => {
            this.selectData = res.data;

            this.setSelectItem(
              this.selectData.filter(
                (item) =>
                  item[this.field.options.selectValue] === this.fieldModel,
              )[0],
            );

            this.copySelectData = Object.assign(this.selectData);
            let customFunc = new Function(
              'res',
              this.field.options.onSuccessCallback,
            );
            customFunc.call(this, res);
          })
          .catch((err) => {
            if (this.field.options.onFailCallback) {
              let customFunc = new Function(
                'e',
                this.field.options.onFailCallback,
              );
              customFunc.call(this, err);
            }
          });
      } else if (this.field.options.optionItems) {
        this.initOptionsItem(this.field.options.optionItems);
      }
      this.screenList = [
        [
          {
            ...this.conditions,
            elementKey: this.items[0] && this.items[0].value,
          },
        ],
      ];
    },
    /**
     * 删除条件
     * @param {Object} i
     * @param {Object} index
     */
    deleteCondition(i, index) {
      if (this.screenList[i].length === 2) {
        this.screenList[i].splice(index, 1);
      } else {
        this.screenList.splice(i, 1);
      }

      // 重新排序
      this.conditionChange();
    },
    /**
     * 添加条件
     */
    addCondition() {
      if (this.screenList[this.screenList.length - 1].length === 2) {
        this.screenList.push([
          {
            ...this.conditions,
            elementKey: this.items[0] && this.items[0].value,
          },
        ]);
      } else {
        this.screenList[this.screenList.length - 1].push({
          ...this.conditions,
          elementKey: this.items[0] && this.items[0].value,
        });
      }
      this.$forceUpdate();
    },
    initOptionsItem(item) {
      this.selectData = item;
      this.copySelectData = Object.assign(item);
    },
    /**
     * 获取筛选项的label
     * @param {Object} value
     */
    getScreenLabel(value) {
      try {
        if (!value) return '相关信息';
        return this.items.find((item) => item.value === value).label;
      } catch (e) {
        return '相关信息';
      }
    },
  },
};
</script>
