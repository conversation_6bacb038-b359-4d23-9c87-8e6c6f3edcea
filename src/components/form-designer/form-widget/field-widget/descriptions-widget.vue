<template>
  <view-wrapper
    :designer="designer"
    :field="field"
    :rules="rules"
    :design-state="designState"
    :parent-widget="parentWidget"
    :parent-list="parentList"
    :index-of-parent-list="indexOfParentList"
  >
    <template v-if="fieldModel.length > 0">
      <el-descriptions
        :title="field.options.title"
        :direction="field.options.direction"
        :column="field.options.rows"
        :border="field.options.hasFrame"
        :size="field.options.widgetSize"
      >
        <el-descriptions-item
          v-for="(item, index) in fieldModel"
          :key="index"
          :label="item.label"
        >
          <div
            class="pointer"
            v-html="item[field.options.selectValue]"
            @click="valueClick(item, index)"
          />
        </el-descriptions-item>
      </el-descriptions>
    </template>
    <lt-empty v-else />
  </view-wrapper>
</template>

<script>
import viewWrapper from './view-wrapper';
import i18n, { translate } from '@/utils/i18n';
import fieldMixin from '@/components/form-designer/form-widget/field-widget/fieldMixin';
import emitter from 'element-ui/lib/mixins/emitter';
export default {
  name: 'descriptions-widget',
  componentName: 'FieldWidget', //必须固定为FieldWidget，用于接收父级组件的broadcast事件
  components: {
    viewWrapper,
  },
  mixins: [emitter, fieldMixin, i18n],
  props: {
    field: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,

    designState: {
      type: Boolean,
      default: false,
    },
  },
  inject: ['refList', 'formConfig', 'globalOptionData', 'globalModel'],
  data() {
    return {
      oldFieldValue: null, //field组件change之前的值
      fieldModel: [],
      rules: [],
    };
  },
  created() {
    /* 注意：子组件mounted在父组件created之后、父组件mounted之前触发，故子组件mounted需要用到的prop
       需要在父组件created中初始化！！ */
    this.registerToRefList();
    this.initEventHandler();
  },
  mounted() {
    this.handleOnMounted();
    if (this.field.options.autoLoadData) {
      this.sendApi();
    }
  },
  beforeDestroy() {
    this.unregisterFromRefList();
  },
  methods: {
    sendApi() {
      if (this.field.options.api) {
        this.newExecuteInterface()
          .then((res) => {
            this.fieldModel = res.data;

            if (this.field.options.onSuccessCallback) {
              let customFunc = new Function(
                'res',
                this.field.options.onSuccessCallback,
              );
              customFunc.call(this, res);
            }
          })
          .catch((err) => {
            if (this.field.options.onFailCallback) {
              let customFunc = new Function(
                'e',
                this.field.options.onFailCallback,
              );
              customFunc.call(this, err);
            }
          });
      }
    },
    valueClick(item, index) {
      if (this.field.options.onValueClick) {
        let customFunc = new Function(
          'item',
          'index',
          this.field.options.onValueClick,
        );
        customFunc.call(this, item, index);
      }
    },
  },
};
</script>

<style scoped>
/* ::v-deep .el-descriptions-item__content {
  padding: 0 !important;
} */
/*
::v-deep .el-descriptions-item__label {
  padding: 0 !important;
} */
</style>
