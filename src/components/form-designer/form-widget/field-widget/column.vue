<template>
    <el-table-column
      :key="col.id"
      :width="col.width"
      :fixed="col.fix == 'left' ? 'left' : col.fix == 'right' ? 'right' : false"
      v-if="!col.children || col.children.length == 0"
      :label="col.label"
      :prop="col.prop"
      :align="col.align"
      :sortable="col.sortable ? 'custom' : false"
      :show-overflow-tooltip="col.isEdit ? false : col.overflow"
    >
      <template slot="header" slot-scope="scope">
        <template v-if="col.showSort || col.showBlank || col.showSearch">
          <el-popover
            placement="bottom-end"
            width="300"
            @show="$emit('columnShowQuery')"
            trigger="click"
          >
            <div style="min-height: 100px" v-if="columnQuery.length">
              <el-radio-group
                v-if="col.showSort"
                v-model="columnQuery[index].order"
                class="sort-header"
              >
                <el-radio-button label="ascending">升序</el-radio-button>
                <el-radio-button label="descending">降序</el-radio-button>
              </el-radio-group>
              <!--头部搜索-->

              <el-radio-group
                v-if="col.showBlank"
                v-model="columnQuery[index].blank"
                class="sort-main"
              >
                <el-radio :label="1">全部</el-radio><br />
                <el-radio :label="2">已填</el-radio><br />
                <el-radio :label="3">未填</el-radio>
              </el-radio-group>

              <template v-if="col.showSearch">
                <template v-if="col.searchType === 'date'">
                  <el-date-picker
                    v-model="columnQuery[index].input"
                    style="width: 275px"
                    type="daterange"
                    format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                  >
                  </el-date-picker>
                </template>
                <template v-else-if="col.searchType === 'number'">
                  <el-input
                    v-model="columnQuery[index].input"
                    prefix-icon="el-icon-search"
                    placeholder="请输入搜索内容"
                  />
                  <div
                    style="
                      display: flex;
                      justify-content: space-between;
                      align-items: center;
                      margin-top: 10px;
                    "
                  >
                    <el-input-number
                      v-model="columnQuery[index].min"
                      style="width: 125px"
                      controls-position="right"
                      placeholder="最小值"
                    />~<el-input-number
                      v-model="columnQuery[index].max"
                      style="width: 125px"
                      controls-position="right"
                      placeholder="最大值"
                    />
                  </div>
                </template>
                <template v-else-if="col.searchType == 'select'">
                  <div
                    style="
                      margin: 10px 0;
                      width: 269px;
                      border: 1px solid RGB(228, 231, 237);
                      border-radius: 10px;
                      height: 250px;
                      overflow-x: scroll;
                    "
                  >
                    <template
                      v-if="
                        columnQuery[index].selectItems &&
                        columnQuery[index].selectItems.length
                      "
                    >
                      <div
                        class="pd-t10 pd-lr5"
                        v-if="columnQuery[index].selectItems.length > 9"
                      >
                        <el-input
                          placeholder="搜索"
                          v-model="columnQuery[index].filter"
                          size="mini"
                        ></el-input>
                      </div>
                      <el-checkbox-group
                        v-model="columnQuery[index].select"
                        style="margin-top: 10px; display: flex; flex-flow: column"
                      >
                        <el-checkbox
                          style="margin: 2px 20px"
                          v-for="(colItem, i) in handleFilter(
                            columnQuery[index].selectItems,
                            columnQuery[index].filter,
                            'label',
                          )"
                          :key="i"
                          :label="colItem.value"
                          >{{ colItem.label }}</el-checkbox
                        >
                      </el-checkbox-group>
                    </template>
                  </div>
                </template>
                <template v-else>
                  <el-input
                    v-model="columnQuery[index].input"
                    prefix-icon="el-icon-search"
                    placeholder="请输入搜索内容"
                  />
                </template>
              </template>

              <div class="d-flex j-end mr-t5">
                <el-button
                  size="mini"
                  type="primary"
                  class="mr-r5"
                  @click="$emit('confirmPop')"
                >
                  确认
                </el-button>
                <el-button size="mini" @click="$emit('closePop')">
                  取消
                </el-button>
              </div>
            </div>
            <div
              slot="reference"
              class="labelQuery"
              :style="{ textAlign: col.align }"
              style="cursor: pointer"
            >
              {{ scope.column.label }}
              <i class="el-icon-caret-bottom"></i>
            </div>
          </el-popover>
        </template>
        <template v-else>
          <template v-if="col.icon">
            <i v-if="col.icon.includes('el-icon')" :class="col.icon"></i>
            <svg-icon v-else :icon-class="col.icon" />
          </template>
          {{ col.label }}
        </template>
      </template>
      <template slot-scope="scope">
        <span
          v-if="col.code"
          v-html="
            codeComp(scope.row, scope.$index, col.code, scope.row[col.prop])
          "
        />
        <template v-else-if="col.colType">
          <template v-if="col.colType == 'user' && scope.row[col.prop]">
            <span>{{ userNames(scope.row[col.prop]) }}</span>
          </template>
          <template v-if="col.colType == 'switch'">
            <el-switch
              :disabled="field.options.disabled"
              :readonly="field.options.readonly"
              v-model="scope.row[col.prop]"
              @click.native.stop="switchClick(scope)"
              active-color="#13ce66"
              :active-value="getSwitchExtendValue(scope.row[col.prop])[0]"
              :inactive-value="getSwitchExtendValue(scope.row[col.prop])[1]"
            />
          </template>
          <el-link v-if="col.colType == 'longText'" type="primary" @click="previewLongText(scope.row[col.prop])">
            {{ scope.row[col.prop] }}
          </el-link>
          <el-link
            v-if="col.colType == 'link'"
            type="primary"
            @click.native.stop="switchClick(scope)"
            >{{ scope.row[col.prop] }}</el-link
          >
          <el-tag
            v-if="col.colType == 'tag'"
            :type="tagType(scope.row[col.prop]).type"
            >{{ tagType(scope.row[col.prop]).label }}</el-tag
          >
          <span v-if="col.colType == 'time'"
            >{{ format(scope.row[col.prop]) }}
          </span>
          <el-link
            v-if="col.colType == 'file' && scope.row[col.prop]"
            type="primary"
            @click="preview(scope.row[col.prop])"
          >
            查看
          </el-link>
          <div v-else>{{ col.filePlaceholder || '' }}</div>
        </template>
        <template v-else>
          <template v-if="isShowEdit(scope.row)">
            <el-input
              v-if="hasColType(col, scope.row, 'input')"
              style="width: 100% !important"
              v-model="scope.row[col.prop]"
              @input="inputChange($event, scope)"
              @blur="blurChange($event, scope)"
              @focus="focusChange($event, scope)"
              @keyup.native.enter="handleEnter($event, scope)"
              :placeholder="'请输入' + col.label"
            />
            <el-date-picker
              v-else-if="hasColType(col, scope.row, 'datetime')"
              style="width: 100% !important"
              value-format="yyyy-MM-dd HH:mm:ss"
              type="datetime"
              v-model="scope.row[col.prop]"
              @change="inputChange($event, scope)"
              @blur="blurChange($event, scope)"
              @focus="focusChange($event, scope)"
              :placeholder="'请选择' + col.label"
            />
            <el-date-picker
              v-else-if="hasColType(col, scope.row, 'date')"
              style="width: 100% !important"
              value-format="yyyy-MM-dd"
              type="date"
              v-model="scope.row[col.prop]"
              @change="inputChange($event, scope)"
              @blur="blurChange($event, scope)"
              @focus="focusChange($event, scope)"
              :placeholder="'请选择' + col.label"
            />
            <el-time-picker
              v-else-if="hasColType(col, scope.row, 'time')"
              v-model="scope.row[col.prop]"
              format="HH:mm"
              value-format="HH:mm"
              @change="inputChange($event, scope)"
              @blur="blurChange($event, scope)"
              @focus="focusChange($event, scope)"
              :placeholder="'请选择' + col.label"
              style="width: 100% !important"
            />
            <el-input
              v-else-if="hasColType(col, scope.row, 'number')"
              style="width: 100% !important"
              type="number"
              v-model="scope.row[col.prop]"
              @input="inputChange($event, scope, {}, col)"
              @blur="blurChange($event, scope)"
              @focus="focusChange($event, scope)"
              :placeholder="'请输入' + col.label"
            />
            <el-select
              v-else-if="hasColType(col, scope.row, 'select')"
              style="width: 100% !important"
              @input="selectInput($event, scope.row, col.prop)"
              :value="toArray(scope.row, col)"
              :placeholder="'请选择' + col.label"
              clearable
              filterable
              :allow-create="getColConfig(col, scope.row).allowCreate"
              :multiple="getColConfig(col, scope.row).multiple"
              @change="selectChange($event, scope, 'select')"
              @blur="blurChange($event, scope)"
              @focus="focusChange($event, scope)"
            >
              <el-option
                v-for="item in getColOptions(col, scope.row)"
                :label="item[getSelectLabelField(col, scope.row)]"
                :value="item[getSelectValueField(col, scope.row)]"
                :key="item[getSelectValueField(col, scope.row)]"
              />
            </el-select>
            <el-cascader
              v-else-if="hasColType(col, scope.row, 'cascader')"
              style="width: 100% !important"
              :value="handleCascaderData(scope.row[col.prop])"
              :placeholder="'请选择' + col.label"
              clearable
              filterable
              :props="{
                multiple: getColConfig(col, scope.row).multiple,
                checkStrictly: getColConfig(col, scope.row).checkStrictly,
                emitPath: getColConfig(col, scope.row).showAllLevels,
                value: getSelectValueField(col, scope.row),
                label: getSelectLabelField(col, scope.row),
                children:
                  getColConfig(col, scope.row).selectChildren || 'children',
              }"
              :options="getColOptions(col, scope.row)"
              :show-all-levels="getColConfig(col, scope.row).showAllLevels"
              @change="selectChange($event, scope, 'cascader')"
              @blur="blurChange($event, scope)"
              @focus="focusChange($event, scope)"
            >
            </el-cascader>

            <el-switch
              v-else-if="hasColType(col, scope.row, 'switch')"
              style="width: 100% !important"
              v-model="scope.row[col.prop]"
              @change="inputChange($event, scope)"
              active-color="#13ce66"
              :active-value="getSwitchExtendValue(scope.row[col.prop])[0]"
              :inactive-value="getSwitchExtendValue(scope.row[col.prop])[1]"
            />
            <span v-if="hasColType(col, scope.row, 'code')">自动生成</span>
          </template>
          <template
            v-else-if="
              !isShowEdit(scope.row) && hasColType(col, scope.row, 'switch')
            "
          >
            <el-switch
              v-model="scope.row[col.prop]"
              active-color="#13ce66"
              :active-value="getSwitchExtendValue(scope.row[col.prop])[0]"
              :inactive-value="getSwitchExtendValue(scope.row[col.prop])[1]"
              disabled
            />
          </template>
          <span v-else>{{ getPreviewText(scope.row, col, scope.$index) }}</span>
        </template>
      </template>
    </el-table-column>
    <el-table-column
      v-else-if="!col.hidden"
      :key="col.id + 1"
      :width="col.width"
      :fixed="col.fix == 'left' ? 'left' : col.fix == 'right' ? 'right' : false"
      :label="col.label"
      :align="col.align"
      :sortable="col.sortable ? 'custom' : false"
    >
      <template v-for="(subCol, index) in col.children">
        <Column
          v-if="!subCol.hidden"
          :key="subCol.id"
          :col="subCol"
          :hasChildren="true"
          :selectData="selectData"
          :index="index"
          :field="field"
          :disableRelation="disableRelation"
          :dict-map="dictMap"
          :user-map="userMap"
          :selectDict="selectDict"
        ></Column>
      </template>
    </el-table-column>

</template>

<script>
import fieldMixin from '@/components/form-designer/form-widget/field-widget/fieldMixin';
import emitter from 'element-ui/lib/mixins/emitter';
import { getUserAvatars } from '@/api/system/user';
import i18n from '@/utils/i18n';
import PinyinMatch from 'pinyin-match';
export default {
  name: 'Column',
  mixins: [emitter, fieldMixin, i18n],
  props: {
    col: {
      type: Object,
    },
    index: {
      type: Number,
    },
    field: {
      type: Object,
      default: () => {},
    },
    dictMap: {
      type: Object,
    },
    userMap: {
      type: Object,
      default: null,
    },
    selectData: {
      type: Object,
    },
    hasChildren: {
      type: Boolean,
      default: false,
    },
    disableRelation: {
      type: Object,
    },
    selectDict: {
      type: Object,
    },
    columnQuery: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      lonTextDialogVisible: false,
      switchTypeValue: {
        number: [0, 1], // string类型的bool不太好判断
      },
    };
  },
  methods: {
    previewLongText(text){
      this.$ltKKFilePreview.previewLongText(text);
    },
    selectInput(e, row, prop) {
      this.$set(row, prop, e)
    },
    // scope.row[col.prop]
    toArray(row, col){
      let value = row[col.prop]

      if (typeof value == 'string' && value.startsWith('[') && value.endsWith(']')) {
        try {
          return JSON.parse(value);
        } catch (e) {
          // 如果 JSON 解析失败，返回原值
          console.error('JSON parsing error:', e);
          return value;
        }
      }
      if (typeof value == 'string' && this.getColConfig(col, row).multiple) {
        try {
          const parsed = JSON.parse(value);
          return Array.isArray(parsed) ? parsed : (parsed ? parsed.split(',') : parsed)
        } catch (e) {
          return Array.isArray(value) ? value : (value ? value.split(',') : value)
        }
      }
      return value;
    },
    handleCascaderData(value){
      // 数据可能是: '1,2' 或者 [1,2] 或者 '[1,2]',将这些转为数组
      try {
        const parsed = JSON.parse(value);
        return Array.isArray(parsed) ? parsed : (parsed ? parsed.split(',') : parsed)
      } catch (e) {
        return Array.isArray(value) ? value : (value ? value.split(',') : value)
      }
    },
    handleFilter(items, val, field) {
      return items.filter((item) => {
        if (!val) {
          return true;
        }
        if (item[field].toUpperCase().includes(val.toUpperCase())) {
          return true;
        }
        return PinyinMatch.match(item[field], val);
      });
    },
    selectList(row) {
      let prop = this.col?.prop;
      let api = this.col?.selectConfig?.api;
      let isModel = this.col?.selectConfig?.isModel;
      let optionsModel = this.col?.selectConfig?.optionsModel;
      let optionItems = this.col?.selectConfig?.options?.optionItems;
      let optionsDict = this.col?.selectConfig?.optionsDict;
      if (isModel) {
        return this.selectData[`${prop}-${optionsModel}`];
      }
      if (api) {
        if (
          this.col.selectConfig?.paramConfig &&
          Object.keys(this.col.selectConfig.paramConfig).length
        ) {
          let json = JSON.parse(
            JSON.stringify(this.col.selectConfig.paramConfig),
          );
          for (let key in json) {
            json[key] = row[json[key]];
          }

          return this.selectData[`${prop}-${api}`]
            ? this.selectData[`${prop}-${api}`][JSON.stringify(json)]
            : [];
        } else {
          return this.selectData[`${prop}-${api}`];
        }
      }
      if (optionsDict) {
        return this.selectData[`${prop}-${optionsDict}`];
      }
      if (optionItems) return optionItems;
      return [];
    },
    // 判断每一行的列的类型
    hasColType(col, row, type) {
      let temp = row['_' + col.prop + '_config']?.type;
      if (col.editType === 'custom' && temp) {
        return temp === type;
      } else {
        return col.editType === type;
      }
    },
    getColConfig(col, row) {
      let temp = {};
      if (col.editType === 'custom') {
        let key = '_' + col.prop + '_config';
        if (!row[key]) {
          this.$message.error(
            `${col.prop}字段为自定义编辑类型,并未在行数据中发现其对应的${key}字段`,
          );
        }
        temp = row[key];
      } else {
        temp = col.selectConfig;
      }
      if (temp && !'multiple' in temp) {
        temp.multiple = false;
      }
      return temp;
    },
    getColOptions(col, row) {
      let temp = row['_' + col.prop + '_config']?.options?.optionItems;
      if (col.editType === 'custom' && temp) {
        return temp;
      }

      if (this.selectDict && this.field.options.rowKey in row) {
        let columnSelectKey = row[this.field.options.rowKey] + '-' + col.prop;
        if (columnSelectKey in this.selectDict) {
          return this.selectDict[columnSelectKey];
        }
        let rowSelectKey = row[this.field.options.rowKey];
        if (rowSelectKey in this.selectDict) {
          return this.selectDict[rowSelectKey];
        }
      }

      return this.selectList(row);
    },
    userNames(userIds) {
      try {
        let target = [];
        let finishedList = [];
        userIds = (userIds + '').split(',');
        if (this.field.options.isLazy && this.field.options.treeTable) {
          userIds.forEach((item) => {
            if (Object.keys(this.userMap).length && item) {
              if (this.userMap[item]) {
                target.push(this.userMap[item].nickName);
              } else {
                finishedList.push(item);
              }
            } else {
              target.push(item);
            }
          });
          if (finishedList.length) {
            finishedList = [...new Set(finishedList)];
            getUserAvatars({ userIds: finishedList }).then((data) => {
              this.$emit('userChange', data.data);
            });
          }
        }
        return userIds
          .map((userId) => {
            return this.userMap[userId] ? this.userMap[userId].nickName : '';
          })
          .join();
      } catch (error) {
        return '';
      }
    },
    format(val) {
      if (val) {
        val = val + '';
        // 过滤掉时区
        if (val.length > 19) {
          val = val.slice(0, 19);
        }
        return val.replace('T', ' ');
      }
      return '';
    },
    tagType(value) {
      if (value === false) {
        value = 'false';
      }
      if (
        this.dictMap &&
        this.col.prop in this.dictMap &&
        value in this.dictMap[this.col.prop]
      ) {
        return {
          value: value,
          type: this.dictMap[this.col.prop][value],
          label: value,
        };
      }
      if (
        this.col.tagTypes &&
        this.col.tagTypes.find((e) => e.value == value)
      ) {
        let t = this.col.tagTypes.find((e) => e.value == value);
        if (!t.label || t.label == '') {
          t.label = value;
        }
        return t;
      }
      return { value: value, type: 'default', label: value };
    },

    codeComp(row, $index, code, text) {
      return new Function('row', '$index', 'text', code)(row, $index, text);
    },
    switchClick(scope) {
      if (this.col.clickCode) {
        let JS = new Function('row', '$index', this.col.clickCode);
        JS.call(this, scope.row, scope.$index);
      }
    },
    inputChange(e, scope, extra = {}, col) {
      if (col && col.editType == 'number' && col.inputConfig.precision) {
        let fun = (value, round) => {
          value += '';
          if (value.includes('.')) {
            let integer = value.split('.')[0];
            let decimal = value.split('.')[1];
            if (decimal.length > round) {
              return `${integer}.${decimal.slice(0, round)}`;
            }
          }
          return value;
        };
        scope.row[col.prop] = fun(e, col.inputConfig.precision);
      }
      if (this.field.options.onEditChange) {
        let JS = new Function(
          'val',
          'fieldName',
          'rowIndex',
          'colIndex',
          'rowData',
          'colData',
          'extra',
          this.field.options.onEditChange,
        );
        JS.call(
          this,
          e,
          this.col.prop,
          scope.$index,
          this.index,
          scope.row,
          this.col,
          extra,
        );
      }
    },
    blurChange(e, scope, extra = {}) {
      if (this.field.options.onBlurChange) {
        let JS = new Function(
          'val',
          'fieldName',
          'rowIndex',
          'colIndex',
          'rowData',
          'colData',
          'extra',
          this.field.options.onBlurChange,
        );
        JS.call(
          this,
          scope.row[this.col.prop],
          this.col.prop,
          scope.$index,
          this.index,
          scope.row,
          this.col,
          extra,
        );
      }
    },
    focusChange(e, scope, extra = {}) {
      if (this.field.options.onFocusChange) {
        let JS = new Function(
          'val',
          'fieldName',
          'rowIndex',
          'colIndex',
          'rowData',
          'colData',
          'extra',
          this.field.options.onFocusChange,
        );
        JS.call(
          this,
          scope.row[this.col.prop],
          this.col.prop,
          scope.$index,
          this.index,
          scope.row,
          this.col,
          extra,
        );
      }
    },
    handleEnter(e, scope, extra = {}) {
      if (this.field.options.onTableEnter) {
        let JS = new Function(
          'val',
          'fieldName',
          'rowIndex',
          'colIndex',
          'rowData',
          'colData',
          'extra',
          this.field.options.onTableEnter,
        );
        JS.call(
          this,
          scope.row[this.col.prop],
          this.col.prop,
          scope.$index,
          this.index,
          scope.row,
          this.col,
          extra,
        );
      }
    },
    preview(fileId) {
      if (fileId.length < 10) {
        console.error('请检查fieId字段是否正常');
      } else {
        this.$ltKKFilePreview.open(fileId);
      }
    },
    getPreviewText(row, col, index) {
      let selectConfig = this.getColConfig(col, row);
      if (
        ['custom', 'select', 'cascader'].includes(col.editType) &&
        this.getColOptions(col, row)
      ) {
        let getLabel = (value, options) => {
          if (Array.isArray(value)) {
            let resArr = [];
            let index = 0;
            let list = options;
            while (index < value.length) {
              let obj = list?.find(
                (item) =>
                  item[
                    selectConfig.isModel
                      ? selectConfig.modelValue
                      : selectConfig?.selectValue || 'value'
                  ] == value[index],
              );
              if (obj) {
                resArr.push(
                  obj[
                    selectConfig.isModel
                      ? selectConfig.modelLabel
                      : selectConfig?.selectLabel || 'label'
                  ],
                );
                list = obj[selectConfig?.selectChildren || 'children'];
                index++;
              } else {
                break;
              }
            }
            return resArr.length ? resArr.join('/') : value;
          } else {
            let res = '';
            this.$array.handleDFS(
              options,
              selectConfig?.selectChildren || 'children',
              (item) => {
                if (item[selectConfig?.selectValue || 'value'] == value) {
                  res = item[selectConfig?.selectLabel || 'label'];
                }
              },
            );
            return res === '' ? value : res;
          }
        };
        if (selectConfig?.multiple) {
          if (col.editType == 'cascader' && Array.isArray(row[col.prop])) {
            return row[col.prop]
              .map((item) => getLabel(item, this.getColOptions(col, row)))
              .join(',');
          }

          let arr = this.getColOptions(col, row).filter((item) => {
            let rowData = row[col.prop];
            if (!rowData) return false;
            if (typeof rowData == 'string') {
              if (rowData.includes('[') && rowData.includes(']')) {
                // rowData =
                row[col.prop] = JSON.parse(rowData);
              } else {
                rowData = rowData.split(',');
              }
            }
            return rowData.includes(item[selectConfig?.selectValue || 'value']);
          });

          if (arr.length) {
            return arr
              .map((item) => item[selectConfig?.selectLabel || 'label'])
              .join(',');
          }
        } else {
          if (col.editType == 'cascader') {
            return getLabel(this.handleCascaderData(row[col.prop]), this.getColOptions(col, row));
          }

          let isModel = col?.selectConfig?.isModel;
          let obj = this.getColOptions(col, row).find(
            (item) =>
              item[(isModel ? selectConfig?.modelValue : selectConfig?.selectValue) || 'value'] == row[col.prop],
          );

          if (obj) return obj[(isModel ? selectConfig?.modelLabel : selectConfig?.selectLabel) || 'label'];
        }
      }
      return row[col.prop];
    },
    selectChange(e, scope, type) {
      let extra = {};
      this.$parent.doLayout()
      if (type === 'select') {
        extra = {
          active: this.getColOptions(this.col, scope.row).find(
            (item) =>
              item[
                this.getColConfig(this.col, scope.row)?.selectValue || 'value'
              ] == scope.row[this.col.prop],
          ),
          selectList: this.getColOptions(this.col, scope.row),
        };
      }
      if (type === 'cascader') {
        extra = {
          text: 'cascader级联选择器 "额外参数" 功能待定',
        };

        scope.row[this.col.prop] = e
      }

      this.inputChange(e, scope, extra);
    },
    isShowEdit(row) {
      let { isEdit } = row;
      // 流程中可设置可编辑状态，配置可编辑状态所有数据自动进入编辑状态
      if (
        this?.field?.options?.defaultEdit !== undefined &&
        this?.field?.options?.defaultEdit
      ) {
        isEdit = true;
      }

      const { editable, prop, editType } = this.col;

      let rowKey = this?.field?.options?.rowKey;
      let bol = isEdit && editable;
      if (prop == rowKey) {
        return false;
      }
      if (bol && rowKey) {
        let primaryKey = row[rowKey];
        // 编码类型不参与编辑
        if (editType === 'code' && primaryKey) return false;
        let temp = this.disableRelation[primaryKey];
        return this.disableRelation && primaryKey && temp && prop in temp
          ? !temp[prop]
          : true;
      }
      return isEdit && editable;
    },
    /**
     * 获取switch类型扩展
     * @param {*} val
     */
    getSwitchExtendValue(val) {
      if (['true', 'false'].includes(val)) {
        return ['true', 'false'];
      }
      if (['1', '0'].includes(val)) {
        return ['1', '0'];
      }
      if ([1, 0].includes(val)) {
        return [1, 0];
      }
      return [true, false];
    },
    // 获取select的label字段
    getSelectLabelField(col, row) {
      let config = this.getColConfig(col, row);

      if (config.isModel) {
        return config.modelLabel;
      }
      return config.selectLabel || 'label';
    },
    // 获取select的value字段
    getSelectValueField(col, row) {
      let config = this.getColConfig(col, row);
      if (config.isModel) {
        return config.modelValue + '';
      }

      if (config.selectValue) {
        return config.selectValue + '';
      }

      return 'value';
    },
  },
};
</script>
<style lang="scss" scoped>
// ::v-deep  th.el-table__cell:has(.labelQuery){
//     &:hover{
//         background: #a0cfff !important;
//     }
// }
.sort-header {
  display: flex;
  margin-bottom: 10px;

  ::v-deep .el-radio-button {
    width: 50%;
  }

  ::v-deep .el-radio-button__inner {
    width: 100%;
  }
}

.sort-main {
  display: flex;
  justify-content: center;
  margin: 10px 0;
}
</style>
