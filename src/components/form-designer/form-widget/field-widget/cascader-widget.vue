<template>
  <form-item-wrapper
    :designer="designer"
    :field="field"
    :rules="rules"
    :design-state="designState"
    :parent-widget="parentWidget"
    :parent-list="parentList"
    :index-of-parent-list="indexOfParentList"
    :sub-form-row-index="subFormRowIndex"
    :sub-form-col-index="subFormColIndex"
    :sub-form-row-id="subFormRowId"
    :echo-field-data="echoFieldData"
    :sub-edit-flag="subEditFlag"
  >
    <el-tooltip
      placement="top"
      effect="light"
      :disabled="!field.options.disabled"
      :content="labels && labels.join('/')"
    >
      <el-cascader
        ref="fieldEditor"
        :props="{
          multiple: field.options.multiple,
          checkStrictly: field.options.checkStrictly,
          emitPath: field.options.showAllLevels,
          value: field.options.modelValue || field.options.selectValue || 'value',
          label: field.options.modelLabel ||  field.options.selectLabel || 'label',
          children: field.options.selectChildren || 'children',
        }"
        :options="optionItems"
        v-model="fieldModel"
        class="full-width-input"
        :disabled="field.options.disabled"
        :size="field.options.size"
        :show-all-levels="field.options.showAllLevels"
        :collapse-tags="field.options.collapseTag"
        :clearable="field.options.clearable"
        :filterable="field.options.filterable"
        :placeholder="
          field.options.placeholder ||
          i18nt('render.hint.selectPlaceholder') + field.options.label
        "
        @focus="handleFocusCustomEvent"
        @blur="handleBlurCustomEvent"
        @change="handleChangeEvent"
      >
      </el-cascader>
    </el-tooltip>
  </form-item-wrapper>
</template>

<script>
import FormItemWrapper from './form-item-wrapper';
import emitter from 'element-ui/lib/mixins/emitter';
import i18n, { translate } from '@/utils/i18n';
import fieldMixin from '@/components/form-designer/form-widget/field-widget/fieldMixin';
import {getCommonTableCascader} from "@/api/tool/gen";
export default {
  name: 'cascader-widget',
  componentName: 'FieldWidget', //必须固定为FieldWidget，用于接收父级组件的broadcast事件
  mixins: [emitter, fieldMixin, i18n],
  props: {
    field: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,

    designState: {
      type: Boolean,
      default: false,
    },

    subFormRowIndex: {
      /* 子表单组件行索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormColIndex: {
      /* 子表单组件列索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormRowId: {
      /* 子表单组件行Id，唯一id且不可变 */ type: String,
      default: '',
    },
    subEditFlag: {
      type: Object,
      default: null,
    },
    echoFieldData: {
      type: Array,
      default: () => [],
    },
  },
  components: {
    FormItemWrapper,
  },
  inject: ['refList', 'formConfig', 'globalOptionData', 'globalModel'],
  data() {
    return {
      oldFieldValue: null, //field组件change之前的值
      fieldModel: null,
      rules: [],
      optionItems: null,
      dataType: 'string',
    };
  },

  watch: {
    'field.options.optionItems': {
      immediate: true,
      handler(val) {
        this.optionItems = this.field.options.optionItems;
      },
      deep: true,
    },
  },
  computed: {
    labels() {
      let labels = [];
      let fun = (arr, index) => {
        if (this.fieldModel[index]) {
          let obj = arr.find(
            (e) =>
              e[this.field.options.modelValue || this.field.options.selectValue || 'value'] ==
              this.fieldModel[index],
          );
          if (obj) {
            labels.push(obj[ this.field.options.modelLabel ||  this.field.options.selectLabel || 'label']);
            if (
              obj[this.field.options.selectChildren || 'children'] &&
              obj[this.field.options.selectChildren || 'children'].length > 0
            ) {
              fun(
                obj[this.field.options.selectChildren || 'children'],
                index + 1,
              );
            }
          }
        }
      };
      if (this.fieldModel && this.fieldModel.length > 0) {
        fun(this.optionItems, 0);
      }
      return labels;
    },
  },
  created() {
    /* 注意：子组件mounted在父组件created之后、父组件mounted之前触发，故子组件mounted需要用到的prop
         需要在父组件created中初始化！！ */
    this.initOptionItems();
    this.initFieldModel();
    this.registerToRefList();
    this.initEventHandler();
    this.buildFieldRules();
    this.handleOnCreated();
  },

  mounted() {
    this.handleOnMounted();
    if(this.field.options.autoLoadData){
      this.sendApi();
    }
  },

  beforeDestroy() {
    this.unregisterFromRefList();
  },

  methods: {
    sendApi() {
      // 通过模型获取数据
      if (this.field.options.optionsModel) {
        getCommonTableCascader(this.field.options.optionsModel).then(res =>{
          this.optionItems = res.data;
          // this.setSelectItem(
          //   this.selectData.filter(
          //     (item) => item[this.field.options.modelValue] === this.fieldModel,
          //   )[0],
          // );
          // this.copySelectData = Object.assign(this.selectData);
          // this.handleSuccess(res)
          let customFunc = new Function(
            'res',
            this.field.options.onSuccessCallback,
          );
          customFunc.call(this, res);
        }).catch(e => {
          if (this.field.options.onFailCallback) {
            let customFunc = new Function(
              'e',
              this.field.options.onFailCallback,
            );
            customFunc.call(this, err);
          }
        })
      } else if  (this.field.options.api) {
        this.newExecuteInterface()
          .then((res) => {
            this.optionItems = res.data;
            if (this.optionItems.length) {
              this.dataType =
                typeof this.optionItems[0][this.field.options.selectValue];
            }
            // this.setSelectItem(
            //   this.optionItems.filter(
            //     (item) => item.value === this.fieldModel,
            //   )[0],
            // );
            let customFunc = new Function(
              'res',
              this.field.options.onSuccessCallback,
            );
            customFunc.call(this, res);
          })
          .catch((err) => {
            if (this.field.options.onFailCallback) {
              let customFunc = new Function(
                'e',
                this.field.options.onFailCallback,
              );
              customFunc.call(this, err);
            }
          });
      } else if (this.field.options.optionItems) {
        this.optionItems = this.field.options.optionItems;
      }
    },

    // 获取简单对象
    getCascaderCheckItem() {
      let checks = this.$refs.fieldEditor.getCheckedNodes();
      if (
        checks.length == 0 ||
        this.fieldModel == null ||
        this.fieldModel == [] ||
        this.fieldModel == '' ||
        this.optionItems == null ||
        this.optionItems.length == 0
      ) {
        return [];
      }
      return checks.map((e) => {
        return { labels: e.pathLabels, value: e.path };
      });
    },
    // 获取完整对象
    getCascaderCheckObjItem() {
      if (
        this.fieldModel == null ||
        this.fieldModel == [] ||
        this.fieldModel == '' ||
        this.optionItems == null ||
        this.optionItems.length == 0
      ) {
        return [];
      }
      return this.$refs.fieldEditor.getCheckedNodes();
    },
    loadOptions(options) {
      this.optionItems = options;
    },
  },
};
</script>
