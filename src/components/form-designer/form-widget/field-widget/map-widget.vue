<template>
  <view-wrapper :designer="designer"  :field="field" :rules="rules" :design-state="designState"
                     :parent-widget="parentWidget" :parent-list="parentList" :index-of-parent-list="indexOfParentList"
                     :sub-form-row-index="subFormRowIndex" :sub-form-col-index="subFormColIndex" :sub-form-row-id="subFormRowId"
                     :echo-field-data="echoFieldData" :sub-edit-flag="subEditFlag">
    <el-amap ref="map" :style="{height:field.options.height+'px' ,width:field.options.width+'px'}" vid="amapDemo" :amap-manager="amapManager" :center="fieldModel" :zoom="zoom" :plugin="plugin" :events="events">
      </el-amap>
  </view-wrapper>
</template>

<script>
import viewWrapper from './view-wrapper'
import i18n, { translate } from '@/utils/i18n'
import fieldMixin from '@/components/form-designer/form-widget/field-widget/fieldMixin'
import emitter from "element-ui/lib/mixins/emitter";
import VueAMap from 'vue-amap'
// 初始化高德地图的 key 和插件
VueAMap.initAMapApiLoader({
  key: '5bdec7024fb8a50db8a7352a8a51a0e2',
  plugin: [
    'AMap.Autocomplete',
    'AMap.PlaceSearch',
    'AMap.Scale',
    'Geocoder',
    'AMap.OverView',
    'AMap.ToolBar',
    'AMap.MapType',
    'AMap.PolyEditor',
    'AMap.CircleEditor',
    'Geolocation',
  ],
});
let amapManager = new VueAMap.AMapManager();
export default {
  name: "map-widget",
  componentName: 'FieldWidget',  //必须固定为FieldWidget，用于接收父级组件的broadcast事件
  components: {
    viewWrapper,
  },
  mixins: [emitter, fieldMixin, i18n],
  props: {
    field: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,

    designState: {
      type: Boolean,
      default: false
    },

    subFormRowIndex: { /* 子表单组件行索引，从0开始计数 */
      type: Number,
      default: -1
    },
    subFormColIndex: { /* 子表单组件列索引，从0开始计数 */
      type: Number,
      default: -1
    },
    subFormRowId: { /* 子表单组件行Id，唯一id且不可变 */
      type: String,
      default: ''
    },
    subEditFlag: {
      type: Object,
      default: null
    },
    echoFieldData:{
        type: Array,
        default:()=>[]
    },
  },
  inject: ['refList', 'formConfig', 'globalOptionData', 'globalModel'],
  data() {
    return {
      oldFieldValue: null, //field组件change之前的值
      rules: [],
      amapManager,
          zoom: 12,
          fieldModel: [121.59996, 31.197646],
          events: {
            init: (o) => {
              o.getCity(result => {
                console.log(result)
              })
            },
            'moveend': () => {
            },
            'zoomchange': () => {
            },
            'click': (e) => {
            }
          },
          plugin:['ToolBar']
        };

    },
  created() {

    /* 注意：子组件mounted在父组件created之后、父组件mounted之前触发，故子组件mounted需要用到的prop
       需要在父组件created中初始化！！ */
    this.registerToRefList()
    this.initEventHandler()
    this.handleOnCreated()

  },
  mounted() {
    this.handleOnMounted()
  },
  beforeDestroy() {
    this.unregisterFromRefList()
  },
}
</script>

<style scoped>
::v-deep .amap-logo{
  display: none !important;
}
</style>
