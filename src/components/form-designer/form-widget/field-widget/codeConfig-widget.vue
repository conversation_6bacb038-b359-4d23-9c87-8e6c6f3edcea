<template>
  <view-wrapper
    :designer="designer"
    :field="field"
    :rules="rules"
    :design-state="designState"
    :parent-widget="parentWidget"
    :parent-list="parentList"
    :index-of-parent-list="indexOfParentList"
    :sub-form-row-index="subFormRowIndex"
    :sub-form-col-index="subFormColIndex"
    :sub-form-row-id="subFormRowId"
    :echo-field-data="echoFieldData"
    :sub-edit-flag="subEditFlag"
  >
    <div
      v-if="arr.length"
      ref="print"
      id="codeConfig"
      :style="{
        display: 'flex',
        width: 'fit-content',
        flexDirection:
          field.options.direction == 'horizontal' ? 'row' : 'column',
        flexWrap: 'wrap',
      }"
      :key="randomKey"
    >
      <div
        v-for="(componentData, index) in arr"
        :key="index"
        class="canvas"
        :style="{
          width: canvasStyleData.width + 'px',
          height: canvasStyleData.height + 'px',
        }"
      >
        <ComponentWrapper
          v-for="(item, index) in componentData"
          :key="index"
          :config="item"
          :designState="false"
        />
      </div>
    </div>
    <lt-empty v-else></lt-empty>
    <printBarcodeTagDialog ref="printBarcodeTagDialogRef" />
    <!-- 提供打印的标签   -->
    <!-- 创建一个隐藏的iframe用于打印 -->
    <iframe id="printFrame" style="display: none"></iframe>
  </view-wrapper>
</template>

<script>
import viewWrapper from "./view-wrapper";
import i18n, { translate } from "@/utils/i18n";
import { viewCodeManage } from "@/api/interfaces/interfaces";
import fieldMixin from "@/components/form-designer/form-widget/field-widget/fieldMixin";
import emitter from "element-ui/lib/mixins/emitter";
import ComponentWrapper from "@/directive/dragPage/Editor/ComponentWrapper";
import QRCode from "qrcode";
import { getBarcodeTag } from "@/api/ldf/ldfInfo"
import printBarcodeTagDialog from "@/views/tool/excelreport/viewer/printBarcodeTagDialog";
import { builderBarCodeTag, getBarCodeStyle } from '../../../../api/ldf/ldfInfo'
import store from "@/store";
export default {
  name: "codeConfig-widget",
  componentName: "FieldWidget", //必须固定为FieldWidget，用于接收父级组件的broadcast事件
  components: {
    viewWrapper,
    ComponentWrapper,
    printBarcodeTagDialog
  },
  mixins: [emitter, fieldMixin, i18n],
  props: {
    field: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,
    designState: {
      type: Boolean,
      default: false,
    },
    subFormRowIndex: {
      /* 子表单组件行索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormColIndex: {
      /* 子表单组件列索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormRowId: {
      /* 子表单组件行Id，唯一id且不可变 */ type: String,
      default: "",
    },
    subEditFlag: {
      type: Object,
      default: null,
    },
    echoFieldData: {
      type: Array,
      default: () => [],
    },
  },
  inject: ["refList", "formConfig", "globalOptionData", "globalModel"],
  data() {
    return {
      oldFieldValue: null, //field组件change之前的值
      fieldModel: {},
      rules: [],
      arr: [],
      canvasStyleData: {},
      randomKey: ''
    };
  },
  created() {
    /* 注意：子组件mounted在父组件created之后、父组件mounted之前触发，故子组件mounted需要用到的prop
       需要在父组件created中初始化！！ */
    this.registerToRefList();
    this.initEventHandler();
  },
  mounted() {
    if (this.field.options.codeId) {
      getBarcodeTag(this.field.options.codeId).then(r => {
        let res = JSON.parse(r.data.context);
        this.arr = [res.componentData];
        this.canvasStyleData = res.canvasStyleData;
        this.setValue(this.fieldModel)
        this.handleOnMounted();
      }).catch(err => {
        console.log(err);
      })
    }
  },
  beforeDestroy() {
    this.unregisterFromRefList();
  },
  methods: {
    initCode(codeId, callback) {
      viewCodeManage(codeId).then((r) => {
        let res = JSON.parse(r.data.codeJson);
        this.arr = [res.componentData];
        this.canvasStyleData = res.canvasStyleData;
        this.randomKey = Math.random()
        this.field.options.codeId = codeId

        callback && callback()
      });
    },
    setValue(params) {
      this.fieldModel = params
      if (Array.isArray(params)) {
        let arr = this.arr[0];
        let res = [];
        params.forEach((obj) => {
          let data = JSON.parse(JSON.stringify(arr));
          data.forEach((item) => {
            if (["v-field", "BarCode", "Table"].includes(item.component) && obj[item.column]) {
              item.propValue = obj[item.column];
            } else if (item.component == "Picture" && obj[item.column]) {
              QRCode.toDataURL(obj[item.column], (err, url) => {
                if (!err) item.propValue = url;
              });
            }
          });
          res.push(data);
        });
        this.arr = res;
      } else {
        this.arr[0] && this.arr[0].forEach((item) => {
          if (["v-field", "BarCode", "Table"].includes(item.component) && params [item.column]) {
             item.propValue = params[item.column];
          } else if (item.component == "Picture" && params[item.column]) {
            QRCode.toDataURL(params[item.column], (err, url) => {
              if (!err) item.propValue = url;
            });
          }
        })
        this.arr=this.arr.slice(0,1)
      }
    },
    print() {
      if (['', null, undefined].includes(this.field.options.codeId)) {
        this.$message.error('请传入条码id')
        return
      }

      // this.$refs.printBarcodeTagDialogRef.open(
      //   this.field.options.codeId,
      //   this.fieldModel
      // );
      this.barCodePrint(this.field.options.codeId, this.fieldModel)


      // this.$print.print(this.$refs.print);
    },
    barCodePrint(formId, multipleSelection) {
      let barCodeUids = []
      getBarCodeStyle(formId).then(style => {
        let width = (parseInt(style.data.canvasStyleData.width) / 300) * 25
        let height = (parseInt(style.data.canvasStyleData.height) / 300) * 25
        builderBarCodeTag(formId, multipleSelection).then(res => {
          res.data.forEach(uid => {
            // .replace("stage-api", "dev-api") 本地图片不显示
            barCodeUids.push(`${store.state.app.uploadUrl}/interfaces/barcodeTag/${uid}/image`)
          });
          // 获取iframe元素
          const iframe = document.getElementById('printFrame');
          const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
          // 设置iframe的内容
          iframeDoc.open();
          iframeDoc.write(`
            <!DOCTYPE html>
            <html>
              <head>
                <title>Print Preview</title>
                <style>
                  /* 在这里添加打印样式 */
                  @media print {
                    body {
                      margin: 0;
                      padding: 0;
                      transform: none;
                      -webkit-print-color-adjust: exact;
                      print-color-adjust: exact;
                    }
                    img {
                      width: ${width}mm !important;
                      height: ${height}mm !important;
                      image-rendering: pixelated;
                      -webkit-transform: none;
                      transform: none;
                      margin-left: 4mm;
                      margin-top: 2mm;
                    }
                  }
                  @page {
                    margin: 0;
                  }
                  body {
                    margin: 0;
                  }
                </style>
              </head>
              <body>
                <!-- 在这里插入要打印的内容 -->
                ${barCodeUids.map(url => `<img src="${url}" alt="Print Image" width="${width}mm" height="${height}mm">`).join('')}
              </body>
            </html>
          `);
          iframeDoc.close();

          // 等待iframe内容加载完成
          iframe.onload = function () {
            // 调用iframe的打印功能
            iframe.contentWindow.focus(); // 确保iframe获得焦点
            iframe.contentWindow.print();
          };

        });
      })
    },
  },
};
</script>

<style scoped>
.canvas {
  background: #fff;
  position: relative;
}
</style>
