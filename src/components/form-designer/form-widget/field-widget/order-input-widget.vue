<template>
  <form-item-wrapper :designer="designer" :field="field" :rules="rules" :design-state="designState" :parent-widget="parentWidget" :parent-list="parentList" :index-of-parent-list="indexOfParentList" :sub-form-row-index="subFormRowIndex" :sub-form-col-index="subFormColIndex" :sub-form-row-id="subFormRowId" :echo-field-data="echoFieldData" :sub-edit-flag="subEditFlag">
    <el-input ref="fieldEditor" v-model="fieldModel" :disabled="true" :readonly="true" :size="field.options.size" class="hide-spin-button" :type="inputType" :show-password="field.options.showPassword" :placeholder="field.options.placeholder || '提交后自动生成编码' " :clearable="field.options.clearable" :minlength="field.options.minLength" :maxlength="field.options.maxLength" :show-word-limit="field.options.showWordLimit" :prefix-icon="field.options.prefixIcon" :suffix-icon="field.options.suffixIcon" @focus="handleFocusCustomEvent" @blur="handleBlurCustomEvent" @input="handleInputCustomEvent" @change="handleChangeEvent">
      <el-button slot="append" v-if="field.options.appendButton" :disabled="field.options.disabled || field.options.appendButtonDisabled" :class="field.options.buttonIcon" @click.native="emitAppendButtonClick"></el-button>
    </el-input>
  </form-item-wrapper>
</template>

<script>
import FormItemWrapper from './form-item-wrapper';
import emitter from 'element-ui/lib/mixins/emitter';
import i18n, { translate } from '@/utils/i18n';
import fieldMixin from '@/components/form-designer/form-widget/field-widget/fieldMixin';
import { getInfoNumbering, getInfoNumberingForMap } from '@/api/columnEditApi/columnEditApi';
export default {
  name: 'order-input-widget',
  componentName: 'FieldWidget', //必须固定为FieldWidget，用于接收父级组件的broadcast事件
  mixins: [emitter, fieldMixin, i18n],
  props: {
    field: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,

    designState: {
      type: Boolean,
      default: false,
    },

    subFormRowIndex: {
      /* 子表单组件行索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormColIndex: {
      /* 子表单组件列索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormRowId: {
      /* 子表单组件行Id，唯一id且不可变 */ type: String,
      default: '',
    },
    subEditFlag: {
      type: Object,
      default: null,
    },
    echoFieldData: {
      type: Array,
      default: () => [],
    },
  },
  components: {
    FormItemWrapper,
  },
  inject: ['refList', 'formConfig', 'globalOptionData', 'globalModel'],
  data() {
    return {
      oldFieldValue: null, //field组件change之前的值
      fieldModel: null,
      rules: [],
    };
  },
  computed: {
    inputType() {
      if (this.field.options.type === 'number') {
        return 'text'; //当input的type设置为number时，如果输入非数字字符，则v-model拿到的值为空字符串，无法实现输入校验！故屏蔽之！！
      }

      return this.field.options.type;
    },
  },
  beforeCreate() {
    /* 这里不能访问方法和属性！！ */
  },

  created() {
    /* 注意：子组件mounted在父组件created之后、父组件mounted之前触发，故子组件mounted需要用到的prop
         需要在父组件created中初始化！！ */
    this.initFieldModel();
    this.registerToRefList();
    this.initEventHandler();
    this.buildFieldRules();

    this.handleOnCreated();
  },

  mounted() {
    this.handleOnMounted();
    this.buildNumber();
  },

  beforeDestroy() {
    this.unregisterFromRefList();
  },
  watch: {
    'field.options.ruleDefinition': {
      deep: true,
      handler() {
        let ruleDefinition = this.field.options.ruleDefinition
        if (ruleDefinition.length > 0) {
          let buildNum = ""
          ruleDefinition.forEach(element => {
            if (element.ruleType == 1) {
              buildNum += element.context;
            } else if (element.ruleType == 2) {
              buildNum += this.formatTime(new Date(), element.format)
            } else if (element.ruleType == 3) {
              buildNum += "{" + element.column + "}"
            } else if (element.ruleType == 4) {
              if (element.openLength) {
                buildNum += this.paddingZero(element.numberLength)
              } else {
                buildNum += "1"
              }
            }
          });
          this.setValue(buildNum)
        }
      },
    },
  },
  methods: {
    paddingZero(len) {
      let zero = "";
      for (let i = 0; i < len - 1; i++) {
        zero += "0"
      }
      return zero + "1";
    },
    formatTime(date, format) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');

      switch (format) {
        case 'yyyy': return year;
        case 'yyyyMM': return `${year}${month}`;
        case 'yyyyMMdd': return `${year}${month}${day}`;
        case 'yyyyMMddHHmm': return `${year}${month}${day}${hours}${minutes}`;
        case 'yyyyMMddHHmmss': return `${year}${month}${day}${hours}${minutes}${seconds}`;
        default: return '';
      }
    },
    buildNumber(data) {
      if (this.field.options.busType && (this.getValue() == null || this.getValue() == '')) {
        if (data) {
          data['numberingId'] = this.field.options.busType
          getInfoNumberingForMap(data).then((res) => {
            this.setValue(res.data);
          })
        } else {
          getInfoNumbering(this.field.options.busType).then((res) => {
            this.setValue(res.data);
          });
        }
      }
    },
  },
};
</script>
