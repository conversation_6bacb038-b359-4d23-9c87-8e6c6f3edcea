<template>
  <form-item-wrapper
    :designer="designer"
    :field="field"
    :rules="rules"
    :design-state="designState"
    :parent-widget="parentWidget"
    :parent-list="parentList"
    :index-of-parent-list="indexOfParentList"
    :sub-form-row-index="subFormRowIndex"
    :sub-form-col-index="subFormColIndex"
    :sub-form-row-id="subFormRowId"
    :echo-field-data="echoFieldData"
    :sub-edit-flag="subEditFlag"
  >
    <div v-if="field.options.suffixIcon" class="d-flex a-center suffix">
      <el-select
        v-if="field"
        ref="fieldEditor"
        v-model="fieldModel"
        :style="{ color: field.options.color, flex: 1 }"
        :class="['hide-spin-button', field.options.color ? 'color' : '']"
        :disabled="field.options.disabled"
        :size="field.options.size"
        :clearable="field.options.clearable"
        :filterable="field.options.filterable"
        :allow-create="field.options.allowCreate"
        :default-first-option="allowDefaultFirstOption"
        :filter-method="handleFilter"
        :automatic-dropdown="field.options.automaticDropdown"
        :multiple="field.options.multiple"
        :multiple-limit="field.options.multipleLimit"
        :placeholder="
          field.options.placeholder ||
          i18nt('render.hint.selectPlaceholder') + field.options.label
        "
        :collapse-tags="field.options.collapseTag"
        :remote="this.field.options.remote"
        :remote-method="remoteQuery"
        @focus="handleFocusCustomEvent"
        @blur="handleBlurCustomEvent"
        @change="handleChangeEvent"
        @visible-change="visibleChange"
      >
        <el-option
          v-for="item in selectData"
          :key="item[getValueField]"
          :label="item[getLabelField]"
          :value="item[getValueField]"
          :disabled="
            item.disabled ||
            (item.has_enable !== undefined ? !item.has_enable : false) ||
            item.status === '1'
          "
        >
          <span style="float: left">{{ item[getLabelField] }}</span>
          <span
            v-if="item._describe != null || item.remark != null"
            style="float: right; color: #8492a6; font-size: 13px"
            >{{ item._describe || item.remark }}</span
          >
        </el-option>
      </el-select>
      <div class="icon" @click="suffixClick">
        <i :class="[field.options.suffixIcon]"></i>
      </div>
    </div>
    <template v-else>
      <el-select
        v-if="field"
        ref="fieldEditor"
        v-model="fieldModel"
        :style="{ color: field.options.color }"
        :class="['hide-spin-button', field.options.color ? 'color' : '']"
        :disabled="field.options.disabled"
        :size="field.options.size"
        :clearable="field.options.clearable"
        :filterable="field.options.filterable"
        :allow-create="field.options.allowCreate"
        :default-first-option="allowDefaultFirstOption"
        :filter-method="handleFilter"
        :automatic-dropdown="field.options.automaticDropdown"
        :multiple="field.options.multiple"
        :multiple-limit="field.options.multipleLimit"
        :placeholder="
          field.options.placeholder ||
          i18nt('render.hint.selectPlaceholder') + field.options.label
        "
        :collapse-tags="field.options.collapseTag"
        :remote="this.field.options.remote"
        :remote-method="remoteQuery"
        @focus="handleFocusCustomEvent"
        @blur="handleBlurCustomEvent"
        @change="handleChangeEvent"
        @visible-change="visibleChange"
      >
        <el-option
          v-for="item in selectData"
          :key="item[getValueField]"
          :label="item[getLabelField]"
          :value="item[getValueField]"
          :disabled="
            item.disabled ||
            (item.has_enable !== undefined ? !item.has_enable : false) ||
            item.status === '1'
          "
        >
          <span style="float: left">{{ item[getLabelField] }}</span>
          <span
            v-if="item._describe != null || item.remark != null"
            style="float: right; color: #8492a6; font-size: 13px"
            >{{ item._describe || item.remark }}</span
          >
        </el-option>
      </el-select>
    </template>
  </form-item-wrapper>
</template>

<script>
import PinyinMatch from 'pinyin-match';
import FormItemWrapper from './form-item-wrapper';
import emitter from 'element-ui/lib/mixins/emitter';
import i18n, { translate } from '@/utils/i18n';
import fieldMixin from '@/components/form-designer/form-widget/field-widget/fieldMixin';
import { getSelectTableList } from '@/api/tool/gen';
import { getDicts } from '@/api/system/dict/data.js';

export default {
  name: 'select-widget',
  componentName: 'FieldWidget', //必须固定为FieldWidget，用于接收父级组件的broadcast事件
  mixins: [emitter, fieldMixin, i18n],
  props: {
    field: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,

    designState: {
      type: Boolean,
      default: false,
    },

    subFormRowIndex: {
      /* 子表单组件行索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormColIndex: {
      /* 子表单组件列索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormRowId: {
      /* 子表单组件行Id，唯一id且不可变 */ type: String,
      default: '',
    },
    subEditFlag: {
      type: Object,
      default: null,
    },
    echoFieldData: {
      type: Array,
      default: () => [],
    },
  },
  components: {
    FormItemWrapper,
  },
  inject: [
    'refList',
    'formConfig',
    'globalOptionData',
    'globalModel',
    'status',
  ],
  data() {
    return {
      oldFieldValue: null, //field组件change之前的值
      fieldModel: null,
      selectItem: null,
      selectData: [],
      copySelectData: [],
      rules: [],
      dataType: 'string',
    };
  },
  watch: {
    'field.options.optionItems': {
      immediate: true,
      handler(val) {
        this.selectData = this.field.options.optionItems;
        this.copySelectData = this.field.options.optionItems;
        // 转换选项中的value值为string
        this.itemToStr();
      },
      deep: true,
    },
    fieldModel(newVal, oldVal) {
      if (!newVal || !newVal.length) return;
      if (typeof newVal === 'object') {
        if (!oldVal) oldVal = [];
        let disposeArr = newVal
          .filter((item) => !oldVal.includes(item))
          .concat(oldVal.filter((item) => !newVal.includes(item)));
        if (newVal.length > oldVal.length && disposeArr.length) {
          this.existIncludeValue(disposeArr[0]);
        }
      } else {
        this.existIncludeValue(newVal);
      }
      this.oldFieldValue = this.fieldModel
    },
    allInterfaceData(data) {
      if(data[this.field.id] && this.field.options.autoLoadData) {
        this.sendApi(data[this.field.id])
      }
    }
  },
  computed: {
    allowDefaultFirstOption() {
      return (
        !!this.field.options.filterable && !!this.field.options.allowCreate
      );
    },
    color() {
      if (!!this.field.options.color) {
        return this.field.options.color;
      }
    },
    getLabelField() {
      return (
        this.field.options.modelLabel ||
        this.field.options.selectLabel ||
        'label'
      );
    },
    getValueField() {
      return (
        this.field.options.modelValue ||
        this.field.options.selectValue ||
        'value'
      );
    },
  },
  beforeCreate() {
    /* 这里不能访问方法和属性！！ */
  },

  created() {
    /* 注意：子组件mounted在父组件created之后、父组件mounted之前触发，故子组件mounted需要用到的prop
         需要在父组件created中初始化！！ */
    this.initOptionItems();
    this.initFieldModel();
    this.registerToRefList();
    this.initEventHandler();
    this.buildFieldRules();
    this.handleOnCreated();
    // this.splitValues();
  },

  mounted() {
    this.handleOnMounted();
    if(this.allInterfaceData[this.field.id] && this.field.options.autoLoadData) {
      this.sendApi(this.allInterfaceData[this.field.id]);
    }
  },

  beforeDestroy() {
    this.unregisterFromRefList();
  },
  methods: {
    suffixClick() {
      if (this.field.options.onAppendClick) {
        let fun = new Function('', this.field.options.onAppendClick);
        fun.call(this);
      }
    },
    existIncludeValue(value) {
      //是否存在创建项value
      let isTrue = false;
      this.selectData.forEach((e) => {
        if (e.value === value) {
          isTrue = true;
        }
      });
      if (!isTrue && this.field.options.allowCreateFn) {
        let JS = new Function('value', this.field.options.allowCreateFn);
        JS.call(this, value);
      }
    },
    valueToStr() {},
    sendApi(res = null) {
      if (this.status !== 'authority') {
        // 通过模型获取数据
        if (this.field.options.optionsModel) {
          const handleRes = (res) => {
            this.selectData = res.data;
            this.setSelectItem(
              this.selectData.filter(
                (item) =>
                  item[this.field.options.modelValue] === this.fieldModel,
              )[0],
            );
            this.copySelectData = Object.assign(this.selectData);
            this.handleSuccess(res);
          }
          if(res) {
            handleRes(res)
            return
          }

          getSelectTableList(
            this.field.options.optionsModel,
            this.fieldModel,
            this.getValueField,
          )
            .then((res) => {
              this.handleRes(res)
            })
            .catch((e) => {
              this.handleFail(e);
            });
        } else if (this.field.options.optionsDict) {
          const handleRes = (res) => {
            if (res.data) {
              this.selectData = res.data.map((d) => {
                return { value: d.dictValue, label: d.dictLabel };
              });
              this.copySelectData = Object.assign(this.selectData);
              this.handleSuccess(res);
            }
          }
          if(res) {
            handleRes(res)
            return
          }
          // 通过字段获取数据
          getDicts(this.field.options.optionsDict)
            .then((res) => {
              handleRes(res)
            })
            .catch((e) => {
              this.handleFail(e);
            });
        } else if (
          this.field.options.api != '' &&
          this.field.options.api != null
        ) {
          const handleRes = (res) => {
            this.selectData = res.data;
            if (this.selectData.length) {
              this.dataType = typeof this.selectData[0][this.getValueField];
            }

            this.setSelectItem(
              this.selectData.filter(
                (item) => item[this.getValueField] === this.fieldModel,
              )[0],
            );

            this.copySelectData = Object.assign(this.selectData);
            this.handleSuccess(res);
          }
          if(res) {
            handleRes(res)
            return
          }

          this.newExecuteInterface()
            .then((res) => {
              handleRes(res)
            })
            .catch((e) => {
              this.handleFail(e);
            });
        } else if (this.field.options.optionItems) {
          this.initOptionsItem(this.field.options.optionItems);
        }
      }
    },
    // 将item选项中的value的类型转换成String类型
    itemToStr() {
      if (this.selectData && this.selectData.length > 0) {
        this.selectData.forEach((item) => {
          if (typeof item[this.getValueField] == 'number') {
            item[this.getValueField] = item[this.getValueField].toString();
          }
        });
      }
      if (this.copySelectData && this.copySelectData.length > 0) {
        this.copySelectData.forEach((item) => {
          if (typeof item[this.getValueField] == 'number') {
            item[this.getValueField] = item[this.getValueField].toString();
          }
        });
      }
    },
    handleFilter(val) {
      // this.fieldModel=val
      // 对绑定数据赋值
      if (val) {
        this.selectData = this.copySelectData.filter((item) => {
          // 如果直接包含输入值直接返回true
          let label = item[this.getLabelField];
          if (label) {
            if (label.toUpperCase().includes(val.toUpperCase())) {
              return true;
            }
            // 输入值拼音d
            return PinyinMatch.match(label, val);
          }
        });
      } else {
        this.selectData = this.copySelectData;
      }
    },
    splitValues() {
      if (this.field.options.multiple && this.fieldModel) {
        if (typeof this.fieldModel == 'string') {
          this.fieldModel = this.fieldModel.split(',');
          // this.setSelectItem(
          //   this.selectData.filter((item) => this.fieldModel.includes(item[this.getValueField]))[0],
          // );
        }
      }
    },
    setSelectItem(item) {
      this.selectItem = item;
      // 转换选项中的value值为string
      this.itemToStr();
    },
    initOptionsItem(item) {
      this.selectData = item;
      this.copySelectData = Object.assign(item);
    },
    getSelectItem() {
      // if (this.selectItem) {
      //   return this.selectItem;
      // }
      if (
        (this.field.options.optionItems != null &&
          this.field.options.optionItems.length > 0) ||
        (this.selectData != null && this.selectData.length > 0)
      ) {
        let items = this.selectData || this.field.options.optionItems;
        if (this.field.options.multiple) {
          return items.filter((item) =>
            this.fieldModel.includes(item[this.getValueField]),
          );
        } else {
          return items.filter(
            (item) => item[this.getValueField] == this.fieldModel,
          )[0];
        }
      } else {
        return null;
      }
    },
    visibleChange(e) {
      if (!e) {
        setTimeout(() => {
          this.selectData = JSON.parse(JSON.stringify(this.copySelectData));
        }, 100);
      }
    },
    getSelectData() {
      return this.selectData;
    },
    handleSuccess(res) {
      if (this.field.options.onSuccessCallback) {
        let customFunc = new Function(
          'res',
          this.field.options.onSuccessCallback,
        );
        customFunc.call(this, res);
      }
    },
    handleFail(err) {
      if (this.field.options.onFailCallback) {
        let customFunc = new Function('e', this.field.options.onFailCallback);
        customFunc.call(this, err);
      }
    },
  },
};
</script>

<style lang="scss">
.color {
  .el-input__inner {
    color: var(--color);
  }
}

.full-width-input {
  width: 100% !important;
}
</style>
<style lang="scss" scoped>
::v-deep .el-select {
  width: 100%;
}
::v-deep .suffix .el-input__inner {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.suffix .icon {
  cursor: pointer;
  width: 48px;
  height: 36.67px;
  background: #f5f7fa;
  color: #909399;
  display: flex;
  justify-content: center;
  border-radius: 0 4px 4px 0;
  align-items: center;
  border: 1px solid #dcdfe6;
  border-left: none;
  & > i {
    font-size: 14px;
  }
}
</style>
