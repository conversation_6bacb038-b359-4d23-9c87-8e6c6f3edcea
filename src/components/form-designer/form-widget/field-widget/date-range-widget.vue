<template>
  <form-item-wrapper
    :designer="designer"
    :field="field"
    :rules="rules"
    :design-state="designState"
    :parent-widget="parentWidget"
    :parent-list="parentList"
    :index-of-parent-list="indexOfParentList"
    :sub-form-row-index="subFormRowIndex"
    :sub-form-col-index="subFormColIndex"
    :sub-form-row-id="subFormRowId"
    :echo-field-data="echoFieldData"
    :sub-edit-flag="subEditFlag"
  >
    <div v-if="field.options.type == 'yearrange'" style="display: flex">
      <el-date-picker
        type="year"
        v-model="begin"
        class="full-width-input"
        :disabled="field.options.disabled"
        :readonly="field.options.readonly"
        :size="field.options.size"
        :clearable="field.options.clearable"
        :editable="field.options.editable"
        format="yyyy年"
        :value-format="field.options.valueFormat"
      >
      </el-date-picker>
      <span style="margin: 0 5px">~</span>
      <el-date-picker
        type="year"
        v-model="end"
        class="full-width-input"
        :disabled="field.options.disabled"
        :readonly="field.options.readonly"
        :size="field.options.size"
        :clearable="field.options.clearable"
        :editable="field.options.editable"
        format="yyyy年"
        :value-format="field.options.valueFormat"
      >
      </el-date-picker>
    </div>
    <el-date-picker
      v-else
      ref="fieldEditor"
      :type="field.options.type"
      v-model="processValue"
      class="full-width-input"
      :disabled="field.options.disabled"
      :readonly="field.options.readonly"
      :size="field.options.size"
      :clearable="field.options.clearable"
      :editable="field.options.editable"
      :format="field.options.format"
      :value-format="field.options.valueFormat"
      :picker-options="pickerOptions"
      :start-placeholder="
        field.options.startPlaceholder ||
        i18nt('render.hint.startDatePlaceholder')
      "
      :end-placeholder="
        field.options.endPlaceholder || i18nt('render.hint.endDatePlaceholder')
      "
      @focus="handleFocusCustomEvent"
      @blur="handleBlurCustomEvent"
      @change="handleChangeEvent"
    >
    </el-date-picker>
  </form-item-wrapper>
</template>

<script>
import FormItemWrapper from './form-item-wrapper';
import emitter from 'element-ui/lib/mixins/emitter';
import i18n from '@/utils/i18n';
import fieldMixin from '@/components/form-designer/form-widget/field-widget/fieldMixin';

export default {
  name: 'date-range-widget',
  componentName: 'FieldWidget', //必须固定为FieldWidget，用于接收父级组件的broadcast事件
  mixins: [emitter, fieldMixin, i18n],
  props: {
    field: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,

    designState: {
      type: Boolean,
      default: false,
    },

    subFormRowIndex: {
      /* 子表单组件行索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormColIndex: {
      /* 子表单组件列索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormRowId: {
      /* 子表单组件行Id，唯一id且不可变 */ type: String,
      default: '',
    },
    subEditFlag: {
      type: Object,
      default: null,
    },
    echoFieldData: {
      type: Array,
      default: () => [],
    },
  },
  components: {
    FormItemWrapper,
  },
  inject: ['refList', 'formConfig', 'globalOptionData', 'globalModel'],
  data() {
    return {
      oldFieldValue: null, //field组件change之前的值
      fieldModel: null,
      rules: [],
      processValue: null,
      begin: null,
      end: null,
    };
  },
  computed: {
    pickerOptions() {
      const shortcuts = [];

      // 动态判断 shortcuts 字段的值
      if (this.field.options.type == 'monthrange') {
        shortcuts.push(
          {
            text: '本月',
            onClick(picker) {
              picker.$emit('pick', [new Date(), new Date()]);
            },
          },
          {
            text: '最近三个月',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setMonth(start.getMonth() - 3);
              picker.$emit('pick', [start, end]);
            },
          },
          {
            text: '今年至今',
            onClick(picker) {
              const end = new Date();
              const start = new Date(new Date().getFullYear(), 0);
              picker.$emit('pick', [start, end]);
            },
          },
        );
      } else if (
        this.field.options.type == 'daterange' ||
        this.field.options.type == 'datetimerange'
      ) {
        shortcuts.push(
          {
            text: '今天',
            onClick(picker) {
              const end = new Date();
              const start = new Date(new Date().toLocaleDateString());
              picker.$emit('pick', [start, end]);
            },
          },
          {
            text: '最近一周',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit('pick', [start, end]);
            },
          },
          {
            text: '最近一个月',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit('pick', [start, end]);
            },
          },
          {
            text: '最近三个月',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit('pick', [start, end]);
            },
          },
          {
            text: '上一个月',
            onClick: (picker) => {
              const now = new Date();
              const firstDayOfPreviousMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
              const lastDayOfPreviousMonth = new Date(now.getFullYear(), now.getMonth(), 0);

              picker.$emit('pick', [firstDayOfPreviousMonth, lastDayOfPreviousMonth]);
            }
          }
        );
      }
      /*
        this.getWidgetRef('daterange43877').$refs.fieldEditor.pickerOptions.shortcuts.push()
      */

      return {
        disabledDate: (time, time2) => {
          console.log('time', time);
          if (this.field.options.onDisabledDate) {
            let subFormData = null;
            if (this.subFormItemFlag || this.trendsTabFormItemFlag) {
              subFormData = this.formModel[this.subFormName];
            }
            let mountFunc = new Function(
              'value',
              'time',
              'subFormData',
              'rowId',
              this.field.options.onDisabledDate,
            );
            return mountFunc.call(
              this,
              this.fieldModel,
              time,
              subFormData,
              this.subFormRowIndex,
            );
          }
          return false;
        },
        shortcuts: shortcuts,
      };
    },
  },

  watch: {
    begin(n, o) {
      this.setValue(n + ',' + this.end);
    },
    end(n, o) {
      this.setValue(this.begin + ',' + n);
    },
    'field.options': {
      handler(newVal, oldVal) {
        this.setCurrentDate();
      },
      deep: true,
    },
    fieldModel: {
      handler(val) {
        if (val != '' && val != null) {
          if (typeof val == 'string') {
            this.processValue = val.split(',');
          }
          this.$forceUpdate();
        }
      },
      deep: true,
      immediate: true,
    },
    processValue: {
      handler(val) {
        if (val && val != '') {
          this.fieldModel = val.join();
          this.$forceUpdate();
        } else {
          this.fieldModel = '';
        }
      },
      deep: true,
      immediate: true,
    },
  },
  created() {
    /* 注意：子组件mounted在父组件created之后、父组件mounted之前触发，故子组件mounted需要用到的prop
         需要在父组件created中初始化！！ */
    this.initFieldModel();
    this.registerToRefList();
    this.initEventHandler();
    this.buildFieldRules();
    this.handleOnCreated();
    // 当当前没有值的情况再设置默认值
    if (!this.processValue || this.processValue.length === 0) {
      this.setCurrentDate();
    }
    if (!this.field.options.hasOwnProperty('shortcuts')) {
      this.$set(this.field.options, 'shortcuts', true);
    }
  },

  mounted() {
    this.handleOnMounted();
  },

  beforeDestroy() {
    this.unregisterFromRefList();
  },

  methods: {
    setCurrentDate() {
      const { defaultRange, valueFormat } = this.field.options;
      let startDate = null;
      switch (defaultRange) {
        // 当天
        case 'day':
          startDate = this.$moment();
          break;
        case 'week':
          // 本周
          startDate = this.$moment().startOf('week');
          break;
        case 'month':
          // 本月
          startDate = this.$moment().startOf('month');
          break;
        case 'lastWeek':
          // 最近一周
          startDate = this.$moment().subtract(7, 'days');
          break;
        case 'lastMonth':
          // 最近一个月
          startDate = this.$moment().subtract(30, 'days');
          break;
        case 'last3Month':
          // 最近三个月
          startDate = this.$moment().subtract(90, 'days');
          break;
      }

      startDate = startDate.format('yyyy-MM-DD');
      let endDate = this.$moment().format('yyyy-MM-DD');

      if (valueFormat === 'yyyy-MM-dd HH:mm:ss') {
        startDate += ' 00:00:00';
        endDate += ' 23:59:59';
      }
      this.setValue(startDate + ',' + endDate);
    },
  },
};
</script>
