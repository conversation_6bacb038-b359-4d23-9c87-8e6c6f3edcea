<template>
  <view-wrapper
    :designer="designer"
    :field="field"
    :rules="rules"
    :design-state="designState"
    :parent-widget="parentWidget"
    :parent-list="parentList"
    :index-of-parent-list="indexOfParentList"
    :sub-form-row-index="subFormRowIndex"
    :sub-form-col-index="subFormColIndex"
    :sub-form-row-id="subFormRowId"
    :echo-field-data="echoFieldData"
    :sub-edit-flag="subEditFlag"
  >
    <el-container>
      <el-header style="display: flex; justify-content: flex-end; align-items: center;">
        <div>
          <el-button type="primary" plain @click="handleUse" v-if="field.options.showUse">应用</el-button>
          <el-button type="primary" @click="saveData" v-if="field.options.showSave">保存 ctrl+s</el-button>
        </div>
      </el-header>
      <processRoute style="width: 100%;" ref="processRouteRef" v-loading="loading" :processConfig="field.options.processConfig" />
    </el-container>
  </view-wrapper>
</template>

<script>
import viewWrapper from './view-wrapper';
import i18n from '@/utils/i18n';
import fieldMixin from '@/components/form-designer/form-widget/field-widget/fieldMixin';
import emitter from 'element-ui/lib/mixins/emitter';
import processRoute from '@/views/processConfig/index.vue';
import {
  newExecuteInterface,
} from '@/api/interfaces/interfaces';
export default {
  name: 'processRoute-widget',
  componentName: 'FieldWidget', //必须固定为FieldWidget，用于接收父级组件的broadcast事件
  components: {
    viewWrapper,
    processRoute,
  },
  mixins: [emitter, fieldMixin, i18n],
  props: {
    field: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,

    designState: {
      type: Boolean,
      default: false,
    },

    subFormRowIndex: {
      /* 子表单组件行索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormColIndex: {
      /* 子表单组件列索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormRowId: {
      /* 子表单组件行Id，唯一id且不可变 */ type: String,
      default: '',
    },
    subEditFlag: {
      type: Object,
      default: null,
    },
    echoFieldData: {
      type: Array,
      default: () => [],
    },
  },
  inject: ['refList', 'formConfig', 'globalOptionData', 'globalModel'],
  data() {
    return {
      oldFieldValue: null, //field组件change之前的值
      fieldModel: null,
      rules: [],
      loading: false,
      disabled: false,
      nodeList: [],
    };
  },
  created() {
    /* 注意：子组件mounted在父组件created之后、父组件mounted之前触发，故子组件mounted需要用到的prop
       需要在父组件created中初始化！！ */
    this.registerToRefList();
    this.initEventHandler();
    this.handleOnCreated();
  },
  mounted() {
    this.handleOnMounted();
    this.initGraph();
    // 添加事件监听器
    document.addEventListener('keydown', this.handleSave);
  },
  beforeDestroy() {
    this.unregisterFromRefList();
    // 移除事件监听器
    document.removeEventListener('keydown', this.handleSave);
  },
  methods: {
    // 监听ctrl+s事件
    handleSave(event) {
      // 检查是否按下了 Ctrl（或 Cmd）和 S 键
      if ((event.ctrlKey || event.metaKey) && event.key === 's') {
        event.preventDefault(); // 阻止默认行为（例如弹出保存对话框）
        // 执行保存操作
        this.saveData();
      }
    },
    // 保存数据
    saveData() {
      // 前端保存画布数据
      let row = this.getGraphData();
      this.$set(this.field.options, 'jsonData', row);
      this.$message.success('保存成功');
      // 实现保存后端逻辑
      if (this.field.options.onSubSave) {
        let changeFn = new Function('row', this.field.options.onSubSave);
        changeFn.call(this, row);
      }
    },
    // 应用
    handleUse() {
      // 实现使用逻辑
      if (this.field.options.onUseData) {
        let dataTree = this.getGraphData();
        let changeFn = new Function('data', this.field.options.onUseData);
        changeFn.call(this, dataTree);
      }
    },
    // 获取左侧列表
    getNodeList() {
      newExecuteInterface({
        apiId: this.field.options.processConfig.menuList.api,
        body: { },
      })
      .then((res) => {
        this.nodeList = res.data;
        if (this.nodeList.length > 0) {
          // 将节点对应的类型绑定的视图设置进去
          this.nodeList.forEach(item => {
            item.value = this.field.options.processConfig.typeList.find(ele => ele.type == item.type)?.value;
          });
          this.initStencil();
        }
      })
      .catch((err) => {
        this.$message.error(err.message);
      });
    },
    // 初始化画布
    initGraph() {
      this.$refs.processRouteRef.initGraph();
      // 有保存的画布数据
      if (this.field.options.jsonData) {
        this.initGraphData(this.field.options.jsonData);
      }
      // 获取左侧列表
      if (this.field.options.processConfig.menuList.api) {
        this.getNodeList();
      }
    },
    // 初始化左侧节点列表
    initStencil() {
      this.$refs.processRouteRef && this.$refs.processRouteRef.initStencil(this.nodeList);
    },
    // 渲染画布数据
    initGraphData(data) {
      // 延时一点，避免画布还没渲染完成
      setTimeout(()=> {
        this.$refs.processRouteRef && this.$refs.processRouteRef.initGraphData(data);
      }, 300)
    },
    // 获取画布数据
    getGraphData() {
      return this.$refs.processRouteRef.getGraphData();
    },
    // 获取画布树状数据
    getGraphTreeData() {
      let data = this.getGraphData();
      let dataTree = convertFlowToTreeWithSort(data.cells);
      return dataTree;
    },
    // 生成树状数据
    convertFlowToTreeWithSort(cells) {
      // 创建节点映射表（id -> 节点）
      const nodeMap = {};
      // 存储节点间的父子关系（childId -> parentId）
      const parentMap = {};
      // 存储节点的直接子节点（parentId -> [childId]）
      const childrenMap = {};
      // 存储节点在原始数据中的索引位置
      const nodePositionMap = {};
      
      // 节点计数器（用于全局排序）
      let globalSortCounter = 0;
      
      // 第一遍遍历：收集所有矩形节点和位置信息
      let nodeIndex = 0;
      cells.forEach((cell, index) => {
        if (cell.shape === 'custom-rect') {
          nodeMap[cell.id] = {
            id: cell.id,
            label: cell.attrs?.text?.text || cell.data?.label || '未命名节点',
            type: cell.data?.type,
            children: []
          };
          // 记录节点在节点序列中的位置
          nodePositionMap[cell.id] = nodeIndex;
          nodeIndex++;
        }
      });

      // 第二遍遍历：处理连接线关系
      cells.forEach(cell => {
        if (cell.shape === 'edge') {
          const sourceId = cell.source.cell;
          const targetId = cell.target.cell;
          
          // 只处理存在的节点
          if (nodeMap[sourceId] && nodeMap[targetId]) {
            // 记录父子关系
            parentMap[targetId] = sourceId;
            
            // 记录子节点关系
            if (!childrenMap[sourceId]) childrenMap[sourceId] = [];
            childrenMap[sourceId].push({
              id: targetId,
              sort: nodePositionMap[targetId] // 使用节点位置作为排序依据
            });
          }
        }
      });

      // 第三遍遍历：处理群组关系
      cells.forEach(cell => {
        if (cell.shape === 'custom-rect' && cell.children) {
          const groupId = cell.id;
          
          // 确保群组节点存在
          if (nodeMap[groupId]) {
            cell.children.forEach(childId => {
              // 确保子节点存在
              if (nodeMap[childId]) {
                // 群组节点是子节点的父节点
                parentMap[childId] = groupId;
                
                // 记录子节点关系
                if (!childrenMap[groupId]) childrenMap[groupId] = [];
                childrenMap[groupId].push({
                  id: childId,
                  sort: nodePositionMap[childId] // 使用节点位置作为排序依据
                });
              }
            });
          }
        }
      });

      // 第四遍：处理显式父节点关系（如parent属性）
      cells.forEach(cell => {
        if (cell.shape === 'custom-rect' && cell.parent) {
          const childId = cell.id;
          const parentId = cell.parent;
          
          // 确保父子节点都存在
          if (nodeMap[childId] && nodeMap[parentId]) {
            // 记录父子关系
            parentMap[childId] = parentId;
            
            // 记录子节点关系
            if (!childrenMap[parentId]) childrenMap[parentId] = [];
            childrenMap[parentId].push({
              id: childId,
              sort: nodePositionMap[childId] // 使用节点位置作为排序依据
            });
          }
        }
      });

      // 找到根节点（没有父节点的节点）
      const rootNodes = Object.keys(nodeMap).filter(id => !parentMap[id]);

      // 递归构建树结构（添加全局排序功能）
      const buildTree = (nodeId) => {
        const node = { 
          ...nodeMap[nodeId],
          children: [] // 重置children，下面会重新构建
        };
        
        // 分配全局排序值
        node.sort = globalSortCounter++;
        
        // 添加子节点（按sort值排序）
        if (childrenMap[nodeId]) {
          // 按sort值升序排序
          const sortedChildren = [...childrenMap[nodeId]]
            .sort((a, b) => a.sort - b.sort);
          
          // 递归处理所有子节点
          for (const child of sortedChildren) {
            const childNode = buildTree(child.id);
            node.children.push(childNode);
          }
        }
        
        return node;
      };

      // 从根节点开始构建树（根节点按sort值排序）
      return rootNodes
        .map(id => ({ id, sort: nodePositionMap[id] }))
        .sort((a, b) => a.sort - b.sort)
        .map(item => buildTree(item.id));
    }
  },
};
</script>

<style scoped></style>
