<template>
  <view-wrapper :designer="designer" :field="field" :design-state="designState" :parent-widget="parentWidget"
    :parent-list="parentList" :index-of-parent-list="indexOfParentList" :sub-form-row-index="subFormRowIndex"
    :sub-form-col-index="subFormColIndex" :sub-form-row-id="subFormRowId" :echo-field-data="echoFieldData">
    <template v-if="field.options.customSteps && field.options.customSteps.length >= 1">
      <div class="list-box" 
        ref="listBox" 
        :class="{
          vertical: field.options.direction === 'vertical',
        }"
        @touchstart="touchstartHandler"
        @touchend="touchendHandler"
        @touchmove="touchmoveHandler"
      >
        <div class="d-flex a-center" :class="{
          vertical: field.options.direction === 'vertical',
        }" v-for="(item, index) in field.options.customSteps" :key="index">
          <div class="machineItem procedureItem" :class="{
              disabled: item.disabled,
              active: fieldModel == item.value,
            }" :style="[{
              'background': fieldModel == item.value ? field.options.highColor ? field.options.highColor : 'rgba(64, 158, 255, 1)' : '',
              'color': fieldModel == item.value ? field.options.textColor ? field.options.textColor : 'rgba(255, 255, 255, 1)' : '',
            }, rowStyle(item, index)]" 
            @click="selectStep(item)"
          >
            <span>
              <template v-if="item.icon">
                <i v-if="item.icon.includes('el-icon')"
                  :style="{ color: fieldModel == item.value ? field.options.highColor : item.color }"
                  :class="item.icon"></i>
                <svg-icon v-else :icon-class="item.icon"
                  :style="{ color: item.color }" />
              </template>
              {{ item.label }}
            </span>
          </div>
          <div class="item" v-if="index < field.options.customSteps.length - 1">
            <i class="el-icon-d-arrow-right" v-if="field.options.direction === 'horizontal'"
              style="margin: 0 20px;"></i>
            <i class="el-icon-bottom" v-else style="margin: 15px 0px;"></i>
          </div>
        </div>
      </div>
    </template>
    <el-empty v-else description="请先进行步骤配置！"></el-empty>
  </view-wrapper>
</template>

<script>
import viewWrapper from "./view-wrapper";
import i18n, { translate } from "@/utils/i18n";
import fieldMixin from "@/components/form-designer/form-widget/field-widget/fieldMixin";
import FormItemWrapper from '@/components/form-designer/form-widget/field-widget/form-item-wrapper';
import emitter from "element-ui/lib/mixins/emitter";
export default {
  name: "custom-steps-widget",
  componentName: "FieldWidget", //必须固定为FieldWidget，用于接收父级组件的broadcast事件
  components: {
    viewWrapper,
    FormItemWrapper,
  },
  mixins: [emitter, fieldMixin, i18n],
  inject: ["refList", "formConfig", "globalOptionData", "globalModel"],
  props: {
    field: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,

    designState: {
      type: Boolean,
      default: false,
    },

    subFormRowIndex: {
      /* 子表单组件行索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormColIndex: {
      /* 子表单组件列索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormRowId: {
      /* 子表单组件行Id，唯一id且不可变 */ type: String,
      default: "",
    },
    subEditFlag: {
      type: Object,
      default: null,
    },
    echoFieldData: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      fieldModel: null,
      mouseFlag: false,
      offsetLeft: 0,
      scrollLeft: 0,
      offsetTop: 0,
      scrollTop: 0,
    };
  },
  created() {
    /* 注意：子组件mounted在父组件created之后、父组件mounted之前触发，故子组件mounted需要用到的prop
       需要在父组件created中初始化！！ */
    this.initFieldModel();
    this.registerToRefList();
    this.initEventHandler();
  },
  mounted() {
    this.handleOnMounted();
    if (this.field.options.autoLoadData) {
      this.sendApi();
    }
  },
  watch:{
    fieldModel: {
      handler(n,o){
        let index = this.field.options?.customSteps?.findIndex((item) => item.value == n);
        if(index > -1) {
          // 将对应的步骤移入到中间
          setTimeout(() => {
            document.querySelectorAll(".machineItem")[index].scrollIntoView({
              behavior: 'smooth',   // 滚动行为：'auto'（默认）或 'smooth'（平滑滚动）
              block: 'center',       // 垂直对齐方式：'start'（默认），'center'，'end'，'nearest'
              inline: 'center'       // 水平对齐方式：'start'（默认），'center'，'end'，'nearest'
            })
          }, 200);
        }
        if (this.field.options.onChange) {
          let customFn = new Function('value','oldValue',this.field.options.onChange);
          customFn.call(this,n,o);
        }
      },
      immediate:true
    }
  },
  methods: {
    sendApi() {
      if(this.field.options.api != '' && this.field.options.api != null) {
        this.newExecuteInterface().then((res) => {
          this.field.options.customSteps = res.data;
          let customFunc = new Function(
            'res',
            this.field.options.onSuccessCallback,
          );
          customFunc.call(this, res);
        }).catch((err) => {
          if (this.field.options.onFailCallback) {
            let customFunc = new Function(
              'e',
              this.field.options.onFailCallback,
            );
            customFunc.call(this, err);
          }
        });
      }
    },
    selectStep(item) {
      if (item.disabled) {
        return;
      }
      this.fieldModel = item.value;
    },
    getSelectItem() {
      return this.fieldModel;
    },
    reloadOptions(data) {
      this.field.options.customSteps = data;
    },
    getSelectData() {
      return this.field.options.customSteps;
    },
    rowStyle(row, rowIndex) {
      if (this.field.options.onRowStyle) {
        let JS = new Function('row', 'rowIndex', this.field.options.onRowStyle);
        return JS.call(this, row, rowIndex);
      }
      return null;
    },
    touchstartHandler(event){
      let divData = this.$refs.listBox;
      this.mouseFlag = true;
      this.offsetLeft = event.touches[0].pageX - divData.offsetLeft;
      this.scrollLeft = divData.scrollLeft;
      this.offsetTop = event.touches[0].pageY - divData.offsetTop;
      this.scrollTop = divData.scrollTop;
    },
    touchendHandler(e){
      this.mouseFlag = false;
    },
    touchmoveHandler(e){
      let divData = this.$refs.listBox;
      if (!this.mouseFlag) return;
      e.preventDefault();
      const x = e.touches[0].pageX - divData.offsetLeft;
      const y = e.touches[0].pageY - divData.offsetTop;
      const walkX = (x - this.offsetLeft) * 2; // scroll-fast
      const walkY = (y - this.offsetTop) * 2; // scroll-fast
      divData.scrollLeft = this.scrollLeft - walkX;
      divData.scrollTop = this.scrollTop - walkY;
    },
  },
};
</script>

<style lang="scss" scoped>
.list-box {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  height: 100%;
  width: 100%;
  overflow: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
  padding: 10px;
  .machineItem {
    border: 1px solid #409EFF;
    padding: 10px 5px;
    cursor: pointer;
    border-radius: 5px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    float: left;
    background-color: #fff;
    &.disabled {
      background-color: #ccc;
    }
  }
  .procedureItem {
    width: auto;
    min-width: 120px;
    min-height: 55px;
    white-space: nowrap;
  }
  .item {
    color: #409EFF;
    i {
      font-weight: 600;
    }
  }
}
.vertical {
  flex-flow: column;
}
</style>
