<template>
  <static-content-wrapper :designer="designer" :field="field" :design-state="designState"
                          :parent-widget="parentWidget" :parent-list="parentList" :index-of-parent-list="indexOfParentList"
                          :sub-form-row-index="subFormRowIndex" :sub-form-col-index="subFormColIndex" :sub-form-row-id="subFormRowId"
                          :echo-field-data="echoFieldData" :sub-edit-flag="subEditFlag" style="width: 100%;display: block;" >
    <div ref="fieldEditor" v-html="field.options.htmlContent"></div>
  </static-content-wrapper>
</template>

<script>
  import StaticContentWrapper from './static-content-wrapper'
  import emitter from 'element-ui/lib/mixins/emitter'
  import i18n, {translate} from "@/utils/i18n";
  import fieldMixin from "@/components/form-designer/form-widget/field-widget/fieldMixin";

  export default {
    name: "html-text-widget",
    componentName: 'FieldWidget',  //必须固定为FieldWidget，用于接收父级组件的broadcast事件
    mixins: [emitter, fieldMixin, i18n],
    props: {
      field: Object,
      parentWidget: Object,
      parentList: Array,
      indexOfParentList: Number,
      designer: Object,

      designState: {
        type: Boolean,
        default: false
      },

      subFormRowIndex: { /* 子表单组件行索引，从0开始计数 */
        type: Number,
        default: -1
      },
      subFormColIndex: { /* 子表单组件列索引，从0开始计数 */
        type: Number,
        default: -1
      },
      subFormRowId: { /* 子表单组件行Id，唯一id且不可变 */
        type: String,
        default: ''
      },
      subEditFlag: {
        type: Object,
        default: null
      },
      echoFieldData:{
        type: Array,
        default:()=>[]
    },

    },
    components: {
      StaticContentWrapper,
    },
    computed: {

    },
    beforeCreate() {
      /* 这里不能访问方法和属性！！ */
    },

    created() {
      /* 注意：子组件mounted在父组件created之后、父组件mounted之前触发，故子组件mounted需要用到的prop
         需要在父组件created中初始化！！ */
      this.registerToRefList()
      this.initEventHandler()

      this.handleOnCreated()
    },

    mounted() {
      this.handleOnMounted()
    },

    beforeDestroy() {
      this.unregisterFromRefList()
    },

    methods: {
          setValue(value) {
           this.field.options.htmlContent = value;
      },
    }

  }
</script>

<style lang="scss">
// 工时统计的样式
.workHours {
  display: flex;
  flex-wrap: wrap;
  padding: 20px;
  .item {
    width: calc(25% - 15px);
    border: 2px solid #f5f6f7;
    padding: 20px;
    margin-right: 20px;
    margin-bottom: 20px;
    border-radius: 10px;
    .first {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 25px;
      & > div:nth-of-type(1) {
        :nth-child(1) {
          font-size: 20px;
          margin-bottom: 5px;
        }
        :nth-child(2) {
          color: #9faab3;
          font-size: 14px;
        }
      }
      & > div:nth-of-type(2) {
        color: var(--primary-color);
      }
    }
    .progress {
      width: 100%;
      position: relative;
      height: 6px;
      background: #E5EAF1;
      border-radius: 4px;
      margin-bottom: 15px;
      .value {
        position: absolute;
        left: 0;
        top: 0;
        background: var(--primary-color);
        height: 100%;
        border-radius: 4px;
      }
    }
    .infoItem {
      color: #9faab3;
      display: flex;
      justify-content: space-between;
      margin-bottom: 15px;
      font-size: 14px;
    }
    .modifying {
      font-size: 14px;
      padding-left: 30px;
      margin-bottom: 10px;
    }
    .start {
      font-size: 14px;
      color: #9faab3;
      padding-left: 30px;
    }
    &:nth-child(4n) {
      margin-right: 0;
    }
  }
}
</style>