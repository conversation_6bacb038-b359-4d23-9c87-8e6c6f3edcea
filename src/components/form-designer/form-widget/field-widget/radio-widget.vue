<template>
  <form-item-wrapper :designer="designer" :field="field" :rules="rules" :design-state="designState"
    :parent-widget="parentWidget" :parent-list="parentList" :index-of-parent-list="indexOfParentList"
    :sub-form-row-index="subFormRowIndex" :sub-form-col-index="subFormColIndex" :sub-form-row-id="subFormRowId"
    :echo-field-data="echoFieldData" >
    <el-radio-group ref="fieldEditor" v-model="fieldModel" :disabled="field.options.disabled" :size="field.options.size"
      @change="handleChangeEvent">
      <template v-if="!!field.options.buttonStyle">
        <el-radio-button v-for="(item, index) in field.options.optionItems" :key="index" :label="item[getValueField]"
                         :disabled="item.disabled" :border="field.options.border"   :api-id="field.options.api"
                         :style="{display: field.options.displayStyle}">{{item[getLabelField]}}</el-radio-button>
      </template>
      <template v-else>
        <el-radio v-for="(item, index) in field.options.optionItems" :key="index" :label="item[getValueField]+''"
                  :disabled="item.disabled" :border="field.options.border"   :api-id="field.options.api"
                  :style="{display: field.options.displayStyle}">{{item[getLabelField]}}</el-radio>
      </template>
    </el-radio-group>
  </form-item-wrapper>
</template>

<script>
  import FormItemWrapper from './form-item-wrapper'
  import emitter from 'element-ui/lib/mixins/emitter'
  import i18n, {translate} from "@/utils/i18n";
  import fieldMixin from "@/components/form-designer/form-widget/field-widget/fieldMixin";
  import {getCommonTableList} from "@/api/tool/gen";
  import { getDicts } from '@/api/system/dict/data.js';
  export default {
    name: "radio-widget",
    componentName: 'FieldWidget',  //必须固定为FieldWidget，用于接收父级组件的broadcast事件
    mixins: [emitter, fieldMixin, i18n],
    props: {
      field: Object,
      parentWidget: Object,
      parentList: Array,
      indexOfParentList: Number,
      designer: Object,

    designState: {
      type: Boolean,
      default: false,
    },

    subFormRowIndex: {
      /* 子表单组件行索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormColIndex: {
      /* 子表单组件列索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormRowId: {
      /* 子表单组件行Id，唯一id且不可变 */ type: String,
      default: "",
    },
    echoFieldData: {
      type: Array,
      default: () => [],
    },
  },
  watch: {
    allInterfaceData(data) {
      if(data[this.field.id] && this.field.options.autoLoadData) {
        this.sendApi(data[this.field.id])
      }
    }
  },
  computed: {
    getLabelField() {
      return this.field.options.modelLabel || 'label';
    },
    getValueField() {
      return this.field.options.modelValue || 'value';
    },
  },
  components: {
    FormItemWrapper,
  },
  inject: ["refList", "formConfig", "globalOptionData", "globalModel"],
  data() {
    return {
      oldFieldValue: null, //field组件change之前的值
      fieldModel: null,
      rules: [],
    };
  },
  created() {
    /* 注意：子组件mounted在父组件created之后、父组件mounted之前触发，故子组件mounted需要用到的prop
         需要在父组件created中初始化！！ */
    this.initOptionItems();
    this.initFieldModel();
    this.registerToRefList();
    this.initEventHandler();
    this.buildFieldRules();
    this.handleOnCreated();
  },
  mounted() {
    this.handleOnMounted();
    if(this.allInterfaceData[this.field.id] && this.field.options.autoLoadData) {
      this.sendApi(this.allInterfaceData[this.field.id]);
    }
  },
  beforeDestroy() {
    this.unregisterFromRefList();
  },
  methods: {
    getSelectData() {
      return this.field.options.optionItems;
    },
    getSelectItem() {
      if (
        (this.field.options.optionItems != null &&
          this.field.options.optionItems.length > 0)
      ) {
        let items = this.field.options.optionItems;
        if (this.field.options.multiple) {
          return items.filter(
            (item) => this.fieldModel.includes(item[this.getValueField]),
          )
        } else {
          return items.filter(
            (item) => item[this.getValueField] == this.fieldModel,
          )[0];
        }

      } else {
        return null;
      }
    },
    sendApi(res = null) {
      if (this.field.options.optionsModel) {
        if(res) {
          this.field.options.optionItems = res.data
          return
        }
        getCommonTableList(this.field.options.optionsModel).then(res =>{
          this.field.options.optionItems = res.data
        })
      } else if (this.field.options.optionsDict) {
        const handleRes = (res) => {
          if (res.data) {
            this.field.options.optionItems = res.data.map((d) => {
              return { value: d.dictValue, label: d.dictLabel };
            });
            this.handleSuccess(res)
          }
        }
        if(res) {
          handleRes(res)
          return
        }
        // 通过字段获取数据
        getDicts(this.field.options.optionsDict).then((res) => {
          handleRes(res)
        }).catch(e => {
          this.handleFail(e)
        })
      } else if(this.field.options.api!='' && this.field.options.api!=null){
        const handleRes = (res) => {
          this.field.options.optionItems = res.data;
          let customFunc = new Function(
            'res',
            this.field.options.onSuccessCallback,
          );
          customFunc.call(this, res);
        }
        if(res) {
          handleRes(res)
          return
        }
        this.newExecuteInterface().then((res) => {
          handleRes(res)
        }).catch((err) => {
          if (this.field.options.onFailCallback) {
            let customFunc = new Function(
              'e',
              this.field.options.onFailCallback,
            );
            customFunc.call(this, err);
          }
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped></style>
