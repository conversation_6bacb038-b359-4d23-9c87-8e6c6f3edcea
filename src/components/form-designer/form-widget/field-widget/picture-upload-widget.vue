<template>
  <form-item-wrapper
    :designer="designer"
    :field="field"
    :rules="rules"
    :design-state="designState"
    :parent-widget="parentWidget"
    :parent-list="parentList"
    :index-of-parent-list="indexOfParentList"
    :sub-form-row-index="subFormRowIndex"
    :sub-form-col-index="subFormColIndex"
    :sub-form-row-id="subFormRowId"
    :echo-field-data="echoFieldData" :sub-edit-flag="subEditFlag"
  >
    <lt-upload-list
      ref="fieldEditor"
      :disabled="field.options.disabled"
      :file-id="fieldModel"
      :headers="uploadHeaders"
      :multiple="field.options.multipleSelect"
      :show-file-list="field.options.showFileList"
      :fileTypes="field.options.fileTypes"
      list-type="picture-card"
      :class="{'hideUploadDiv': uploadBtnHidden}"
      :limit="field.options.limit"
      :on-exceed="handlePictureExceed"
      :before-upload="beforePictureUpload"
      :on-success="handlePictureUpload"
      :on-error="handelUploadError"
      :on-remove="handlePictureRemove"
      @onChange="change"
    />
  </form-item-wrapper>
</template>

<script>
import FormItemWrapper from './form-item-wrapper'
import emitter from 'element-ui/lib/mixins/emitter'
import i18n, { translate } from '@/utils/i18n'
import { deepClone } from '@/utils/util'
import fieldMixin from '@/components/form-designer/form-widget/field-widget/fieldMixin'
import { createDesigner } from '@/components/form-designer/designer'

export default {
  name: 'PictureUploadWidget',
  componentName: 'FieldWidget',
  components: {
    FormItemWrapper
  }, // 必须固定为FieldWidget，用于接收父级组件的broadcast事件

  mixins: [emitter, fieldMixin, i18n],
  inject: ['refList', 'formConfig', 'globalOptionData', 'globalModel'],
  props: {
    field: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,

    designState: {
      type: Boolean,
      default: false
    },

    subFormRowIndex: { /* 子表单组件行索引，从0开始计数 */
      type: Number,
      default: -1
    },
    subFormColIndex: { /* 子表单组件列索引，从0开始计数 */
      type: Number,
      default: -1
    },
    subFormRowId: { /* 子表单组件行Id，唯一id且不可变 */
      type: String,
      default: ''
    },
    subEditFlag: {
      type: Object,
      default: null
    },
    echoFieldData:{
        type: Array,
        default:()=>[]
    },

  },
  data() {
    return {
      oldFieldValue: null, // field组件change之前的值
      fieldModel: null,
      rules: [],

      uploadHeaders: {},
      uploadData: {
        key: '' // 七牛云上传文件名
      },
      fileList: [], // 上传文件列表
      uploadBtnHidden: false
    }
  },
  computed: {

  },
  watch: {
    fieldModel() {
      console.log(this.fieldModel)
    }
  },
  beforeCreate() {
  },

  created() {
    this.initFieldModel()
    this.registerToRefList()
    this.initEventHandler()
    this.buildFieldRules()

    this.handleOnCreated()
  },
  mounted() {
    this.handleOnMounted()
  },

  beforeDestroy() {
    this.unregisterFromRefList()
  },
  methods: {
    async change() {
      this.setValue(await this.$refs.fieldEditor.upload())
    },
    handlePictureExceed() {
      this.$message.error(`最多上传${this.field.options.limit}个文件`)
    },

    beforePictureUpload(file) {
      let fileTypeCheckResult = false
      if (!!this.field.options && !!this.field.options.fileTypes) {
        let uploadFileTypes = this.field.options.fileTypes
        if (uploadFileTypes.length > 0) {
          fileTypeCheckResult = uploadFileTypes.some((ft) => {
            return file.type === 'image/' + ft
          })
        }
      }
      if (!fileTypeCheckResult) {
        this.$message.error(this.i18nt('render.hint.unsupportedFileType') + file.type)
        return false
      }

      let fileSizeCheckResult = false
      let uploadFileMaxSize = 5 // 5MB
      if (!!this.field.options && !!this.field.options.fileMaxSize) {
        uploadFileMaxSize = this.field.options.fileMaxSize
      }
      fileSizeCheckResult = file.size / 1024 / 1024 <= uploadFileMaxSize
      if (!fileSizeCheckResult) {
        this.$message.error(this.$('render.hint.fileSizeExceed') + uploadFileMaxSize + 'MB')
        return false
      }

      this.uploadData.key = file.name
      return this.handleOnBeforeUpload(file)
    },

    handleOnBeforeUpload(file) {
      if (this.field.options.onBeforeUpload) {
        let bfFunc = new Function('file', this.field.options.onBeforeUpload)
        let result = bfFunc.call(this, file)
        if (typeof result === 'boolean') {
          return result
        } else {
          return true
        }
      }

      return true
    },

    handlePictureUpload(res, file, fileList) {
      if (this.field.options.onUploadSuccess) {
        let customFn = new Function('result', 'file', 'fileList', this.field.options.onUploadSuccess)
        customFn.call(this, res, file, fileList)
      } else {
        if (file.status === 'success') {
          this.fileList.push(file)
          this.updateUploadFieldModelAndEmitDataChange()

          this.uploadBtnHidden = this.fileList.length >= this.field.options.limit
          // console.log('test========', this.uploadBtnHidden)
        }
      }
    },

    handlePictureRemove(file, fileList) {
      let foundIdx = -1
      this.fileList.forEach((tf, idx) => {
        if (tf.name === file.name) {
          foundIdx = idx
        }
      })

      this.fileList = fileList
      this.uploadBtnHidden = fileList.length >= this.field.options.limit
    },

    handelUploadError(err, file, fileList) {
      if (this.field.options.onUploadError) {
        let customFn = new Function('error', 'file', 'fileList', this.field.options.onUploadError)
        customFn.call(this, err, file, fileList)
      } else {
        this.$message({
          message: this.i18nt('render.hint.uploadError') + err,
          duration: 3000,
          type: 'error'
        })
      }
    }

  }
}
</script>

<style lang="scss" scoped>
  .hideUploadDiv {
    ::v-deep div.el-upload--picture-card { /* 隐藏最后的图片上传按钮 */
      display: none;
    }

    ::v-deep div.el-upload--text { /* 隐藏最后的文件上传按钮 */
      display: none;
    }

    ::v-deep div.el-upload__tip { /* 隐藏最后的文件上传按钮提示 */
      display: none;
    }
  }

</style>
