<template>
  <static-content-wrapper
    :designer="designer"
    :field="field"
    :design-state="designState"
    :parent-widget="parentWidget"
    :parent-list="parentList"
    :index-of-parent-list="indexOfParentList"
    :sub-form-row-index="subFormRowIndex"
    :sub-form-col-index="subFormColIndex"
    :sub-form-row-id="subFormRowId"
  >
    <div :style="{color:field.options.color,fontSize:field.options.fontSize+'px',...style}" >
      <template v-if="field.options.showType == 'text'">
        {{ fieldModel }}
      </template>
      <el-tooltip
        v-else
        :content="field.options.tooltip"
        :disabled="field.options.tooltip==''"
        :effect="field.options.effect"
        :placement="field.options.placement"
      >
        <svg-icon :icon-class="field.options.textIcon" />
      </el-tooltip>
    </div>
  </static-content-wrapper>
</template>

<script>
import StaticContentWrapper from "./static-content-wrapper";
import emitter from "element-ui/lib/mixins/emitter";
import i18n, { translate } from "@/utils/i18n";
import fieldMixin from "./fieldMixin.js";

export default {
  name: "static-text-widget",
  componentName: "FieldWidget", //必须固定为FieldWidget，用于接收父级组件的broadcast事件
  mixins: [emitter, fieldMixin, i18n],
  props: {
    field: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,

    designState: {
      type: Boolean,
      default: false,
    },

    subFormRowIndex: {
      /* 子表单组件行索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormColIndex: {
      /* 子表单组件列索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormRowId: {
      /* 子表单组件行Id，唯一id且不可变 */ type: String,
      default: "",
    },
    subEditFlag: {
      type: Object,
      default: null,
    },
    echoFieldData: {
      type: Array,
      default: () => [],
    },
  },
  components: {
    StaticContentWrapper,
  },
  watch: {
    "field.options.textContent": {
      immediate: true,
      handler(val) {
        this.fieldModel = this.field.options.textContent;
      },
      deep: true,
    },
  },
  data() {
    return {
      oldFieldValue: null, //field组件change之前的值
      fieldModel: null,
      style: {},
    };
  },
  created() {
    /* 注意：子组件mounted在父组件created之后、父组件mounted之前触发，故子组件mounted需要用到的prop
         需要在父组件created中初始化！！ */
    this.initFieldModel();
    this.registerToRefList();
    this.initEventHandler();
    this.handleOnCreated();
  },

  mounted() {
    this.handleOnMounted();
    if (!this.fieldModel && this.field.options.textContent) {
      this.fieldModel = this.field.options.textContent;
    }
    if(!this.field.options.hasOwnProperty("showType")){
      this.$set(this.field.options,"showType",'text');
    }
    if(!this.field.options.fontSize){
      this.$set(this.field.options,"fontSize",16);
    }
  },

  beforeDestroy() {
    this.unregisterFromRefList();
  },

  methods: {
    setStyle(style) {
      this.style = style;
    },
  },
};
</script>
