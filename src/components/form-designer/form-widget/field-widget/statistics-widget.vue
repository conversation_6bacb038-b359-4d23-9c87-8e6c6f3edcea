<template>
  <view-wrapper
    :designer="designer"
    :field="field"
    :design-state="designState"
    :parent-widget="parentWidget"
    :parent-list="parentList"
    :index-of-parent-list="indexOfParentList"
  >
    <div
      class="statistics-box"
      :style="{
        'color': field.options.statisticsColor,
        'background-color': field.options.backgdColor,
      }"
    >
      <div
        v-if="field.options.titleContent"
        class="block"
        :style="{
          'color': field.options.statisticsColor,
          'font-size': field.options.titleSize + 'px',
        }">
        {{ field.options.titleContent }}
      </div>
      <div class="d-flex j-sb" style="overflow-x: auto">
        <div
          v-for="(item,index) in field.options.optionItems"
          :key="index"
          class="d-flex j-center a-center"
          :style="{
            'width': 100 / field.options.optionItems.length + '%',
            'min-width': '100px'
          }"
        >
          <div class="flex-1">
            <div
              class="d-flex j-center item-title"
              :style="{
                color: field.options.textColor,
                'font-size': field.options.valueSize + 'px',
              }"
            >
              {{ item[getValueField] }}
            </div>
            <div
              class="d-flex j-center"
              :style="{
                color: field.options.titleColor,
                'font-size': field.options.labelSize + 'px',
              }"
            >
              {{ item[getLabelField] }}
            </div>
          </div>
          <div
            class="divider"
            v-if="index < field.options.optionItems.length - 1"
            :style="{
              'background-color': field.options.lineColor,
            }"
          ></div>
        </div>
      </div>
    </div>
  </view-wrapper>
</template>
<script>
import viewWrapper from './view-wrapper';
import fieldMixin from '@/components/form-designer/form-widget/field-widget/fieldMixin';
import { newExecuteInterface } from '@/api/interfaces/interfaces';
export default {
  name: 'statistics-widget',
  componentName: 'FieldWidget', //必须固定为FieldWidget，用于接收父级组件的broadcast事件
  components: {
    viewWrapper,
  },
  mixins: [fieldMixin],
  props: {
    field: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,
    designState: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    getLabelField() {
      return this.field.options.selectLabel || 'label';
    },
    getValueField() {
      return this.field.options.selectValue || 'value';
    },
  },
  created() {
    /* 注意：子组件mounted在父组件created之后、父组件mounted之前触发，故子组件mounted需要用到的prop
       需要在父组件created中初始化！！ */
    this.registerToRefList();
    this.initEventHandler();
    this.handleOnCreated();
  },
  mounted() {
    this.handleOnMounted();
    if (this.field.options.autoLoadData) {
      this.sendApi();
    }
  },
  methods: {
    sendApi() {
      if (this.field.options.api) {
        // 获取参数配置
        let param = this.getRequestParam();
        newExecuteInterface({
          apiId: this.field.options.api,
          body: param,
        })
          .then((res) => {
            this.field.options.optionItems = res.data
            let customFunc = new Function(
              'res',
              this.field.options.onSuccessCallback,
            );
            customFunc.call(this, res);
          })
          .catch((err) => {
            if (this.field.options.onFailCallback) {
              let customFunc = new Function(
                'e',
                this.field.options.onFailCallback,
              );
              customFunc.call(this, err);
            }
          });
      }
    },
    setStatisticalData(data) {
      this.field.options.optionItems = data;
    }
  },
};
</script>
<style lang="scss" scoped>
.statistics-box {
  height: 100%;
  width: 100%;
}
.block {
  width: 100%;
  padding-left: 8px;
  font-weight: bold;
  margin-top: 15px;
  margin-bottom: 10px;
  font-size: 24px;
}
.divider {
  height: 30px;
  width: 2px;
  margin: 0 8px;
  vertical-align: middle;
  position: relative;
}
.item-title {
  font-weight: 600;
  font-size: 18px;
  height: 36px;
}
</style>
