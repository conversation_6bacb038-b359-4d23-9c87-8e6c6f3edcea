<template>
  <form-item-wrapper
    :designer="designer"
    :field="field"
    :rules="rules"
    :design-state="designState"
    :parent-widget="parentWidget"
    :parent-list="parentList"
    :index-of-parent-list="indexOfParentList"
    :sub-form-row-index="subFormRowIndex"
    :sub-form-col-index="subFormColIndex"
    :sub-form-row-id="subFormRowId"
    :echo-field-data="echoFieldData"
    :sub-edit-flag="subEditFlag"
    class="table-view"
  >
    <file-upload  ref="fileUpload"
                  v-model="fieldModel"
                 :uploadURL="field.options.uploadURL"
                 :limit="field.options.limit"
                 :fileSize="field.options.fileMaxSize"
                 :fileType="field.options.fileTypes"
                 :isShowTip="field.options.isShowTip"
                 :showFileList="field.options.showFileList"
                 :multipleSelect="field.options.multipleSelect"
                 :drag="field.options.fileUploadType"
                 :buttonLabel="field.options.fileUploadButtonLabel"
                 :disabled="field.options.disabled"
                 :readonly="field.options.readonly"
                 :designState="designState"
                 @handleOnBeforeUpload="handleOnBeforeUpload"
    @handleFileUpload="handleFileUpload" @handelUploadError="handelUploadError">
    </file-upload>
</form-item-wrapper>
</template>

<script>
  import FormItemWrapper from './form-item-wrapper'
  import emitter from 'element-ui/lib/mixins/emitter'
  import i18n, {translate} from "@/utils/i18n";
  import {deepClone} from "@/utils/util";
  import fieldMixin from "@/components/form-designer/form-widget/field-widget/fieldMixin";

  let selectFileText = "'" + translate('render.hint.selectFile') + "'"

  export default {
    name: "file-upload-widget",
    componentName: 'FieldWidget',  //必须固定为FieldWidget，用于接收父级组件的broadcast事件
    mixins: [emitter, fieldMixin, i18n],
    props: {
      field: Object,
      parentWidget: Object,
      parentList: Array,
      indexOfParentList: Number,
      designer: Object,

      designState: {
        type: Boolean,
        default: false
      },

      subFormRowIndex: { /* 子表单组件行索引，从0开始计数 */
        type: Number,
        default: -1
      },
      subFormColIndex: { /* 子表单组件列索引，从0开始计数 */
        type: Number,
        default: -1
      },
      subFormRowId: { /* 子表单组件行Id，唯一id且不可变 */
        type: String,
        default: ''
      },
      subEditFlag: {
        type: Object,
        default: null
      },
      echoFieldData:{
        type: Array,
        default:()=>[]
    },


    },
    components: {
      FormItemWrapper,
    },
    inject: ['refList', 'formConfig', 'globalOptionData', 'globalModel'],
    data() {
      return {
        oldFieldValue: null, //field组件change之前的值
        fieldModel: null,
        rules: [],

        uploadHeaders: {},
        uploadData: {
          key: '',  //七牛云上传文件名
          //token: '',  //七牛云上传token

          //policy: '',  //又拍云上传policy
          //authorization: '',  //又拍云上传签名
        },
        fileList: [],  //上传文件列表
        uploadBtnHidden: false,

        styleVariables: {
          '--select-file-action': selectFileText,
        },
      }
    },
    computed: {

    },
    beforeCreate() {
      /* 这里不能访问方法和属性！！ */
    },

    created() {
      /* 注意：子组件mounted在父组件created之后、父组件mounted之前触发，故子组件mounted需要用到的prop
         需要在父组件created中初始化！！ */
      this.initFieldModel()
      this.registerToRefList()
      this.initEventHandler()
      this.buildFieldRules()
      this.handleOnCreated()
    },

    mounted() {
      this.handleOnMounted()
    },

    beforeDestroy() {
      this.unregisterFromRefList()
    },

    methods: {
      clearFiles() {
        try {
          if (this.$refs.fileUpload) {
            this.$refs.fileUpload.clear()
          }
          if (this.$refs.fileUpload.$refs.upload) {
            this.$refs.fileUpload.$refs.upload.clearFiles()
          }
        }catch (e) {
          console.error('清空文件组件值失败',e )
        }
      },
      handleOnBeforeUpload(file) {
        if (!!this.field.options.onBeforeUpload) {
          let bfFunc = new Function('file', this.field.options.onBeforeUpload)
          let result = bfFunc.call(this, file)
          if (typeof result === 'boolean') {
            return result
          } else {
            return true
          }
        }
        return true
      },

      handleFileUpload(res, file, fileList) {

        this.setValue(Array.isArray(res.data)?res.data[0].fileId:res.data.fileId)
        if (!!this.field.options.onUploadSuccess) {
          let mountFunc = new Function('result', 'file', 'fileList', this.field.options.onUploadSuccess)
          mountFunc.call(this, res, file, fileList)
        }
      },


      handelUploadError(err, file, fileList) {
        if (!!this.field.options.onUploadError) {
          let customFn = new Function('error', 'file', 'fileList', this.field.options.onUploadError)
          customFn.call(this, err, file, fileList)
        }
      },

    }
  }
</script>

<style lang="scss" scoped>
  .dynamicPseudoAfter ::v-deep .el-upload.el-upload--text {
    color:  var(--primary-color);
    font-size: 12px;
    .el-icon-plus:after {
      content: var(--select-file-action);
    }
  }

  .hideUploadDiv {
    ::v-deep div.el-upload--picture-card { /* 隐藏最后的图片上传按钮 */
      display: none;
    }

    ::v-deep div.el-upload--text { /* 隐藏最后的文件上传按钮 */
      display: none;
    }

    ::v-deep div.el-upload__tip { /* 隐藏最后的文件上传按钮 */
      display: none;
    }
  }

  .upload-file-list {
    font-size: 12px;

    .file-action {
      color:  var(--primary-color);;
      margin-left: 5px;
      margin-right: 5px;
      cursor: pointer;
    }
  }

</style>
