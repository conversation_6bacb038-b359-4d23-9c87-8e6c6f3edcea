<template>
  <static-content-wrapper
    :designer="designer"
    :field="field"
    :design-state="designState"
    :parent-widget="parentWidget"
    :parent-list="parentList"
    :index-of-parent-list="indexOfParentList"
    :sub-form-row-index="subFormRowIndex"
    :sub-form-col-index="subFormColIndex"
    :sub-form-row-id="subFormRowId"
    :echo-field-data="echoFieldData"
    :sub-edit-flag="subEditFlag"
  >


    <div class="block" v-if="field.options.direction == 'vertical'">{{
      field.options.label
    }}</div>
    <el-divider
       v-else
      :direction="field.options.direction"
      :content-position="field.options.contentPosition"
    >
      {{ field.options.label }}</el-divider
    >

  </static-content-wrapper>
</template>

<script>
import StaticContentWrapper from './static-content-wrapper';
import emitter from 'element-ui/lib/mixins/emitter';
import i18n, { translate } from '@/utils/i18n';
import fieldMixin from '@/components/form-designer/form-widget/field-widget/fieldMixin';

export default {
  name: 'divider-widget',
  componentName: 'FieldWidget', //必须固定为FieldWidget，用于接收父级组件的broadcast事件
  mixins: [emitter, fieldMixin, i18n],
  props: {
    field: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,

    designState: {
      type: Boolean,
      default: false,
    },

    subFormRowIndex: {
      /* 子表单组件行索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormColIndex: {
      /* 子表单组件列索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormRowId: {
      /* 子表单组件行Id，唯一id且不可变 */ type: String,
      default: '',
    },
    subEditFlag: {
      type: Object,
      default: null,
    },
    echoFieldData: {
      type: Array,
      default: () => [],
    },
  },
  components: {
    StaticContentWrapper,
  },
  computed: {},
  beforeCreate() {
    /* 这里不能访问方法和属性！！ */
  },

  created() {
    /* 注意：子组件mounted在父组件created之后、父组件mounted之前触发，故子组件mounted需要用到的prop
         需要在父组件created中初始化！！ */
    this.registerToRefList();
    this.initEventHandler();

    this.handleOnCreated();
  },

  mounted() {
    this.handleOnMounted();
  },

  beforeDestroy() {
    this.unregisterFromRefList();
  },

  methods: {},
};
</script>

<style lang="scss" scoped>

// ::v-deep .el-divider--vertical {
//   background-color: #333;
//   border-radius: 5px;
//   width: 5px;
//   height: 18px;
// }

.block{
  width: 100%;
  height: 32px;
  padding-left: 8px;
  background: linear-gradient(65deg, rgba(49,119,255,0.15) 0%, rgba(49,119,255,0.01) 100%);
  border-left: 4px solid var(--primary-color);
  color: var(--primary-color);
  font-size: 20px;
  line-height: 32px;
  font-weight: bold;
}

::v-deep .static-content-item {
  width: 100%;
}
</style>
