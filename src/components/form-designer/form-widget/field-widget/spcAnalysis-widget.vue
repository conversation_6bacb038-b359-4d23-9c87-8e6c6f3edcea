<template>
  <view-wrapper :designer="designer" :field="field" :rules="rules" :design-state="designState"
    :parent-widget="parentWidget" :parent-list="parentList" :index-of-parent-list="indexOfParentList">
    <div v-loading="loading">
      <div>
        <el-descriptions class="mr-b10" v-if="chartData.info && chartData.info.length" :column="4" border>

          <el-descriptions-item v-for="(item, index) in chartData.info" :key="item.label" :label="item.label">
            {{ item.value }}
          </el-descriptions-item>

        </el-descriptions>
        <div v-if="chartData.info && chartData.info.length" class="d-flex flex-wrap">

          <div v-for="(item, index) in chartData.info" :key="item.label" class="d-flex j-sb a-center mr-b10"
            :class="[(index+1) % 4 == 0 ? '' : 'pd-r10']" style="width: 25%;">
            <span style="color: #606266;font-weight: bold;">{{ item.label }}:</span>
            <div style="color: #606266;width: 200px;height: 36px;background: #f5f7fa;line-height: 36px;text-align: right;padding-right: 10px;border-radius: 5px;">{{ item.value }}</div>
          </div>
        </div>
        <div class="d-flex j-sb ">
          <div class="d-flex j-sb a-center pd-r10 " style="width: 22%;">
            <span style="color: #606266;font-weight: bold;">上控制限:</span>
            <el-input-number :min="0" v-model="obj.ucl"></el-input-number>
          </div>
          <div class="d-flex j-sb a-center pd-r10 " style="width: 22%;">
            <span style="color: #606266;font-weight: bold;">下控制限:</span>
            <el-input-number :min="0" v-model="obj.lcl"></el-input-number>
          </div>
          <div class="d-flex j-sb a-center pd-r10" style="width: 22%;">
            <span style="color: #606266;font-weight: bold;">上限值:</span>
            <el-input-number :min="0" v-model="obj.usl"></el-input-number>
          </div>
          <div class="d-flex j-sb a-center " style="width: 22%;">
            <span style="color: #606266;font-weight: bold;">下限值:</span>
            <el-input-number :min="0" v-model="obj.lsl"></el-input-number>
          </div>
        </div>
        <div class="d-flex j-sb mr-tb10">
          <div style="width: 92%;">
            <div class="collapse-title" @click="isCollapse = !isCollapse">
              判异准则
              <i class="el-icon mr-l5" :class="[isCollapse ? 'el-icon-caret-top' : 'el-icon-caret-bottom']"></i>
            </div>

            <div class="collapse-box"
              :style="{ height: isCollapse ? '420px' : 0, borderBottom: isCollapse ? '1px solid #e5e5e5' : 'none' }"
              style="font-size: 14px;color: #606266;font-weight: 600">
              <div class="d-flex flex-wrap pd-lr10 rules">
                <div class="d-flex a-center">
                  <el-checkbox v-model="obj.rule1" class="mr-r10" />
                  <span>判异准则①：1个点距离中心线大于</span>
                  <el-input-number :min="0" controls-position="right" size="mini" style="width: 100px;margin: 0 5px;"
                    v-model="obj.rule1Values"></el-input-number>
                  <span>个标准差</span>
                </div>
                <div class="d-flex a-center">
                  <el-checkbox v-model="obj.rule2" class="mr-r10" />
                  <span>判异准则②：连续</span>
                  <el-input-number :min="0" controls-position="right" size="mini" style="width: 100px;margin: 0 5px;"
                    v-model="obj.rule2Values"></el-input-number>
                  <span>点在中心线的同一侧</span>
                </div>
                <div class="d-flex a-center">
                  <el-checkbox v-model="obj.rule3" class="mr-r10" />
                  <span>判异准则③：连续</span>
                  <el-input-number :min="0" controls-position="right" size="mini" style="width: 100px;margin: 0 5px;"
                    v-model="obj.rule3Values"></el-input-number>
                  <span>点全部递增或者递减</span>
                </div>
                <div class="d-flex a-center">
                  <el-checkbox v-model="obj.rule4" class="mr-r10" />
                  <span>判异准则④：连续</span>
                  <el-input-number :min="0" controls-position="right" size="mini" style="width: 100px;margin: 0 5px;"
                    v-model="obj.rule4Values"></el-input-number>
                  <span>点上下交错</span>
                </div>
                <div class="d-flex a-center">
                  <el-checkbox v-model="obj.rule5" class="mr-r10" />
                  <span>判异准则⑤：</span>
                  <el-input-number :min="0" controls-position="right" size="mini" style="width: 100px;margin: 0 5px;"
                    v-model="obj.rule5Values"></el-input-number>
                  <span>点(K)中存在K-1个点距离中心线(同侧)大于两个标准差</span>
                </div>
                <div class="d-flex a-center">
                  <el-checkbox v-model="obj.rule6" class="mr-r10" />
                  <span>判异准则⑥：</span>
                  <el-input-number :min="0" controls-position="right" size="mini" style="width: 100px;margin: 0 5px;"
                    v-model="obj.rule6Values"></el-input-number>
                  <span>点(K)中存在K-1个点距离中心线(同侧)大于一个标准差</span>
                </div>
                <div class="d-flex a-center">
                  <el-checkbox v-model="obj.rule7" class="mr-r10" />
                  <span>判异准则⑦：连续</span>
                  <el-input-number :min="0" controls-position="right" size="mini" style="width: 100px;margin: 0 5px;"
                    v-model="obj.rule7Values"></el-input-number>
                  <span>点距离中心线(任一侧)1个标准差以内</span>
                </div>
                <div class="d-flex a-center">
                  <el-checkbox v-model="obj.rule8" class="mr-r10" />
                  <span>判异准则⑧：连续</span>
                  <el-input-number :min="0" controls-position="right" size="mini" style="width: 100px;margin: 0 5px;"
                    v-model="obj.rule8Values"></el-input-number>
                  <span>点距离中心线(任一侧)大于1个标准差</span>
                </div>
                <div class="d-flex a-center">
                  <el-checkbox v-model="obj.rule9" class="mr-r10" />
                  <span>判异准则⑨：连续</span>
                  <el-input-number :min="0" controls-position="right" size="mini" style="width: 100px;margin: 0 5px;"
                    v-model="obj.rule9Value1"></el-input-number>
                  <span>点均值偏移大于</span>
                  <el-input-number :min="0" controls-position="right" size="mini" style="width: 100px;margin: 0 5px;"
                    v-model="obj.rule9Value2"></el-input-number>
                  <span>个标准差</span>
                </div>
                <div class="d-flex a-center">
                  <el-checkbox v-model="obj.rule10" class="mr-r10" />
                  <span>判异准则⑩：连续</span>
                  <el-input-number :min="0" controls-position="right" size="mini" style="width: 100px;margin: 0 5px;"
                    v-model="obj.rule10Value1"></el-input-number>
                  <span>点中</span>
                  <el-input-number :min="0" controls-position="right" size="mini" style="width: 100px;margin: 0 5px;"
                    v-model="obj.rule10Value2"></el-input-number>
                  <span>点以上距离中心线大于</span>
                  <el-input-number :min="0" controls-position="right" size="mini" style="width: 100px;margin: 0 5px;"
                    v-model="obj.rule10Value3"></el-input-number>
                  <span>个标准差</span>
                </div>
                <div class="d-flex a-center">
                  <el-checkbox v-model="obj.rule11" class="mr-r10" />
                  <span>判异准则⑪：超出内控线上限</span>
                  <el-input-number :min="0" controls-position="right" size="mini" style="width: 100px;margin: 0 5px;"
                    v-model="obj.rule11Value1"></el-input-number>
                  <span>内控线下限</span>
                  <el-input-number :min="0" controls-position="right" size="mini" style="width: 100px;margin: 0 5px;"
                    v-model="obj.rule11Value2"></el-input-number>
                  <span>个标准差</span>
                </div>
                <div class="d-flex a-center">
                  <el-checkbox v-model="obj.rule12" class="mr-r10" />
                  <span>判异准则⑫：连续</span>
                  <el-input-number :min="0" controls-position="right" size="mini" style="width: 100px;margin: 0 5px;"
                    v-model="obj.rule12Value1"></el-input-number>
                  <span>点的均值偏移T/2*</span>
                  <el-input-number :min="0" controls-position="right" size="mini" style="width: 100px;margin: 0 5px;"
                    v-model="obj.rule12Value2"></el-input-number>
                  <span>％</span>
                </div>
                <div class="d-flex a-center">
                  <el-checkbox v-model="obj.customCl" class="mr-r10" />
                  <span>使用输入的控制限</span>
                </div>
              </div>

            </div>
          </div>
          <el-button @click="analysis" type="primary" icon="el-icon-refresh-right" size="small" style="height: 40px;">重新分析</el-button>
          <el-button v-if="designState" @click="saveRules" type="primary" size="small" style="height: 40px;">
            <svg-icon
              icon-class="保存">
            </svg-icon>
            保存规则
          </el-button>
        </div>

      </div>
      <div class="d-flex j-sb mr-t10 chart-container">
        <lt-card class="chart-box long" title="I控制图">
          <el-radio-group style="position: absolute;top: 15px;right: 15px" v-model="radio"
            @change="() => { setIchart(), setMrchart() }">
            <el-radio :label="0">序号</el-radio>
            <el-radio :label="1">批次</el-radio>
            <el-radio :label="2">时间</el-radio>
          </el-radio-group>
          <lt-chart height="415px" ref="iChart"></lt-chart>
        </lt-card>
        <lt-card class="chart-box short" title="过程能力">
          <div class="d-flex j-sb">
            <div style="width: 31%;">
              <div class="bg label">过程汇总</div>
              <div class="d-flex j-sb mr-t10">
                <div class="bg" style="width: 50%;">
                  <p class="val">上规格线</p>
                  <p class="val">下规格线</p>
                  <p class="val">批次数量</p>
                  <p class="val">样本均值</p>
                  <p class="val">组内Sigma</p>
                  <p class="val">总Sigma</p>
                  <p class="val">稳定性指标</p>
                </div>
                <div class="bg right" style="width: 50%;">
                  <p class="val">{{ obj.usl }}</p>
                  <p class="val">{{ obj.lsl }}</p>
                  <p class="val">{{ chartData.va.val.num }}</p>
                  <p class="val">{{ chartData.va.val.avg }}</p>
                  <p class="val">{{ chartData.va.val.partSigma }}</p>
                  <p class="val">{{ chartData.va.val.wholeSigma }}</p>
                  <p class="val">{{ chartData.va.val.stable }}</p>
                </div>
              </div>
            </div>
            <div style="width: 31%;">
              <div class="bg label">组内Sigma能力</div>
              <div class="d-flex j-sb mr-t10">
                <div class="bg" style="width: 50%;">
                  <p class="val">Cp</p>
                  <p class="val">Cpu</p>
                  <p class="val">Cpl</p>
                  <p class="val">Cpk</p>
                </div>
                <div class="bg right" style="width: 50%;">
                  <p class="val">{{ chartData.va.val.cp }}</p>
                  <p class="val">{{ chartData.va.val.cpu }}</p>
                  <p class="val">{{ chartData.va.val.cpl }}</p>
                  <p class="val">{{ chartData.va.val.cpk }}</p>
                </div>
              </div>
            </div>
            <div style="width: 31%;">
              <div class="bg label">总Sigma能力</div>
              <div class="d-flex j-sb mr-t10">
                <div class="bg" style="width: 50%;">
                  <p class="val">Pp</p>
                  <p class="val">Ppu</p>
                  <p class="val">Ppl</p>
                  <p class="val">Ppk</p>
                </div>
                <div class="bg right" style="width: 50%;">
                  <p class="val">{{ chartData.va.val.pp }}</p>
                  <p class="val">{{ chartData.va.val.ppu }}</p>
                  <p class="val">{{ chartData.va.val.ppl }}</p>
                  <p class="val">{{ chartData.va.val.ppk }}</p>
                </div>
              </div>
            </div>
          </div>
          <div style="width: 31%;margin: 10px 0;" class="bg label">
            不合格
          </div>
          <div class="d-flex bg">
            <div style="width: 25%;padding-left: 0;" class="label">
              对应部分
            </div>
            <div style="width: 25%;text-align: right;padding-right: 0;" class="label">
              观测百分比
            </div>
            <div style="width: 25%;text-align: right;padding-right: 0;" class="label">
              期望内百分比
            </div>
            <div style="width: 25%;text-align: right;padding-right: 0;" class="label">
              期望总体百分比
            </div>
          </div>
          <div class="d-flex">
            <div style="width: 25%;" class="bg">
              <p class="val">低于下规格限</p>
              <p class="val">高于下规格限</p>
              <p class="val">规格外合计</p>
            </div>
            <div style="width: 25%;" class="right">
              <p class="val">
                {{ !chartData.va.val.unObserveL ? " " : chartData.va.val.unObserveL + "%" }}
              </p>
              <p class="val">
                {{ !chartData.va.val.unObserveU ? " " : chartData.va.val.unObserveU + "%" }}
              </p>
              <p class="val">
                {{ !chartData.va.val.unObserve ? "0" : chartData.va.val.unObserve + "%" }}
              </p>
            </div>
            <div style="width: 25%;" class="right">
              <p class="val">
                {{ !chartData.va.val.unExpectPartL ? " " : chartData.va.val.unExpectPartL + "%" }}
              </p>
              <p class="val">
                {{ !chartData.va.val.unExpectPartU ? " " : chartData.va.val.unExpectPartU + "%" }}
              </p>
              <p class="val">
                {{ !chartData.va.val.unExpectPart ? "0" : chartData.va.val.unExpectPart + "%" }}
              </p>
            </div>
            <div style="width: 25%;" class="right">
              <p class="val">
                {{ !chartData.va.val.unExpectWholeL ? " " : chartData.va.val.unExpectWholeL + "%" }}
              </p>
              <p class="val">
                {{ !chartData.va.val.unExpectWholeU ? " " : chartData.va.val.unExpectWholeU + "%" }}
              </p>
              <p class="val">
                {{ !chartData.va.val.unExpectWhole ? "0" : chartData.va.val.unExpectWhole + "%" }}
              </p>
            </div>

          </div>
          <div class="d-flex j-sb mr-t10">
            <div style="width: 20%;" class="bg label">
              COV
            </div>
            <div style="width: 20%;line-height: 30px;text-align: right;font-weight: bold">
              {{ chartData.va.val.cov }}
            </div>
            <div style="width: 20%;" class="bg label">
              Mean偏移
            </div>
            <div style="width: 20%;line-height: 30px;text-align: right;font-weight: bold">
              {{ chartData.va.val.mean }}
            </div>
          </div>
        </lt-card>
      </div>
      <div class="d-flex j-sb mr-t10 chart-container">
        <lt-card class="chart-box long" title="MR控制图">
          <lt-chart height="415px" ref="mrChart"></lt-chart>
        </lt-card>
        <lt-card class="chart-box short" title="拟合正态">
          <lt-chart height="355px" ref="ztChart"></lt-chart>
          <div class="d-flex j-sb mr-t10">
            <div class="bg label d-flex a-center" style="width: 45%;">Anderson-Darling</div>
            <div style="width: 45%;" class="d-flex j-sb">
              <div>
                <p class="val">A2</p>
                <p class="val">模拟的P值</p>
              </div>
              <div class="right">
                <p class="val"> {{ chartData.va.val.a2 }} </p>
                <p class="val" :style="{ color: chartData.va.val.p < 0.05 ? 'red' : '' }">
                  {{ chartData.va.val.p }}
                </p>
              </div>
            </div>
          </div>
        </lt-card>
      </div>
    </div>
    <el-dialog :visible="ruleVisible" title="规则" @close="ruleVisible = false">
      <el-checkbox-group v-model="warringList" @change="() => { setIchart(), setMrchart() }">
        <el-checkbox v-for="item in msg" :key="item" :label="item">{{ item }}</el-checkbox>
      </el-checkbox-group>
    </el-dialog>

  </view-wrapper>
</template>

<script>
import viewWrapper from './view-wrapper';
import i18n, { translate } from '@/utils/i18n';
import fieldMixin from '@/components/form-designer/form-widget/field-widget/fieldMixin';
import emitter from 'element-ui/lib/mixins/emitter';
import {getSpcData} from '@/api/ldf/ldfInfo.js'
export default {
  name: 'spcAnalysis-widget',
  componentName: 'FieldWidget', //必须固定为FieldWidget，用于接收父级组件的broadcast事件
  components: {
    viewWrapper,
  },
  mixins: [emitter, fieldMixin, i18n],
  props: {
    field: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,
    designState: {
      type: Boolean,
      default: false,
    },
  },
  inject: ['refList', 'formConfig', 'globalOptionData', 'globalModel'],
  data() {
    return {
      oldFieldValue: null, //field组件change之前的值
      fieldModel: null,
      rules: [],
      chartOption: null,
      radio: 0,
      ruleVisible: false,
      msg: [],
      loading: false,
      warringList: [],
      isCollapse: true,
      obj: {
        usl: null,
        lsl: null,
        ucl: null,
        lcl: null,
        rule1: false,
        rule1Values: 3,
        rule2: false,
        rule2Values: 9,
        rule3: false,
        rule3Values: 6,
        rule4: false,
        rule4Values: 14,
        rule5: false,
        rule5Values: 3,
        rule6: false,
        rule6Values: 5,
        rule7: false,
        rule7Values: 15,
        rule8: false,
        rule8Values: 8,
        rule9: false,
        rule9Values: "",
        rule9Value1: 10,
        rule9Value2: 1.5,
        rule10: false,
        rule10Values: "",
        rule10Value1: 20,
        rule10Value2: 2,
        rule10Value3: 3,
        rule11: false,
        rule11Values: "",
        rule11Value1: "",
        rule11Value2: "",
        rule12: false,
        rule12Values: "",
        rule12Value1: 10,
        rule12Value2: 30,
      },
      chartData: {
        data: [],
        va: {
          val: {},
          warring: [],
        },
        info: []
      },
      chartI: null,
      chartMr: null,
      chartZt: null,
    };
  },
  created() {
    /* 注意：子组件mounted在父组件created之后、父组件mounted之前触发，故子组件mounted需要用到的prop
       需要在父组件created中初始化！！ */
    this.registerToRefList();
    this.initEventHandler();
    this.handleOnCreated();
  },
  mounted() {
    this.handleOnMounted();
    // 判断有之前保存的判异规则
    if (Object.keys(this.field.options.spcConfig).length > 0) {
      this.setValue(this.field.options.spcConfig);
    }
    this.$refs.iChart.showEmpty()
    this.$refs.mrChart.showEmpty()
    this.$refs.ztChart.showEmpty()
  },
  beforeDestroy() {
    this.unregisterFromRefList();
  },
  watch: {
    obj: {
      deep: true,
      handler(newVal, oldVal) {
        if (this.field.options.onFormChange) {
          let fun = new Function('data', this.field.options.onFormChange);
          fun.call(this, this.getValue());
        }
      }
    }
  },
  methods: {
    saveRules() {
      this.$set(this.field.options, 'spcConfig', this.getValue());
      this.$message.success('保存成功');
    },
    analysis(){
      this.loading = true;
      getSpcData({
        spcConfig:this.getValue(),
        configData:this.chartData.data
      }).then(res=>{
        this.setData(res.data)
      }).finally(()=>{
        this.loading=false
      })
    },
    setValue(data) {
      if (data.rule9Values&&data.rule9Values.includes(",")) {
        data.rule9Value1 = data.rule9Values.split(",")[0];
        data.rule9Value2 = data.rule9Values.split(",")[1];
      }
      if (data.rule10Values&&data.rule10Values.includes(",")) {
        data.rule10Value1 = data.rule10Values.split(",")[0];
        data.rule10Value2 = data.rule10Values.split(",")[1];
        data.rule10Value3 = data.rule10Values.split(",")[2];
      }
      if (data.rule11Values&&data.rule11Values.includes(",")) {
        data.rule11Value1 = data.rule11Values.split(",")[0];
        data.rule11Value2 = data.rule11Values.split(",")[1];
      }
      if (data.rule12Values && data.rule12Values.includes(",")) {
        data.rule12Value1 = data.rule12Values.split(",")[0];
        data.rule12Value2 = data.rule12Values.split(",")[1];
      }
      this.obj = {...this.obj,...data}
    },
    getValue() {
      let data = structuredClone(this.obj)
      data.rule9Values = data.rule9Value1 + "," + data.rule9Value2;
      data.rule10Values = data.rule10Value1 + "," + data.rule10Value2 + "," + data.rule10Value3;
      data.rule11Values = (data.rule11Value1 || "") + (data.rule11Value2 ? "," + data.rule11Value2 : "");
      data.rule12Values = data.rule12Value1 + "," + data.rule12Value2;
      return data
    },
    setLoading(flag) {
      this.loading = flag
    },
    setData(data) {
      for(let k in data.va.val){
        if(data.va.val[k] == 'NaN'){
          data.va.val[k] = ''
        }
      }
      this.chartData = data


      setTimeout(() => {
        this.setIchart();
        this.setMrchart();
        this.setZtchart();
      }, 200);

    },
    setIchart() {
      let data = this.chartData;
      let radio = this.radio;
      let xAxis = [];
      let yAxis = [];
      data.data.forEach((item, index) => {
        if (radio == 0) {
          xAxis.push(index + 1 + "");
        } else if (radio == 1) {
          xAxis.push(item.sampleNo);
        } else {
          xAxis.push(item.sampleTime);
        }
        yAxis.push(item.value);
      });
      let pointData = [];
      data.va.warring.forEach((item) => {
        if (item.i.length) {
          item.i.forEach((i, index) => {
            let obj = pointData.find(
              (e) => e.xAxis == xAxis[i] && e.yAxis == yAxis[i]
            );
            if (obj) {
              obj.msg.push(item.title);
            } else {
              pointData.push({
                xAxis: xAxis[i],
                yAxis: yAxis[i],
                msg: [item.title],
              });
            }
          });
        }
      });
      let legendArr = ["I值", "USL", "UCL", "CL", "LCL", "LSL"]
      if (!data.va.val.usl) {
        legendArr = legendArr.filter(item => item != 'USL')
      }
      if (data.va.val.lcl <= 0) {
        legendArr = legendArr.filter(item => item != 'LCL')
      }
      if (!data.va.val.lsl) {
        legendArr = legendArr.filter(item => item != 'LSL')
      }
      var option = {
        backgroundColor: '',
        tooltip: {
          trigger: "axis",
          borderWidth: 0,
          axisPointer: {
            lineStyle: {
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                global: false,
              },
            },
          },
          formatter: function (value) {
            let msg = "";
            if (value.length > 0) {
              let obj = pointData.find((e) => e.xAxis == value[0].name);
              if (obj) {
                obj.msg.forEach((text) => {
                  msg += text + "<br>";
                });
              }
              msg += "选中异常点：" + value[0].name + "<br>";
              if (radio == 2) {
                msg += "选中批次：" + data.data[value[0].dataIndex].sampleNo + "<br>";
              }
              for (let i in value) {
                if (value[i].seriesName == "I值") {
                  msg +=
                    " <div style='width: 10px;height: 10px;background-color: #2ec7c9; border-radius: 50%;display: inline-block;'></div> &nbsp;&nbsp;";
                } else if (
                  value[i].seriesName == "USL" ||
                  value[i].seriesName == "LSL"
                ) {
                  msg +=
                    " <div style='width: 10px;height: 10px;background-color: #aa0000; border-radius: 50%;display: inline-block;'></div> &nbsp;&nbsp;";
                } else if (
                  value[i].seriesName == "UCL" ||
                  value[i].seriesName == "CL" ||
                  value[i].seriesName == "LCL"
                ) {
                  msg +=
                    " <div style='width: 10px;height: 10px;background-color: #01a2d9; border-radius: 50%;display: inline-block;'></div> &nbsp;&nbsp;";
                }
                msg += value[i].seriesName + "：" + value[i].data + "<br>";
              }
            }
            return msg;
          },
        },
        legend: {
          top: "bottom",
          data: legendArr,
        },
        grid: {
          top: "8%",
          bottom: "15%",
          left: "4%",
          right: "4%",
          containLabel: true,
        },
        calculable: true,
        dataZoom: [
          {
            type: "slider",
            maxSpan: Math.min(100, 5000 / xAxis.length),
          },
        ],
        xAxis: [
          {
            splitLine: {
              show: false,
            },
            data: xAxis,
            type: "category",
            boundaryGap: false,
            axisLabel: {
              show: true,
            },
          },
        ],
        yAxis: [
          {
            type: "value",
            scale: true,
            splitLine: {
              show: false,
            },
            // axisTick: {
            //    show: true // 显示刻度线
            // },
            axisLine: {
              show: true // 显示刻度线
            },
            splitArea: false
          },
        ],
        series: [
          {
            name: "I值",
            yAxisIndex: 0,
            data: yAxis, //[0.5, 0.3, 0.6, 0.8, 0.9, 1, 0.4, 0.2, 0.1, 0.5, 0.7, 0.9],
            type: "line",
            symbol: "circle", // 设置标记的图形为circle
            symbolSize: 9, //折线点的大小
            smooth:false,
            lineStyle: {
              width: 2,
              type: "solid", //'dotted'虚线 'solid'实线
              color: "#2ec7c9",
            },
            itemStyle: {
              emphasis: {
                shadowColor: "#01a2d9",
                shadowBlur: 15,
                shadowOffsetX: 0,
                shadowOffsetY: 0,
              },
              color: (params) => {
                if (pointData.find((e) => e.xAxis == params.name)) {
                  let obj = pointData.find((e) => e.xAxis == params.name);
                  if (obj.msg.some(e => this.warringList.includes(e))) {
                    return '#FF0000'
                  } else {
                    return "#fc5531";
                  }
                }
                return "#2ec7c9";
              },
            },
          },
          {
            name: "USL",
            data: new Array(xAxis.length).fill(data.va.val.usl), //[0.7, 0.7, 0.7, 0.7, 0.7, 0.7, 0.7, 0.7, 0.7, 0.7, 0.7, 0.7],
            type: "line",
            symbol: "circle", // 设置标记的图形为circle
            symbolSize: 3, //折线点的大小
            itemStyle: {
              normal: {
                color: "#aa0000",
                lineStyle: {
                  width: 2,
                  type: "solid", //'dotted'虚线 'solid'实线
                  color: "#aa0000",
                },
              },
            },
          },
          {
            name: "UCL",
            data: new Array(xAxis.length).fill(data.va.val.ucl), //[0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3],
            type: "line",
            symbol: "circle", // 设置标记的图形为circle
            symbolSize: 0, //折线点的大小
            itemStyle: {
              normal: {
                color: "#01a2d9",
                lineStyle: {
                  width: 3,
                  type: "dotted", //'dotted'虚线 'solid'实线
                  color: "#01a2d9",
                },
              },
            },
          },
          {
            name: "CL",
            data: new Array(xAxis.length).fill(data.va.val.cl), //[0.2, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2,],
            type: "line",
            symbol: "circle", // 设置标记的图形为circle
            symbolSize: 0, //折线点的大小
            itemStyle: {
              normal: {
                color: "#01a2d9",
                lineStyle: {
                  width: 3,
                  type: "dotted", //'dotted'虚线 'solid'实线
                  color: "#01a2d9",
                },
              },
            },
          },
          {
            name: "LCL",
            data: new Array(xAxis.length).fill(data.va.val.lcl < 0 ? null : data.va.val.lcl), //[0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4,],
            type: "line",
            symbol: "circle", // 设置标记的图形为circle
            symbolSize: 0, //折线点的大小
            itemStyle: {
              normal: {
                color: "#01a2d9",
                lineStyle: {
                  width: 3,
                  type: "dotted", //'dotted'虚线 'solid'实线
                  color: "#01a2d9",
                },
              },
            },
          },
          {
            name: "LSL",
            data: new Array(xAxis.length).fill(data.va.val.lsl), //[0.7, 0.7, 0.7, 0.7, 0.7, 0.7, 0.7, 0.7, 0.7, 0.7, 0.7, 0.7],
            type: "line",
            symbol: "circle", // 设置标记的图形为circle
            symbolSize: 3, //折线点的大小
            itemStyle: {
              normal: {
                color: "#aa0000",
                lineStyle: {
                  width: 2,
                  type: "solid", //'dotted'虚线 'solid'实线
                  color: "#aa0000",
                },
              },
            },
          },
        ],
      };
      if (this.obj.rule11) {
        if (this.obj.rule11Value1) {
          option.legend.data.push('内控线上限')
          option.series.push({
            name: "内控线上限",
            data: new Array(xAxis.length).fill(this.obj.rule11Value1), //[0.7, 0.7, 0.7, 0.7, 0.7, 0.7, 0.7, 0.7, 0.7, 0.7, 0.7, 0.7],
            type: "line",
            symbol: "circle", // 设置标记的图形为circle
            symbolSize: 3, //折线点的大小
            itemStyle: {
              normal: {
                color: "#259821",
                lineStyle: {
                  width: 2,
                  type: "solid", //'dotted'虚线 'solid'实线
                  color: "#259821",
                },
              },
            },
          })
        }
        if (this.obj.rule11Value2) {
          option.legend.data.push('内控线下限')
          option.series.push({
            name: "内控线下限",
            data: new Array(xAxis.length).fill(this.obj.rule11Value2), //[0.7, 0.7, 0.7, 0.7, 0.7, 0.7, 0.7, 0.7, 0.7, 0.7, 0.7, 0.7],
            type: "line",
            symbol: "circle", // 设置标记的图形为circle
            symbolSize: 3, //折线点的大小
            itemStyle: {
              normal: {
                color: "#259821",
                lineStyle: {
                  width: 2,
                  type: "solid", //'dotted'虚线 'solid'实线
                  color: "#259821",
                },
              },
            },
          })
        }

      }

      let ref = this.$refs.iChart.getChartObj()
      ref.off("click");

      ref.on("click", (param) => {
        if (
          param.componentType == "series" &&
          param.seriesName == "I值" &&
          pointData.find((e) => e.xAxis == param.name)
        ) {
          this.msg = pointData.find((e) => e.xAxis == param.name).msg
          this.ruleVisible = true
        }
      });
      ref.setOption(option, true);

    },
    setMrchart() {
      let data = this.chartData;
      let radio = this.radio;
      let xAxis = [];
      let yAxis = [null];
      data.data.forEach((item, index) => {
        if (radio == 0) {
          xAxis.push(index + 1 + "");
        } else if (radio == 1) {
          xAxis.push(item.sampleNo);
        } else {
          xAxis.push(item.sampleTime);
        }
        yAxis.push(data.va.val.mrArr[index]);
      });
      let pointData = [];
      data.va.warring.forEach((item) => {
        if (item.mr.length) {
          item.mr.forEach((i, index) => {
            i += 1;
            let obj = pointData.find(
              (e) => e.xAxis == xAxis[i] && e.yAxis == yAxis[i]
            );
            if (obj) {
              obj.msg.push(item.title);
            } else {
              pointData.push({
                xAxis: xAxis[i],
                yAxis: yAxis[i],
                msg: [item.title],
              });
            }
          });
        }
      });
      let legendArr = ["MR值", "USL", "UCL", "CL", "LCL", "LSL"]
      if (!data.va.val.usl) {
        legendArr = legendArr.filter(item => item != 'USL')
      }
      if (data.va.val.lcl <= 0) {
        legendArr = legendArr.filter(item => item != 'LCL')
      }
      if (!data.va.val.lsl) {
        legendArr = legendArr.filter(item => item != 'LSL')
      }
      var option = {
        tooltip: {
          trigger: "axis",
          borderWidth: 0,
          axisPointer: {
            lineStyle: {
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                global: false,
              },
            },
          },
          formatter: function (value) {
            let msg = "";
            if (value.length > 0) {
              let obj = pointData.find((e) => e.xAxis == value[0].name);
              if (obj) {
                obj.msg.forEach((text) => {
                  msg += text + "<br>";
                });
              }
              msg += "选中异常点：" + value[0].name + "<br>";
              for (let i in value) {
                if (value[i].seriesName == "MR值") {
                  msg +=
                    " <div style='width: 10px;height: 10px;background-color: #2ec7c9; border-radius: 50%;display: inline-block;'></div> &nbsp;&nbsp;";
                } else if (
                  value[i].seriesName == "USL" ||
                  value[i].seriesName == "LSL"
                ) {
                  msg +=
                    " <div style='width: 10px;height: 10px;background-color: #aa0000; border-radius: 50%;display: inline-block;'></div> &nbsp;&nbsp;";
                } else if (
                  value[i].seriesName == "UCL" ||
                  value[i].seriesName == "CL" ||
                  value[i].seriesName == "LCL"
                ) {
                  msg +=
                    " <div style='width: 10px;height: 10px;background-color: #01a2d9; border-radius: 50%;display: inline-block;'></div> &nbsp;&nbsp;";
                }
                msg += value[i].seriesName + "：" + value[i].data + "<br>";
              }
            }
            return msg;
          },
        },
        legend: {
          top: "bottom",
          data: legendArr,
        },
        grid: {
          top: "8%",
          bottom: "15%",
          left: "4%",
          right: "4%",
          containLabel: true,
        },
        calculable: true,
        dataZoom: [
          {
            type: "slider",
            maxSpan: Math.min(100, 5000 / xAxis.length),
          },
        ],
        xAxis: [
          {
            splitLine: {
              show: false,
            },
            data: xAxis,
            type: "category",
            boundaryGap: false,
            axisLabel: {
              show: true,
            },
          },
        ],
        yAxis: [
          {
            type: "value",
            scale: true,
            splitLine: {
              show: false,
            },
            axisLine: {
              show: true // 显示刻度线
            },
            splitArea: false
          },
        ],
        series: [
          {
            name: "MR值",
            yAxisIndex: 0,
            data: yAxis, //[0.5, 0.3, 0.6, 0.8, 0.9, 1, 0.4, 0.2, 0.1, 0.5, 0.7, 0.9],
            type: "line",
            symbol: "circle", // 设置标记的图形为circle
            symbolSize: 9, //折线点的大小
            smooth:false,
            lineStyle: {
              width: 2,
              type: "solid", //'dotted'虚线 'solid'实线
              color: "#2ec7c9",
            },
            itemStyle: {
              emphasis: {
                shadowColor: "#01a2d9",
                shadowBlur: 15,
                shadowOffsetX: 0,
                shadowOffsetY: 0,
              },
              color: (params) => {
                if (pointData.find((e) => e.xAxis == params.name)) {
                  let obj = pointData.find((e) => e.xAxis == params.name);
                  if (obj.msg.some(e => this.warringList.includes(e))) {
                    return '#FF0000'
                  } else {
                    return "#fc5531";
                  }
                }
                return "#2ec7c9";
              },
            },
          },
          {
            name: "UCL",
            data: new Array(xAxis.length).fill(data.va.val.mucl), //[0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3, 0.3],
            type: "line",
            symbol: "circle", // 设置标记的图形为circle
            symbolSize: 0, //折线点的大小
            itemStyle: {
              normal: {
                color: "#01a2d9",
                lineStyle: {
                  width: 3,
                  type: "dotted", //'dotted'虚线 'solid'实线
                  color: "#01a2d9",
                },
              },
            },
          },
          {
            name: "CL",
            data: new Array(xAxis.length).fill(data.va.val.mcl), //[0.2, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2,],
            type: "line",
            symbol: "circle", // 设置标记的图形为circle
            symbolSize: 0, //折线点的大小
            itemStyle: {
              normal: {
                color: "#01a2d9",
                lineStyle: {
                  width: 3,
                  type: "dotted", //'dotted'虚线 'solid'实线
                  color: "#01a2d9",
                },
              },
            },
          },
          {
            name: "LCL",
            data: new Array(xAxis.length).fill(data.va.val.mlcl), //[0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4,],
            type: "line",
            symbol: "circle", // 设置标记的图形为circle
            symbolSize: 0, //折线点的大小
            itemStyle: {
              normal: {
                color: "#01a2d9",
                lineStyle: {
                  width: 3,
                  type: "dotted", //'dotted'虚线 'solid'实线
                  color: "#01a2d9",
                },
              },
            },
          },
        ],
      };
      let ref = this.$refs.mrChart.getChartObj()
      ref.off("click");
      ref.on("click", (param) => {
        if (
          param.componentType == "series" &&
          param.seriesName == "MR值" &&
          pointData.find((e) => e.xAxis == param.name)
        ) {

          this.msg = pointData.find((e) => e.xAxis == param.name).msg
          this.ruleVisible = true

        }
      });
      ref.setOption(option, true);
    },
    setZtchart() {
      let data = this.chartData;
      let markLineList = [];
      if (data.va.val.usl) {
        markLineList.push({
          xAxis: data.va.val.fitNormal.length - 1,
          label: { formatter: `USL:${data.va.val.usl}` },
        });
      }
      if (data.va.val.lsl) {
        markLineList.push({
          xAxis: 0,
          label: { formatter: `LSL:${data.va.val.lsl}` },
        });
      }

      this.$refs.ztChart.setOption({
        tooltip: {
          trigger: "axis",
          borderWidth: 0,
          axisPointer: {
            lineStyle: {
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                global: false,
              },
            },
          },
          formatter: function (value) {

            let arr = data.va.val.fitNormal.map((e) => e.x)

            let half = (+arr[1]) - (+arr[0])
            let msg = "";
            msg += value[0].name + "<br>"
            msg += "start：" + ((+value[0].name) - (half / 2)).toFixed(4) + "<br>" + "end：" + ((+value[0].name) + (half / 2)).toFixed(4) + "<br>"
            for (let i in value) {
              if (value[i].seriesName == "") {
                msg +=
                  "value";
              } else if (value[i].seriesName == "拟合曲线(组内)") {
                msg +=
                  " <div style='width: 10px;height: 10px;background-color: #01a2d9; border-radius: 50%;display: inline-block;'></div>&nbsp;";
              } else if (value[i].seriesName == "拟合曲线(整体)") {
                msg +=
                  " <div style='width: 10px;height: 10px;background-color: #046b9d; border-radius: 50%;display: inline-block;'></div>&nbsp;";
              }
              msg += value[i].seriesName + "：" + value[i].data + "<br>";
            }
            return msg;
          },
        },
        legend: {
          top: "bottom",
          // data: data.VIEWNAME
          data: ["拟合曲线(整体)", "拟合曲线(组内)"],
        },
        grid: {
          top: "10%",
          left: "10%",
          right: "8%",
          bottom: "20%",
        },
        calculable: true,
        xAxis: [
          {
            splitLine: {
              show: false,
            },
            data: data.va.val.fitNormal.map((e) => e.x),
            type: "category",
            // boundaryGap: false,
            axisLabel: {
              interval: 0,
              rotate: 30, //倾斜的程度
            },
          },
        ],
        yAxis: [
          {
            splitLine: {
              show: false,
            },
            axisLine: {
              show: true // 显示刻度线
            },
            splitArea: false
          },
          {
            position: "right",
            axisTick: {
              show: false,
            },
            axisLabel: {
              show: true,
              interval: "auto",
            },
            show: true,
            splitLine: {
              show: false,
            },
            axisLine: {
              show: true // 显示刻度线
            },
            splitArea: false
          },
        ],
        series: [
          {
            name: "",
            yAxisIndex: 1,
            data: data.va.val.fitNormal.map((e) => e.num), //[0, 2, 3, 4, 5, 6, 5, 4, 3],
            type: "bar",
            //barWidth: 20,
            barGap: "0%",
            barCategoryGap: "0%",
            label: {
              show: true,
              position: "top",
              textStyle: {
                fontSize: 10,
                color: "#474848",
              },
            },
            itemStyle: {
              normal: {
                color: "#01a2d9",
              },
              emphasis: {
                shadowBlur: 20,
                shadowOffsetX: 0,
                shadowColor: "#01a2d9",
              },
            },
            markLine: {
              symbol: ["none"],
              label: { show: true },
              lineStyle: {
                color: "#ff0000",
                width: 2,
                type: 'solid'
              },
              data: markLineList,
            },
          },
          {
            name: "拟合曲线(整体)",
            data: data.va.val.fitNormal.map((e) => e.whole), //[5.5, 6, 6.5, 6, 5.5, 5],
            type: "line",
            smooth: true,
            symbol: "circle", // 设置标记的图形为circle
            symbolSize: 3, //折线点的大小
            itemStyle: {
              normal: {
                color: "#84a7b8",
                lineStyle: {
                  width: 3,
                  type: "dotted", //'dotted'虚线 'solid'实线
                  color: "#84a7b8",
                },
              },
            },
          },
          {
            name: "拟合曲线(组内)",
            data: data.va.val.fitNormal.map((e) => e.part), //[5.5, 6, 5.5],
            type: "line",
            smooth: true,
            symbol: "circle", // 设置标记的图形为circle
            symbolSize: 3, //折线点的大小
            itemStyle: {
              color: "#2ec7c9",
              normal: {
                color: "#2ec7c9",
                lineStyle: {
                  width: 2,
                  type: "solid", //'dotted'虚线 'solid'实线
                  color: "#2ec7c9",
                },
              },
            },
          },
        ],
      });
    }
  },
};
</script>

<style scoped lang="scss">
.collapse-title {
  height: 40px;
  border: 1px solid #e5e5e5;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #606266;
  cursor: pointer;
  font-weight: bold;

  &:hover {
    background: #f5f7fa;
    color: #409eff;
  }
}

.collapse-box {
  transition: all 0.3s;
  transform-origin: top;
  border: 1px solid #e5e5e5;
  border-top: none;
  display: flex;
  justify-content: center;
  overflow: hidden;

  .rules {
    &>div:nth-child(odd) {
      width: 45%;
    }
    &>div:nth-child(even) {
      width: 55%;
    }
  }
}

.chart-container {
  height: 480px;

  .chart-box {
    height: 100%;
    border: 1px solid #e5e5e5;
    position: relative;
    line-height: 20px;

    &.long {
      width: 60.5%;
    }

    &.short {
      width: 39%;
    }

    .bg {
      background: #f5f7fa;
      padding: 0 5px;
    }

    .right>p {
      text-align: right;
      min-height: 18px;
    }

    p {
      margin: 0 0 5px 0;
    }

    .label {
      padding: 5px;
      font-size: 14px;
      font-weight: bold;
    }

    .val {
      font-size: 12px;
      font-weight: bold;
    }
  }
}
</style>
