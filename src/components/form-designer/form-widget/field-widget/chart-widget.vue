<template>
  <view-wrapper :designer="designer" :field="field" :rules="rules" :design-state="designState"
    :parent-widget="parentWidget" :parent-list="parentList" :index-of-parent-list="indexOfParentList"
    :sub-form-row-index="subFormRowIndex" :sub-form-col-index="subFormColIndex" :sub-form-row-id="subFormRowId"
    :echo-field-data="echoFieldData" :sub-edit-flag="subEditFlag">
    <lt-chart ref="chart" :theme="field.options.theme" :height="(field.options.height || 300) + 'px'" />
  </view-wrapper>
</template>

<script>
import viewWrapper from './view-wrapper';
import i18n, { translate } from '@/utils/i18n';
import fieldMixin from '@/components/form-designer/form-widget/field-widget/fieldMixin';
import emitter from 'element-ui/lib/mixins/emitter';
export default {
  name: 'chart-widget',
  componentName: 'FieldWidget', //必须固定为FieldWidget，用于接收父级组件的broadcast事件
  components: {
    viewWrapper,
  },
  mixins: [emitter, fieldMixin, i18n],
  props: {
    field: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,

    designState: {
      type: Boolean,
      default: false,
    },

    subFormRowIndex: {
      /* 子表单组件行索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormColIndex: {
      /* 子表单组件列索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormRowId: {
      /* 子表单组件行Id，唯一id且不可变 */ type: String,
      default: '',
    },
    subEditFlag: {
      type: Object,
      default: null,
    },
    echoFieldData: {
      type: Array,
      default: () => [],
    },
  },
  inject: ['refList', 'formConfig', 'globalOptionData', 'globalModel'],
  data() {
    return {
      oldFieldValue: null, //field组件change之前的值
      fieldModel: null,
      rules: [],
      chartOption: null,
    };
  },
  created() {
    /* 注意：子组件mounted在父组件created之后、父组件mounted之前触发，故子组件mounted需要用到的prop
       需要在父组件created中初始化！！ */
    this.registerToRefList();
    this.initEventHandler();
    this.handleOnCreated();
  },
  mounted() {
    this.handleOnMounted();
    if (!this.chartOption) {
      this.showEmpty();
    }
  },
  beforeDestroy() {
    this.unregisterFromRefList();
  },
  methods: {
    initChart(chartOption) {
      if (JSON.stringify(chartOption) === JSON.stringify(this.chartOption)) {
        this.resize();
        return;
      }

      this.$nextTick(() => {
        this.chartOption = chartOption;
        this.$refs.chart.initChart(chartOption);
      });
    },
    getChartObj() {
      return this.$refs.chart.getChartObj();
    },
    setOption(option) {
      this.$refs.chart.setOption(option);
    },
    showEmpty() {
      this.chartOption = null
      this.$refs.chart.showEmpty();
    },
    resize() {
      this.$refs.chart.changeReSize();
    },
    showLoading() {
      this.$refs.chart.showLoading();
    },
    hideLoading() {
      this.$refs.chart.hideLoading();
    },
    setLineChart({ xAxis, yAxis, smooth = true }) {
      this.initChart({
        tooltip: {
          trigger: 'axis',
        },
        xAxis: {
          type: 'category',
          data: xAxis,
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            textStyle: {
              color: '#999999',
            },
          },
        },
        yAxis: {
          type: 'value',
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            textStyle: {
              color: '#999999',
            },
          },
        },
        grid: {
          left: 50,
          right: 50,
        },
        series: [
          {
            data: yAxis,
            type: 'line',
            smooth,
          },
        ],
      });
    },
    setManyLineChart({ source, smooth = true }) {
      let option = {
        backgroundColor: '#ffffff',
        legend: {},
        tooltip: {
          trigger: 'axis',
        },
        grid: {
          top: 40,
          left: 50,
          right: 50,
        },
        dataset: {
          source,
        },

        xAxis: {
          type: 'category',
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            textStyle: {
              color: '#999999',
            },
          },
        },
        yAxis: {
          gridIndex: 0,
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            textStyle: {
              color: '#999999',
            },
          },
        },
      };

      option.series = new Array(source.length - 1).fill('').map((item) => {
        return {
          type: 'line',
          smooth,
          seriesLayoutBy: 'row',
        };
      });

      this.initChart(option);
    },
    setSanKeyChart({ title, data, links }) {  //桑基图
      this.initChart({
        title: {
          text: title
        },
        tooltip: {
          trigger: 'item',
          triggerOn: 'mousemove'
        },
        series: [
          {
            type: 'sankey',
            data: data,
            links: links,
            emphasis: {
              focus: 'adjacency'
            },

            lineStyle: {
              curveness: 0.5
            }
          }
        ]
      })
    },
    setBarChart({ xAxis, yAxis }) {
      this.initChart({
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          },
        },
        xAxis: {
          type: 'category',
          data: xAxis,
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            textStyle: {
              color: '#999999',
            },
          },
        },
        yAxis: {
          type: 'value',
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            textStyle: {
              color: '#999999',
            },
          },
        },
        series: [
          {
            data: yAxis,
            type: 'bar',
          },
        ],
      });
    },
    setManyBarChart({ source }) {
      let option = {
        legend: {},
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          },
        },
        dataset: {
          source,
        },
        xAxis: {
          type: 'category',
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            textStyle: {
              color: '#999999',
            },
          },
        },
        yAxis: {
          type: 'value',
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            textStyle: {
              color: '#999999',
            },
          },
        },
        grid: {
          top: 40,
          left: 50,
          right: 50,
        },
        series: [
          { type: 'bar', seriesLayoutBy: 'row', emphasis: { focus: 'series' } },
          { type: 'bar', seriesLayoutBy: 'row', emphasis: { focus: 'series' } },
          { type: 'bar', seriesLayoutBy: 'row', emphasis: { focus: 'series' } },
        ],
      };
      option.series = new Array(source.length - 1).fill('').map((item) => {
        return {
          type: 'bar',
          seriesLayoutBy: 'row',
        };
      });
      this.initChart(option);
    },
    setPieChart({ data, radius = '50%' }) {
      this.initChart({
        tooltip: {
          trigger: 'item',
        },
        legend: {},
        series: [
          {
            type: 'pie',
            radius,
            data,
          },
        ],
      });
    },
  },
};
</script>

<style scoped>
.ab {
  position: absolute;
}
</style>
