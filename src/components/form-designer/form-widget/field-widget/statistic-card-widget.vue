<template>
  <view-wrapper
    :designer="designer"
    :field="field"
    :rules="rules"
    :design-state="designState"
    :parent-widget="parentWidget"
    :parent-list="parentList"
    :index-of-parent-list="indexOfParentList"
    class="table-view"
  >
    <div
      style="height: 100%;position: relative;"
      :style="{
        width: field.options.cardWidth.indexOf('%') > -1 ? '100%' : field.options.cardWidth + 'px'
      }"
    >
      <div
        :style="{
          'background-image': `linear-gradient(${field.options.gradientDirection},
            ${field.options.gradientColor0 ? field.options.gradientColor0 : field.options.gradientColor1} ,
            ${field.options.gradientColor1 ? field.options.gradientColor1 : field.options.gradientColor0}
          )`
        }"
        class="content"
      >
        <div
          :style="{
            'margin-right': field.options.distance + 'px',
          }"
          class="content-left"
        >
          <i
            :class="field.options.statisticIcon"
            v-if="field.options.statisticIcon"
            :style="{
              width: field.options.iconSize + 'px',
              height: field.options.iconSize + 'px',
              'font-size': field.options.iconSize + 'px',
              color: field.options.iconColor,
              'background-color': field.options.iconBackColor,
              'box-sizing': 'content-box',
              'border-radius': '50%',
              'padding': '10px'
            }"
          ></i>
        </div>
        <div class="content-right">
          <span
            class="content-right-first"
            :style="{
              'font-family': field.options.fontFamily,
              'font-size': field.options.firstSize + 'px',
              'height':field.options.firstSize + 'px',
              color:field.options.firstColor,
              'font-weight':field.options.firstWeight,
              'margin-bottom':field.options.lineDistance +'px'
            }"
          >
            {{ field.options.mode == 1 ? field.options.firstLine : field.options.secondLine }}
            <span
              v-if="field.options.mode == 2"
              :style="{
                'margin-left':'10px',
                'font-size': field.options.unitSize + 'px',
                color:field.options.unitColor,
              }"
            >
              {{ field.options.unit }}
            </span>
          </span>
          <span
            :style="{
              'font-family': field.options.fontFamily,
              'font-size': field.options.secondSize + 'px',
              'height':field.options.secondSize + 'px',
              color:field.options.secondColor,
              'font-weight':field.options.secondWeight,
            }"
            class="content-right-second"
          >
            {{ field.options.mode == 1 ? field.options.secondLine : field.options.firstLine }}
            <span
              v-if="field.options.mode == 1"
              :style="{
                'margin-left':'10px',
                'font-size': field.options.unitSize + 'px',
                color:field.options.unitColor,
              }"
            >
              {{ field.options.unit }}
            </span>
          </span>
        </div>
      </div>
    </div>
  </view-wrapper>
</template>
<script>
import viewWrapper from './view-wrapper';
import fieldMixin from '@/components/form-designer/form-widget/field-widget/fieldMixin';
import { newExecuteInterface } from '@/api/interfaces/interfaces';
export default {
  name: 'statisticCard-widget',
  componentName: 'FieldWidget', //必须固定为FieldWidget，用于接收父级组件的broadcast事件
  components: {
    viewWrapper,
  },
  mixins: [fieldMixin],
  props: {
    field: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,
    designState: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      rules: [],
      timer: null,
    };
  },
  created() {
    /* 注意：子组件mounted在父组件created之后、父组件mounted之前触发，故子组件mounted需要用到的prop
       需要在父组件created中初始化！！ */
    this.registerToRefList();
    this.initEventHandler();
    this.handleOnCreated();
  },
  mounted() {
    this.handleOnMounted();
    if (this.field.options.autoLoadData) {
      this.sendApi();
    }
  },
  methods: {
    sendApi() {
      if (this.field.options.api) {
        // 获取参数配置
        let param = this.getRequestParam();
        newExecuteInterface({
          apiId: this.field.options.api,
          body: param,
        })
          .then((res) => {
            this.field.options.secondLine = res.data
            let customFunc = new Function(
              'res',
              this.field.options.onSuccessCallback,
            );
            customFunc.call(this, res);
          })
          .catch((err) => {
            if (this.field.options.onFailCallback) {
              let customFunc = new Function(
                'e',
                this.field.options.onFailCallback,
              );
              customFunc.call(this, err);
            }
          });
      }
    },
    setValue(data) {
      this.field.options.secondLine = data
    },
    getValue() {
      return this.field.options.secondLine
    },
    setTitle(data) {
      this.field.options.firstLine = data
    },
    setUnit(data) {
      this.field.options.unit = data
    }
  },
  watch: {
    'field.options.mode': function (val) {
      this.sendApi();
    },
    'field.options.polling': function (val) {
      clearInterval(this.timer)
      if (val) {
        this.timer = setInterval(()=> {
          this.sendApi();
        },this.field.options.pollTime || 500)
      }
    }
  }
};
</script>
<style lang="scss" scoped>
@import '../../../../assets/numberFont/stylesheet.css';
.content {
  display: flex;
  flex-direction: row;
  height: 100%;
  width: 100%;
  justify-content: center;
  align-items: center;
  padding: 10px;
  box-sizing: border-box;
  .content-left {
    display: flex;
    flex-direction: row;
    height: 100%;
    align-items: center;
  }
  .content-right {
    display: flex;
    flex-direction: column;
    height: 100%;
    justify-content: center;
  }
  .content-right-first {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding-bottom: 5px;
  }
  .content-right-second {
    display: flex;
    flex-direction: row;
    align-items: center;
  }
}
</style>
