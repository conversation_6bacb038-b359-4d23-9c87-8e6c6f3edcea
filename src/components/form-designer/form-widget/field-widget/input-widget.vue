<template>
  <form-item-wrapper
    :designer="designer"
    :field="field"
    :rules="rules"
    :design-state="designState"
    :parent-widget="parentWidget"
    :parent-list="parentList"
    :index-of-parent-list="indexOfParentList"
    :sub-form-row-index="subFormRowIndex"
    :sub-form-col-index="subFormColIndex"
    :sub-form-row-id="subFormRowId"
    :echo-field-data="echoFieldData"
    :sub-edit-flag="subEditFlag"
  >
    <el-input
      ref="fieldEditor"
      v-model="fieldModel"
      :disabled="field.options.disabled"
      :readonly="field.options.readonly"
      :size="field.options.size"
      :style="{ color: field.options.color }"
      :class="['hide-spin-button', field.options.color ? 'color' : '']"
      :type="field.options.type"
      :show-password="field.options.showPassword"
      :placeholder="field.options.placeholder || '请输入' + field.options.label"
      :clearable="field.options.clearable"
      :minlength="field.options.minLength"
      :maxlength="field.options.maxLength"
      :show-word-limit="field.options.showWordLimit"
      @focus="handleFocusCustomEvent"
      @blur="handleBlurCustomEvent"
      @input="handleInput"
      @change="handleChangeEvent"
      @keyup.native.enter="onEnter"
    >
      <div v-if="field.options.prefixIcon" slot="prepend" @click="handlePrependClick">
        <el-button v-if="field.options.prefixIcon.includes('el-')" :icon="field.options.prefixIcon"></el-button>
        <template v-else>{{ field.options.prefixIcon }}</template>
      </div>

      <div v-if="field.options.suffixIcon" slot="append" @click="handleAppendClick">
        <el-button v-if="field.options.suffixIcon.includes('el-')" :icon="field.options.suffixIcon"></el-button>
        <template v-else>{{ field.options.suffixIcon }}</template>
      </div>
    </el-input>
  </form-item-wrapper>
</template>

<script>
import FormItemWrapper from './form-item-wrapper';
import emitter from 'element-ui/lib/mixins/emitter';
import i18n, { translate } from '@/utils/i18n';
import fieldMixin from '@/components/form-designer/form-widget/field-widget/fieldMixin';

export default {
  name: 'input-widget',
  componentName: 'FieldWidget', //必须固定为FieldWidget，用于接收父级组件的broadcast事件
  mixins: [emitter, fieldMixin, i18n],
  props: {
    field: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,
    designState: {
      type: Boolean,
      default: false,
    },

    subFormRowIndex: {
      /* 子表单组件行索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormColIndex: {
      /* 子表单组件列索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormRowId: {
      /* 子表单组件行Id，唯一id且不可变 */ type: String,
      default: '',
    },
    subEditFlag: {
      type: Object,
      default: null,
    },
    echoFieldData: {
      type: Array,
      default: () => [],
    },
    itemObj: {
      type: Object,
      default: () => ({
        name: '111',
      }),
    },
  },
  components: {
    FormItemWrapper,
  },
  inject: ['refList', 'formConfig', 'globalOptionData', 'globalModel', 'status'],
  data() {
    return {
      oldFieldValue: null, //field组件change之前的值
      fieldModel: null,
      rules: [],
      test: null,
    };
  },
  computed: {
    color() {
      if (!!this.field.options.color) {
        return this.field.options.color;
      }
    },
  },
  beforeCreate() {
    /* 这里不能访问方法和属性！！ */
  },

  created() {
    /* 注意：子组件mounted在父组件created之后、父组件mounted之前触发，故子组件mounted需要用到的prop
         需要在父组件created中初始化！！ */
    this.initFieldModel();
    this.initAsData();
    this.registerToRefList();
    this.initEventHandler();
    this.buildFieldRules();

    this.handleOnCreated();
  },

  mounted() {
    this.handleOnMounted();
  },

  beforeDestroy() {
    this.unregisterFromRefList();
  },

  methods: {
    onEnter(){
      let item=this
      while(item.$parent){
        if(item.$options._componentTag==="condition-container-item"){
          item.query()
          break;
        }
        item=item.$parent
      }
      if (this.field.options.onEnter) {
        new Function('value', this.field.options.onEnter).call(this, this.fieldModel);
      }
    },
    handlePrependClick() {
      if (this.field.options.onPrependClick) {
        let fun = new Function('', this.field.options.onPrependClick);
        fun.call(this);
      }
    },
    handleAppendClick() {
      if (this.field.options.onAppendClick) {
        let fun = new Function('', this.field.options.onAppendClick);
        fun.call(this);
      }
    },
    handleInput(e) {
      const { type, maxLength, minLength } = this.field.options
      if(type === 'number') {
        if(!['', null, undefined].includes(maxLength) && e > maxLength) {
          this.fieldModel = maxLength
        }
        if(!['', null, undefined].includes(minLength) && e < minLength) {
          this.fieldModel = minLength
        }
      }
      this.handleInputCustomEvent(e)
    }
  },
};
</script>

<style lang="scss">
.color {
  .el-input__inner {
    color: var(--color);
  }
}
</style>
