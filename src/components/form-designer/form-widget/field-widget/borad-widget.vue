<template>
  <form-item-wrapper
    :designer="designer"
    :field="field"
    :rules="rules"
    :design-state="designState"
    :parent-widget="parentWidget"
    :parent-list="parentList"
    :index-of-parent-list="indexOfParentList"
    :sub-form-row-index="subFormRowIndex"
    :sub-form-col-index="subFormColIndex"
    :sub-form-row-id="subFormRowId"
    :echo-field-data="echoFieldData"
    :sub-edit-flag="subEditFlag"
  >
  <div v-if="!isPre" style="border:2px dotted #ccc;border-radius:5px" :style="{ width:(+field.options.width+4)+'px',height:(+field.options.height+4)+'px'}">
    <sign-canvas
      class="sign-canvas"
      ref="SignCanvas"
      :options="options"
      v-model="fieldModel"
    />
    <div v-if="show" :style="{width:field.options.width+'px',height:field.options.height+'px'}" style="position:absolute;left:0;top:0;z-index:999;cursor:no-drop"></div>
  </div>
  <img :src="fieldModel" v-else style="border:2px dotted #ccc;border-radius:5px" :style="{ width:(+field.options.width+4)+'px',height:(+field.options.height+4)+'px'}" alt="">

    <div :style="{width:field.options.width+'px'}" style="margin-top:5px;display:flex;justify-content:flex-end;">
      <el-button size="small" v-if="isRewrite" @click="clear">重写</el-button>
      <el-button size="small" type="primary" v-if="!isPre" @click="save">确认</el-button>
    </div>
  </form-item-wrapper>
</template>

<script>
import FormItemWrapper from "./form-item-wrapper";
import emitter from "element-ui/lib/mixins/emitter";
import i18n, { translate } from "@/utils/i18n";
import fieldMixin from "@/components/form-designer/form-widget/field-widget/fieldMixin";
import SignCanvas from 'sign-canvas';

export default {
  name: "borad-widget",
  componentName: "FieldWidget", //必须固定为FieldWidget，用于接收父级组件的broadcast事件
  mixins: [emitter, fieldMixin, i18n],
  props: {
    field: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,
    designState: {
      type: Boolean,
      default: false,
    },

    subFormRowIndex: {
      /* 子表单组件行索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormColIndex: {
      /* 子表单组件列索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormRowId: {
      /* 子表单组件行Id，唯一id且不可变 */ type: String,
      default: "",
    },
    subEditFlag: {
      type: Object,
      default: null,
    },
    echoFieldData: {
      type: Array,
      default: () => [],
    },
    itemObj: {
      type: Object,
      default: () => ({
        name: "111",
      }),
    },
  },
  components: {
    FormItemWrapper,
    SignCanvas
  },
  inject: ["refList", "formConfig", "globalOptionData", "globalModel"],
  data() {
    return {
      fieldModel: null,
      test: null,
      rules: [],
      show:false,
      isPre:false,
      isRewrite:true
    };
  },
  watch: {
    fieldModel: {
      handler(val) {
        if (val) {
          // this.$refs.SignCanvas.
        } else {
          this.$refs.SignCanvas&&this.$refs.SignCanvas.canvasClear();
        }
      },
      deep: true,
      immediate: true
    }
  },
  computed: {
    options() {
      return {
        isShowBorder:false,
        canvasWidth: this.field.options.width,
        canvasHeight: this.field.options.height,
        bgColor: this.field.options.bgColor,
        borderColor: "#ddd",
        writeWidth: this.field.options.writeWidth,
        writeColor: this.field.options.color,
        isSign: true,
      };
    },
  },
  created() {
    /* 注意：子组件mounted在父组件created之后、父组件mounted之前触发，故子组件mounted需要用到的prop
         需要在父组件created中初始化！！ */
    this.initFieldModel();
    this.registerToRefList();
    this.initEventHandler();
    this.buildFieldRules();

    this.handleOnCreated();
  },

  mounted() {
    this.handleOnMounted();
  },

  beforeDestroy() {
    this.unregisterFromRefList();
  },

  methods: {
    clear() {
      this.isPre=false
      this.isRewrite=true
      this.$refs.SignCanvas.canvasClear();
      this.setValue(null)
      this.show=false
    },
    preview(val,isRewrite=true){
      this.setValue(val)
      this.isRewrite=isRewrite
      this.isPre=true
    },
    save() {
      this.show=true
      this.setValue(this.$refs.SignCanvas.saveAsImg())
      // if (this.fieldModel) {
      //   let url = this.$refs.SignCanvas.saveAsImg(); //拿到想要复制的值
      //   let copyInput = document.createElement("input"); //创建input元素
      //   document.body.appendChild(copyInput); //向页面底部追加输入框
      //   copyInput.setAttribute("value", url); //添加属性，将url赋值给input元素的value属性
      //   copyInput.select(); //选择input元素
      //   document.execCommand("Copy"); //执行复制命令
      //   this.$message.success("base64值已复制"); //弹出提示信息，不同组件可能存在写法不同
      //   //复制之后再删除元素，否则无法成功赋值
      //   copyInput.remove(); //删除动态创建的节点
      // }else{
      //   this.$message.warning('画布为空')
      // }
    },
    // download() {
    //   if (this.fieldModel) {
    //     this.$refs.SignCanvas.downloadSignImg();
    //   }else{
    //     this.$message.warning('画布为空')
    //   }
    // },
  },
};
</script>

