<!--
/**
 * author: vformAdmin
 * email: <EMAIL>
 * website: http://www.vform666.com
 * date: 2021.08.18
 * remark: 如果要分发VForm源码，需在本文件顶部保留此文件头信息！！
 */
-->

<template>
  <div
    class="field-wrapper"
    :class="[ field.id, !!this.designer ? 'design-time-bottom-margin' : '' ]"
  >
    <el-form-item
      v-if="!!field.formItemFlag"
      v-show="!field.options.hidden || designState === true || status === 'authority'"
      :class="[
        selected ? 'selected' : '',
        labelAlign,
        customClass,
        field.options.required ? 'required' : '',
        field.id
      ]"
      :title="field.type === 'file-upload' ? field.options.label : ''"
      @click.native.stop="selectField(field)"
    >
      <slot></slot>
    </el-form-item>
    <template v-if="!!this.designer">
      <div class="field-action" v-if="designer.selectedId === field.id">
         <i
          v-if="docMap[field.type]"
          class="el-icon-tickets"
          title="文档"
          @click.stop="toDoc"
        ></i>
         <i
          class="el-icon-back"
          title="选中前一个节点(alt + ←)"
          @click.stop="selectBroWidget('last')"
        ></i>
        <i
          class="el-icon-right"
          title="选中后一个节点(alt + →)"
          @click.stop="selectBroWidget('next')"
        ></i>
        <i
          class="el-icon-top"
          title="选中父节点(alt + ↑)"
          @click.stop="selectParentWidget"
        ></i>
        <i
          class="el-icon-copy-document"
          :title="i18nt('designer.hint.cloneWidget')"
          @click.stop="cloneField(field)"
        ></i>
        <i
          class="el-icon-delete"
          title="移除组件(delete)"
          @click.stop="removeFieldWidget"
        ></i>
      </div>
      <div
        class="drag-handler background-opacity"
        v-if="designer.selectedId === field.id"
      >
        <i class="el-icon-rank" :title="i18nt('designer.hint.dragHandler')"></i>
        <i>{{
          i18n2t(
            `designer.widgetLabel.${field.type}`,
            `extension.widgetLabel.${field.type}`,
          )
        }}</i>
        <i v-if="field.options.hidden === true" class="el-icon-view"></i>
      </div>
    </template>

    <template v-if="status === 'authority'">
      <template v-if="field.type === 'data-table'">
        <div class="permissionMasking ">
          <el-button-group class="maskingButton">
            <el-button :type="hasMaskingHidden" @click="setDataTableRule" icon="el-icon-view">配置表格权限</el-button>
          </el-button-group>
        </div>
        <div class="permissionHidden" v-if="field.options.hidden">
        <span style="display: flex;  position: absolute;top: 0;right: 0; bottom: 0;left: 0;z-index: 70 !important;align-items: center;justify-content: center;cursor: pointer;">
          <i class="el-icon-view"></i>
        </span>
        </div>
        <el-dialog
          title="表格权限"
          :visible.sync="dialogVisible"
          style="zoom: 1.6 !important;"
          :modal="true"
          width="36%">
          <div class="block">表格权限</div>
          <el-form inline label-width="70px">
            <el-row>
              <el-col :span="12">
                <el-form-item label="表格显示: ">
                  <el-button-group>
                    <el-button :type="!field.options.hidden? 'primary':'default'" @click="$set(field.options, 'hidden', false)">显示</el-button>
                    <el-button :type="field.options.hidden? 'primary':'default'" @click="$set(field.options, 'hidden', true)">隐藏</el-button>
                  </el-button-group>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="表格编辑: " v-if="!field.options.hidden">
                  <el-button-group>
                    <el-button :type="field.options.defaultEdit? 'primary':'default'" @click="$set(field.options, 'defaultEdit', true)" >可编辑</el-button>
                    <el-button :type="!field.options.defaultEdit? 'primary':'default'" @click="$set(field.options, 'defaultEdit', false)" >不可编辑</el-button>
                  </el-button-group>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
          <template v-if="field.options.btns && field.options.btns.length">
            <div class="block">操作按钮</div>
            <el-row>
              <el-form label-width="70px" v-for="(item, index) in field.options.btns" :key="index">
                <el-form-item :label="item.name">
                  <el-button-group >
                    <el-button :type="!item.hidden? 'primary':'default'" @click="$set(item, 'hidden', false)" >显示</el-button>
                    <el-button :type="item.hidden? 'primary':'default'" @click="$set(item, 'hidden', true)" >隐藏</el-button>
                  </el-button-group>
                </el-form-item>
              </el-form>
            </el-row>
          </template>
          <template v-if="field.options.leftBtns && field.options.leftBtns.length">
            <el-row>
              <div class="block">头部左侧按钮</div>
              <el-form label-width="70px" v-for="(item, index) in field.options.leftBtns" :key="index">
                <el-form-item :label="item.name+':'">
                  <el-button-group>
                    <el-button :type="!item.hidden? 'primary':'default'" @click="$set(item, 'hidden', false)" >显示</el-button>
                    <el-button :type="item.hidden? 'primary':'default'" @click="$set(item, 'hidden', true)" >隐藏</el-button>
                  </el-button-group>
                </el-form-item>
              </el-form>
            </el-row>
          </template>
          <div class="block">列权限</div>
          <el-form label-width="70px" v-for="(item, index) in getFieldCols" :key="index" inline >
            <el-row>
              <el-col :span="12" v-if="!item.isShow">
                <el-form-item :label="item.label + ':'">
                  <el-button-group >
                    <el-button :type="!item.hidden? 'primary':'default'" @click="changeField(item.prop,'hidden', false)">显示</el-button>
                    <el-button :type="item.hidden? 'primary':'default'" @click="changeField(item.prop,'hidden', true)">隐藏</el-button>
                  </el-button-group>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="item.isShow?item.label+':': '是否编辑:'" v-if="!item.hidden">
                  <el-button-group>
                    <el-button :disabled="!field.options.defaultEdit" :type="item.editable? 'primary':'default'" @click="changeField(item.prop,'editable', true)">可编辑</el-button>
                    <el-button :disabled="!field.options.defaultEdit" :type="!item.editable? 'primary':'default'" @click="changeField(item.prop,'editable', false)">不可编辑</el-button>
                  </el-button-group>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-dialog>
      </template>
      <template v-else-if="field.type === 'file-upload'">
        <div class="permissionMasking ">
          <el-button-group class="maskingButton">
            <el-button :type="hasMaskingHidden" @click="setFileUploadRule" icon="el-icon-view">配置文件上传权限</el-button>
          </el-button-group>
        </div>
        <div class="permissionHidden" v-if="field.options.hidden">
          <span style="display: flex;  position: absolute;top: 0;right: 0; bottom: 0;left: 0;z-index: 70 !important;align-items: center;justify-content: center;cursor: pointer;">
            <i class="el-icon-view"></i>
          </span>
        </div>
        <el-dialog
          title="上传文件权限"
          :visible.sync="dialogVisible2"
          style="zoom: 1.6 !important;"
          :modal="true"
          width="36%">
          <div class="block">组件权限</div>
          <el-form inline label-width="70px">
            <el-row>
              <el-col :span="12">
                <el-form-item label="组件显示: ">
                  <el-button-group>
                    <el-button :type="!field.options.hidden? 'primary':'default'" @click="$set(field.options, 'hidden', false)">显示</el-button>
                    <el-button :type="field.options.hidden? 'primary':'default'" @click="$set(field.options, 'hidden', true)">隐藏</el-button>
                  </el-button-group>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="组件编辑: " v-if="!field.options.hidden">
                  <el-button-group>
                    <el-button :type="field.options.disabled? 'primary':'default'" @click="$set(field.options, 'disabled', true)" >可编辑</el-button>
                    <el-button :type="!field.options.disabled? 'primary':'default'" @click="$set(field.options, 'disabled', false)" >不可编辑</el-button>
                  </el-button-group>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
          <template v-if="!field.options.hidden">
            <div class="block">操作按钮</div>
            <el-form inline label-width="70px">
              <el-row>
                <el-col :span="12">
                  <el-form-item label="下载: ">
                    <el-button-group>
                      <el-button :type="!field.options.showDown? 'primary':'default'" @click="$set(field.options, 'showDown', false)">显示</el-button>
                      <el-button :type="field.options.showDown? 'primary':'default'" @click="$set(field.options, 'showDown', true)">隐藏</el-button>
                    </el-button-group>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="删除: ">
                    <el-button-group>
                      <el-button :type="!field.options.showDel? 'primary':'default'" @click="$set(field.options, 'showDel', false)">显示</el-button>
                      <el-button :type="field.options.showDel? 'primary':'default'" @click="$set(field.options, 'showDel', true)">隐藏</el-button>
                    </el-button-group>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </template>
        </el-dialog>
      </template>
      <template v-else>
        <div class="permissionMasking ">
          <el-button-group class="maskingButton">
            <el-button :type="hasMaskingHidden" @click="setMaskingHidden" icon="el-icon-view">隐藏</el-button>
          </el-button-group>
        </div>
        <div class="permissionHidden" v-if="field.options.hidden">
          <span style="display: flex;  position: absolute;top: 0;right: 0; bottom: 0;left: 0;z-index: 70 !important;align-items: center;justify-content: center;cursor: pointer;">
            <i class="el-icon-view"></i>
          </span>
        </div>
      </template>
    </template>
  </div>
</template>

<script>
import i18n from '@/utils/i18n';
import copyMixin from '@/components/form-designer/copyMixin.js';
import bus from '@/magic-editor/scripts/bus';
import { docMap } from '../../setting-panel/docMap';
export default {
  name: 'view-wrapper',
  mixins: [i18n, copyMixin],
  props: {
    field: Object,
    designer: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,

    designState: {
      type: Boolean,
      default: false,
    },

    subFormRowIndex: {
      /* 子表单组件行索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormColIndex: {
      /* 子表单组件列索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormRowId: {
      /* 子表单组件行Id，唯一id且不可变 */ type: String|Number,
      default: '',
    },
    subEditFlag: {
      type: Object,
      default: null,
    },
    echoFieldData: {
      type: Array,
      default: () => [],
    },
    rules: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      dialogVisible: false,
      dialogVisible2: false,
      docMap
    }
  },
  inject: ['formConfig', 'status'],
  computed: {
    selected() {
      return !!this.designer && this.field.id === this.designer.selectedId;
    },
    getFieldCols(){
      const result = [];
      const traverse = (data) => {
        if (!data.children || data.children.length === 0) {
          // data["isShow"] = false;
          result.push(data);
        } else {
          data.children.forEach(child => traverse(child));
        }
      };
      for (let i = 0; i < this.field.options.cols.length; i++) {
        let colsItem = this.field.options.cols[i];
        if(colsItem.children && colsItem.children.length > 0){
          // result.push(colsItem);
          traverse(colsItem)
        }else{
          result.push(colsItem);
        }
      }
      //this.field.options.cols.forEach(field => traverse(field));
      return result
    },
    label() {
      if (!!this.field.options.labelHidden) {
        return '';
      }

      return this.field.options.label;
    },

    labelWidth() {
      if (!!this.field.options.labelHidden) {
        return 0;
      }

      if (!!this.field.options.labelWidth) {
        return this.field.options.labelWidth;
      }

      if (!!this.designer) {
        return this.designer.formConfig.labelWidth;
      } else {
        return this.formConfig.labelWidth;
      }
    },

    labelAlign() {
      if (!!this.field.options.labelAlign) {
        return this.field.options.labelAlign;
      }

      if (!!this.designer) {
        return this.designer.formConfig.labelAlign || 'label-left-align';
      } else {
        return this.formConfig.labelAlign || 'label-left-align';
      }
    },

    customClass() {
      return !!this.field.options.customClass
        ? this.field.options.customClass.join(' ')
        : '';
    },

    subFormName() {
      return !!this.parentWidget ? this.parentWidget.options.name : '';
    },

    subFormItemFlag() {
      return !!this.parentWidget
        ? this.parentWidget.type === 'sub-form'
        : false;
    },
    trendsTabFlag() {
      return !!this.parentWidget
        ? this.parentWidget.type === 'trends-tab'
        : false;
    },
    hasMaskingHidden() {
      return this.field.options.hidden? 'primary':'default'
    },
  },
  mounted() {

    if (!bus._events[`keydown${this.field.id}`]) {
      bus.$on(`keydown${this.field.id}`, (e) => {
        if (e.code == "Delete") {
          this.removeFieldWidget();
        } else if (e.altKey && e.keyCode == 37) {
          this.selectBroWidget("last");
        } else if (e.altKey && e.keyCode == 38) {
          this.selectParentWidget();
        } else if (e.altKey && e.keyCode == 39) {
          this.selectBroWidget("next");
        }
      });
    }
  },
  methods: {
    toDoc(){
      window.open('http://192.168.10.110:8888/from/field/'+this.docMap[this.field.type],'_blank');
    },
    changeField(porpValue, attribute, val){
      const traverse = (data) => {
        if(porpValue == data.prop){
          this.$set(data, attribute, val)
        } else {
          if (data.children && data.children.length !== 0) {
            data.children.forEach(child => traverse(child));

            if (attribute === 'hidden') {
              let allFalse = true
              for (let i = 0; i < data.children.length; i++) {
                allFalse = allFalse && data.children[i][attribute]
              }
              this.$set(data, attribute, allFalse)
            }
          }
        }

      };

      this.field.options.cols.forEach(field => traverse(field));
    },
    cloneField(field) {
      let newField = this.designer.copyNewFieldWidget(field);
      this.parentList.splice(this.indexOfParentList + 1, 0, newField);
      this.designer.setSelected(newField);
    },
    selectField(field) {
      if (!!this.designer) {
        this.designer.setSelected(field);
        this.designer.emitEvent('field-selected', this.parentWidget); //发送选中组件的父组件对象
      }
    },

    selectParentWidget() {
      if (this.parentWidget) {
        this.designer.setSelected(this.parentWidget);
      } else {
        this.designer.clearSelected();
      }
    },

    moveUpWidget() {
      this.designer.moveUpWidget(this.parentList, this.indexOfParentList);
      this.designer.emitHistoryChange();
    },

    moveDownWidget() {
      this.designer.moveDownWidget(this.parentList, this.indexOfParentList);
      this.designer.emitHistoryChange();
    },

    removeFieldWidget() {
      if (!!this.parentList) {
        let nextSelected = null;
        if (this.parentList.length === 1) {
          if (!!this.parentWidget) {
            nextSelected = this.parentWidget;
          }
        } else if (this.parentList.length === 1 + this.indexOfParentList) {
          nextSelected = this.parentList[this.indexOfParentList - 1];
        } else {
          nextSelected = this.parentList[this.indexOfParentList + 1];
        }

        this.$nextTick(() => {
          this.parentList.splice(this.indexOfParentList, 1);
          //if (!!nextSelected) {
          this.designer.setSelected(nextSelected);
          //}

          this.designer.emitHistoryChange();
        });
      }
    },

    getPropName() {
      if (this.trendsTabFlag && !this.designState) {
        // 返回trendsTab 数据结构
        return (
          this.subFormName +
          '.' +
          this.subFormRowIndex +
          '.' +
          this.field.options.name +
          ''
        );
      } else if (this.subFormItemFlag && !this.designState) {
        return (
          this.subFormName +
          '.' +
          this.subFormRowIndex +
          '.' +
          this.field.options.name +
          ''
        );
      } else {
        return this.field.options.name;
      }
    },
    setMaskingHidden() {
      this.field.options.hidden = !this.field.options.hidden
    },
    setDataTableRule() {
      this.dialogVisible = true
    },
    setFileUploadRule() {
      this.dialogVisible2 = true
    },
  },
};
</script>

<style lang="scss" scoped>
;

.field-wrapper {
  position: relative;

  .field-action {
    position: absolute;
    //bottom: -24px;
    bottom: 0;
    right: -2px;
    height: 22px;
    line-height: 22px;
    background: var(--primary-color);
    z-index: 9;

    i {
      font-size: 14px;
      color: #fff;
      margin: 0 5px;
      cursor: pointer;
    }
  }

  .drag-handler {
    position: absolute;
    top: 0;
    //bottom: -22px;  /* 拖拽手柄位于组件下方，有时无法正常拖动，原因未明？？ */
    left: -1px;
    height: 20px;
    line-height: 20px;
    background: var(--primary-color);
    z-index: 9;

    i {
      font-size: 12px;
      font-style: normal;
      color: #fff;
      margin: 4px;
      cursor: move;
    }

    &:hover {
      //opacity: 1;
      background: var(--primary-color);
    }
  }
}

.el-form-item {
  //margin-bottom: 0 !important;
  //margin-bottom: 6px;

  //margin-top: 2px;
  position: relative;

  ::v-deep .el-form-item__label {
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  ::v-deep .el-form-item__content {
    //position: unset;  /* TODO: 忘了这个样式设置是为了解决什么问题？？ */
    margin-left: 0 !important;
    display: block;
  }

  span.custom-label i {
    margin: 0 3px;
  }

  /* 隐藏Chrome浏览器中el-input数字输入框右侧的上下调整小箭头 */
  ::v-deep .hide-spin-button input::-webkit-outer-spin-button,
  ::v-deep .hide-spin-button input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
  }

  /* 隐藏Firefox浏览器中el-input数字输入框右侧的上下调整小箭头 */
  ::v-deep .hide-spin-button input[type='number'] {
    -moz-appearance: textfield;
  }
}

.required ::v-deep .el-form-item__label::before {
  content: '*';
  color: red;
  margin-right: 4px;
}

.static-content-item {
  min-height: 20px;
  display: flex; /* 垂直居中 */
  align-items: center; /* 垂直居中 */

  ::v-deep .el-divider--horizontal {
    margin: 0;
  }
}

.el-form-item.selected,
.static-content-item.selected {
  outline: 2px solid var(--primary-color);
}

::v-deep .label-left-align .el-form-item__label {
  text-align: left;
}

::v-deep .label-center-align .el-form-item__label {
  text-align: center;
}

::v-deep .label-right-align .el-form-item__label {
  text-align: right;
}

.permissionMasking {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 99;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all .3s;
}
.permissionHidden {
  display: flex;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 98;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  background: rgba(0,0,0,.1);
  transition: all .3s;
}

.permissionMasking:hover {
  display: flex;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 99;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  background: rgba(0,0,0,.3);
  transition: all .3s;
}
.maskingButton {
  display: none;
  zoom: 0.8
}
.permissionMasking:hover .maskingButton {
  display: flex;
}


.block {
  width: 100%;
  height: 32px;
  padding-left: 8px;
  background: linear-gradient(
      65deg,
      rgba(49, 119, 255, 0.15) 0%,
      rgba(49, 119, 255, 0.01) 100%
  );
  border-left: 4px solid var(--primary-color);
  color: var(--primary-color);
  font-size: 20px;
  line-height: 32px;
  font-weight: bold;
  margin-bottom: 10px;
}

::v-deep .el-form-item {
  margin-bottom: 10px !important;
}

::v-deep .el-dialog__body{
  overflow: auto;
}
.table-view .el-form-item {
  margin-bottom: 0 !important;
}
</style>
