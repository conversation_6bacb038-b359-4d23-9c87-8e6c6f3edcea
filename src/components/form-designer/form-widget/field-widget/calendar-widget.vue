<template>
  <view-wrapper
    :designer="designer"
    :field="field"
    :design-state="designState"
    :parent-widget="parentWidget"
    :parent-list="parentList"
    :index-of-parent-list="indexOfParentList"
    :sub-form-row-index="subFormRowIndex"
    :sub-form-col-index="subFormColIndex"
    :sub-form-row-id="subFormRowId"
    :echo-field-data="echoFieldData"
    :sub-edit-flag="subEditFlag"
  >
    <el-calendar v-if="field.options.customHtml" v-model="fieldModel" :key="key">
      <template slot="dateCell" slot-scope="{ date,data }">
        <div v-html="slotValue(date,data)"></div>
      </template>
    </el-calendar>
    <el-calendar v-model="fieldModel" v-else />
  </view-wrapper>
</template>

<script>
import viewWrapper from "./view-wrapper";
import i18n from "@/utils/i18n";
import fieldMixin from "@/components/form-designer/form-widget/field-widget/fieldMixin";
import emitter from "element-ui/lib/mixins/emitter";
import { number } from 'echarts';
export default {
  name: "calendar-widget",
  componentName: "FieldWidget", //必须固定为FieldWidget，用于接收父级组件的broadcast事件
  components: {
    viewWrapper,
  },
  mixins: [emitter, fieldMixin, i18n],
  props: {
    field: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,

    designState: {
      type: Boolean,
      default: false,
    },

    subFormRowIndex: {
      /* 子表单组件行索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormColIndex: {
      /* 子表单组件列索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormRowId: {
      /* 子表单组件行Id，唯一id且不可变 */ type: String,
      default: "",
    },
    subEditFlag: {
      type: Object,
      default: null,
    },
    echoFieldData: {
      type: Array,
      default: () => [],
    },
  },
  inject: ["refList", "formConfig", "globalOptionData", "globalModel"],
  data() {
    return {
      oldFieldValue: null, //field组件change之前的值
      fieldModel: new Date(),
      key: null,
    };
  },
  computed: {
    slotValue: function () {
      var _this=this
      return function (date,data) {
        let fun = new Function('date',"data", _this.field.options.slot);
        if (!this.key) {
          this.key = new Date().getTime();
        }
        return fun.call(_this,date.toLocaleDateString(), data);
      };
    },
  },
  created() {
    /* 注意：子组件mounted在父组件created之后、父组件mounted之前触发，故子组件mounted需要用到的prop
       需要在父组件created中初始化！！ */
    this.registerToRefList();
    this.initEventHandler();
    this.handleOnCreated();
  },
  mounted() {
    this.handleOnMounted();
  },
  beforeDestroy() {
    this.unregisterFromRefList();
  },
  watch:{
    fieldModel(value,oldValue){

      if(this.field.options.onChange){
        let fn=new Function("value",'oldValue',this.field.options.onChange)
        fn.call(this,value.toLocaleDateString(),oldValue.toLocaleDateString())
      }
    }
  },
  methods: {

  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-calendar{
  border: 1px solid #eee;
  border-radius: 5px;
}
::v-deep .el-calendar-table .el-calendar-day {
  height: 50px;
  text-align: center;
  padding: 0;
  &>div{
    width: 100%;
    height: 100%;
    &>div{
    width: 100%;
    height: 100%;
  }
  }
}
::v-deep .el-calendar-table td.is-selected{
  & div{
    color: var(--primary-color) !important;

  }

}

</style>
