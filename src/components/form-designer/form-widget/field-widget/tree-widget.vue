<template>
  <form-item-wrapper :designer="designer" :field="field" :rules="rules" :design-state="designState"
    :parent-widget="parentWidget" :parent-list="parentList" :index-of-parent-list="indexOfParentList"
    :sub-form-row-index="subFormRowIndex" :sub-form-col-index="subFormColIndex" :sub-form-row-id="subFormRowId"
    :echo-field-data="echoFieldData" :sub-edit-flag="subEditFlag">
    <Treeselect v-model="fieldModel" class="full-width-input" :options="options" :disabled="field.options.disabled"
      :clearable="field.options.clearable" :searchable="field.options.filterable" :multiple="field.options.multiple"
      :normalizer="normalizer" :placeholder="field.options.placeholder || i18nt('render.hint.selectPlaceholder')
        " :disable-branch-nodes="true" @select="changeValue"
      @deselect="deslelct" :flat="field.options.selectTreeFlat" :disableBranchNodes="field.options.disableBranchNodes" />
  </form-item-wrapper>
</template>

<script>
import FormItemWrapper from './form-item-wrapper';
import emitter from 'element-ui/lib/mixins/emitter';
import i18n, { translate } from '@/utils/i18n';
import fieldMixin from '@/components/form-designer/form-widget/field-widget/fieldMixin';
import Treeselect from '@riophae/vue-treeselect';
import '@riophae/vue-treeselect/dist/vue-treeselect.css';
import { deepClone } from '@/utils/util';

// 需要disable-branch-nodes参数的接口
const disableBranchApiIds = [
  '7daad577d565457f84fe17ebb3291587', // 人员树
  'c4fe24b1ec3840a78ccbc13c91928ae3', // 设备树
];
// :api-id="field.options.api"
export default {
  name: 'TreeWidget',
  componentName: 'FieldWidget',
  components: {
    FormItemWrapper,
    Treeselect,
  }, // 必须固定为FieldWidget，用于接收父级组件的broadcast事件
  mixins: [emitter, fieldMixin, i18n],
  inject: ['refList', 'formConfig', 'globalOptionData', 'globalModel'],
  props: {
    field: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,

    designState: {
      type: Boolean,
      default: false,
    },

    subFormRowIndex: {
      /* 子表单组件行索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormColIndex: {
      /* 子表单组件列索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormRowId: {
      /* 子表单组件行Id，唯一id且不可变 */ type: String,
      default: '',
    },
    subEditFlag: {
      type: Object,
      default: null,
    },
    echoFieldData: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      value: null,
      oldFieldValue: null, // field组件change之前的值
      fieldModel: null,
      rules: [],
      options: [],
      selectItem: null,
      disableBranchApiIds,
    };
  },
  computed: {
    allowDefaultFirstOption() {
      return (
        !!this.field.options.filterable && !!this.field.options.allowCreate
      );
    },
    getOptionsSelectValue() {
      return this.field.options.selectValue || 'id';
    },
    getSelectLabel() {
      return this.field.options.selectLabel || 'label';
    },
  },
  watch: {
    fieldModel() {
      if(['', null, undefined].includes(this.fieldModel)) {
        this.fieldModel = undefined;
        this.handleChangeEvent(null);
      }
    },
  },
  beforeCreate() {
    /* 这里不能访问方法和属性！！ */
  },
  created() {
    /* 注意：子组件mounted在父组件created之后、父组件mounted之前触发，故子组件mounted需要用到的prop
         需要在父组件created中初始化！！ */
    this.initOptionItems();
    this.initFieldModel();
    this.registerToRefList();
    this.initEventHandler();
    this.buildFieldRules();
    this.handleOnCreated();


    if (this.field.options.multiple && typeof this.fieldModel == 'string') {
      this.fieldModel = this.fieldModel.split(',');
    }
  },
  mounted() {
    this.handleOnMounted();
    if (this.field.options.autoLoadData) {
      this.sendApi();
    }
  },
  beforeDestroy() {
    this.unregisterFromRefList();
  },
  methods: {
    sendApi() {
      if (this.field.options.api) {
        this.newExecuteInterface().then((res) => {
          this.options = res.data;
          let customFunc = new Function(
            'res',
            this.field.options.onSuccessCallback,
          );
          customFunc.call(this, res);
        }).catch((err) => {
          if (this.field.options.onFailCallback) {
            let customFunc = new Function(
              'e',
              this.field.options.onFailCallback,
            );
            customFunc.call(this, err);
          }
        });
      }
    },
    changeValue(data) {
      setTimeout(() => {
        if (this.field.options.multiple) {
          this.handleChangeEvent(this.fieldModel);
        } else {
          this.fieldModel = data[this.getOptionsSelectValue];
          this.selectItem = data;
          this.handleChangeEvent(data[this.getOptionsSelectValue]);
        }
      }, 200);
    },
    deslelct(data) {
      setTimeout(() => {
        if (this.field.options.multiple) {
          this.handleChangeEvent(this.fieldModel);
        } else {
          this.fieldModel = data[this.getOptionsSelectValue];
          this.handleChangeEvent(data[this.getOptionsSelectValue]);
        }
      }, 200);
    },
    getSelectItem() {
      return this.selectItem;
    },
    normalizer(node) {
      //当子节点也就是children=[]时候去掉子节点
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node[this.getOptionsSelectValue],
        label: node[this.getSelectLabel],
        children: node.children,
      };
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .vue-treeselect__menu-container {
  font-size: 14px !important;
}

::v-deep .vue-treeselect__value-container {
  font-size: 14px !important;
}
</style>
