<template>
  <view-wrapper
    :designer="designer"
    :field="field"
    :design-state="designState"
    :parent-widget="parentWidget"
    :parent-list="parentList"
    :index-of-parent-list="indexOfParentList"
    :sub-form-row-index="subFormRowIndex"
    :sub-form-col-index="subFormColIndex"
    :sub-form-row-id="subFormRowId"
    :echo-field-data="echoFieldData"
    :sub-edit-flag="subEditFlag"
  >
    <div
      class="ganttBox"
      v-loading="loading"
      :style="{ height: computedHeight || field.options.minHeight + 'px' }"
    >
      <div
        class="ganttClass"
        ref="gantt"
        style="width: 100%; height: 100%"
        v-if="data.length"
      ></div>
      <lt-empty v-else class="j-center" style="width: 100%; height: 100%" />
    </div>
  </view-wrapper>
</template>

<script>
import i18n, { translate } from "@/utils/i18n";
import viewWrapper from "./view-wrapper";
import fieldMixin from "@/components/form-designer/form-widget/field-widget/fieldMixin";
import emitter from "element-ui/lib/mixins/emitter";
import { gantt } from "dhtmlx-gantt";
import "dhtmlx-gantt/codebase/dhtmlxgantt.css";
import { XRWebGLLayer } from "three";
export default {
  name: "gantt-widget",
  componentName: "FieldWidget", //必须固定为FieldWidget，用于接收父级组件的broadcast事件
  components: {
    viewWrapper,
  },
  mixins: [emitter, fieldMixin, i18n],
  props: {
    field: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    designer: Object,
    designState: {
      type: Boolean,
      default: false,
    },

    subFormRowIndex: {
      /* 子表单组件行索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormColIndex: {
      /* 子表单组件列索引，从0开始计数 */ type: Number,
      default: -1,
    },
    subFormRowId: {
      /* 子表单组件行Id，唯一id且不可变 */ type: String,
      default: "",
    },
    subEditFlag: {
      type: Object,
      default: null,
    },
    echoFieldData: {
      type: Array,
      default: null,
    },
  },
  inject: ["refList", "formConfig", "globalOptionData", "globalModel"],
  data() {
    return {
      data: [],
      links: [],
      loading: false,
      grain: 4,
      computedHeight: "",
      dateRange: [],
    };
  },
  created() {
    /* 注意：子组件mounted在父组件created之后、父组件mounted之前触发，故子组件mounted需要用到的prop
       需要在父组件created中初始化！！ */
    this.registerToRefList();
    this.initEventHandler();
    this.handleOnCreated();
    // 将cols配置复制到组件中
  },
  mounted() {
    if (this.field.options.defaultDateRange) {
      this.dateRange = this.field.options.defaultDateRange;
    }
    if (this.field.options.defaultGrain) {
      this.grain = this.field.options.defaultGrain;
    }
    this.handleOnMounted();
  },

  methods: {
    initConfig() {
      this.loading = true;
      gantt.clearAll();
      gantt.config = {
        ...gantt.config,
        ...{
          readonly: this.field.options.readonly, // 只读
          show_grid: true, // 是否显示左侧树表格
          show_task_cells: true,
          fit_tasks: true, // 时间范围自动适应
          auto_types: true,
          autoscroll: true,
          drag_links: false,
          drag_progress: false,
          date_format: "%Y-%m-%d %H:%i:%s",
          task_date: "%m-%d",
          show_progress: false,
          autosize: true, // 自适应甘特图的尺寸大小, 使得在不出现滚动条的情况下, 显示全部任务
          open_tree_initially: true, // 初始化的时候就展开树结构
          min_task_grid_row_height: 40,
          row_height: 40,
          scale_height: 80,
          columns: this.field.options.ganttCols,
          show_task_cells: true,
        },
      };
      gantt.plugins({
        auto_scheduling: true,
        undo: true,
        redo: true,
        tooltip: true,
        marker: true,
      });

      //设置开始结束时间
      let dateRange = this.dateRange;
      if (dateRange && dateRange.length) {
        if (dateRange[0].length == 10) {
          dateRange[0] = dateRange[0] + " 00:00:00";
          dateRange[1] = dateRange[1] + " 23:59:59";
        }
        gantt.config.start_date = new Date(dateRange[0]);
        gantt.config.end_date = new Date(dateRange[1]);
      } else {
        gantt.config.start_date = null;
        gantt.config.end_date = null;
      }

      // 1.3 设置中文
      gantt.i18n.setLocale("cn");
      // 1.4 鼠标悬浮框

      gantt.templates.tooltip_text = (start, end, task) => {
        let JS = new Function("task", this.field.options.onGanttTooltip);
        if (JS.call(this, task)) {
          return JS.call(this, task);
        } else {
          return "";
        }
      };

      var dayTemplate = (date) => {
        let weekDays = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"];
        return `${this.$moment(date).format("MM-DD")}(${
          weekDays[new Date(date).getDay()]
        })`;
      };

      var daysStyle = function (date) {
        if (date.getDay() === 0 || date.getDay() === 6) {
          return "weekend";
        }
        return "";
      };
      var weekScaleTemplate = function (date) {
        var dateToStr = gantt.date.date_to_str("%m-%d");
        var endDate = gantt.date.add(
          gantt.date.add(date, 1, "week"),
          -1,
          "day"
        );
        return dateToStr(date) + " - " + dateToStr(endDate);
      };
      if (this.grain == "month") {
        gantt.config.scales = [{ unit: "month", step: 1, format: "%Y-%m" }];
      } else if (this.grain == "week") {
        gantt.config.scales = [
          { unit: "week", step: 1, format: weekScaleTemplate },
        ];
      } else if (this.grain == "day" || this.grain == 24) {
        gantt.config.scales = [
          { unit: "day", step: 1, format: dayTemplate, css: daysStyle },
        ];
      } else {
        gantt.config.scales = [
          { unit: "day", step: 1, format: dayTemplate, css: daysStyle },
          {
            unit: "hour",
            step: this.grain,
            format: "%H:%i",
          },
        ];
        gantt.config.duration_unit = "hour";
      }
      //设置任务条进度内容
      gantt.templates.progress_text = function (start, end, task) {
        return (
          "<div style='text-align:left;color:#fff;padding-left:20px'>" +
          Math.round(task.progress * 100) +
          "% </div>"
        );
      };
      //任务条显示内容
      gantt.templates.task_text = function (start, end, task) {
        // return task.text + '(' + task.duration + '天)';
        return (
          "<div style='text-align:center;color:#fff'>" +
          task.text +
          "(" +
          task.duration +
          (gantt.config.duration_unit == "day" ? "天" : "小时") +
          ")" +
          "</div>"
        );
      };
      // 右侧时间的列宽
      gantt.config.min_column_width = this.field.options.dateWidth;

      // 2.4 定义从后端获取或发送到后端的日期数据解析格式
      gantt.config.xml_date = "%Y-%m-%d %H:%i:%s";

      // 3 拖动配置
      // 3.1 自动调整类型,当存在子节点时自动升级为project
      gantt.config.auto_types = true; // 父子长度一起变化

      // 3.2 设置不可以拖动进度
      // gantt.config.drag_progress = false;
      // 3.3 设置Task不可以拖动
      gantt.config.drag_move = this.field.options.allowDrag;
      // 3.4 设置不可以拖动关系
      // gantt.config.drag_links = false;
      // 3.5 设置不可拖动Task 大小
      gantt.config.drag_resize = this.field.options.allowDrag;
      // 3.7 双击显示明细
      gantt.config.details_on_dblclick = true;

      gantt.attachEvent("onTaskDblClick", (id, e) => {
        let row = this.data.find((item) => item.id === id);
        if (row) {
          let JS = new Function(
            "id",
            "row",
            "data",
            this.field.options.onTaskDblClick
          );
          return JS.call(this, id, row, this.data);
        }
      });

      gantt.attachEvent("onAfterTaskDrag", (id, mode, e) => {
        var row = this.data.find((item) => item.id === id);

        if (row) {
          let bfs=()=>{
            if (this.data.filter((item) => item.parent == row.parent).length == 1) {
              row = this.data.find((item) => item.id === row.parent);
              bfs()
            }
          }
          bfs()
          let arr = this.data.filter(
            (item) =>
              item.parent == row.parent &&
              row.id !== item.id &&
              item.parent != 0
          );
          let fun = (item, start, end) => {
            return (
              (item.start_date >= start && item.start_date < end) ||
              (item.end_date > start && item.end_date <= end)
            );
          };
          if (arr.some((item) => fun(row, item.start_date, item.end_date))) {
            this.$confirm("该任务与其他任务时间有冲突，是否继续？", "提示", {
              confirmButtonText: "确定",
              cancelButtonText: "撤回",
              type: "warning",
            })
              .then(() => {
                row.color = "#ff0000";
                gantt.parse({
                  data: this.data,
                  links: this.links,
                });
                let JS = new Function(
                  "id",
                  "row",
                  "data",
                  this.field.options.onAfterTaskDrag
                );
                return JS.call(this, id, row, this.data);
              })
              .catch(() => {
                row.color = null;
                gantt.undo();
              });
          } else {
            row.color = null;
            gantt.parse({
              data: this.data,
              links: this.links,
            });
            let JS = new Function(
              "id",
              "row",
              "data",
              this.field.options.onAfterTaskDrag
            );
            return JS.call(this, id, row, this.data);
          }
        }
      });

      setTimeout(() => {
        this.data.forEach((item) => {
          item.id && (item.id += "");
          item.parent && (item.parent += "");
          if (item.start_date) {
            if (item.start_date.length == 10) {
              item.start_date += " 00:00:00";
            }
            item.start_date = new Date(item.start_date);
          }
          if (item.end_date) {
            if (item.end_date.length == 10) {
              item.end_date += " 23:59:59";
            }
            item.end_date = new Date(item.end_date);
          }
        });
        // 甘特图初始化和导入数据

        if (this.data.length) {
           gantt.init(this.$refs.gantt);
          gantt.parse({
            data: this.data,
            links: this.links,
          });
        }

        this.loading = false;

        let top = null;
        if (this.field.options.minHeight) {
          top =
            parseFloat($(".ganttClass").offset().top) +
            parseFloat(this.field.options.minHeight) -
            8 +
            "px";
        }
        if (this.field.options.heightFit) {
          top = this.computedHeight;

          top = top.replaceAll(
            "100vh",
            `100vh + ${parseFloat($(".ganttClass").offset().top)}px - 8px `
          );
        }

        $(".scrollHor_cell").css({
          position: "fixed",
          top,
        });
      }, 300);
    },
    setData(data, dateRange) {
      this.data = data.filter((item) => item.start_date !== item.end_date);
      if (dateRange) {
        if (typeof dateRange == "string") {
          dateRange = dateRange.split(",");
        }
        this.dateRange = dateRange;
      } else {
        this.dateRange = this.field.options.defaultDateRange;
      }

      if (this.field.options.heightFit) {
        this.resizeHeight();
      }
      this.initConfig();
    },
    resizeHeight() {
      let h = $(".ganttBox").offset().top;
      this.computedHeight = `calc(100vh - ${h + 40}px)`;
    },
    setLinks(data) {
      this.links = data;
      this.initConfig();
    },
    setLoading(val) {
      this.loading = val;
    },
    setGrain(val) {
      this.grain = val;
      this.initConfig();
    },
    undo() {
      gantt.undo();
    },
    redo() {
      gantt.redo();
    },
  },
};
</script>
<style lang="scss"  scope>
.weekend {
  color: red !important;
}
.gantt_tree_icon.gantt_folder_open {
  display: none;
}
.gantt_tree_icon.gantt_file {
  display: none;
}
.gantt_tree_icon.gantt_folder_closed {
  display: none;
}

.gantt_tree_icon.gantt_close {
  margin-right: 10px;
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAAAAXNSR0IArs4c6QAAAMRJREFUOE+90jEKwjAYBeD/Uegi6OQgCIKTiHexk0dI6dDTdCjNEZzqXUScBEFwcFJwKZQnDanUWBxaMGPIx3v5E0jHhY5O+sEwDJe+71+SJHn8ahDH8bAoimmWZQeTqJTaiwg9z1unaXpqw1EUzcuy3IkItNYrA6tEkluSTwCB1vraxEqpCckcwADA5p3o4JvFd9tmZNG4RtX+x3DqZABnkoE5AOQkZ030BZvJInK0dRcuaoUOrhLNndyB9XvHLr/n/4kvsIdiD3R/61sAAAAASUVORK5CYII=);
}
.gantt_tree_icon.gantt_open {
  margin-right: 10px;
  transform: rotate(-90deg);
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAAAAXNSR0IArs4c6QAAAMRJREFUOE+90jEKwjAYBeD/Uegi6OQgCIKTiHexk0dI6dDTdCjNEZzqXUScBEFwcFJwKZQnDanUWBxaMGPIx3v5E0jHhY5O+sEwDJe+71+SJHn8ahDH8bAoimmWZQeTqJTaiwg9z1unaXpqw1EUzcuy3IkItNYrA6tEkluSTwCB1vraxEqpCckcwADA5p3o4JvFd9tmZNG4RtX+x3DqZABnkoE5AOQkZ030BZvJInK0dRcuaoUOrhLNndyB9XvHLr/n/4kvsIdiD3R/61sAAAAASUVORK5CYII=);
}
.gantt_tooltip {
  border-radius: 10px;
  z-index: 999999;
  & > p {
    font-size: 12px;
    color: #333;
  }
}
</style>
<style scoped lang="scss">
::v-deep .gantt_task .gantt_task_scale .gantt_scale_cell {
  background-color: var(--table-bg-color);
  color: #101010;
  font-weight: bold;
  border-right: 1px solid #cecece;
}
::v-deep .gantt_grid_scale .gantt_grid_head_cell {
  background-color: var(--table-bg-color);
  color: #101010;
  font-weight: bold;
}
</style>
<!--

  {
    title: '设置甘特图数据',
    codeBlock: `/**
 * 设置甘特图数据
 * @param { data }  Array  见下
 */
this.getWidgetRef(#id).setData([{
    id: 1, // 任务标识，可用来标识父子关系、连接links等
    text: '任务1', // 任务名
    start_date: '2023-11-03 00:00:00', // 开始时间
    end_date: '2023-11-05 00:00:00', // 结束时间
    duration: 10, // 时长
    progress: 0.6, // 项目的进度
    color: 'var(--primary-color)', // 颜色
  },
  {
    id: 12,
    text: '任务1-明细1',
    start_date: '2023-11-03 00:00:00',
    end_date: '2023-11-04 10:00:00',
    duration: 5,
    parent: 1,  // 父节点id,以此来表达树节点
    progress: 0.6,
    color: 'pink',
    process: '工序1',
  },
  {
    id: 13,
    text: '任务1-明细2',
    start_date: '2023-11-03 03:03:03',
    duration: 5,
    parent: 1,
    progress: 0.2,
    color: 'green',
    process: '工序2',
  },
  {
    text: '任务3',
    start_date: '2023-11-03 15:00:00',
    end_date: '2023-11-04 20:00:00',
    id: 23,
    progress: 0.43,
    color: 'green',
  },
])`,
    widgets: ['gantt'],
  },
  {
    title: '设置甘特图数据连线关系',
    codeBlock: `/**
 * 设置甘特图数据连线关系
 * @param { data }  Array  见下
 */
this.getWidgetRef(#id).setLinks([{
  id: 1, // 主键
  source: 12, // 起始id
  target: 13, // 结束id
  type: '0', // 连线的方式
}])`,
    widgets: ['gantt'],
  },
  {
    title: '设置甘特图表头',
    codeBlock: `/**
 * 设置甘特图表头
 * @param { data }  Array  见下
 */
this.getWidgetRef(#id).setColumns([
  {
    name: 'text', // task列定义的变量
    label: '阶段名字', // 列头显示的文本
    width: 250, // 宽度
    tree: true, // 是否显示连接树关系
  },
  {
    name: 'process',
    label: '工序',
    width: 100,
  },
])`,
    widgets: ['gantt'],
  },
 -->
