<template>
  <div class="codeBox">
    <el-row>
      <el-col :span="3" >
        <componentTree ref="componentTreeRef" style="height: calc(100vh - 200px);font-size: 14px;color: #606266;width: 96%;" menuType="event"/>
      </el-col>
      <el-col :span="21">
        <div
          class="pd-r20"
          style="height: calc(100vh - 200px); overflow: auto"
        >
          <el-input size="mini" :placeholder="'搜索代码项 /' + ' ' + eventInfo.eventData.length" v-model="filterValue" style="width: 91%;" ></el-input>
          <el-button @click="refresh()" type="primary" class="el-icon-refresh" size="mini" style="margin-left: 6px;">刷新</el-button>
          <div class="cardList">
            <el-card
              v-for="(item, index) in eventInfo.eventData.filter((item) => !filterValue || (item.name.includes(filterValue)) || item.code.includes(filterValue))"
              class="box-card"
              :key="index"
              shadow="hover"
            >
              <div slot="header" class="clearfix" style="zoom: 0.85;">
                {{ item.name }}
              </div>
              <div>
                <el-row>
                  <el-col :span="24" style="display: flex; justify-content: space-between; align-items: center;">
                    <el-tag size="mini" style="border-radius: 10px;padding: 0 10px;font-weight: bolder;">{{ item.eventType }}</el-tag>
                    <el-link type="info" class="delete-link" :underline="false"><i class="el-icon-delete" @click="deleteEvent(item, index)"></i></el-link>
                  </el-col>
                  <el-col :span="24" class="mr-t10" @click.native="choseEvent(item)">
                    <pre style="zoom: 0.7;">{{ item.code || '// 请点击编辑事件代码'}}</pre>
                  </el-col>
                </el-row>
              </div>
            </el-card>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import propertyRegister from "../setting-panel/propertyRegister.js";
const { EVENT_LANG_DIC } = propertyRegister;
import CodeEditor from "@/components/code-editor/index";
import componentTree from "../setting-panel/components/componentTree.vue";
import bus from "@/magic-editor/scripts/bus";
export default {
  props: {
    designer: Object,
  },
  data() {
    return {
      eventProps: EVENT_LANG_DIC,
      eventList: [],
      flag: true,
      filterValue:'',
      eventInfo: {
        eventVisible: false,
        component: "",
        eventType: "",
        eventData: [],
        code: ""
      },
    };
  },
  components: {
    CodeEditor,
    componentTree,
  },
  mounted() {
    // 事件添加监听
    bus.$off("addEvent");
    bus.$on("addEvent", this.addEvent);

    // 脚本弹窗保存回调
    bus.$off("saveEventHandler");
    bus.$on("saveEventHandler", (res) => {
      let eventDataItem = this.eventInfo.eventData.find(e => e.component === res.id && e.eventType === res.eventType)
      if(res.columnKey){
        eventDataItem = this.eventInfo.eventData.find(e => e.component === res.id && e.eventType === res.eventType && e.columnKey === res.columnKey)
      }
      if(eventDataItem){
        eventDataItem.code = res.code

        this.$set(this.designer, "selectedWidgetName", null);
        this.$set(this.designer, "selectedWidgetColumnKey", null);

        this.$set(this.designer.formConfig, "eventInfo", this.eventInfo.eventData)
      }
    });

    // 监听formConfig变化
    let attempts = 0;
    const maxAttempts = 5;
    const checkInterval = 1200; // 1.2 秒

    const checkFormConfig = () => {
      if (this.designer.formConfig) {
        this.$refs.componentTreeRef.init(this.designer.widgetList);

        if(this.compareVersions(this.designer.formConfig.version, "1.0.1") < 0){
          this.refresh()
        } else {
          this.eventInfo.eventData = this.designer.formConfig.eventInfo ? this.designer.formConfig.eventInfo : []
        }
      } else if (attempts < maxAttempts) {
        attempts++;
        setTimeout(checkFormConfig, checkInterval);
      } else {
        console.log('尝试 5 次后仍未获取到 formConfig');
      }
    };

    setTimeout(checkFormConfig, checkInterval);
  },
  methods: {
    // 版本比较
    // 如果 version1 小于 version2 ，则返回 -1 ；
    // 如果 version1 大于 version2 ，则返回 1 ；
    // 如果两个版本号相等，则返回 0
    compareVersions(version1, version2) {
      if(!version1){
        return -1
      }
      const v1Parts = version1.split('.').map(Number);
      const v2Parts = version2.split('.').map(Number);
      const maxLength = Math.max(v1Parts.length, v2Parts.length);

      for (let i = 0; i < maxLength; i++) {
        const num1 = v1Parts[i] || 0;
        const num2 = v2Parts[i] || 0;

        if (num1 < num2) {
          return -1;
        } else if (num1 > num2) {
          return 1;
        }
      }

      return 0;
    },
    refresh(){
      this.eventInfo.eventData = []
      let arry = this.initEventList()
      if(arry && arry.length > 0){
        arry.forEach(item => {
          this.eventInfo.eventData.push({
            component: item.obj.id,
            eventType: item.key,
            code: item.value,
            name: item.name,
            type: item.type,
            columnKey: item.columnKey,
          })
        })

        this.$set(this.designer, "formConfig", {
          ...this.designer.formConfig,
          version: "1.0.1",
          eventInfo: this.eventInfo.eventData,
        })
      }
    },
    deleteEvent(eventItem, index) {
      this.$confirm('是否确定删除此事件代码', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        this.$array.handleWidgetList(this.designer["widgetList"], (item) => {
          if (item.type === 'data-table' && eventItem.type) {
            const { options } = item;
            const optionKeyMap = {
              'data-table-cols': {prop:'cols', itemKey: 'id'},
              'data-table-btns': {prop:'btns', itemKey: 'cust-id'},
              'data-table-leftBtns': {prop:'leftBtns', itemKey: 'cust-id'},
            };
            const optionKey = optionKeyMap[eventItem.type];

            if (options[optionKey.prop] && options[optionKey.prop].length > 0) {
              const targetItem = options[optionKey.prop].find((item) => item[optionKey.itemKey] === eventItem.columnKey);
              if(targetItem){
                targetItem[eventItem.eventType] = ""
              }
              return
            }
          } else {
            if(item.id === eventItem.component) {
              item.options[eventItem.eventType] = "";
              return
            }
          }
        })
        this.eventInfo.eventData.splice(index, 1);

        this.$set(this.designer.formConfig, "eventInfo", this.eventInfo.eventData)
      })
    },
    choseEvent(eventItem){
      this.$array.handleWidgetList(this.designer["widgetList"], (item) => {
        if (item.type === 'data-table' && eventItem.type) {
          if(eventItem.component == item.id){
            const { options } = item;
              const optionKeyMap = {
                'data-table-cols': 'cols',
                'data-table-btns': 'btns',
                'data-table-leftBtns': 'leftBtns'
              };
              const optionKey = optionKeyMap[eventItem.type];

              if (optionKey && options[optionKey] && options[optionKey].length) {

                this.setDesignerSelectedInfo(item, eventItem);
                return;
              }
          }
        } else {
          if(item.id === eventItem.component) {
            this.$set(this.designer, "selectedWidget", item)
            this.$set(this.designer, "selectedId", item.id)
            this.$set(this.designer, "selectedWidgetName", item.options.name)
            this.$set(this.designer, "selectedWidgetColumnKey", null);
            this.$set(this.designer, "selectedWidgetType", null);
            this.$nextTick(() => {
              bus.$emit('callEditEventHandler', eventItem.eventType, [])
            })
            return
          }
        }
      })
    },
    setDesignerSelectedInfo(item, eventItem) {
      this.$set(this.designer, "selectedWidget", item);
      this.$set(this.designer, "selectedId", item.id);
      this.$set(this.designer, "selectedWidgetName", item.options.name);
      this.$set(this.designer, "selectedWidgetType", eventItem.type);
      this.$set(this.designer, "selectedWidgetColumnKey", eventItem.columnKey);

      this.$nextTick(() => {
        bus.$emit('callEditEventHandler', eventItem.eventType, [])
      });
    },
    addEvent(component) {
      let {item,data} = component;
      let eventDataItem= null

      if(["data-table-cols", "data-table-btns", "data-table-leftBtns"].includes(data.type)){
        eventDataItem = this.eventInfo.eventData.length > 0 ? this.eventInfo.eventData.find(e => e.component === data.id && e.columnKey === data.columnKey && e.eventType === item.value) : null
      } else {
        eventDataItem = this.eventInfo.eventData.length > 0 ? this.eventInfo.eventData.find(e => e.component === data.id && e.eventType === item.value) : null
      }
      if (!eventDataItem) {
        let name = `${data.id}.${data.label ? data.label + '.' : ""}.【${item.label}】`
        if(["data-table-cols", "data-table-btns", "data-table-leftBtns"].includes(data.type)){
          name = `${data.id}.data-table.【${item.label}】.${data.label}`
        }

        this.eventInfo.eventData.push({
          "name": name,
          "desc": "事件描述",
          "component": data.id,
          "eventType": item.value,
          "columnKey": data.columnKey,
          "type": ["data-table-cols", "data-table-btns", "data-table-leftBtns"].includes(data.type) ? data.type : "",
          "code": "// 请点击编辑事件代码"
        })

        this.$set(this.designer.formConfig, "eventInfo", this.eventInfo.eventData)
      } else {
        this.choseEvent(eventDataItem)
      }
    },
    eventCommit() {
      if(this.eventInfo.eventData.length > 0){
        for (let i = 0; i < this.eventInfo.eventData.length; i++) {
          console.log(this.designer)
          let event = this.eventInfo.eventData[i]
          if (event.component === "") {
            // 全局事件
            this.formConfig[event.eventType] = event.code
          }
        }

        this.$set(this.formConfig, "eventInfo", this.eventInfo.eventData)
      }

      this.eventInfo.eventVisible = false
    },
    initEventList() {
      let arr = [];

      this.$array.handleWidgetList(this.designer.widgetList || [], (item) => {
        for (let key in EVENT_LANG_DIC) {
          if (!this.$string.isAllComments(item.options[key])) {
            arr.push({
              name: `${item.id}.${item.options.label?item.options.label + '.' : ""}【${
                EVENT_LANG_DIC[key]
              }】`,
              value: item.options[key],
              obj: item,
              key: key,
            });
          }
        }
        if (item.type == "data-table") {
          const { options } = item;
          if (options.leftBtns.length) {
            options.leftBtns.forEach((e) => {
              if (!this.$string.isAllComments(e.fun) && e.name) {
                arr.push({
                  name: `${item.id}.${item.options.label || ""}.【${
                    EVENT_LANG_DIC.tableLeftClick
                  }】.${e.name}`,
                  value: e.fun,
                  obj: item,
                  key: 'fun',
                  type: 'data-table-leftBtns',
                  columnKey: e['cust-id']
                });
              }
              if (!this.$string.isAllComments(e.displayFun) && e.name) {
                arr.push({
                  name: `${item.id}.${item.options.label || ""}.【${
                    EVENT_LANG_DIC.tableLeftShow
                  }】.${e.name}`,
                  value: e.displayFun,
                  obj: item,
                  key: 'displayFun',
                  type: 'data-table-leftBtns',
                  columnKey: e['cust-id']
                });
              }
            });
          }
          if (options.btns.length) {
            options.btns.forEach((e) => {
              if (!this.$string.isAllComments(e.fun) && e.name) {
                arr.push({
                  name: `${item.id}.${item.options.label || ""}.【${EVENT_LANG_DIC.tableOperateClick}】.${e.name}`,
                  value: e.fun,
                  obj: item,
                  key: 'fun',
                  type: 'data-table-btns',
                  columnKey: e['cust-id']
                });
              }
              if (!this.$string.isAllComments(e.displayFun) && e.name) {
                arr.push({
                  name: `${item.id}.${item.options.label || ""}.【${EVENT_LANG_DIC.tableOperateShow}】.${e.name}`,
                  value: e.displayFun,
                  obj: item,
                  key: 'displayFun',
                  type: 'data-table-btns',
                  columnKey: e['cust-id']
                });
              }
            });
          }
          if (options.cols.length) {
            options.cols.forEach((e) => {
              if (!this.$string.isAllComments(e.code) && e.label) {
                arr.push({
                  name: `${item.id}.${item.options.label || ""}.【${EVENT_LANG_DIC.tableColHtml}】.${e.label}`,
                  value: e.code,
                  obj: item,
                  type: 'data-table-cols',
                  columnKey: e.id,
                  key: 'code',
                });
              }
              if (!this.$string.isAllComments(e.clickCode) && e.label) {
                arr.push({
                  name: `${item.id}.${item.options.label || ""}.【${EVENT_LANG_DIC.tableColLink}】.${e.label}`,
                  value: e.clickCode,
                  obj: item,
                  key: 'clickCode',
                  type: 'data-table-cols',
                  columnKey: e.id,
                });
              }
            });
          }
        }
      });
      return arr
    }
  },
};
</script>

<style lang="scss" scoped>
.codeBox {
  position: relative;
  // padding-left: 220px !important;
}
// ::v-deep .componentTree {
//   position: absolute;
//   left: 10px;
// }

.delete-link:hover{
  color: red
}
.cardList {
  margin-top: 15px;
  display: flex;
  flex-wrap: wrap;
  .box-card {
    position: relative;
    width: calc(33.33% - 10px) ;
    margin-bottom: 15px;
    margin-right: 15px;
    &:nth-child(3n) {
      margin-right: 0;
    }
  }

  ::v-deep .el-descriptions-item__content {
    width: 100% !important;
  }

  .mr-t10 {
    background-color: #f9fafb;
    padding: 5px 10px;
    border-radius: 4px;
    cursor: pointer;
    max-height: 100px;
    overflow:hidden;

    box-shadow: 0 0 1px rgba(0, 0, 0, 0);
    -webkit-transition-duration: 0.3s;
    transition-duration: 0.3s;
    -webkit-transition-property: transform;
    transition-property: transform;
  }

  .mr-t10:hover {
    background-color: #f9fafb;
    padding: 5px 10px;
    border-radius: 4px;
    cursor: pointer;

    -webkit-transform: translateY(-2px);
    transform: translateY(-2px);
  }
}
</style>
