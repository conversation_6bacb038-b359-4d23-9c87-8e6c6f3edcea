<template>
  <div class="form-widget-container">
    <div v-if="groupDialog">
      <el-tabs
        v-model="activeName"
        style="background-color: #ffffff; padding: 0px 10px 0 10px"
      >
        <el-tab-pane
          v-for="item in tabs"
          :label="item.name"
          :key="item.id"
          :name="item.id"
        />
      </el-tabs>
      <el-form
        class="full-height-width widget-form"
        :label-position="labelPosition"
        :class="[customClass, layoutType + '-layout']"
        :size="size"
        :validate-on-rule-change="false"
      >
        <div v-if="designer.widgetList.length === 0" class="no-widget-hint">
          {{ i18nt("designer.noWidgetHint") }}
        </div>

        <draggable
          v-if="activeName == 'vfrom'"
          :list="designer.widgetList"
          v-bind="{ group: 'dragGroup', ghostClass: 'ghost', animation: 300 }"
          handle=".drag-handler"
          @end="onDragEnd"
          @add="onDragAdd"
          @update="onDragUpdate"
          :move="checkMove"
        >
          <transition-group name="fade" tag="div" class="form-widget-list outer-layer">
            <template v-for="(widget, index) in designer.widgetList">
              <template v-if="widget.type != 'dialog' && widget.type != 'drawer'">
                <template v-if="'container' === widget.category">
                  <component
                    :is="getWidgetName(widget)"
                    :widget="widget"
                    :designer="designer"
                    :key="widget.id"
                    :parent-list="designer.widgetList"
                    :index-of-parent-list="index"
                    :parent-widget="null"
                    :class="widget.id"
                  ></component>
                </template>
                <template v-else>
                  <component
                    :is="getWidgetName(widget)"
                    :isFieldList="true"
                    :field="widget"
                    :designer="designer"
                    :key="widget.id"
                    :parent-list="designer.widgetList"
                    :index-of-parent-list="index"
                    :parent-widget="null"
                    :design-state="true"
                    :class="widget.id"
                  ></component>
                </template>
              </template>
            </template>
          </transition-group>
        </draggable>
        <template v-if="activeName !== 'vfrom'">
          <template v-for="(widget, index) in designer.widgetList">
            <template v-if="(widget.type === 'dialog'||widget.type === 'drawer') && activeName === widget.id">
              <template v-if="'container' === widget.category">
                <component
                  :is="getWidgetName(widget)"
                  :widget="widget"
                  :designer="designer"
                  :key="widget.id"
                  :parent-list="designer.widgetList"
                  :index-of-parent-list="index"
                  :parent-widget="null"
                  :class="widget.id"
                ></component>
              </template>
              <template v-else>
                <component
                  :is="getWidgetName(widget)"
                  :isFieldList="true"
                  :field="widget"
                  :designer="designer"
                  :key="widget.id"
                  :parent-list="designer.widgetList"
                  :index-of-parent-list="index"
                  :parent-widget="null"
                  :design-state="true"
                  :class="widget.id"
                ></component>
              </template>
            </template>
          </template>
        </template>
      </el-form>
    </div>
    <div v-else>
      <el-form
        class="full-height-width widget-form"
        :label-position="labelPosition"
        :class="[customClass, layoutType + '-layout']"
        :size="size"
        :validate-on-rule-change="false"
      >
        <div v-if="designer.widgetList.length === 0" class="no-widget-hint">
          {{ i18nt("designer.noWidgetHint") }}
        </div>

        <draggable
          :list="designer.widgetList"
          v-bind="{ group: 'dragGroup', ghostClass: 'ghost', animation: 300 }"
          handle=".drag-handler"
          @end="onDragEnd"
          @add="onDragAdd"
          @update="onDragUpdate"
          :move="checkMove"
        >
          <transition-group name="fade" tag="div" class="form-widget-list outer-layer">
            <template v-for="(widget, index) in designer.widgetList">
              <template v-if="'container' === widget.category">
                <component
                  :is="getWidgetName(widget)"
                  :widget="widget"
                  :designer="designer"
                  :key="widget.id"
                  :parent-list="designer.widgetList"
                  :index-of-parent-list="index"
                  :parent-widget="null"
                  :class="widget.id"
                ></component>
              </template>
              <template v-else>
                <component
                  :is="getWidgetName(widget)"
                  :isFieldList="true"
                  :field="widget"
                  :designer="designer"
                  :key="widget.id"
                  :parent-list="designer.widgetList"
                  :index-of-parent-list="index"
                  :parent-widget="null"
                  :design-state="true"
                  :class="widget.id"
                ></component>
              </template>
            </template>
          </transition-group>
        </draggable>
      </el-form>
    </div>
  </div>
</template>

<script>
import Draggable from "vuedraggable";
import "@/components/form-designer/form-widget/container-widget/index";
import FieldComponents from "@/components/form-designer/form-widget/field-widget/index";
import i18n from "@/utils/i18n";
import bus from "@/magic-editor/scripts/bus";
import containerMixin from "@/components/form-designer/form-widget/container-widget/containerMixin";
import componentTree from "../setting-panel/components/componentTree.vue";
import { deepClone, generateId } from "@/utils/util";
export default {
  name: "VFormWidget",
  componentName: "VFormWidget",
  mixins: [i18n, containerMixin],
  components: {
    componentTree,
    Draggable,

    ...FieldComponents,
  },
  props: {
    designer: Object,
    formConfig: Object,
    optionData: {
      //prop传入的选项数据
      type: Object,
      default: () => {},
    },
  },
  provide() {
    return {
      refList: this.widgetRefList,
      formConfig: this.formConfig,
      globalOptionData: this.optionData,
      openDialogTableShow: null,
      globalModel: {
        formModel: this.formModel,
      },
      busUid: "",
      status: "",
    };
  },
  watch: {
    "designer.widgetList": {
      handler(val) {
        // console.log('不修改问题',this.status)
        // 由于流程权限配置也使用了 不能触发这个事件， 流程权限配置的时候 status 等于 authority 其他时候是空
        // if (this.status === "" || this.status === null || this.status === undefined) {
        //   bus.$emit("updateViewTemplate");
        // }
        // 处理移动后界面布局的问题
        // for (let i = 0; i < this.designer.widgetList.length; i++) {
        //   let thisField = this.designer.widgetList[i];
        //   //第一层不是容器类型就必须使用栅格包裹
        //   if (this.designer.widgetList[i].category !== "container") {
        //     // 创建栅格
        //     let newCrid = deepClone(this.designer.getContainerByType("grid"));
        //
        //     newCrid.id = newCrid.type.replace(/-/g, "") + generateId();
        //     newCrid.options.name = newCrid.id;
        //
        //     // 创建栅格
        //     let newCridCol = deepClone(
        //       this.designer.getContainerByType("grid-col")
        //     );
        //
        //     let tmpCridId = generateId();
        //     newCridCol.id = "grid-col-" + tmpCridId;
        //     newCridCol.options.name = "gridCol" + tmpCridId;
        //     newCridCol.options.span = 24;
        //     newCridCol.widgetList.push(thisField);
        //     newCridCol = deepClone(newCridCol);
        //
        //     // 将栅格列放入栅格
        //     newCrid.cols.push(newCridCol);
        //     this.designer.widgetList.splice(i, 1, newCrid);
        //   } else if (this.designer.widgetList[i].type === "grid") {
        //     // 栅格需要判断是不是空栅格 空的需要删除掉
        //     let deli = 0;
        //     for (let j = 0; j < this.designer.widgetList[i].cols.length; j++) {
        //       if (this.designer.widgetList[i].cols[j].widgetList.length === 0) {
        //         deli++;
        //       }
        //     }
        //     if (this.designer.widgetList[i].cols.length === deli) {
        //       this.designer.widgetList.splice(i, 1);
        //     }
        //   }
        // }
        let tabs = [{ name: "主界面", id: "vfrom" }];
        let cr = (arr) => {
          arr.forEach((item) => {
            try {
              if (item.type == "dialog"||item.type=='drawer') {
                tabs.push({
                  name: item.options.mark
                    ? item.options.mark
                    : item.options.label||item.id,
                  id: item.id,
                });
              }
              if (item.cols && item.cols.length > 0) {
                cr(item.cols);
              }
              if (item.widgetList && item.widgetList.length > 0) {
                cr(item.widgetList);
              }
            } catch (e) {
              // 里面可能有栅格等字段没有清除接口
            }
          });
        };
        //迭代
        cr(this.designer.widgetList);
        this.tabs = tabs;
        let nowTabId = "vfrom";
        for (let i = 0; i < tabs.length; i++) {
          if (this.activeName == tabs[i].id) {
            nowTabId = tabs[i].id;
          }
        }
        this.activeName = nowTabId;
      },
      deep: true,
    },
    "designer.formConfig": {
      handler(val) {
        // 由于流程权限配置也使用了 不能触发这个事件， 流程权限配置的时候 status 等于 authority 其他时候是空
        // if (this.status === "" || this.status === null || this.status === undefined) {
        //   bus.$emit("updateViewTemplate");
        // }
      },
      deep: true,
    },
    tabs: {
      handler(val, old) {
        if (this.flag) {
          if (val.length > old.length) {
            for (let i = 0; i < val.length; i++) {
              if (!old[i] || val[i].id !== old[i].id) {
                this.activeName = val[i].id;
                break;
              }
            }
          } else if (val.length < old.length) {
            this.activeName = "vfrom";
          }
        }
      },
      immediate: false,
    },
    activeName: {
      handler(val, old) {
        this.$array.handleWidgetList(this.designer.widgetList, (item) => {
          if (item.id === val) {
            this.selectWidget(item);
          }
        });
      },
    },
  },
  data() {
    return {
      formModel: {},
      widgetRefList: {},
      activeName: "vfrom",
      tabs: [],
      groupDialog: true,
      flag: false,
      tabPosition: "", //最外部区分配置界面和代码的选项卡标识
    };
  },
  computed: {
    labelPosition() {
      if (
        !!this.designer.formConfig &&
        !!this.designer.formConfig.labelPosition
      ) {
        return this.designer.formConfig.labelPosition;
      }
      return "left";
    },
    size() {
      if (!!this.designer.formConfig && !!this.designer.formConfig.size) {
        return this.designer.formConfig.size;
      }
      return "medium";
    },
    customClass() {
      return this.designer.formConfig.customClass || "";
    },
    layoutType() {
      return this.designer.getLayoutType();
    },
  },
  created() {


    // 监听任务
    bus.$on("onVFromGroupDialogChange", (flag) => {
      // 编辑界面是否将弹窗分组
      this.groupDialog = flag;
    });
  },

  mounted() {
            this.designer.initDesigner();
    this.disableFirefoxDefaultDrop(); /* 禁用Firefox默认拖拽搜索功能!! */
    this.designer.registerFormWidget(this);

    this.designer.isDesign = true;
    window.addEventListener("keydown", this.handleEvent);
    setTimeout(() => {
      this.flag = true;
    }, 1000);
  },
  methods: {
    tabClick() {
      this.$nextTick(() => {
        this.$refs.componentTreeRef.init(this.designer.widgetList);
      });
    },
    handleEvent(e) {
      bus.$emit(`keydown${this.designer.selectedId}`, e);
      bus.$emit("shortcutKeys", e);
      if (e.altKey && [37, 38, 39, 40].includes(e.keyCode)) {
        // 阻止默认事件
        e.preventDefault();
      }
    },
    getWidgetName(widget) {
      return widget.type + "-widget";
    },

    disableFirefoxDefaultDrop() {
      let isFirefox =
        navigator.userAgent.toLowerCase().indexOf("firefox") !== -1;
      if (isFirefox) {
        document.body.ondrop = function (event) {
          event.stopPropagation();
          event.preventDefault();
        };
      }
    },

    onDragEnd(evt) {
      console.log("drag end000", evt);
    },

    onDragAdd(evt) {
      const newIndex = evt.newIndex;
      if (!!this.designer.widgetList[newIndex]) {
        this.designer.setSelected(this.designer.widgetList[newIndex]);
      }

      this.designer.emitHistoryChange();
    },

    onDragUpdate() {
      /* 在VueDraggable内拖拽组件发生位置变化时会触发update，未发生组件位置变化不会触发！！ */
      this.designer.emitHistoryChange();
    },

    checkMove(evt) {
      return this.designer.checkWidgetMove(evt);
    },

    getFormData() {
      return this.formModel;
    },

    getWidgetRef(widgetName, showError) {
      let foundRef = this.widgetRefList[widgetName];
      if (!foundRef && !!showError) {
        this.$message.error(
          this.i18nt("designer.hint.refNotFound") + widgetName
        );
      }
      return foundRef;
    },
  },
  beforeDestroy() {
    // 页面销毁 解除
    window.removeEventListener("keydown", this.handleEvent);
  },
};
</script>

<style lang="scss" scoped>
.container-scroll-bar {
  ::v-deep .el-scrollbar__wrap,
  ::v-deep .el-scrollbar__view {
    overflow-x: hidden;
  }
}

.form-widget-container {
  padding: 0 10px;
  background: #f1f2f3;
  box-sizing: border-box;
  overflow-x: hidden;
  overflow-y: auto;
  position: relative;

  .el-form.full-height-width {
    //     background-image:    linear-gradient(
    //   90deg,
    //   rgba(180, 180, 180, 0.15) 1%,
    //   rgba(0, 0, 0, 0) 1%
    // ),
    // linear-gradient(rgba(180, 180, 180, 0.3) 1%, rgba(0, 0, 0, 0) 1%);
    // background-size: 25% 10%;
    background-color: #fff;
    position: relative;
    &::after {
      position: absolute;
      top: 0;
      left: 0;
      content: "";
      width: 100%;
      height: 100%;
      background-image: linear-gradient(
          0deg,
          black 0px,
          black 1px,
          transparent 1px,
          transparent 100px
        ),
        linear-gradient(
          90deg,
          black 0px,
          black 1px,
          transparent 1px,
          transparent 100px
        );
      background-size: 8.323333% 60px;
      opacity: 0.1;
      z-index: -1;
    }

    height: 100%;
    // background: #ffffff;
    z-index: 10;
    .no-widget-hint {
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      font-size: 18px;
      color: #999999;
    }

    .form-widget-list {
      padding: 2px;
      min-height: calc(100vh - 180px)
    }
  }

  .el-form.PC-layout {
    //
    min-height: calc(100vh - 180px);
  }

  .el-form.Pad-layout {
    margin: 0 auto;
    width: 960px;
    border-radius: 15px;
    box-shadow: 0 0 1px 10px #495060;
  }

  .el-form.H5-layout {
    margin: 0 auto;
    width: 420px;
    border-radius: 15px;
    //border-width: 10px;
    box-shadow: 0 0 1px 10px #495060;
  }

  .el-form.widget-form ::v-deep .el-row {
    padding: 2px;
    border: 1px dashed rgba(170, 170, 170, 0.75);
  }
}

.grid-cell {
  min-height: 30px;
  border-right: 1px dotted #cccccc;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s;
}

.fade-enter,
.fade-leave-to {
  opacity: 0;
}
</style>
