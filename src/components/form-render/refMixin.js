export default {
  inject: ['formConfig'],
  methods: {
    initRefList() {
      if (this.refList !== null && !!this.widget.options.name) {
        this.refList[this.widget.options.name] = this;
      }
    },
    $setInterval(name, callback, time) {
      let that = this;
      if (!window[name]) {
        window[name] = setInterval(() => {
          callback && callback.call(that);
        }, time);
        this.$once('hook:beforeDestroy', () => {
          if (window[name]) {
            clearInterval(window[name]);
            window[name] = undefined;
          }
        });
      }
    },
    getWidgetRef(widgetName, showError) {
      let foundRef = this.refList[widgetName];
      if (!foundRef && !!showError) {
        this.$message.error(this.i18nt('render.hint.refNotFound') + widgetName);
      }
      return foundRef;
    },

    $getValue(id) {
      if (this.getWidgetRef(id)) {
        return this.getWidgetRef(id).getValue();
      } else if (
        this?.formConfig?.store &&
        this?.formConfig?.store.hasOwnProperty(id)
      ) {
        return this.formConfig.store[id];
      } else if (
        this?.formConfig?.temp &&
        this?.formConfig?.temp.hasOwnProperty(id)
      ) {
        return this.formConfig.temp[id];
      }
    },

    $setValue(id, value) {
      if (this.getWidgetRef(id)) {
        this.getWidgetRef(id).setValue(value);
      } else if (this.formConfig.store) {
        this.$set(this.formConfig.store, id, value);
      }
    },
    $setLoading(id) {
      return this.$loading({
        target: this.getWidgetRef(id) ? this.getWidgetRef(id).$el : undefined,
      });
    },

    getFormRef() {
      /* 获取VFrom引用，必须在VForm组件created之后方可调用 */
      return this.refList['v_form_ref'];
    },

    getWriteState() {
      /** true 为编辑即后端已经该了表单值，  false 为提交后端已经给了值*/
      return this.writeState;
    },
    closePreviewDialog() {
      let that = this;
      while (that.$options?.name !== 'previewDialog') {
        if (that.$options?.name === 'App') {
          break;
        }
        that = that.$parent;
      }
      that.close();
    },
    // 获取引用模板外层的组件实例对象
    getParentWidgetRef(widgetName) {
      let foundRef = undefined;
      const dfs = (root) => {
        if (root.$el.id == 'app') {
          console.error(
            '未找到对应组件，请检查唯一名称‘' + widgetName + '’是否正确！',
          );
          return;
        }
        if (root?.widgetRefList?.[widgetName]) {
          foundRef = root.widgetRefList[widgetName];
          return;
        }
        root.$parent && dfs(root.$parent);
      };
      dfs(this);
      return foundRef;
    },
  },
};
