import { EVENT_LANG_DIC } from "@/components/form-designer/setting-panel/propertyRegister.js";

export const eventMap = {
  dialog: [
    {
      value: "onCreated",
      label: "创建事件",
    },
    {
      value: "onDialogClose",
      label: "弹窗关闭事件",
    },
  ],
  card: [
    {
      value: "onMounted",
      label: "挂载事件",
    },
  ],
  "condition-container": [
    {
      value: "onSearchClick",
      label: "搜索点击事件",
    },
    {
      value: "onResetClick",
      label: "重置点击事件",
    },
  ],
  tab: [
    {
      value: "tabChange",
      label: "标签切换事件",
    },
  ],
  condition: [
    {
      value: "onQuery",
      label: "查询点击事件",
    },
  ],
  "custom-condition": [
    {
      value: "onCreated",
      label: "创建事件",
    },
    {
      value: "onMounted",
      label: "挂载事件",
    },
  ],
  drawer: [
    {
      value: "onCreated",
      label: "创建事件",
    },
    {
      value: "onDialogClose",
      label: "弹窗关闭事件",
    },
  ],
  "sub-form": [
    {
      value: "onSubFormRowAdd",
      label: "子表单行添加事件",
    },
    {
      value: "onSubFormRowAdd",
      label: "子表单行添加事件",
    },
    {
      value: "onSubFormRowInsert",
      label: "子表单插入行事件",
    },
    {
      value: "onSubFormRowDelete",
      label: "子表单行删除事件",
    },
    {
      value: "onSubFormRowChange",
      label: "子表单行改变事件",
    },
    {
      value: "onSubSave",
      label: "保存事件",
    },
  ],
  "dialog-body": [
    {
      value: "onCreated",
      label: "创建事件",
    },
    {
      value: "onMounted",
      label: "挂载事件",
    },
  ],
  "dialog-footer": [
    {
      value: "onCreated",
      label: "创建事件",
    },
    {
      value: "onMounted",
      label: "挂载事件",
    },
  ],
  "condition-container-footer": [
    {
      value: "onCreated",
      label: "创建事件",
    },
    {
      value: "onMounted",
      label: "挂载事件",
    },
  ],
  "condition-container-body": [
    {
      value: "onCreated",
      label: "创建事件",
    },
    {
      value: "onMounted",
      label: "挂载事件",
    },
  ],
  input: [
    {
      value: "onCreated",
      label: "创建事件",
    },
    {
      value: "onMounted",
      label: "挂载事件",
    },
    {
      value: "onInput",
      label: "输入事件",
    },
    {
      value: "onChange",
      label: "改变事件",
    },
    {
      value: "onFocus",
      label: "聚焦事件",
    },
    {
      value: "onBlur",
      label: "失焦事件",
    },
    {
      value: "onValidate",
      label: "验证事件",
    },
    {
      value: "onPrependClick",
      label: "前缀点击事件",
    },
    {
      value: "onAppendClick",
      label: "后缀点击事件",
    },
    {
      value: "onEnter",
      label: "回车事件",
    },
  ],
  select: [
    {
      value: "onCreated",
      label: "创建事件",
    },
    {
      value: "onMounted",
      label: "挂载事件",
    },
    {
      value: "onRemoteQuery",
      label: "远程搜索事件",
    },
    {
      value: "onChange",
      label: "改变事件",
    },
    {
      value: "allowCreateFn",
      label: "创建选项事件",
    },
    {
      value: "onFocus",
      label: "聚焦事件",
    },
    {
      value: "onBlur",
      label: "失焦事件",
    },
    {
      value: "onValidate",
      label: "验证事件",
    },
    {
      value: "onAppendClick",
      label: "后缀点击事件",
    },
  ],
  "order-input": [
    {
      value: "onCreated",
      label: "创建事件",
    },
    {
      value: "onMounted",
      label: "挂载事件",
    },
    {
      value: "onInput",
      label: "输入事件",
    },
    {
      value: "onChange",
      label: "改变事件",
    },
    {
      value: "onFocus",
      label: "聚焦事件",
    },
    {
      value: "onBlur",
      label: "失焦事件",
    },
    {
      value: "onValidate",
      label: "验证事件",
    },
  ],
  textarea: [
    {
      value: "onCreated",
      label: "创建事件",
    },
    {
      value: "onMounted",
      label: "挂载事件",
    },
    {
      value: "onInput",
      label: "输入事件",
    },
    {
      value: "onChange",
      label: "改变事件",
    },
    {
      value: "onFocus",
      label: "聚焦事件",
    },
    {
      value: "onBlur",
      label: "失焦事件",
    },
    {
      value: "onValidate",
      label: "验证事件",
    },
  ],
  number: [
    {
      value: "onCreated",
      label: "创建事件",
    },
    {
      value: "onMounted",
      label: "挂载事件",
    },
    {
      value: "onInput",
      label: "输入事件",
    },
    {
      value: "onChange",
      label: "改变事件",
    },
    {
      value: "onFocus",
      label: "聚焦事件",
    },
    {
      value: "onBlur",
      label: "失焦事件",
    },
    {
      value: "onValidate",
      label: "验证事件",
    },
  ],
  radio: [
    {
      value: "onCreated",
      label: "创建事件",
    },
    {
      value: "onMounted",
      label: "挂载事件",
    },
    {
      value: "onChange",
      label: "改变事件",
    },
    {
      value: "onValidate",
      label: "验证事件",
    },
  ],
  checkbox: [
    {
      value: "onCreated",
      label: "创建事件",
    },
    {
      value: "onMounted",
      label: "挂载事件",
    },
    {
      value: "onChange",
      label: "改变事件",
    },
    {
      value: "onValidate",
      label: "验证事件",
    },
  ],
  tree: [
    {
      value: "onCreated",
      label: "创建事件",
    },
    {
      value: "onMounted",
      label: "挂载事件",
    },
    {
      value: "onRemoteQuery",
      label: "远程搜索事件",
    },
    {
      value: "onChange",
      label: "改变事件",
    },
    {
      value: "onFocus",
      label: "聚焦事件",
    },
    {
      value: "onBlur",
      label: "失焦事件",
    },
    {
      value: "onValidate",
      label: "验证事件",
    },
  ],
  "picture-upload": [
    {
      value: "onCreated",
      label: "创建事件",
    },
    {
      value: "onMounted",
      label: "挂载事件",
    },
    {
      value: "onBeforeUpload",
      label: "上传前事件",
    },
    {
      value: "onUploadSuccess",
      label: "上传成功事件",
    },
    {
      value: "onUploadError",
      label: "上传失败事件",
    },
    {
      value: "onValidate",
      label: "验证事件",
    },
  ],
  cascader: [
    {
      value: "onCreated",
      label: "创建事件",
    },
    {
      value: "onMounted",
      label: "挂载事件",
    },
    {
      value: "onChange",
      label: "改变事件",
    },
    {
      value: "onFocus",
      label: "聚焦事件",
    },
    {
      value: "onBlur",
      label: "失焦事件",
    },
    {
      value: "onValidate",
      label: "验证事件",
    },
  ],
  "file-upload": [
    {
      value: "onCreated",
      label: "创建事件",
    },
    {
      value: "onMounted",
      label: "挂载事件",
    },
    {
      value: "onBeforeUpload",
      label: "上传前事件",
    },
    {
      value: "onUploadSuccess",
      label: "上传成功事件",
    },
    {
      value: "onUploadError",
      label: "上传失败事件",
    },
    {
      value: "onValidate",
      label: "验证事件",
    },
  ],
  "select-page": [
    {
      value: "onCreated",
      label: "创建事件",
    },
    {
      value: "onMounted",
      label: "挂载事件",
    },
    {
      value: "onRemoteQuery",
      label: "远程搜索事件",
    },
    {
      value: "onChange",
      label: "改变事件",
    },
    {
      value: "onFocus",
      label: "聚焦事件",
    },
    {
      value: "onBlur",
      label: "失焦事件",
    },
    {
      value: "onValidate",
      label: "验证事件",
    },
  ],
  time: [
    {
      value: "onCreated",
      label: "创建事件",
    },
    {
      value: "onMounted",
      label: "挂载事件",
    },
    {
      value: "onChange",
      label: "改变事件",
    },
    {
      value: "onFocus",
      label: "聚焦事件",
    },
    {
      value: "onBlur",
      label: "失焦事件",
    },
    {
      value: "onValidate",
      label: "验证事件",
    },
  ],
  "time-range": [
    {
      value: "onCreated",
      label: "创建事件",
    },
    {
      value: "onMounted",
      label: "挂载事件",
    },
    {
      value: "onChange",
      label: "改变事件",
    },
    {
      value: "onFocus",
      label: "聚焦事件",
    },
    {
      value: "onBlur",
      label: "失焦事件",
    },
    {
      value: "onValidate",
      label: "验证事件",
    },
  ],
  date: [
    {
      value: "onCreated",
      label: "创建事件",
    },
    {
      value: "onMounted",
      label: "挂载事件",
    },
    {
      value: "onChange",
      label: "改变事件",
    },
    {
      value: "onFocus",
      label: "聚焦事件",
    },
    {
      value: "onBlur",
      label: "失焦事件",
    },
    {
      value: "onValidate",
      label: "验证事件",
    },
    {
      value: "onDisabledDate",
      label: "日期禁用事件",
    },
  ],
  "date-time": [
    {
      value: "onCreated",
      label: "创建事件",
    },
    {
      value: "onMounted",
      label: "挂载事件",
    },
    {
      value: "onChange",
      label: "改变事件",
    },
    {
      value: "onFocus",
      label: "聚焦事件",
    },
    {
      value: "onBlur",
      label: "失焦事件",
    },
    {
      value: "onValidate",
      label: "验证事件",
    },
  ],
  "date-range": [
    {
      value: "onCreated",
      label: "创建事件",
    },
    {
      value: "onMounted",
      label: "挂载事件",
    },
    {
      value: "onChange",
      label: "改变事件",
    },
    {
      value: "onFocus",
      label: "聚焦事件",
    },
    {
      value: "onBlur",
      label: "失焦事件",
    },
    {
      value: "onValidate",
      label: "验证事件",
    },
    {
      value: "onDisabledDate",
      label: "日期禁用事件",
    },
  ],
  switch: [
    {
      value: "onCreated",
      label: "创建事件",
    },
    {
      value: "onMounted",
      label: "挂载事件",
    },
    {
      value: "onChange",
      label: "改变事件",
    },
    {
      value: "onDisabledDate",
      label: "日期禁用事件",
    },
  ],
  rate: [
    {
      value: "onCreated",
      label: "创建事件",
    },
    {
      value: "onMounted",
      label: "挂载事件",
    },
    {
      value: "onChange",
      label: "改变事件",
    },
    {
      value: "onDisabledDate",
      label: "日期禁用事件",
    },
  ],
  color: [
    {
      value: "onCreated",
      label: "创建事件",
    },
    {
      value: "onMounted",
      label: "挂载事件",
    },
    {
      value: "onChange",
      label: "改变事件",
    },
    {
      value: "onDisabledDate",
      label: "日期禁用事件",
    },
  ],
  slider: [
    {
      value: "onCreated",
      label: "创建事件",
    },
    {
      value: "onMounted",
      label: "挂载事件",
    },
    {
      value: "onChange",
      label: "改变事件",
    },
    {
      value: "onDisabledDate",
      label: "日期禁用事件",
    },
  ],
  userinput: [
    {
      value: "onCreated",
      label: "创建事件",
    },
    {
      value: "onMounted",
      label: "挂载事件",
    },
  ],
  deptinput: [
    {
      value: "onCreated",
      label: "创建事件",
    },
    {
      value: "onMounted",
      label: "挂载事件",
    },
  ],
  "rich-editor": [
    {
      value: "onCreated",
      label: "创建事件",
    },
    {
      value: "onMounted",
      label: "挂载事件",
    },
    {
      value: "onValidate",
      label: "验证事件",
    },
  ],
  "data-table": [
    {
      value: "onCreated",
      label: "创建事件",
    },
    {
      value: "onMounted",
      label: "挂载事件",
    },
    {
      value: "onClickPrint",
      label: "点击打印事件",
    },
    {
      value: "onClickExport",
      label: "点击导出事件",
    },
    {
      value: "onClickSearch",
      label: "点击搜索事件",
    },
    {
      value: "onCellDblclick",
      label: "单元格双击事件",
    },
    {
      value: "onExpandChange",
      label: "展开行改变事件",
    },
    {
      value: "onEditChange",
      label: "行内编辑改变事件",
    },
    {
      value: "onBlurChange",
      label: "行内编辑失焦事件",
    },
    {
      value: "onFocusChange",
      label: "行内编辑聚焦事件",
    },
    {
      value: "onTableEnter",
      label: "行内编辑回车事件",
    },
    {
      value: "onSelection",
      label: "选择事件",
    },
    {
      value: "onRowStyle",
      label: "行样式",
    },
    {
      value: "onRowClass",
      label: "表头样式",
    },
    {
      value: "onCellStyle",
      label: "单元格样式",
    },
    {
      value: "onSpanMethod",
      label: "合并行列方法",
    },
    {
      value: "onCellClick",
      label: "单击单元格",
    },
    {
      value: "onRowClick",
      label: "单击行",
    },
    {
      value: "onSortChange",
      label: "拖拽回调",
    },
    {
      value: "onPaginationChange",
      label: "分页改变事件",
    },
    {
      value: "onCopyCallback",
      label: "粘贴回调事件",
    },
    {
      value: "onLazy",
      label: "懒加载",
    },
    {
      value: "onCurrentChange",
      label: "选择事件",
    }
  ],
  "data-table-btns": [
    {
      value: "fun",
      label: "表格操作列点击事件",
    },
    {
      value: "displayFun",
      label: "表格操作列显示条件",
    }
  ],
  "data-table-cols": [
    {
      value: "clickCode",
      label: "表格列事件",
      other: ['link', 'switch']
    },
    {
      value: "code",
      label: "自定义HTMl",
      other: ['html']
    }
  ],
  "data-table-leftBtns": [
    {
      value: "fun",
      label: "表格左侧按钮点击事件",
    },
    {
      value: "displayFun",
      label: "表格左侧按钮显示条件",
    }
  ],
  button: [
    {
      value: "onCreated",
      label: "创建事件",
    },
    {
      value: "onMounted",
      label: "挂载事件",
    },
    {
      value: "onClick",
      label: "点击事件",
    },
  ],
  "custom-tree": [
    {
      value: "onCreated",
      label: "创建事件",
    },
    {
      value: "onMounted",
      label: "挂载事件",
    },
    {
      value: "onRenderContent",
      label: "内容区JSX",
    },
    {
      value: "onFilterNodeMethod",
      label: "筛选方法",
    },
    {
      value: "onNodeClick",
      label: "点击节点",
    },
    {
      value: "onNodeCheck",
      label: "选中节点",
    },
    {
      value: "onLazy",
      label: "懒加载",
    },
    {
      value: "onScrollEnd",
      label: "滚动条触底事件",
    },
    {
      value: "onNodeStyle",
      label: "节点样式",
    },
  ],
  chart: [
    {
      value: "onCreated",
      label: "创建事件",
    },
    {
      value: "onMounted",
      label: "挂载事件",
    },
  ],
  spcAnalysis: [
    {
      value: "onCreated",
      label: "创建事件",
    },
    {
      value: "onMounted",
      label: "挂载事件",
    },
    {
      value: "onFormChange",
      label: "表单改变事件",
    },
  ],
  "static-text": [
    {
      value: "onCreated",
      label: "创建事件",
    },
    {
      value: "onMounted",
      label: "挂载事件",
    },
  ],
  "html-text": [
    {
      value: "onCreated",
      label: "创建事件",
    },
    {
      value: "onMounted",
      label: "挂载事件",
    },
  ],
  divider: [
    {
      value: "onCreated",
      label: "创建事件",
    },
    {
      value: "onMounted",
      label: "挂载事件",
    },
  ],
  "organizational-structure": [
    {
      value: "onCreated",
      label: "创建事件",
    },
    {
      value: "onMounted",
      label: "挂载事件",
    },
    {
      value: "onNodeClick",
      label: "点击节点",
    },
    {
      value: "onNodeCheck",
      label: "选中节点",
    },
  ],
  gantt: [
    {
      value: "onCreated",
      label: "创建事件",
    },
    {
      value: "onMounted",
      label: "挂载事件",
    },
    {
      value: "onTaskDblClick",
      label: "双击任务事件",
    },
    {
      value: "onAfterTaskDrag",
      label: "拖拽任务事件",
    },
    {
      value: "onGanttTooltip",
      label: "任务悬浮框内容",
    },
  ],
  map: [
    {
      value: "onCreated",
      label: "创建事件",
    },
    {
      value: "onMounted",
      label: "挂载事件",
    },
  ],
  position: [
    {
      value: "onCreated",
      label: "创建事件",
    },
    {
      value: "onMounted",
      label: "挂载事件",
    },
  ],
  qrcode: [
    {
      value: "onCreated",
      label: "创建事件",
    },
    {
      value: "onMounted",
      label: "挂载事件",
    },
  ],
  "vfrom-quote": [
    {
      value: "onCreated",
      label: "创建事件",
    },
    {
      value: "onMounted",
      label: "挂载事件",
    },
  ],
  data: [
    {
      value: "onCreated",
      label: "创建事件",
    },
    {
      value: "onMounted",
      label: "挂载事件",
    },
    {
      value: "onBeforeDestroy",
      label: "销毁前事件",
    },
  ],
  carousel: [
    {
      value: "onCreated",
      label: "创建事件",
    },
    {
      value: "onMounted",
      label: "挂载事件",
    },
  ],
  timeline: [
    {
      value: "onCreated",
      label: "创建事件",
    },
    {
      value: "onMounted",
      label: "挂载事件",
    },
  ],
  calendar: [
    {
      value: "onCreated",
      label: "创建事件",
    },
    {
      value: "onMounted",
      label: "挂载事件",
    },
    {
      value: "onChange",
      label: "改变事件",
    },
  ],
  barCode: [
    {
      value: "onCreated",
      label: "创建事件",
    },
    {
      value: "onMounted",
      label: "挂载事件",
    },
  ],
  progress: [
    {
      value: "onCreated",
      label: "创建事件",
    },
    {
      value: "onMounted",
      label: "挂载事件",
    },
    {
      value: "onProgressFormat",
      label: "文字内容",
    },
  ],
  transfer: [
    {
      value: "onChange",
      label: "改变事件",
    },
    {
      value: "onMounted",
      label: "挂载事件",
    },
  ],
  image: [
    {
      value: "onMounted",
      label: "挂载事件",
    },
    {
      value: "onImageClick",
      label: "图片点击事件",
    },
  ],
  codeConfig: [
    {
      value: "onMounted",
      label: "挂载事件",
    },
  ],
  steps: [
    {
      value: "onMounted",
      label: "挂载事件",
    },
    {
      value: "onChange",
      label: "改变事件",
    },
  ],
  descriptions: [
    {
      value: "onMounted",
      label: "挂载事件",
    },
    {
      value: "onValueClick",
      label: "点击右侧值",
    },
  ],
  onlineComponents: [
    {
      value: "onCreated",
      label: "创建事件",
    },
    {
      value: "onMounted",
      label: "挂载事件",
    },
    {
      value: "onHandleEmit",
      label: "接收事件",
    },
  ],
  webSocket: [
    {
      value: "onCreated",
      label: "创建事件",
    },
    {
      value: "onMounted",
      label: "挂载事件",
    },
    {
      value: "onMessage",
      label: "消息接收事件",
    },
  ],
  reportPrint: [
    {
      value: "onCreated",
      label: "创建事件",
    },
    {
      value: "onMounted",
      label: "挂载事件",
    },
  ],
  tablePrint: [
    {
      value: "onCreated",
      label: "创建事件",
    },
    {
      value: "onMounted",
      label: "挂载事件",
    },
  ],
  dropdown: [
    {
      value: "onCreated",
      label: "创建事件",
    },
    {
      value: "onMounted",
      label: "挂载事件",
    },
    {
      value: "onClick",
      label: "点击事件",
    },
    {
      value: "onCommand",
      label: "选中菜单项事件",
    },
  ],
  statisticCard: [
    {
      value: "onCreated",
      label: "创建事件",
    },
    {
      value: "onMounted",
      label: "挂载事件",
    },
  ],
  statistics: [
    {
      value: "onCreated",
      label: "创建事件",
    },
    {
      value: "onMounted",
      label: "挂载事件",
    },
  ],
  pointer: [
    {
      value: "onCreated",
      label: "创建事件",
    },
    {
      value: "onMounted",
      label: "挂载事件",
    },
    {
      value: "onNodeClick",
      label: "点击节点",
    },
    {
      value: "onPointerClick",
      label: "坐标点击事件",
    },
  ],
  scoringCard: [
    {
      value: "onMounted",
      label: "挂载事件",
    },
  ],
  ruleEngine: [
    {
      value: "onMounted",
      label: "挂载事件",
    },
  ],
  "custom-component": [
    {
      value: "onCreated",
      label: "创建事件",
    },
    {
      value: "onMounted",
      label: "挂载事件",
    },
    {
      value: "onReceive",
      label: "接收事件",
    },
  ],
  dashboard: [
    {
      value: "onCreated",
      label: "创建事件",
    },
    {
      value: "onMounted",
      label: "挂载事件",
    },
  ],
};

export const getEventLang = (event) => {
  return EVENT_LANG_DIC[event];
};

export const getEventtMenu = (type) => {
  return eventMap[type] || []; // 返回对应类型的事件菜单数组，或者空数组作为默认值
};
