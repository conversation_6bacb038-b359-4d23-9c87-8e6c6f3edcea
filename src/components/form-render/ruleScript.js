import Vue from 'vue';

const ruleScript = [{
  "name": "字段操作", "type": "field",
  "children": [{
    "type": "hiddenField",
    "name": "隐藏组件",
    "component": "input5515616",
    "setting": 1,
    "script": "this.getWidgetRef($env.componentId).setHidden(true)"
  }, {
    "type": "showField",
    "name": "显示组件",
    "setting": 1,
    "component": "input5515616",
    "script": "this.getWidgetRef($env.componentId).setHidden(false)"
  }, {
    "type": "setReadonly",
    "name": "只读组件",
    "setting": 1,
    "component": "input5515616",
    "script": "this.getWidgetRef($env.componentId).field.options.readonly = true"
  }, {
    "type": "setDisabled",
    "name": "禁用组件",
    "setting": 1,
    "component": "input5515616",
    "script": "this.getWidgetRef($env.componentId).field.options.disabled = true"
  }, {
    "type": "setWrite",
    "name": "可编辑组件",
    "setting": 1,
    "component": "input5515616",
    "script": "this.getWidgetRef($env.componentId).field.options.readonly = false; \n" + "this.getWidgetRef($env.componentId).field.options.disabled = false"
  }, {
    "type": "setRequired",
    "name": "设置必填",
    "setting": 1,
    "component": "input5515616",
    "script": "this.getWidgetRef($env.componentId).setRequired(true)"
  }, {
    "type": "setNotRequired",
    "name": "设置非必填",
    "setting": 1,
    "component": "input5515616",
    "script": "this.getWidgetRef($env.componentId).setRequired(false)"
  }, {
    "type": "resetField",
    "name": "重置组件",
    "setting": 1,
    "component": "input5515616",
    "script": "this.getWidgetRef($env.componentId).resetField()"
  }, {
    "type": "focus",
    "name": "聚焦组件",
    "setting": 1,
    "component": "input5515616",
    "script": "this.getWidgetRef($env.componentId).focus()"
  }]
},
  {
    "name": "接口操作", "type": "interfaces",
    children: [
      {
        "type": "sendInterfaces",
        "name": "接口调用",
        "component": "",
        "setting": 4,
        "script": "this.$interface($env.args.apiId,$env.args.param);"
      }
    ]
  },
  {
    "name": "集成操作", "type": "operations",
    children: [
      {
        "type": "sendInterfaces",
        "name": "数据赋值弹出层 tips:适用表格行事件触发,确保弹出层已打开",
        "component": "",
        "setting": 0,
        "script": "this.$loading({lock: true,fullscreen: false,spinner: \"el-icon-loading\",background: \"rgba(0, 0, 0, 0.7)\"});"
      },
      {
        "type": "arrToStr",
        "name": "数组转字符串 tips: arr.join()",
        "component": "",
        "setting": 0,
        "script": "this.$loading({lock: true,fullscreen: false,spinner: \"el-icon-loading\",background: \"rgba(0, 0, 0, 0.7)\"});"
      },
      {
        "type": "strToArr",
        "name": "字符串转数组 tips: arr.split()",
        "component": "",
        "setting": 0,
        "script": "this.$loading({lock: true,fullscreen: false,spinner: \"el-icon-loading\",background: \"rgba(0, 0, 0, 0.7)\"});"
      },
      {
        "type": "dateFormat",
        "name": "时间格式化",
        "component": "",
        "setting": 0,
        "script": "this.$loading({lock: true,fullscreen: false,spinner: \"el-icon-loading\",background: \"rgba(0, 0, 0, 0.7)\"});"
      }
    ]
  },
  {
    "name": "逻辑操作", "type": "logic",
    children: [
      {
        "type": "if",
        "name": "if判断",
        "component": "",
        "setting": 0,
        "script": "this.$loading({lock: true,fullscreen: false,spinner: \"el-icon-loading\",background: \"rgba(0, 0, 0, 0.7)\"});"
      },
      {
        "type": "setTimeout",
        "name": "延时触发器",
        "component": "",
        "setting": 0,
        "script": "this.$loading({lock: true,fullscreen: false,spinner: \"el-icon-loading\",background: \"rgba(0, 0, 0, 0.7)\"});"
      }
    ]
  },
  {
    "name": "模型操作", "type": "model",
    children: [
      {
        "type": "openModelDialog",
        "name": "操作模型数据（自动判断新增修改）",
        "component": "",
        "setting": 5,
        "script": "this.openModelOperation($env.modelName, $env.args)"
      },
      {
        "type": "delModelData",
        "name": "删除模型数据（逻辑删除）",
        "component": "",
        "setting": 5,
        "script": "this.openModelOperation($env.modelName, $env.args)"
      },
      {
        "type": "enableModelData",
        "name": "启用/停用模型数据（取反）",
        "component": "",
        "setting": 5,
        "script": "this.openModelOperation($env.modelName, $env.args)"
      }
    ]
  },
  {
    "name": "弹出层操作", "type": "dialog",
    children: [
      {
        "type": "openDialog",
        "name": "打开弹出层",
        "component": "",
        "setting": 3,
        "script": "this.getWidgetRef($env.componentId).setDialogVisible(true, () => {\n" +
          "})"
      },
      {
        "type": "closeDialog",
        "name": "关闭弹出层",
        "component": "",
        "setting": 3,
        "script": "this.getWidgetRef($env.componentId).setDialogVisible(false, () => {\n" +
          "})"
      }
    ]
  },
  {
    "name": "数据表格操作", "type": "dataTable",
    children: [
      {
        "type": "updateRefresh",
        "name": "刷新表格",
        "component": "",
        "setting": 6,
        "script": "this.getWidgetRef($env.componentId).updateRefresh()"
      }
    ]
  },
  {
    "name": "全局操作", "type": "global",
    "children": [
      {
      "type": "loading",
      "name": "loading",
      "component": "",
      "setting": 2,
      "script": "this.$loading({lock: true,fullscreen: false,spinner: \"el-icon-loading\",background: \"rgba(0, 0, 0, 0.7)\"});"
    },
    {
      "type": "closeLoading",
      "name": "关闭Loading",
      "component": "",
      "setting": 2,
      "script": "this.$loading({lock: true,fullscreen: false,spinner: \"el-icon-loading\",background: \"rgba(0, 0, 0, 0.7)\"});"
    },
    {
      "type": "message_info",
      "name": "普通消息",
      "component": "",
      "setting": 2,
      "script": " this.$message($env.value);"
    },
    {
      "type": "message_warning",
      "name": "警告消息",
      "component": "",
      "setting": 2,
      "script": "this.$message({message: $env.value,type: 'warning'});"
    },
    {
      "type": "message_success",
      "name": "成功消息",
      "component": "",
      "setting": 2,
      "script": "this.$message({message: $env.value,type: 'success'});"
    },
      {
        "type": "message_error",
        "name": "错误消息",
        "component": "",
        "setting": 2,
        "script": "this.$message({message: $env.value,type: 'error'});"
      }
    ]
  }
]


export default ruleScript;
