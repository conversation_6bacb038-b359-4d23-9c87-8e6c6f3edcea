<template>
  <container-item-wrapper :widget="widget">
    <el-dialog :title="widget.options.label" ref="dialog" append-to-body :data-isDrag="widget.options.isDrag ? '1' : '0'"
      v-drag-dialog :visible.sync="dialogVisible" :width="`${getWidth() || 60}%`"
      :before-close="handleClose" :close-on-click-modal="widget.options.closeOnClickModal"
      :fullscreen="widget.options.fullscreen" :show-close="widget.options.showClose" @open="openComplete"
      @close="closeComplete">
      <template slot="title">
        <p style="
            font-weight: 600;
            font-size: 18px;
            color: #333;
            margin: 0;
          ">
          {{ widget.options.label }}
        </p>
      </template>
      <template v-if="remakeCom">
        <div v-loading="loading" v-if="!!widget.widgetList[0].widgetList &&
          widget.widgetList[0].widgetList.length > 0
          ">
          <template v-for="(subWidget, swIdx) in widget.widgetList[0].widgetList">
            <template v-if="'container' === subWidget.category">
              <component :is="subWidget.type + '-item'" :widget="subWidget" :key="swIdx" :parent-list="widget.widgetList"
                :index-of-parent-list="swIdx" :parent-widget="widget"></component>
            </template>
            <template v-else>
              <component :is="subWidget.type + '-widget'" :field="subWidget" :designer="null" :key="swIdx"
                :parent-list="widget.widgetList" :index-of-parent-list="swIdx" :parent-widget="widget"></component>
            </template>
          </template>
        </div>
      </template>
      <div slot="footer" class="dialog-footer">
        <div class="d-flex j-center" v-if="!widget.options.isCustom && widget.options.leftBtns.length">
          <div class="d-flex">
            <template v-for="(item, index) in widget.options.leftBtns">
              <el-button :key="index" :type="item.type" :style="{
                backgroundColor: item.bgColor,
                borderColor: item.bgColor,
              }" :loading="item.loading" @click="handleLeftClick(item.fun)" v-if="handleLeftDisplay(item.displayFun) && !item.hidden">
                <template v-if="item.icon">
                  <i v-if="item.icon && item.icon.includes('el-icon')" :style="{ color: item.color }"
                    :class="item.icon"></i>
                  <svg-icon :class="[
                    !!!item.color && item.type
                      ? 'el-button--' + item.type
                      : '',
                  ]" v-else :icon-class="item.icon" :style="{ color: item.color, margin: 0 }" />
                </template>
                {{ item.name }}
              </el-button>
            </template>
          </div>
        </div>
        <template v-else>
          <template v-if="!!widget.widgetList[1].widgetList &&
            widget.widgetList[1].widgetList.length > 0
            ">
            <template v-for="(subWidget, swIdx) in widget.widgetList[1].widgetList">
              <template v-if="'container' === subWidget.category">
                <component :is="subWidget.type + '-item'" :widget="subWidget" :key="swIdx"
                  :parent-list="widget.widgetList" :index-of-parent-list="swIdx" :parent-widget="widget"></component>
              </template>
              <template v-else>
                <component :is="subWidget.type + '-widget'" :field="subWidget" :designer="null" :key="swIdx"
                  :parent-list="widget.widgetList" :index-of-parent-list="swIdx" :parent-widget="widget"></component>
              </template>
            </template>
          </template>
        </template>
      </div>
    </el-dialog>
    <dialogItemAuthority :widget="widget" v-if="status === 'authority'" />
  </container-item-wrapper>
</template>

<script>
import emitter from 'element-ui/lib/mixins/emitter';
import i18n from '@/utils/i18n';
import fieldMixin from '@/components/form-designer/form-widget/field-widget/fieldMixin';
import refMixin from '@/components/form-render/refMixin';
import ContainerItemWrapper from '@/components/form-render/container-item/container-item-wrapper';
import containerItemMixin from '@/components/form-render/container-item/containerItemMixin';
import FieldComponents from '@/components/form-designer/form-widget/field-widget/index';
import dialogItemAuthority from './dialog-item-authority.vue'
export default {
  name: 'dialog-item',
  componentName: 'ContainerItem',
  mixins: [emitter, i18n, refMixin, fieldMixin, containerItemMixin],
  components: {
    ContainerItemWrapper,
    ...FieldComponents,
    dialogItemAuthority
  },
  props: {
    widget: Object,
  },
  data() {
    return {
      dialogVisible: false,
      callbackFlag: null,
      timer: '',
      loading: false,
      remakeCom: true
    };
  },
  inject: ['refList', 'sfRefList', 'globalModel'],
  computed: {
    customClass() {
      return this.widget.options.customClass || '';
    },
  },
  watch: {
    dialogVisible(val) {
      if (this.widget.options.destroyOnClose) {
        this.remakeCom = val;
      }
    },
  },
  created() {
    this.initRefList();
    if (!this.widget.options.hasOwnProperty('isCustom')) {
      this.$set(this.widget.options, 'isCustom', true);
    }
  },
  beforeDestroy() {
    this.unregisterFromRefList();
  },
  methods: {
    getWidth(){
      if(window.innerWidth<1300){
        return this.widget.options.dialogWidthPad
      }else{
        return this.widget.options.dialogWidth
      }
    },
    setDialogLoading(state) {
      this.loading = state;
    },
    setLoading(index, state) {
      if (this.widget.options.leftBtns && this.widget.options.leftBtns.length) {
        this.$set(this.widget.options.leftBtns[index], 'loading', state);
      }
    },
    handleLeftClick(fun) {
      if (fun) {
        let JS = new Function(fun);
        JS.call(this);
      }
    },
    handleLeftDisplay(displayFun) {
      try {
        if (!displayFun) {
          return true;
        } else {
          let cell = new Function(displayFun);
          return cell.call(this);
        }
      } catch (e) {
        return true;
      }
    },
    setDialogVisible(flag, callback) {
      this.dialogVisible = flag;
      this.clearValidate();
      if (!flag && this.widget.options.destroyOnClose) {
        this.clear();
      }

      if (!flag) {
        callback && callback();
        return;
      }
      if (callback) {
        let timer = null;
        try {
          timer = setInterval(() => {
            // console.log('弹窗组件循环')
            if (this.callbackFlag) {
              this.callbackFlag = false;
              clearInterval(timer);
              let row = callback();
              if (row && Object.keys(row).length) this.setCategoryValue(row);
            }
          }, 100);
        } catch (error) {
          clearInterval(timer);
        }
      }
    },

    setDefaultValue(data) {
      if (data && Object.keys(data).length) {
        // 递归遍历
        const dfc = (root) => {
          root.forEach((item) => {
            let alias = item?.options?.alias;
            if (alias && alias in data) {
              this.getWidgetRef(item.id).setValue(data[alias]);
            }
            if ('widgetList' in item && item['widgetList'].length)
              dfc(item['widgetList']);
            if ('cols' in item && item['cols'].length) dfc(item['cols']);
          });
        };
        dfc(this.widget.widgetList);
      } else {
        console.error('setDefaultValue方法接收的参数为一个对象');
      }
    },

    setIsDisable(flag) {
      let list = this.widget.widgetList[0].widgetList;
      let fun = (arr) => {
        arr.forEach((item) => {
          if (item.options.name) {
            this.getWidgetRef(item.options.name).setDisabled &&
              this.getWidgetRef(item.options.name).setDisabled(flag);
          }
          if (item.cols || item.widgetList) {
            fun(item.cols || item.widgetList);
          }
        });
      };
      fun(list);
    },
  },
};
</script>

<style lang="scss" scoped>

// 修改弹窗内下拉树内容将弹窗撑大的问题
// ::v-deep .el-dialog {
//   overflow: visible;
// }
</style>
