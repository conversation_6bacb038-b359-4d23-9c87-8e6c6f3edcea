<template>
  <div>
    <container-item-wrapper :widget="widget">
      <div :class="[customClass]">
        <component
          v-for="(subWidget, index) in widget.widgetList"
          :is="subWidget.type + '-widget'"
          :field="subWidget"
          :key="subWidget.id"
          :parent-list="widget.widgetList"
          :subFormRowIndex="index"
          :parent-widget="widget"
        />
      </div>
    </container-item-wrapper>
  </div>
</template>

<script>
import emitter from 'element-ui/lib/mixins/emitter';
import i18n from '@/utils/i18n';
import fieldMixin from '@/components/form-designer/form-widget/field-widget/fieldMixin';
import refMixin from '@/components/form-render/refMixin';
import ContainerItemWrapper from '@/components/form-render/container-item/container-item-wrapper';
import containerItemMixin from '@/components/form-render/container-item/containerItemMixin';
import FieldComponents from '@/components/form-designer/form-widget/field-widget/index';

export default {
  name: 'iteration-item',
  componentName: 'ContainerItem',
  mixins: [emitter, i18n, refMixin, fieldMixin, containerItemMixin],
  components: {
    ContainerItemWrapper,
    ...FieldComponents,
  },
  props: {
    widget: Object,
    tabIndex: null,
    tablFildId: null,
  },
  inject: ['refList', 'sfRefList', 'globalModel'],
  data() {
    return {
      value: [],
      baseWidget: null,
      renderWidget: null
    };
  },
  computed: {
    customClass() {
      return this.widget.options.customClass || '';
    },
  },
  created() {
    this.initRefList();
  },
  mounted() {
    this.handleOnMounted();

  },
  beforeDestroy() {
    this.unregisterFromRefList();
  },
  methods: {
    setValue(items) {
      this.value = items;
      let arr = [];
      if(!this.baseWidget) {
        this.baseWidget = JSON.parse(JSON.stringify(this.widget.widgetList))
      }
      else {
        this.widget.widgetList = []
      }
      this.value.forEach((item, index) => {
        let temp = JSON.parse(JSON.stringify(this.baseWidget));
        temp.forEach((widgetItem) => {
          widgetItem.id = widgetItem.id + '-' + index;
          widgetItem.options.name = widgetItem.options.name + '-' + index;
          widgetItem.options.param = item;
        });
        arr.push(...temp);
      });
      this.widget.widgetList = JSON.parse(JSON.stringify(arr));
    },
    getValue() {
      return this.value
    },
    setIterationCount(data, callback) {
      this.setValue(data);
      setTimeout(() => {
        callback && callback();
      }, 100);
    },
  },
};
</script>

<style lang="scss" scoped></style>
