<template>
  <el-card style="margin-bottom: 20px">
    <div slot="header">
      <span v-if="widget.options.label === '弹出层'">弹出层</span>
      <span v-else>弹出层-{{ widget.options.label }}</span>
    </div>
    <div
      v-if="
        !!widget.widgetList[0].widgetList &&
        widget.widgetList[0].widgetList.length > 0
      "
    >
      <template v-for="(subWidget, swIdx) in widget.widgetList[0].widgetList">
        <template v-if="'container' === subWidget.category">
          <component
            :is="subWidget.type + '-item'"
            :widget="subWidget"
            :key="swIdx"
            :parent-list="widget.widgetList"
            :index-of-parent-list="swIdx"
            :parent-widget="widget"
          ></component>
        </template>
        <template v-else>
          <component
            :is="subWidget.type + '-widget'"
            :field="subWidget"
            :designer="null"
            :key="swIdx"
            :parent-list="widget.widgetList"
            :index-of-parent-list="swIdx"
            :parent-widget="widget"
          ></component>
        </template>
      </template>
    </div>
    <div class="dialog-footer">
      <div
        class="d-flex j-center"
        v-if="!widget.options.isCustom && widget.options.leftBtns.length"
      >
        <div class="d-flex">
          <template>
            <div
              v-for="(item, index) in widget.options.leftBtns"
              class="btnBox"
              :key="index"
            >
              <el-button
                :type="item.type"
                :style="{
                  backgroundColor: item.bgColor,
                  borderColor: item.bgColor,
                }"
                :loading="item.loading"
              >
                <template v-if="item.icon">
                  <i
                    v-if="item.icon && item.icon.includes('el-icon')"
                    :style="{ color: item.color }"
                    :class="item.icon"
                  ></i>
                  <svg-icon
                    :class="[
                      !!!item.color && item.type
                        ? 'el-button--' + item.type
                        : '',
                    ]"
                    v-else
                    :icon-class="item.icon"
                    :style="{ color: item.color, margin: 0 }"
                  />
                </template>
                {{ item.name }}
              </el-button>

              <div class="permissionMasking">
                <el-button-group class="maskingButton">
                  <el-button
                    :type="item.hidden ? 'primary' : 'default'"
                    @click="setMaskingHidden(index)"
                    icon="el-icon-view"
                    >隐藏</el-button
                  >
                </el-button-group>
              </div>
              <div class="permissionHidden" v-if="item.hidden">
                <span
                  style="
                    display: flex;
                    position: absolute;
                    top: 0;
                    right: 0;
                    bottom: 0;
                    left: 0;
                    z-index: 70 !important;
                    align-items: center;
                    justify-content: center;
                    cursor: pointer;
                  "
                >
                  <i class="el-icon-view"></i>
                </span>
              </div>
            </div>
          </template>
        </div>
      </div>
      <template v-else>
        <template
          v-if="
            !!widget.widgetList[1].widgetList &&
            widget.widgetList[1].widgetList.length > 0
          "
        >
          <template
            v-for="(subWidget, swIdx) in widget.widgetList[1].widgetList"
          >
            <template v-if="'container' === subWidget.category">
              <component
                :is="subWidget.type + '-item'"
                :widget="subWidget"
                :key="swIdx"
                :parent-list="widget.widgetList"
                :index-of-parent-list="swIdx"
                :parent-widget="widget"
              ></component>
            </template>
            <template v-else>
              <component
                :is="subWidget.type + '-widget'"
                :field="subWidget"
                :designer="null"
                :key="swIdx"
                :parent-list="widget.widgetList"
                :index-of-parent-list="swIdx"
                :parent-widget="widget"
              ></component>
            </template>
          </template>
        </template>
      </template>
    </div>
  </el-card>
</template>

<script>
import ContainerItemWrapper from '@/components/form-render/container-item/container-item-wrapper';
import FieldComponents from '@/components/form-designer/form-widget/field-widget/index';

export default {
  name: 'dialog-item-authority',
  props: {
    widget: Object,
  },
  components: {
    ContainerItemWrapper,
    ...FieldComponents,
  },
  data() {
    return {};
  },
  methods: {
    setMaskingHidden(index) {
      this.$set(
        this.widget.options.leftBtns[index],
        'hidden',
        !this.widget.options.leftBtns[index].hidden,
      );
    },
  },
};
</script>

<style scoped lang="scss">
.btnBox {
  position: relative;
  margin-right: 10px;
  .permissionMasking {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 99;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s;
  }
  .permissionMasking:hover {
    display: flex;
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 99;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    background: rgba(0, 0, 0, 0.3);
    transition: all 0.3s;
  }
  .maskingButton {
    display: none;
  }
  .permissionMasking:hover .maskingButton {
    display: flex;
  }
  .permissionHidden {
    display: flex;
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 98;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    background: rgba(0, 0, 0, 0.1);
    transition: all 0.3s;
  }
}
</style>
