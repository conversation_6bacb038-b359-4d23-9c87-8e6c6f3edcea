<template>
  <container-item-wrapper :widget="widget" class="d-flex">
    <div
      v-if="!widget.options.groupPage"
      :key="widget.id"
      class="sub-form-container flex-1"
      v-show="!widget.options.hidden"
    >
      <el-row class="header-row" style="padding: 0">
        <el-row class="mb8" v-if="!widget.options.readonly">
          <el-col :span="1.5">
            <el-button
              type="primary"
              plain
              icon="el-icon-plus"
              size="mini"
              v-if="widget.options.addAble"
              @click="addSubFormRow({}, null)"
              >新增
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="danger"
              plain
              icon="el-icon-delete"
              size="mini"
              :disabled="!checkedIndex.length"
              v-if="widget.options.deleteAble"
              @click="batchDelete"
              >删除
            </el-button>
          </el-col>
          <template v-for="(item, index) in widget.widgetList">
            <el-col :key="index" :span="1.5" v-if="item.type === 'button'">
              <el-button
                :type="item.options.type"
                plain
                size="mini"
                :disabled="!checkedIndex.length || hasEdit"
                @click="buttonExecute(item)"
                >{{ item.options.label }}
              </el-button>
            </el-col>
          </template>
        </el-row>
        <div>
          <div>
            <table
              class="ant-table-fixed"
              style="table-layout: fixed; width: 100%; overflow: auto"
            >
              <thead>
                <tr style="height: 40px">
                  <th
                    v-if="widget.options.deleteAble"
                    style="
                      width: 50px;
                      text-align: left;
                      color: #121315;
                      border-top: 1px solid #e6eaf0;
                      border-bottom: 1px solid #e6eaf0;
                      font-weight: 600;
                      background-color: #f9fafc;
                      position: relative;
                      padding: 4px 4px 4px 4px;
                      border-right: 1px solid #e6eaf0 !important;
                    "
                  >
                    <el-checkbox
                      @change="allSelect()"
                      :indeterminate="isIndeterminate"
                      v-model="checkedIndex.length == rowIdData.length"
                      style="margin: 0 0 10px 10px"
                    ></el-checkbox>
                  </th>
                  <!--                  <th  style="width:100px;text-align: left;color: #121315;border-top: 1px solid #E6EAF0;border-bottom: 1px solid #E6EAF0;font-weight: 600;background-color: #f9fafc;position: relative;padding: 4px 4px 4px 4px; border-right: 1px solid #E6EAF0!important;">-->
                  <!--                    <div style="width: 100%;text-align: center;">操作</div>-->
                  <!--                  </th>-->
                  <template v-for="(subWidget, rowId) in widget.widgetList">
                    <th
                      style="
                        text-align: left;
                        color: #121315;
                        border-top: 1px solid #e6eaf0;
                        border-bottom: 1px solid #e6eaf0;
                        font-weight: 600;
                        position: relative;
                        padding: 4px 0 4px 8px;
                        border-right: 1px solid #e6eaf0 !important;
                      "
                      :style="{
                        width: subWidget.width ? subWidget.width + 'px' : '',
                      }"
                      v-if="!subWidget.options.hidden"
                      :key="rowId"
                      :class="{ 'first-table': rowId == 0 }"
                    >
                      <div
                        v-if="!!subWidget.options.labelIconClass"
                        style="width: 100%"
                        class="custom-label"
                        :class="
                          subWidget.options.labelAlign ||
                          widget.options.labelAlign
                        "
                      >
                        <template
                          v-if="subWidget.options.labelIconPosition === 'front'"
                        >
                          <template v-if="!!subWidget.options.labelTooltip">
                            <el-tooltip
                              :content="subWidget.options.labelTooltip"
                              effect="light"
                            >
                              <i
                                :class="subWidget.options.labelIconClass"
                              ></i></el-tooltip
                            >{{ subWidget.options.label }}</template
                          >
                          <template v-else
                            ><i :class="subWidget.options.labelIconClass"></i
                            >{{ subWidget.options.label }}</template
                          >
                        </template>
                        <template
                          v-else-if="
                            subWidget.options.labelIconPosition === 'rear'
                          "
                        >
                          <template v-if="!!subWidget.options.labelTooltip">
                            {{ subWidget.options.label
                            }}<el-tooltip
                              :content="subWidget.options.labelTooltip"
                              effect="light"
                            >
                              <i
                                :class="subWidget.options.labelIconClass"
                              ></i></el-tooltip
                          ></template>
                          <template v-else>
                            {{ subWidget.options.label
                            }}<i :class="subWidget.options.labelIconClass"></i
                          ></template>
                        </template>
                      </div>
                      <div
                        v-else
                        style="width: 100%"
                        class="custom-label"
                        :class="
                          subWidget.options.labelAlign ||
                          widget.options.labelAlign
                        "
                      >
                        {{ subWidget.options.label }}
                      </div>
                    </th>
                  </template>
                </tr>
              </thead>
              <tbody>
                <template v-for="(rowData, index) in rowIdData">
                  <tr style="height: 40px" :key="rowData">
                    <td
                      v-if="widget.options.deleteAble"
                      style="
                        text-overflow: initial;
                        padding: 2px 8px;
                        border-right: 1px solid #e6eaf0 !important;
                        border-bottom: 1px solid #e6eaf0;
                      "
                    >
                      <el-checkbox
                        :value="checkedIndex.includes(index)"
                        @change="handleSelectionChange(index)"
                        style="margin: 0 0 10px 10px"
                      ></el-checkbox>
                    </td>
                    <!--                    <td style="text-overflow: initial;padding: 2px 8px;border-right: 1px solid #E6EAF0!important;border-bottom: 1px solid #E6EAF0;">-->
                    <!--                      <el-link-->
                    <!--                        class="mr-r10"-->
                    <!--                        :underline="false"-->
                    <!--                        type="primary"-->
                    <!--                      >编辑</el-link>-->
                    <!--                      <el-link-->
                    <!--                        class="mr-r10"-->
                    <!--                        :underline="false"-->
                    <!--                        type="danger"-->
                    <!--                      >删除</el-link>-->
                    <!--                    </td>-->
                    <template v-for="(subWidget, rowId) in widget.widgetList">
                      <td
                        v-if="!subWidget.options.hidden"
                        :style="{
                          width: subWidget.width ? subWidget.width + 'px' : '',
                        }"
                        :key="subWidget.id"
                        style="
                          text-overflow: initial;
                          padding: 0px 4px;
                          border-right: 1px solid #e6eaf0 !important;
                          border-bottom: 1px solid #e6eaf0;
                        "
                      >
                        <div
                          style="width: 100%"
                          class="sub-form-table-column hide-label"
                          :key="subWidget.id + 'tc' + index"
                        >
                          <template>
                            <component
                              :is="subWidget.type + '-widget'"
                              :field="fieldSchemaData[index][rowId]"
                              :key="fieldSchemaData[index][rowId].id"
                              :parent-list="widget.widgetList"
                              :index-of-parent-list="rowId"
                              :parent-widget="widget"
                              :sub-form-row-id="rowIdData[index]"
                              :sub-form-row-index="index"
                              :sub-form-col-index="rowId"
                              :echo-field-data="echoFieldData"
                            ></component>
                          </template>
                        </div>
                      </td>
                      <template v-else>
                        <div style="display: none">
                          <div
                            class="sub-form-table-column hide-label"
                            :key="subWidget.id + 'tc' + index"
                          >
                            <template>
                              <component
                                :is="subWidget.type + '-widget'"
                                :field="fieldSchemaData[index][rowId]"
                                :key="fieldSchemaData[index][rowId].id"
                                :parent-list="widget.widgetList"
                                :index-of-parent-list="rowId"
                                :parent-widget="widget"
                                :sub-form-row-id="rowIdData[index]"
                                :sub-form-row-index="index"
                                :sub-form-col-index="rowId"
                                :echo-field-data="echoFieldData"
                              ></component>
                            </template>
                          </div>
                        </div>
                      </template>
                    </template>
                  </tr>
                </template>
              </tbody>
            </table>
            <div class="d-flex j-center" v-if="rowIdData.length == 0">
              <el-empty :image-size="150"></el-empty>
            </div>
          </div>
        </div>
      </el-row>
    </div>
    <template v-if="widget.options.groupPage">
      <div
        style="width: 20% !important"
        class="el-transfer-panel"
        titles=""
        buttontexts=""
        filterplaceholder=""
        leftdefaultchecked=""
        rightdefaultchecked=""
        value="1,4"
        targetorder="original"
      >
        <p class="el-transfer-panel__header">
          <label class="el-checkbox">
            <span class="el-checkbox__input">
              {{ widget.widgetList[0].options.label }}
            </span>
          </label>
        </p>
        <div class="el-transfer-panel__body">
          <div
            role="group"
            aria-label="checkbox-group"
            class="el-checkbox-group el-transfer-panel__list"
          >
            <label
              class="el-transfer-panel__item"
              v-for="(item, index) in popupList"
              :key="index"
              @click="groupCheckedIndexChange(index)"
              :class="{ 'is-checked': index == groupCheckedIndex }"
            >
              <span class="el-checkbox__label"
                ><span>{{ item }}</span></span
              >
            </label>
            <p class="el-transfer-panel__empty" style="display: none">
              无匹配数据
            </p>
            <p class="el-transfer-panel__empty" style="display: none">无数据</p>
          </div>
        </div>
      </div>
      <div style="width: 76%; margin-left: 4%">
        <template v-for="(item, index) in popupList">
          <div :key="index" v-if="index == groupCheckedIndex">
            <div style="overflow: hidden scroll">
              <table
                tablelayout="fixed"
                class="ant-table-fixed"
                style="table-layout: fixed; width: 0px; min-width: 100%"
              >
                <thead>
                  <tr style="height: 40px">
                    <th
                      v-if="widget.options.deleteAble"
                      style="
                        width: 50px;
                        text-align: left;
                        color: #121315;
                        border-top: 1px solid #e6eaf0;
                        border-bottom: 1px solid #e6eaf0;
                        font-weight: 600;
                        background-color: #f9fafc;
                        position: relative;
                        padding: 4px 0 4px 8px;
                        border-right: 1px solid #e6eaf0 !important;
                      "
                    >
                      <span></span>
                    </th>
                    <th
                      style="
                        text-align: left;
                        color: #121315;
                        border-top: 1px solid #e6eaf0;
                        border-bottom: 1px solid #e6eaf0;
                        font-weight: 600;
                        background-color: #f9fafc;
                        position: relative;
                        padding: 4px 0 4px 8px;
                        border-right: 1px solid #e6eaf0 !important;
                      "
                      v-for="(subWidget, rowId) in widget.widgetList"
                      v-if="
                        subWidget.type != 'button' && !subWidget.options.hidden
                      "
                    >
                      <div
                        class="field-header-column label-center-align"
                        :class="{ 'is-required': subWidget.options.required }"
                      >
                        <span>{{ subWidget.options.label }}</span>
                      </div>
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <template v-for="(rowData, index) in groupData[index]">
                    <tr style="height: 40px" :key="index">
                      <td
                        v-if="widget.options.deleteAble"
                        :key="1"
                        style="
                          text-overflow: initial;
                          padding: 2px 8px;
                          border-right: 1px solid #e6eaf0 !important;
                          border-bottom: 1px solid #e6eaf0;
                        "
                      >
                        <el-checkbox
                          @change="handleSelectionChange(index)"
                          style="margin: 0 0 10px 10px"
                        ></el-checkbox>
                      </td>
                      <template v-for="(subWidget, rowId) in widget.widgetList">
                        <td
                          :key="subWidget.id"
                          v-if="
                            subWidget.type != 'button' &&
                            !subWidget.options.hidden
                          "
                          style="
                            text-overflow: initial;
                            padding: 2px 8px;
                            border-right: 1px solid #e6eaf0 !important;
                            border-bottom: 1px solid #e6eaf0;
                          "
                        >
                          <div
                            class="sub-form-table-column hide-label"
                            :key="subWidget.id + 'tc' + index"
                          >
                            <template>
                              <component
                                :is="subWidget.type + '-widget'"
                                :field="fieldSchemaData[index][rowId]"
                                :key="fieldSchemaData[index][rowId].id"
                                :parent-list="widget.widgetList"
                                :index-of-parent-list="rowId"
                                :parent-widget="widget"
                                :sub-form-row-id="rowIdData[rowData.groupIndex]"
                                :sub-form-row-index="rowData.groupIndex"
                                :sub-form-col-index="rowId"
                              ></component>
                            </template>
                          </div>
                        </td>
                        <template v-else>
                          <div style="display: none">
                            <div
                              class="sub-form-table-column hide-label"
                              :key="subWidget.id + 'tc' + index"
                            >
                              <template>
                                <component
                                  :is="subWidget.type + '-widget'"
                                  :field="fieldSchemaData[index][rowId]"
                                  :key="fieldSchemaData[index][rowId].id"
                                  :parent-list="widget.widgetList"
                                  :index-of-parent-list="rowId"
                                  :parent-widget="widget"
                                  :sub-form-row-id="
                                    rowIdData[rowData.groupIndex]
                                  "
                                  :sub-form-row-index="rowData.groupIndex"
                                  :sub-form-col-index="rowId"
                                ></component>
                              </template>
                            </div>
                          </div>
                        </template>
                      </template>
                    </tr>
                  </template>
                </tbody>
              </table>
            </div>
          </div>
        </template>
      </div>
    </template>
    <el-dialog
      :visible.sync="flowVisible"
      title="流程数据"
      center
      width="1400px"
      append-to-body
    >
      <el-scrollbar class="scrollbar">
        <operationFlowFromCopy
          :definition-id-prop="flowModel.definitionId"
          :select-type-prop="1"
        />
      </el-scrollbar>
    </el-dialog>
  </container-item-wrapper>
</template>

<script>
import emitter from 'element-ui/lib/mixins/emitter';
import i18n from '../../../utils/i18n';
import { deepClone, generateId } from '../../../utils/util';
import refMixin from '../../../components/form-render/refMixin';
import ContainerItemWrapper from './container-item-wrapper';
import containerItemMixin from './containerItemMixin';
import FieldComponents from '@/components/form-designer/form-widget/field-widget/index';
import {
  executeInterface,
  executeInterfaceBatch,
} from '@/api/interfaces/interfaces';
import operationFlowFromCopy from '@/views/tool/variantform/operationFlowFromCopy.vue';

export default {
  name: 'sub-form-item',
  componentName: 'ContainerItem',
  mixins: [emitter, i18n, refMixin, containerItemMixin],
  components: {
    ContainerItemWrapper,
    operationFlowFromCopy,
    ...FieldComponents,
  },
  props: {
    widget: Object,
  },
  inject: ['refList', 'sfRefList', 'globalModel'],
  data() {
    return {
      rowIdData: [],
      flag: true,
      initFlag: false,
      fieldSchemaData: [],
      echoFieldData: [],
      editFlag: {},
      flowVisible: false,
      flowModel: {},
      groupData: [],
      checkedIndex: [], // 选中的值
      isIndeterminate: false, // 全选控制
      originalList: [], // 源数据
      popupList: [], // 分组标题
      groupCheckedIndex: 0, // 分组情况下选中的值
    };
  },
  watch: {
    rowIdData: {
      immediate: true,
      handler(val) {
        if (this.rowIdData.length > 0) {
          this.rowIdData.forEach((item) => {
            this.$set(this.editFlag, item, this.editFlag[item] || false);
          });
        } else {
          this.editFlag = {};
        }
      },
      deep: true,
    },
  },
  created() {
    this.initRefList();
    this.registerSubFormToRefList();
    this.initRowIdData(true);
    this.initFieldSchemaData();
    this.initEventHandler();
    this.initFlag = true;
  },
  mounted() {
    this.handleSubFormFirstRowAdd(); //默认添加首行后，主动触发相关事件！！
    this.echoField(); // 回显值

    // switch 存储的是字符串 需要转换成bool
    this.formModel[this.widget.id].forEach((item, index) => {
      if (this.widget.widgetList.findIndex((e) => e.type === 'switch') != -1) {
        let i =
          this.fieldSchemaData[index][
            this.widget.widgetList.findIndex((e) => e.type === 'switch')
          ].options.name;
        if (item[i]) {
          item[i] = Boolean(JSON.parse(item[i]));
        }
      }
    });
    if (this.widget.options.groupPage) {
      this.groupData = this.dataGroupingToDeepArray();
      let key = this.fieldSchemaData[0][0].options.name;
      let original = [];
      for (let i = 0; i < this.groupData.length; i++) {
        original.push(this.groupData[i][0][key]);
      }
      this.originalList = JSON.stringify(original);
      this.popupList = JSON.parse(this.originalList);
    }
    console.log(this.widget.widgetList);
  },
  beforeDestroy() {
    this.unregisterFromRefList();
  },
  computed: {
    isFixedColumn() {
      return this.widget.widgetList.length >= 5;
    },
    formModel: {
      cache: false,
      get() {
        return this.globalModel.formModel;
      },
    },
    hasButton() {
      return this.widget.widgetList.find((e) => e.type == 'button') != null;
    },
    buttonId() {
      return this.widget.widgetList.find((e) => e.type == 'button').id;
    },
    // 是否在编辑状态
    hasEdit() {
      let hasEdit = false;
      for (const editFlagKey in this.editFlag) {
        if (this.editFlag[editFlagKey]) {
          hasEdit = true;
        }
      }
      return hasEdit;
    },
  },
  methods: {
    // 实时分组数据
    dataGroupingToDeepArray() {
      let array = this.formModel[this.widget.id];
      let key = this.fieldSchemaData[0][0].options.name;
      if (!key) return array;
      var aMap = [];
      var aResult = [];
      for (var i = 0; i < array.length; i++) {
        let item = array[i];
        item['groupIndex'] = i;
        if (aMap.indexOf(item[key]) === -1) {
          aResult.push([item]);
          aMap.push(item[key]);
        } else {
          let index = aMap.indexOf(item[key]);
          aResult[index].push(item);
        }
      }
      return aResult;
    },
    // 检查一组数据是否合格
    checkGroupData(data) {
      for (let itemIndex in data) {
        let itemData = data[itemIndex];
        let fields = this.fieldSchemaData[0];
        for (let j in fields) {
          let field = fields[j];
          if (field.options.required) {
            if (
              itemData[field.options.name] == null ||
              itemData[field.options.name] + '' == '' ||
              itemData[field.options.name].length == 0
            ) {
              this.$message.error(
                field.options.label + '属于必填项，请填写数据',
              );
              return false;
            }
          }
        }
      }
      return true;
    },
    groupCheckedIndexChange(index) {
      let result = this.checkGroupData(this.groupData[this.groupCheckedIndex]);
      if (result) {
        // 行完成方法 groupPage类型传入整组数据
        if (this.widget.options.onSubSave) {
          let changeFn = new Function('row', this.widget.options.onSubSave);
          changeFn.call(this, this.groupData[this.groupCheckedIndex]);
        }
        this.groupCheckedIndex = index;
        this.$forceUpdate();
      }
    },
    setValue(value) {
      this.echoFieldData = value;
    },
    getValue() {
      return this.echoFieldData;
    },
    echoField() {
      try {
        // 调用接口的请求参数
        let executeEntity = [];
        // element_key 与 apiId的对应关系
        let elementToApiMap = {};

        if (
          this.formModel[this.widget.id] == null ||
          this.formModel[this.widget.id].length == 0
        ) {
          this.echoFieldData = [];
          return;
        }
        // 循环子表单存在的列
        this.widget.widgetList.forEach((value) => {
          // 存在bandDataIdsApi 则当前列使用调用接口回显值
          if (value.options.bandDataIdsApi) {
            let ids = [];
            this.formModel[this.widget.id].forEach((itemData, index) => {
              if (itemData[value.id]) {
                ids.push(itemData[value.id]);
              }
            });
            if (ids.length > 0) {
              elementToApiMap[value.id] = value.options.bandDataIdsApi;
              executeEntity.push({
                apiId: value.options.bandDataIdsApi,
                body: { ids: ids.join() },
              });
            }
          } else if (
            value.options.optionItems &&
            value.options.optionItems.length != 0
          ) {
            // 存在前端可配置的下拉框内容 则匹配对应的数据
            let itemMap = {};
            for (let index in value.options.optionItems) {
              itemMap[value.options.optionItems[index].value.toString()] =
                value.options.optionItems[index].label;
            }
            this.formModel[this.widget.id].forEach((itemData, index) => {
              let echoRowData = this.echoFieldData[index] || {};
              if (itemData[value.id]) {
                let selectValue = '';
                if (itemData[value.id] instanceof Array) {
                  selectValue = itemData[value.id].join();
                } else {
                  selectValue = itemData[value.id];
                }
                let selectValueItemStr = [];
                selectValue
                  .toString()
                  .split(',')
                  .forEach((selectValueItem) => {
                    selectValueItemStr.push(
                      itemMap[selectValueItem.toString()] == null
                        ? selectValueItem
                        : itemMap[selectValueItem.toString()],
                    );
                  });
                echoRowData[value.id] = selectValueItemStr.join();
              }
              this.$set(this.echoFieldData, index, echoRowData);
            });
          } else if (value.options.api) {
            executeInterface({
              apiId: value.options.api,
            }).then((res) => {
              let optionItems = res.data;
              let itemMap = {};
              for (let index in optionItems) {
                itemMap[optionItems[index].value.toString()] =
                  optionItems[index].label;
              }
              this.formModel[this.widget.id].forEach((itemData, index) => {
                let echoRowData = this.echoFieldData[index] || {};
                if (itemData[value.id]) {
                  let selectValue = '';
                  if (itemData[value.id] instanceof Array) {
                    selectValue = itemData[value.id].join();
                  } else {
                    selectValue = itemData[value.id];
                  }
                  let selectValueItemStr = [];
                  selectValue
                    .toString()
                    .split(',')
                    .forEach((selectValueItem) => {
                      selectValueItemStr.push(
                        itemMap[selectValueItem.toString()] == null
                          ? selectValueItem
                          : itemMap[selectValueItem.toString()],
                      );
                    });
                  echoRowData[value.id] = selectValueItemStr.join();
                }
                this.$set(this.echoFieldData, index, echoRowData);
              });
            });
          } else {
            // 如果是普通输入值 直接保存到回显对象中
            this.formModel[this.widget.id].forEach((itemData, index) => {
              let echoRowData = this.echoFieldData[index] || {};
              if (itemData[value.id] != null) {
                if (typeof itemData[value.id] == 'boolean') {
                  echoRowData[value.id] = itemData[value.id] ? '是' : '否';
                }
                if (
                  itemData[value.id] === 'true' ||
                  itemData[value.id] === 'false'
                ) {
                  echoRowData[value.id] =
                    itemData[value.id] === 'true' ? '是' : '否';
                } else {
                  echoRowData[value.id] = itemData[value.id];
                }
              }
              this.$set(this.echoFieldData, index, echoRowData);
            });
          }
        });
        // 需要调用接口回显label
        if (executeEntity.length > 0) {
          executeInterfaceBatch(executeEntity).then((res) => {
            // 根据上方循环时保存的element_key 和 bandDataIdsApi 的对应关系对显示数据进行填充
            for (let key in elementToApiMap) {
              let value = elementToApiMap[key];
              let idToMap = res.data[value];
              // 循环表格数据中的当前子表单数据，等对应列数据匹配接口返回的idMap进行匹配
              this.formModel[this.widget.id].forEach((itemData, index) => {
                let echoRowData = this.echoFieldData[index] || {};
                if (itemData[key]) {
                  let selectValue = '';
                  if (itemData[key] instanceof Array) {
                    selectValue = itemData[key].join();
                  } else {
                    selectValue = itemData[key];
                  }
                  let selectValueItemStr = [];
                  selectValue
                    .toString()
                    .split(',')
                    .forEach((selectValueItem) => {
                      selectValueItemStr.push(
                        idToMap[selectValueItem.toString()],
                      );
                    });
                  echoRowData[key] = selectValueItemStr.join();
                }
                this.$set(this.echoFieldData, index, echoRowData);
              });
            }
            this.$forceUpdate();
          });
        }
        this.$forceUpdate();
      } catch (e) {
        console.log(e);
      }
    },
    // 点击查看流程
    flowDialog(flowModelData) {
      this.flowVisible = true;
      this.flowModel = flowModelData;
    },
    // 复选框禁用判断
    checkSelectable(row, index) {
      if (this.hasButton) {
        return !this.formModel[this.widget.id][index][this.buttonId];
      } else {
        return true;
      }
    },
    buttonExecute(item) {
      // 组织参数
      let param = {};
      JSON.parse(item.options.paramConfig).forEach((item) => {
        if (item.type === 'keyValue') {
          if (item.value[1] && item.value[1].includes('.')) {
            let containerForm = item.value[1].split('.')[0];
            let form = item.value[1].split('.')[1];

            // 当前点击的button的子表单中的索引
            param[item.key] =
              this.formModel[containerForm][this.subFormRowIndex][form];
          } else {
            param[item.key] = this.formModel[item.value[1]] || '';
          }
        } else {
          param[item.key] = [];
          // 取所有
          let relation = {};
          item.subset.forEach((item2) => {
            relation[item2.value[1].split('.')[1]] = item2.key.split('.')[1];
          });
          let dataAssemble = (data) => {
            let obj = {};
            for (let i in data) {
              if (relation[i]) {
                obj[relation[i]] = data[i];
              }
            }
            return obj;
          };

          this.formModel[item.value[1]].forEach((item2, index) => {
            if (this.checkedIndex.indexOf(index) > -1) {
              param[item.key].push(dataAssemble(item2));
            }
          });
        }
      });
      executeInterface({
        apiId: item.options.api,
        body: param,
      })
        .then((res) => {
          this.$message({ message: '请求成功', type: 'success' });
          this.checkedIndex.forEach((index) => {
            this.formModel[this.widget.id][index][item.id] = res.data;
          });
          this.$refs.subformTable.clearSelection();
        })
        .catch(() => {});
    },
    editSubFormRow(row, widgetIndex = '') {
      const index = this.rowIdData.findIndex((e) => e == row);
      if (this.editFlag[row]) {
        if (this.getVerify(index, row)) return;
      }

      this.$set(this.editFlag, row, !this.editFlag[row]);

      // 调用自定义的保存事件
      if (
        typeof widgetIndex === 'number' &&
        !this.editFlag[row] &&
        !this.widget.options.readonly &&
        (this.hasButton
          ? !this.formModel[this.widget.id][$index][this.buttonId]
          : true) &&
        !!this.widget.options.onSubSave
      ) {
        let customFunc = new Function('row', this.widget.options.onSubSave);
        customFunc.call(this, row);
      }
    },
    getVerify(I, row) {
      let fage = false;
      let rowData = this.formModel[this.widget.id][I];
      this.widget.widgetList.forEach((item, index) => {
        if (item.options.required) {
          if (
            rowData[item.id] == null ||
            rowData[item.id] === '' ||
            rowData[item.id].length < 1
          ) {
            // 不符合条件
            fage = true;
            this.$message(item.options.label + '不符合条件');
          }
        }
      });
      return fage;
    },
    getRefVerify() {
      let fage = false;
      let rowData = this.formModel[this.widget.id];
      for (let i = 0; i < this.widget.widgetList.length; i++) {
        let item = this.widget.widgetList[i];
        if (item.options.required) {
          for (let j = 0; j < rowData.length; j++) {
            let ele = rowData[j];
            if (ele[item.id].length < 1) {
              fage = true;
              this.$message(
                '第' + (j + 1) + '行' + item.options.label + '不符合条件',
              );
              return fage;
            }
          }
        }
      }
      return fage;
    },
    reset() {
      this.rowIdData = [];
      this.addToRowIdData();
    },
    batchDelete() {
      this.checkedIndex.sort((x, y) => x - y);
      this.$confirm(
        this.i18nt('render.hint.deleteSubFormRow') + '?',
        this.i18nt('render.hint.prompt'),
        {
          confirmButtonText: this.i18nt('render.hint.confirm'),
          cancelButtonText: this.i18nt('render.hint.cancel'),
        },
      )
        .then(() => {
          for (let i = this.checkedIndex.length - 1; i >= 0; i--) {
            let delIndex = this.checkedIndex[i];

            let oldSubFormData = this.formModel[this.widget.options.name] || [];
            let deletedDataRow = deepClone(oldSubFormData[delIndex]);
            oldSubFormData.splice(delIndex, 1);
            this.deleteFromRowIdData(delIndex);
            this.deleteFromFieldSchemaData(delIndex);

            this.handelSubFormRowDelete(oldSubFormData, deletedDataRow);
            this.handleSubFormRowChange(oldSubFormData);
          }
          this.checkedIndex = [];
          this.isIndeterminate =
            this.checkedIndex.length > 0 &&
            this.checkedIndex.length < this.rowIdData.length;
          this.$refs.subformTable.clearSelection();
        })
        .catch(() => {
          //
        });
    },
    // 全选方法
    allSelect() {
      if (this.checkedIndex.length > 0) {
        this.checkedIndex = [];
      } else {
        this.checkedIndex = [];
        this.rowIdData.forEach((v, i) => {
          this.checkedIndex.push(i);
        });
        this.isIndeterminate = false;
      }
    },
    // 当选方法
    handleSelectionChange(index) {
      if (this.checkedIndex.includes(index)) {
        this.checkedIndex = this.checkedIndex.filter((item) => item !== index);
      } else {
        this.checkedIndex.push(index);
      }
      this.isIndeterminate =
        this.checkedIndex.length > 0 &&
        this.checkedIndex.length < this.rowIdData.length;
    },
    getLabelAlign(widget, subWidget) {
      return subWidget.options.labelAlign || widget.options.labelAlign;
    },

    registerSubFormToRefList() {
      if (this.widget.type === 'sub-form') {
        this.sfRefList[this.widget.options.name] = this;
      }
    },

    initRowIdData(initFlag) {
      if (this.widget.type === 'sub-form') {
        this.rowIdData.splice(0, this.rowIdData.length); //清除数组必须用splice，length=0不会响应式更新！！
        let subFormModel = this.formModel[this.widget.options.name];
        if (!!subFormModel && subFormModel.length > 0) {
          subFormModel.forEach(() => {
            this.rowIdData.push('r' + generateId());
          });

          if (!!initFlag) {
            //注意：事件触发需延期执行，SumFormDataChange事件处理代码中可能存在尚未创建完成的组件！！
            setTimeout(() => {
              this.handleSubFormRowChange(subFormModel);
            }, 800);
          }
        }
      }
    },

    addToRowIdData() {
      let newRowId = 'rowId' + generateId();
      this.rowIdData.push(newRowId);
      this.editSubFormRow(newRowId);
    },

    insertToRowIdData(rowIndex) {
      this.rowIdData.splice(rowIndex, 0, 'rowId' + generateId());
    },

    deleteFromRowIdData(rowIndex) {
      this.rowIdData.splice(rowIndex, 1);
    },
    clearFromRowIdData() {
      this.rowIdData = [];
      this.globalModel.formModel[this.widget.options.name] = [];
      this.initFieldSchemaData();
    },
    initFieldSchemaData() {
      //初始化fieldSchemaData！！！
      if (this.widget.type !== 'sub-form') {
        return;
      }

      let rowLength = this.rowIdData.length;
      this.fieldSchemaData.splice(0, this.fieldSchemaData.length); //清除数组必须用splice，length=0不会响应式更新！！
      if (rowLength > 0) {
        for (let i = 0; i < rowLength; i++) {
          let fieldSchemas = [];
          this.widget.widgetList.forEach((swItem) => {
            fieldSchemas.push(this.cloneFieldSchema(swItem));
          });
          this.fieldSchemaData.push(fieldSchemas);
        }
      }
    },

    addToFieldSchemaData(rowIndex) {
      let fieldSchemas = [];
      this.widget.widgetList.forEach((swItem) => {
        fieldSchemas.push(this.cloneFieldSchema(swItem));
      });

      if (rowIndex === undefined) {
        this.fieldSchemaData.push(fieldSchemas);
      } else {
        this.fieldSchemaData.splice(rowIndex, 0, fieldSchemas);
      }
    },

    deleteFromFieldSchemaData(rowIndex) {
      this.fieldSchemaData.splice(rowIndex, 1);
    },

    cloneFieldSchema(fieldWidget) {
      let newFieldSchema = deepClone(fieldWidget);
      newFieldSchema.id = fieldWidget.type + generateId();
      return newFieldSchema;
    },

    initEventHandler() {
      if (this.widget.type !== 'sub-form') {
        return;
      }

      this.$on('setFormData', function (newFormData) {
        this.initRowIdData(false);
        this.initFieldSchemaData();

        let subFormData = newFormData[this.widget.options.name] || [];
        setTimeout(() => {
          //延时触发SubFormRowChange事件, 便于更新计算字段！！
          this.handleSubFormRowChange(subFormData);
        }, 800);
      });
    },

    handleSubFormFirstRowAdd() {
      if (this.widget.type !== 'sub-form') {
        return;
      }

      if (!!this.widget.options.showBlankRow && this.rowIdData.length === 1) {
        let oldSubFormData = this.formModel[this.widget.options.name] || [];
        this.handleSubFormRowAdd(oldSubFormData, this.rowIdData[0]);
        this.handleSubFormRowChange(oldSubFormData);
      }
    },
    setSubFormRow(index, data) {
      let oldSubFormData = this.formModel[this.widget.options.name] || [];
      // 判断列表长度
      if (index <= oldSubFormData.length - 1) {
        Object.assign(oldSubFormData[index], data);
        // 删除原有数据
        this.widget.widgetList.forEach((subWidget) => {
          let swRefName =
            subWidget.options.name + '@row' + this.rowIdData[index];
          let foundSW = this.getWidgetRef(swRefName);
          if (!!foundSW) {
            foundSW.initFieldModel();
          }
        });
      }
      return new Promise((resolve, reject) => {
        resolve(this.rowIdData[index]);
      });
    },
    addSubFormFirstRow(subData) {
      this.addSubFormRow(subData, 0);
      return new Promise((resolve, reject) => {
        resolve(this.rowIdData[0]);
      });
    },
    addSubFormLastRow(subData) {
      this.addSubFormRow(
        subData,
        this.formModel[this.widget.options.name].length,
      );
      return new Promise((resolve, reject) => {
        resolve(
          this.rowIdData[this.formModel[this.widget.options.name].length - 1],
        );
      });
    },
    delSubFormRow(formRowIndex) {
      let oldSubFormData = this.formModel[this.widget.options.name] || [];
      let deletedDataRow = deepClone(oldSubFormData[formRowIndex]);
      oldSubFormData.splice(formRowIndex, 1);
      this.deleteFromRowIdData(formRowIndex);
      this.deleteFromFieldSchemaData(formRowIndex);

      this.handelSubFormRowDelete(oldSubFormData, deletedDataRow);
      this.handleSubFormRowChange(oldSubFormData);
    },

    addSubFormRow(subData, index) {
      let newSubFormDataRow = subData || {};
      if (!newSubFormDataRow) {
        this.widget.widgetList.forEach((subFormItem) => {
          if (!!subFormItem.formItemFlag) {
            newSubFormDataRow[subFormItem.options.name] =
              subFormItem.options.defaultValue;
          }
        });
      }

      let oldSubFormData = this.formModel[this.widget.options.name] || [];
      // 新增下标位置
      index = index == null ? oldSubFormData.length : index;
      oldSubFormData.splice(
        index == null ? oldSubFormData.length : index,
        0,
        newSubFormDataRow,
      );
      // 新增rowIds
      this.insertToRowIdData(index);
      // 新增子组件列表
      this.addToFieldSchemaData(index);

      // 触发新增行事件
      this.handleSubFormRowAdd(oldSubFormData, this.rowIdData[index]);
      // 触发修改时间
      this.handleSubFormRowChange(oldSubFormData);
      return new Promise((resolve, reject) => {
        resolve(this.rowIdData[index]);
      });
    },
    addSubDataFormRow(newSubFormDataRow) {
      let oldSubFormData = this.formModel[this.widget.options.name] || [];
      oldSubFormData.push(newSubFormDataRow);
      this.rowIdData.push('rowId' + generateId());
      this.addToFieldSchemaData();

      this.handleSubFormRowAdd(
        oldSubFormData,
        this.rowIdData[oldSubFormData.length - 1],
      );
      this.handleSubFormRowChange(oldSubFormData);
    },
    insertSubFormRow(beforeFormRowIndex) {
      let newSubFormDataRow = {};
      this.widget.widgetList.forEach((subFormItem) => {
        if (!!subFormItem.formItemFlag) {
          newSubFormDataRow[subFormItem.options.name] =
            subFormItem.options.defaultValue;
        }
      });

      let oldSubFormData = this.formModel[this.widget.options.name] || [];
      oldSubFormData.splice(beforeFormRowIndex, 0, newSubFormDataRow);
      this.insertToRowIdData(beforeFormRowIndex);
      this.addToFieldSchemaData(beforeFormRowIndex);

      this.handleSubFormRowInsert(
        oldSubFormData,
        this.rowIdData[beforeFormRowIndex],
      );
      this.handleSubFormRowChange(oldSubFormData);
    },
    updateSubDataFormRow(newSubFormDataRow, rowId) {
      let oldSubFormData = this.formModel[this.widget.options.name] || [];
      if (oldSubFormData.length >= rowId) {
        oldSubFormData.splice(rowId, 1, newSubFormDataRow);
      }
      this.$set(this.formModel, this.widget.options.name, oldSubFormData);
      let fieldSchemas = [];
      this.widget.widgetList.forEach((swItem) => {
        fieldSchemas.push(this.cloneFieldSchema(swItem));
      });
      this.fieldSchemaData.splice(rowId, 1, fieldSchemas);
      this.rowIdData.splice(rowId, 1, 'rowId' + generateId());
      this.$forceUpdate();
      this.handleSubFormRowChange(newSubFormDataRow);
    },
    deleteSubFormRow(formRowIndex) {
      this.$confirm(
        this.i18nt('render.hint.deleteSubFormRow') + '?',
        this.i18nt('render.hint.prompt'),
        {
          confirmButtonText: this.i18nt('render.hint.confirm'),
          cancelButtonText: this.i18nt('render.hint.cancel'),
        },
      )
        .then(() => {
          let oldSubFormData = this.formModel[this.widget.options.name] || [];
          let deletedDataRow = deepClone(oldSubFormData[formRowIndex]);
          oldSubFormData.splice(formRowIndex, 1);
          this.deleteFromRowIdData(formRowIndex);
          this.deleteFromFieldSchemaData(formRowIndex);

          this.handelSubFormRowDelete(oldSubFormData, deletedDataRow);
          this.handleSubFormRowChange(oldSubFormData);
        })
        .catch(() => {
          //
        });
    },

    handleSubFormRowChange(subFormData) {
      if (!!this.widget.options.onSubFormRowChange) {
        let customFunc = new Function(
          'subFormData',
          this.widget.options.onSubFormRowChange,
        );
        customFunc.call(this, subFormData);
      }
    },

    handleSubFormRowAdd(subFormData, newRowId) {
      if (!!this.widget.options.onSubFormRowAdd) {
        let customFunc = new Function(
          'subFormData',
          'newRowId',
          this.widget.options.onSubFormRowAdd,
        );
        customFunc.call(this, subFormData, newRowId);
      }
    },

    handleSubFormRowInsert(subFormData, newRowId) {
      if (!!this.widget.options.onSubFormRowInsert) {
        let customFunc = new Function(
          'subFormData',
          'newRowId',
          this.widget.options.onSubFormRowInsert,
        );
        customFunc.call(this, subFormData, newRowId);
      }
    },

    handelSubFormRowDelete(subFormData, deletedDataRow) {
      if (!!this.widget.options.onSubFormRowDelete) {
        let customFunc = new Function(
          'subFormData',
          'deletedDataRow',
          this.widget.options.onSubFormRowDelete,
        );
        customFunc.call(this, subFormData, deletedDataRow);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.ant-table-fixed th:first-child,
.ant-table-fixed td:first-child {
  border-left: 1px solid rgb(230, 234, 240) !important;
}

.sub-form-table {
  font-size: 12px !important;
  border-radius: 0.28571429rem !important;
  border-collapse: separate !important;
  color: rgba(0, 0, 0, 0.95);
  overflow: visible; // 处理当表格数据只有一条再显示加载中时 加载中被表格截取
  td {
    padding-top: 4px !important;
    padding-bottom: 4px !important;
    .cell {
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      -webkit-box-orient: vertical;
      padding: 0 5px 0 10px;
    }
  }
}

.is-checked {
  color: var(--primary-color);
}
::v-deep .el-transfer-panel__list {
  height: auto !important;
}
.sub-form-container {
  text-align: left; //IE浏览器强制居左对齐
  overflow: auto;
  white-space: nowrap;

  ::v-deep .el-row.header-row {
    padding-bottom: 0;
  }

  ::v-deep .el-row.sub-form-row {
    padding-top: 3px;
    padding-bottom: 3px;

    .row-number-span {
      margin-left: 16px;
    }
  }
}

div.action-header-column {
  display: inline-block;
  width: 120px;
  margin-left: 2%;

  .action-label {
    margin-right: 12px;
  }

  .action-button {
    padding-left: 8px;
    padding-right: 8px;
  }
}

div.field-header-column {
  display: inline-block;
  //overflow: hidden;
  //white-space: nowrap;  //文字超出长度不自动换行
  //text-overflow: ellipsis;  //文字超出长度显示省略号

  span.custom-label i {
    margin: 0 3px;
  }
}

div.field-header-column.is-required:before {
  content: '*';
  color: red;
  margin-right: 4px;
}

div.label-center-left {
  text-align: left;
}

div.label-center-align {
  text-align: center;
}

div.label-right-align {
  text-align: right;
}

div.sub-form-action-column {
  display: inline-block;
  width: 120px;
  margin-left: 2%;

  ::v-deep .el-form-item {
    margin-bottom: 0;
  }

  ::v-deep .el-button {
    font-size: 14px;
    padding: 0;
  }
}

div.sub-form-action-column.hide-label {
  ::v-deep .el-form-item__label {
    display: none;
  }
}

div.sub-form-table-column {
  display: inline-block;
  //width: 200px;

  ::v-deep .el-form-item {
    //margin-left: 4px;
    //margin-right: 4px;
    margin-bottom: 15px;
  }

  ::v-deep .el-form-item__content {
    margin-left: 0 !important;
    text-align: center;
  }
}

div.sub-form-table-column.hide-label {
  ::v-deep .el-form-item__label {
    display: none;
  }
}
</style>
