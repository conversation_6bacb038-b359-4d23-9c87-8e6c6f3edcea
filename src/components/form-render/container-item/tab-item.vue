<template>
  <container-item-wrapper :widget="widget">
    <div :key="widget.id" class="tab-container" v-show="!widget.options.hidden||status=='authority'">
      <el-tabs
        v-model="activeTabName"
        :type="widget.options.tabType || ''"
        :tab-position="widget.options.tabPosition"
        :stretch="widget.options.stretch"
        :ref="widget.id"
        :class="[customClass]"
      > 
        <template  v-for="(tab, index) in visibleTabs">
           <el-tab-pane
          v-if="!hiddenArr.includes(tab.id)"
          :key="index"
          :disabled="tab.options.disabled"
          :name="tab.options.label"
        >
          <span slot="label">
            <svg-icon :icon-class="tab.options.icon" v-if="tab.options.icon" />
            {{ tab.options.label }}</span
          >
          <template v-for="(subWidget, swIdx) in tab.widgetList">
            <template v-if="'container' === subWidget.category">
              <component
                :is="subWidget.type + '-item'"
                :widget="subWidget"
                :key="swIdx"
                :parent-list="tab.widgetList"
                :index-of-parent-list="swIdx"
                :parent-widget="widget"
              ></component>
            </template>
            <template v-else>
              <component
                :is="subWidget.type + '-widget'"
                :field="subWidget"
                :key="swIdx"
                :parent-list="tab.widgetList"
                :index-of-parent-list="swIdx"
                :parent-widget="widget"
              ></component>
            </template>
          </template>
        </el-tab-pane>
        </template>
       
      </el-tabs>

      <div v-if="status === 'authority'" class="permissionMasking">
        <el-button-group class="maskingButton">
          <el-button :type="hasMaskingHidden" @click="setMaskingHidden" icon="el-icon-view">隐藏</el-button>
        </el-button-group>
      </div>
    
    </div>
  </container-item-wrapper>
</template>

<script>
import emitter from 'element-ui/lib/mixins/emitter';
import i18n from '../../../utils/i18n';
import refMixin from '../../../components/form-render/refMixin';
import ContainerItemWrapper from './container-item-wrapper';
import containerItemMixin from './containerItemMixin';
import FieldComponents from '@/components/form-designer/form-widget/field-widget/index';
import bus from '@/magic-editor/scripts/bus';
import Template from '../../../views/tool/messageComponents/template.vue';

export default {
  name: 'tab-item',
  componentName: 'ContainerItem',
  mixins: [emitter, i18n, refMixin, containerItemMixin],
  components: {
    Template,
    ContainerItemWrapper,
    ...FieldComponents,
  },
  props: {
    widget: Object,
  },
  inject: ['refList', 'sfRefList', 'globalModel','status'],
  data() {
    return {
      activeTabName:'',
      customTabs:null,
      hiddenArr:[]
    };
  },
  computed: {
    visibleTabs() {
      return this.customTabs || this.widget.tabs.filter((tp) => {
        return !tp.options.hidden;
      });
    },
    hasMaskingHidden() {
      return this.widget.options.hidden? 'primary':'default'
    }
  },
  created() {
    this.initRefList();
  },
  mounted() {
    this.activeTabName = this.visibleTabs[0].options.label;
  },
  watch: {
    activeTabName(newValue,oldValue) {
      if (this.widget.options.tabChange&&oldValue!==''){
        this.$nextTick(() => {
          let JS = new Function(
            'oldValue',
            'newValue',
            this.widget.options.tabChange,
          );
          JS.call(this, oldValue, newValue);
        });
      }
    },
  },
  beforeDestroy() {
    this.unregisterFromRefList();
  },
  methods: {
    setMaskingHidden() {
      this.widget.options.hidden = !this.widget.options.hidden
    },
    setTabPaneHidden(id,flag){
      if(flag){
        if(!this.hiddenArr.includes(id)){
          this.hiddenArr.push(id)
          this.activeTabName = this.visibleTabs[this.visibleTabs.findIndex(item=>!this.hiddenArr.includes(item.id))].options.label;
        }
      }else{
        if(this.hiddenArr.includes(id)){
          this.hiddenArr.splice(this.hiddenArr.indexOf(id),1)
        }
      }
    },
    getValue() {
      return this.activeTabName;
    },
    setTabs(arr){
      this.customTabs=arr.map(item=>({options:{label:item},widgetList:[]}))
      this.activeTabName = this.visibleTabs[0].options.label;
    },
    setValue(val) {
      this.activeTabName = val;
    },
    getCurrentTab() {
      return this.visibleTabs.find(
        (item) => item.options.label == this.activeTabName,
      );
    },
  },
};
</script>

<style lang="scss" scoped>
.tab-container{
  position: relative;
}
.permissionMasking {
  display: flex;
  position: absolute;
  top: 5px;
  right: 5px;
  z-index: 98;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  background: rgba(0,0,0,.1);
  transition: all .3s;
}
</style>
