<template>
  <el-col
    class="grid-cell"
    :class="[customClass]"
    v-bind="layoutProps"
    :key="widget.id"
    v-show="!widget.options.hidden"
    v-if="widget.options.authority ? checkGridColPermission(widget) : true"
    :style="{
      margin: marginComp,
      padding: paddingComp,
      border: `${
        (widget.options.border2 ? widget.options.border2.width || 0 : 0) + 'px'
      } ${widget.options.border2 ? widget.options.border2.type : 'none'} ${
        widget.options.border2 ? widget.options.border2.color || '#000' : ''
      }`,
    }"
  >
    <template v-if="!!widget.widgetList && widget.widgetList.length > 0">
      <template v-for="(subWidget, swIdx) in widget.widgetList">
        <template v-if="'container' === subWidget.category">
          <component
            :is="subWidget.type + '-item'"
            :widget="getSubWidget(subWidget,1)"
            :key="swIdx"
            :parent-list="widget.widgetList"
            :index-of-parent-list="swIdx"
            :parent-widget="widget"
            :class="widget.id"
          ></component>
        </template>
        <template v-else-if="tabIndex === null">
          <component
            :is="subWidget.type + '-widget'"
            :field="getSubWidget(subWidget,2)"
            :designer="null"
            :key="swIdx"
            :parent-list="widget.widgetList"
            :index-of-parent-list="swIdx"
            :parent-widget="widget"
            :class="widget.id"
          ></component>
        </template>
        <template v-else>
          <component
            :is="subWidget.type + '-widget'"
            :field="getSubWidget(subWidget,3)"
            :designer="null"
            :key="swIdx"
            :parent-list="widget.widgetList"
            :index-of-parent-list="swIdx"
            :parent-widget="tabWidget"
            :sub-form-row-id="tabIndex + ''"
            :sub-form-row-index="tabIndex"
            :class="widget.id"
          ></component>
        </template>
      </template>
    </template>
  </el-col>
</template>

<script>
import i18n from '../../../utils/i18n'
import refMixin from '../../../components/form-render/refMixin'
import FieldComponents from '@/components/form-designer/form-widget/field-widget/index'
import containerItemMixin from '@/components/form-render/container-item/containerItemMixin'
import { checkBusinessPermission } from '@/utils/permission'

export default {
  name: 'GridColItem',
  componentName: 'ContainerItem',
  mixins: [i18n, refMixin, containerItemMixin],
  components: {
    ...FieldComponents,
  },
  props: {
    widget: Object,
    parentWidget: Object,
    parentList: Array,
    indexOfParentList: Number,
    tabIndex: null,
    tablFildId: null,
    tabWidget: null,
  },
  inject: ['refList', 'globalModel', 'formConfig', 'previewState'],
  data() {
    return {
      layoutProps: {
        span: this.widget.options.span,
        lg: this.widget.options.responsive?(this.widget.options.md || 24):undefined,
        sm: this.widget.options.responsive?(this.widget.options.sm || 12):undefined,
        xs: this.widget.options.responsive?(this.widget.options.xs || 12):undefined,
        offset: this.widget.options.offset || 0,
        push: this.widget.options.push || 0,
        pull: this.widget.options.pull || 0,
      },
    };
  },
  computed: {
    customClass() {
      return this.widget.options.customClass || '';
    },
    marginComp() {
      const { margin } = this.widget.options;
      if (margin) {
        return `${margin.mt || 0}px ${margin.mr || 0}px ${margin.mb || 0}px ${
          margin.ml || 0
        }px `;
      }
      return 0;
    },
    paddingComp() {
      const { padding } = this.widget.options;
      if (padding && this.widget.widgetList.some(item=>!item.options.hidden) ) {
        return `${padding.pt || 0}px ${padding.pr || 0}px ${
          padding.pb || 0
        }px ${padding.pl || 0}px `;
      }
      return 0;
    },
  },
  created() {
    this.initLayoutProps();
    this.initRefList();
  },
  methods: {
    checkGridColPermission(subWidget) {
      let isDesign = this.designer != null ? this.designer.isDesign : false;
      if (isDesign === true) {
        return true;
      }
      let formPermission = this.widget.formPermission;
      subWidget.formPermission = formPermission;
      let authCode = subWidget.options.name
      let authority = subWidget.options.authority
      return checkBusinessPermission(authority,authCode,formPermission)
    },
    getSubWidget(subWidget,type){
      subWidget.formPermission = this.widget.formPermission;
      return subWidget;
    },
    initLayoutProps() {
      if (!!this.widget.options.responsive) {
        if (!!this.previewState) {
          this.layoutProps.md = undefined;
          this.layoutProps.sm = undefined;
          this.layoutProps.xs = undefined;

          let lyType = this.formConfig.layoutType;
          if (lyType === 'H5') {
            this.layoutProps.span = this.widget.options.xs || 12;
          } else if (lyType === 'Pad') {
            this.layoutProps.span = this.widget.options.sm || 12;
          } else {
            this.layoutProps.span = this.widget.options.md || 12;
          }
        } else {
          this.layoutProps.span = undefined;
        }
      } else {
        this.layoutProps.md = undefined;
        this.layoutProps.sm = undefined;
        this.layoutProps.xs = undefined;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
</style>
