<template>
  <container-item-wrapper :widget="widget">
    <div :class="[customClass]" class="layout-container">
      <div class="bg-white radius-6">
        <div class="layout-header radius-6" :style="{
              border: headerWidget.options.border
                ? '1px solid rgb(228, 231, 237)'
                : 'none',
              padding: headerWidget.options.border ? '10px' : 0,
            }"  >
          <template v-if="!!headerWidget && headerWidget.widgetList.length > 0">
            <template v-for="(subWidget, swIdx) in headerWidget.widgetList">
              <template v-if="'container' === subWidget.category">
                <component
                  :is="subWidget.type + '-item'"
                  :widget="subWidget"
                  :key="swIdx"
                  :parent-list="widget.widgetList"
                  :index-of-parent-list="swIdx"
                  :parent-widget="widget"
                />
              </template>
              <template v-else>
                <component
                  :is="subWidget.type + '-widget'"
                  :field="subWidget"
                  :designer="null"
                  :key="swIdx"
                  :parent-list="widget.widgetList"
                  :index-of-parent-list="swIdx"
                  :parent-widget="widget"
                />
              </template>
            </template>
          </template>
        </div>
        <div class="d-flex">
          <div
            v-if="widget.options.layoutModel === 2"
            class="layout-left radius-6"
            :style="leftStyle"
          >
            <div class="layout-left-box">
              <template v-if="!!leftWidget && leftWidget.widgetList.length > 0">
                <template v-for="(subWidget, swIdx) in leftWidget.widgetList">
                  <template v-if="'container' === subWidget.category">
                    <component
                      :is="subWidget.type + '-item'"
                      :widget="subWidget"
                      :key="swIdx"
                      :parent-list="widget.widgetList"
                      :index-of-parent-list="swIdx"
                      :parent-widget="widget"
                    />
                  </template>
                  <template v-else>
                    <component
                      :is="subWidget.type + '-widget'"
                      :field="subWidget"
                      :designer="null"
                      :key="swIdx"
                      :parent-list="widget.widgetList"
                      :index-of-parent-list="swIdx"
                      :parent-widget="widget"
                    />
                  </template>
                </template>
              </template>
            </div>
            <div
              v-if="leftWidget.options.shrink"
              :style="{ right: isOpen ? '-16px' : '-16px' }"
              class="bd-rd d-flex a-center j-center pointer"
              @click="isOpen = !isOpen"
            >
              <i
                class="el-icon-arrow-left"
                :class="{ clear: !isOpen }"
                style="color: #ffffff"
              />
            </div>
          </div>
          <div
            class="layout-body radius-6 flex-1"
            :style="{
              border: bodyWidget.options.border
                ? '1px solid rgb(228, 231, 237)'
                : 'none',
              padding: bodyWidget.options.border ? '10px' : 0,
            }"
          >
            <template v-if="!!bodyWidget && bodyWidget.widgetList.length > 0">
              <template v-for="(subWidget, swIdx) in bodyWidget.widgetList">
                <template v-if="'container' === subWidget.category">
                  <component
                    :is="subWidget.type + '-item'"
                    :widget="subWidget"
                    :key="swIdx"
                    :parent-list="widget.widgetList"
                    :index-of-parent-list="swIdx"
                    :parent-widget="widget"
                  />
                </template>
                <template v-else>
                  <component
                    :is="subWidget.type + '-widget'"
                    :field="subWidget"
                    :designer="null"
                    :key="swIdx"
                    :parent-list="widget.widgetList"
                    :index-of-parent-list="swIdx"
                    :parent-widget="widget"
                  />
                </template>
              </template>
            </template>
          </div>
        </div>
      </div>
    </div>
  </container-item-wrapper>
</template>

<script>
import emitter from 'element-ui/lib/mixins/emitter';
import i18n from '@/utils/i18n';
import refMixin from '@/components/form-render/refMixin';
import ContainerItemWrapper from '@/components/form-render/container-item/container-item-wrapper';
import containerItemMixin from '@/components/form-render/container-item/containerItemMixin';
import FieldComponents from '@/components/form-designer/form-widget/field-widget/index';

export default {
  name: 'layout-item',
  componentName: 'ContainerItem',
  mixins: [emitter, i18n, refMixin, containerItemMixin],
  components: {
    ContainerItemWrapper,
    ...FieldComponents,
  },
  props: {
    widget: Object,
  },
  inject: ['refList', 'sfRefList', 'globalModel'],
  data() {
    return {
      isOpen: true,
    };
  },
  computed: {
    customClass() {
      return this.widget.options.customClass || '';
    },
    headerWidget() {
      return this.widget.widgetList[0];
    },
    leftWidget() {
      return this.widget.widgetList[1];
    },
    bodyWidget() {
      return this.widget.widgetList[2];
    },
    leftStyle() {
      let width = this.leftWidget.options.width
        ? this.leftWidget.options.width + 'px'
        : '200px';
      width = this.isOpen ? width : '0px';
      let marginRight = this.leftWidget.options.shrink ? '25px' : '10px';

      // let border =
      //   !this.isOpen && this.leftWidget.options.shrink
      //     ? 'none'
      //     : '1px solid rgb(228, 231, 237)';
      let border = this.leftWidget.options.border && this.isOpen ? '1px solid rgb(228, 231, 237)' : 'none'
      // if(this.leftWidget.options.border && this.isOpen) {
      //   border = '1px solid rgb(228, 231, 237)'
      // }
      let padding = this.leftWidget.options.border && this.isOpen ?  '10px' : 0
      //  :style="{
      //         border: headerWidget.options.border
      //           ? '1px solid rgb(228, 231, 237)'
      //           : 'none',
      //         padding: headerWidget.options.border ? '10px' : 0,
      //       }"
      return {
        width,
        marginRight,
        border,
        padding
      };
    },
  },
  watch: {},
  mounted() {},
  methods: {},
};
</script>

<style lang="scss" scoped>
.layout-container {
  background-color: #f6f6f6;
  .layout-header {
    min-height: 56px;
    margin-bottom: 10px;

    box-sizing: border-box;
  }
  .layout-left {
    min-height: 500px;
    width: 150px;
    position: relative;
    transition: 0.5s linear;
    .bd-rd {
      border-radius: 0 30px 30px 0;
      width: 15px;
      height: 30px;
      background-color: #e6ebf5;
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      right: -15px;
      transition: 0.5s linear;
      .clear {
        transition: 0.5s linear;
        transform: rotate(180deg);
      }
    }
    .layout-left-box {
      // padding: 10px;
      overflow: hidden;
    }
  }
  .layout-body {
    min-height: 500px;
    padding: 10px;
    overflow: hidden;
  }
}
</style>
