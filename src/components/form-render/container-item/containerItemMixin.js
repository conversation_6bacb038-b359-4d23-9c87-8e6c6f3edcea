import { generateId } from '@/utils/util';
import request from '@/utils/request';
import { getToken, getEnv,getDevelop } from '@/utils/auth';
import store from '@/store';
import { executeDevById} from '@/api/interfaces/interfaces.js'
export default {
  computed: {
    customClass() {
      return this.widget.options.customClass || '';
    },

    formModel: {
      cache: false,
      get() {
        return this.globalModel.formModel;
      },
    },
  },
  inject: ['formConfig', 'busUid', 'status', 'hasRead','flowNode'],
  methods: {
    hasNode(nodeId) {
      return this.flowNode && this.flowNode === nodeId
    },
    http(interfacesId, data, awaitFlag = false) {
      if (awaitFlag) {
        let settings = {
          url: `${process.env.VUE_APP_BASE_API}/interfaces/ldfBusinessInfo/executeDevById`,
          type: 'post',
          timeout: 0,
          data: JSON.stringify({ apiId: interfacesId, body: data }),
          dataType: 'JSON',
          contentType: 'application/json;charset=UTF-8',
          headers: {
            Accept: 'application/json, text/plain, */*',
            Connection: 'keep-alive',
          },
          async: !awaitFlag
        };

        // 是否需要设置 token
        const isToken = (settings.headers || {}).isToken === false;
        // 请求携带来自终端类型
        settings.headers['terminalType'] = 'PC';
        // 加入当前环境
        if (getEnv()) {
          settings.headers['env'] = getEnv();
        }
        // 加入当前客户端id
        if (localStorage.getItem('clientId')) {
          settings.headers['Magic-Request-Client-Id'] =
            localStorage.getItem('clientId');
        }
        if (getDevelop())
          settings.headers['develop'] = getDevelop();

        if (getToken() && !isToken) {
          settings.headers['Authorization'] = 'Bearer ' + getToken(); // 让每个请求携带自定义token 请根据实际情况自行修改
        }
        return $.ajax(settings).responseJSON;
      } else {
        return executeDevById({ apiId: interfacesId, body: data });
      }
    },
    waitHttp(apiId, data) {
      let settings = {
        url: `${process.env.VUE_APP_BASE_API}/interfaces/magicInterfaces/execute`,
        type: 'post',
        timeout: 0,
        data: JSON.stringify({ apiId: apiId, body: data }),
        dataType: 'JSON',
        contentType: 'application/json;charset=UTF-8',
        headers: {
          Accept: 'application/json, text/plain, */*',
          Connection: 'keep-alive',
        },
        async: false,
      };
      // 是否需要设置 token
      const isToken = (settings.headers || {}).isToken === false;
      // 请求携带来自终端类型
      settings.headers['terminalType'] = 'PC';

      if (getToken() && !isToken) {
        settings.headers['Authorization'] = 'Bearer ' + getToken(); // 让每个请求携带自定义token 请根据实际情况自行修改
      }
      return $.ajax(settings).responseJSON;
    },
    asyncHttp(apiId, data) {
      let settings = {
        url: `${process.env.VUE_APP_BASE_API}/interfaces/magicInterfaces/execute`,
        type: 'post',
        timeout: 0,
        data: JSON.stringify({ apiId: apiId, body: data }),
        dataType: 'JSON',
        contentType: 'application/json;charset=UTF-8',
        headers: {
          Accept: 'application/json, text/plain, */*',
          Connection: 'keep-alive',
        },
      };
      // 是否需要设置 token
      const isToken = (settings.headers || {}).isToken === false;
      // 请求携带来自终端类型
      settings.headers['terminalType'] = 'PC';
      if (getToken() && !isToken) {
        settings.headers['Authorization'] = 'Bearer ' + getToken(); // 让每个请求携带自定义token 请根据实际情况自行修改
      }
      return $.ajax(settings);
    },
    getRequest(pp) {
      return request(pp);
    },
    unregisterFromRefList() {
      //销毁容器组件时注销组件ref
      if (this.refList !== null && !!this.widget.options.name) {
        let oldRefName = this.widget.options.name;
        delete this.refList[oldRefName];
      }
    },

    //--------------------- 以下为组件支持外部调用的API方法 begin ------------------//
    /* 提示：用户可自行扩充这些方法！！！ */
    clear() {
      let arr = this.widget.widgetList;
      let that = this;
      let cr = (arr) => {
        arr.forEach((item) => {
          try {
            let widget = that.getWidgetRef(item.id);
            if (
              widget != null &&
              widget.field &&
              widget.field.category != 'category'
            ) {
              that.getWidgetRef(item.id).resetField();
            }

            if (item.cols && item.cols.length > 0) {
              cr(item.cols);
            }
            if (item.tabs && item.tabs.length > 0) {
              cr(item.tabs);
            }
            if (item.items && item.items.length > 0) {
              cr(item.items);
            }
            if (item.widgetList && item.widgetList.length > 0) {
              cr(item.widgetList);
            }
            if (item.type == 'table' && item.rows && item.rows.length > 0) {
              item.rows.forEach((e) => {
                cr(e.cols);
              });
            }
          } catch (e) {
            // 里面可能有栅格等字段没有清除接口
          }
        });
      };
      //迭代
      cr(arr);
    },
    validate() {
      return new Promise((resolve, reject) => {
        let arr = this.widget.widgetList;
        let that = this;
        let formData = {};
        let cr = (arr) => {
          arr.forEach((item) => {
            try {
              let widget = that.getWidgetRef(item.id);
              if (
                widget != null &&
                widget.field &&
                widget.field.category != 'category'
              ) {
                let errorMessage = widget.validate();

                let validateFlag = errorMessage == '' || !errorMessage;

                // if (widget.field.type == 'tree') {
                //   console.error(
                //     `树组件${widget.field.id}无法自动校验,需要自行手动校验`,
                //   );
                // }

                if (!validateFlag) {
                  reject({ field: widget, errorMessage: errorMessage });
                } else {
                  // 如果有别名 设置别名
                  if (widget.field.options.alias) {
                    formData[widget.field.options.alias] = widget.getValue();
                  } else {
                    formData[widget.field.options.name] = widget.getValue();
                  }
                }
              }
              if (item.cols && item.cols.length > 0) {
                cr(item.cols);
              }
              if (item.tabs && item.tabs.length > 0) {
                cr(item.tabs);
              }
              if (item.items && item.items.length > 0) {
                cr(item.items);
              }
              if (item.widgetList && item.widgetList.length > 0) {
                cr(item.widgetList);
              }
              if (item.type == 'table' && item.rows && item.rows.length > 0) {
                item.rows.forEach((e) => {
                  cr(e.cols);
                });
              }
            } catch (e) {
              // 里面可能有栅格等字段没有清除接口
              reject({ field: null, errorMessage: '校验错误' });
            }
          });
        };
        //迭代
        cr(arr);
        resolve(formData);
      });
    },

    clearValidate() {
      this.getFormRef().$refs.renderForm.clearValidate();
    },
    setCategoryValue(data, flag = true) {
      let arr = this.widget.widgetList;
      let that = this;
      let cr = (arr) => {
        arr.forEach((item) => {
          try {
            let widget = that.getWidgetRef(item.id);
            if (
              widget != null &&
              widget.field &&
              widget.field.category != 'category'
            ) {
              try {
                if (
                  data.hasOwnProperty(widget.field.options.name) ||
                  data.hasOwnProperty(widget.field.options.alias)
                ) {
                  let value =
                    data[widget.field.options.name] ||
                    data[widget.field.options.alias];
                  // 空值也设置
                  if (value === null || value === undefined) {
                    if (item.type == 'data-table') {
                      widget.updateTableData([]);
                    } else {
                      widget.resetField();
                    }
                  } else {
                    if (item.type == 'data-table') {
                      if (!value || value !== '') {
                        if (typeof value == 'string') {
                          // 需要转json
                          widget.updateTableData(JSON.parse(value));
                        } else {
                          widget.updateTableData(value);
                        }
                      }
                    } else {
                      widget.setValue(value);
                    }
                  }
                } else if (flag) {
                  if (item.type == 'data-table') {
                    widget.updateTableData([]);
                  } else {
                    widget.resetField();
                  }
                }
              } catch (e) {
                console.error(widget.field.options.label + '设置错误', e);
              }
            }
            if (item.cols && item.cols.length > 0) {
              cr(item.cols);
            }
            if (item.tabs && item.tabs.length > 0) {
              cr(item.tabs);
            }
            if (item.items && item.items.length > 0) {
              cr(item.items);
            }
            if (item.widgetList && item.widgetList.length > 0) {
              cr(item.widgetList);
            }
            if (item.type == 'table' && item.rows && item.rows.length > 0) {
              item.rows.forEach((e) => {
                cr(e.cols);
              });
            }
          } catch (e) {
            // 里面可能有栅格等字段没有清除接口
          }
        });
      };
      //迭代
      cr(arr);
    },
    setHidden(flag) {
      this.widget.options.hidden = flag;
    },

    activeTab(tabIndex) {
      //tabIndex从0计数
      if (tabIndex >= 0 && tabIndex < this.widget.tabs.length) {
        this.widget.tabs.forEach((tp, idx) => {
          tp.options.active = idx === tabIndex;
          if (idx === tabIndex) {
            this.activeTabName = tp.options.name;
          }
        });
      }
    },

    disableTab(tabIndex) {
      if (tabIndex >= 0 && tabIndex < this.widget.tabs.length) {
        this.widget.tabs[tabIndex].options.disabled = true;
      }
    },

    enableTab(tabIndex) {
      if (tabIndex >= 0 && tabIndex < this.widget.tabs.length) {
        this.widget.tabs[tabIndex].options.disabled = false;
      }
    },

    hideTab(tabIndex) {
      if (tabIndex >= 0 && tabIndex < this.widget.tabs.length) {
        this.widget.tabs[tabIndex].options.hidden = true;
      }
    },

    showTab(tabIndex) {
      if (tabIndex >= 0 && tabIndex < this.widget.tabs.length) {
        this.widget.tabs[tabIndex].options.hidden = false;
      }
    },

    disableSubFormRow(rowIndex) {
      this.widget.widgetList.forEach((subWidget) => {
        let swRefName =
          subWidget.options.name + '@row' + this.rowIdData[rowIndex];
        let foundSW = this.getWidgetRef(swRefName);
        if (!!foundSW) {
          foundSW.setDisabled(true);
        }
      });
    },

    enableSubFormRow(rowIndex) {
      this.widget.widgetList.forEach((subWidget) => {
        let swRefName =
          subWidget.options.name + '@row' + this.rowIdData[rowIndex];
        let foundSW = this.getWidgetRef(swRefName);
        if (!!foundSW) {
          foundSW.setDisabled(false);
        }
      });
    },

    disableSubForm() {
      if (this.rowIdData.length > 0) {
        this.rowIdData.forEach((dataRow, rIdx) => {
          this.disableSubFormRow(rIdx);
        });
      }
    },

    enableSubForm() {
      if (this.rowIdData.length > 0) {
        this.rowIdData.forEach((dataRow, rIdx) => {
          this.enableSubFormRow(rIdx);
        });
      }
    },

    resetSubForm() {
      //重置subForm数据为空
      if (this.widget.type === 'sub-form') {
        let subFormModel = this.formModel[this.widget.options.name];
        if (!!subFormModel) {
          subFormModel.splice(0, subFormModel.length);
          this.rowIdData.splice(0, this.rowIdData.length);
        }
      }
    },

    setLabel(newLabel) {
      this.widget.options.label = newLabel;
    },
    getSubFormValues(needValidation = true) {
      if (this.widget.type === 'sub-form') {
        //TODO: 逐行校验子表单！！
        return this.formModel[this.widget.options.name];
      } else {
        this.$message.error(this.i18nt('render.hint.nonSubFormType'));
      }
    },
    handleOnMounted() {
      // 在流程权限阶段不执行加载事件
      if (this.widget.options.onMounted  && this.status !== 'authority') {
        let mountFunc = new Function(this.widget.options.onMounted);
        mountFunc.call(this);
      }
    },
    handleOnCreated() {
      // 在流程权限阶段不执行加载事件
      if (this.widget.options.onCreated  && this.status !== 'authority') {
        let customFunc = new Function(this.widget.options.onCreated);
        customFunc.call(this);
      }
    },
    // validateField(fieldName) { //逐行校验子表单字段
    //   //TODO:
    // },
    //
    // validateSubForm() { //逐行校验子表单全部字段
    //   //TODO:
    // },

    //--------------------- 以上为组件支持外部调用的API方法 end ------------------//
    openComplete() {
      this.callbackFlag = true;
      // 关闭时情况里面字段的校验
      let arr = this.widget.widgetList;
      let that = this;
      let cr = (arr) => {
        arr.forEach((item) => {
          try {
            let widget = that.getWidgetRef(item.id);
            if (
              widget != null &&
              widget.field &&
              widget.field.category != 'category'
            ) {
              try {
                widget.buildFieldRules();
              } catch (e) {
                console.error(widget.field.options.label + '设置错误', e);
              }
            }
            if (item.cols && item.cols.length > 0) {
              cr(item.cols);
            }
            if (item.widgetList && item.widgetList.length > 0) {
              cr(item.widgetList);
            }
            if (widget._props.widget.type === 'tab') {
              widget.activeTabName = widget.visibleTabs[0].options.label;
            }
          } catch (e) {
            // 里面可能有栅格等字段没有清除接口
          }
        });
      };
      //迭代
      cr(arr);
    },
    closeComplete() {
      this.clearValidate();
      if (window[this.widget.id]) {
        clearInterval(window[this.widget.id])
        window[this.widget.id] = undefined
      }
      // this.timer=new Date().getTime()
      if (this.widget.options.destroyOnClose) {
        this.clear();
      }

      // 关闭时情况里面字段的校验
      let arr = this.widget.widgetList;

      this.$array.handleWidgetList(arr, (item) => {
        try {
          let widget = this.getWidgetRef(item.id);
          if (
            widget != null &&
            widget.field &&
            widget.field.category != 'category'
          ) {
            try {
              widget.clearFieldRules();
            } catch (e) {
              console.error(widget.field.options.label + '设置错误', e);
            }
          }
        } catch (e) {
          // 里面可能有栅格等字段没有清除接口
        }
      });
    },
    handleClose(done) {
      if (this.widget.options.hasCloseTips) {
        this.$confirm(this.widget.options.hasCloseTipsContent)
          .then((_) => {
            this.setDialogVisible(false);
            if (!!this.widget.options.onDialogClose) {
              let customFunc = new Function(this.widget.options.onDialogClose);
              customFunc.call(this);
            }
          })
          .catch((_) => { });
      } else {
        done()
        if (!!this.widget.options.onDialogClose) {
          let customFunc = new Function(this.widget.options.onDialogClose);
          customFunc.call(this);
        }
      }
    },

    setHasRole(flag) {
      this.widget.options.hasRole = flag;
    },
    getSonWidgetRef(id) {
      let ref = null;
      const getDataSource = (data) => {
        if (data?.widgetList && data?.widgetList.length) {
          return data?.widgetList;
        }
        if (data?.cols && data?.cols.length) {
          return data?.cols;
        }
        if (data?.tabs && data?.tabs.length) {
          return data?.tabs;
        }

        return [];
      };
      const bfs = (root) => {
        if (root.id === id) {
          ref = this.getWidgetRef(id);
        }
        // console.log(root.id);
        getDataSource(root).forEach((item) => bfs(item));
      };
      bfs(this.widget);
      return ref;
    },
    /**
     * 批量设置栅格下所有节点的属性
     * @param {*} nodeId string  节点id
     * @param {*} config object  option配置项
     */
    setGridAllNode(config) {
      // 1.先找到目标节点
      let targetNode =
        this.widget?.widgetList || this.widget?.cols || this.widget?.tabs || this.widget?.rows;

      if (targetNode && targetNode.length) {
        this.$array.handleWidgetList(targetNode, (item) => {
          if(item.options) {
            for (let key in config) {
              if (key in item.options) {
                item.options[key] = config[key];
              }
            }
          }
        });
      } else {
        this.$message.error(`节点${this.widget.id}下未发现有子元素`);
      }
    },
  },
};
