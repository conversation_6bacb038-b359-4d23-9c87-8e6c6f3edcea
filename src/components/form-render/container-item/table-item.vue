<template>
  <container-item-wrapper :widget="widget">
    <div :key="widget.id" class="table-container">
      <table
        :ref="widget.id"
        class="table-layout"
        :class="[customClass]"
        v-show="!widget.options.hidden || status === 'authority'"
      >
        <tbody>
          <tr v-for="(row, rowIdx) in widget.rows" :key="row.id">
            <template v-for="(colWidget, colIdx) in row.cols">
              <table-cell-item
                v-if="!colWidget.merged"
                :widget="colWidget"
                :key="colIdx"
                :parent-list="widget.cols"
                :row-index="rowIdx"
                :col-index="colIdx"
                :parent-widget="widget"
              ></table-cell-item>
            </template>
          </tr>
        </tbody>
      </table>
      <template v-if="status === 'authority'">
        <div class="permissionMasking">
          <el-button-group class="maskingButton">
            <el-button :type="hasMaskingHidden" @click="setMaskingHidden" icon="el-icon-view">隐藏</el-button>
          </el-button-group>
        </div>
        <div class="permissionHidden" v-if="widget.options.hidden">
          <span style="display: flex;  position: absolute;top: 0;right: 0; bottom: 0;left: 0;z-index: 70 !important;align-items: center;justify-content: center;cursor: pointer;">
            <i class="el-icon-view"></i>
          </span>
        </div>
      </template>
    </div>
  </container-item-wrapper>
</template>

<script>
import emitter from 'element-ui/lib/mixins/emitter';
import i18n from '../../../utils/i18n';
import refMixin from '../../../components/form-render/refMixin';
import ContainerItemWrapper from './container-item-wrapper';
import TableCellItem from './table-cell-item';
import containerItemMixin from './containerItemMixin';

export default {
  name: 'table-item',
  componentName: 'ContainerItem',
  mixins: [emitter, i18n, refMixin, containerItemMixin],
  components: {
    ContainerItemWrapper,
    TableCellItem,
  },
  props: {
    widget: Object,
  },
  inject: ['refList', 'sfRefList', 'globalModel', 'status'],
  created() {
    this.initRefList();
  },
  computed: {
    hasMaskingHidden() {
      return this.widget.options.hidden ? 'primary' : 'default';
    },
  },
  mounted() {},
  beforeDestroy() {
    this.unregisterFromRefList();
  },
  methods: {
    setMaskingHidden() {
      this.widget.options.hidden = !this.widget.options.hidden;
    },
  },
};
</script>

<style lang="scss" scoped>
div.table-container {
  position: relative;
  table.table-layout {
    width: 100%;
    table-layout: fixed;
    border-collapse: collapse;
  }
}
.permissionMasking {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 99;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
}
.permissionHidden {
  display: flex;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 98;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  background: rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
}

.permissionMasking:hover {
  display: flex;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 99;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  background: rgba(0, 0, 0, 0.3);
  transition: all 0.3s;
}
.maskingButton {
  display: none;
  zoom: 0.8;
}
.permissionMasking:hover .maskingButton {
  display: flex;
}
</style>
