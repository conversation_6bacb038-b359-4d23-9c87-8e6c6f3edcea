<template>
  <container-item-wrapper :widget="widget">
    <el-drawer
      :title="widget.options.label"
      :visible.sync="dialogVisible"
      :size="widget.options.dialogWidth + '%' || '50%'"
      :direction="widget.options.drawerDirection"
      :before-close="handleClose" 
      @open="openComplete"
      @close="closeComplete"
    >
      <template v-for="(subWidget, swIdx) in widget.widgetList">
        <template v-if="'container' === subWidget.category">
          <component
            :is="subWidget.type + '-item'"
            :widget="subWidget"
            :key="swIdx"
            :parent-list="widget.widgetList"
            :index-of-parent-list="swIdx"
            :parent-widget="widget"
          ></component>
        </template>
        <template v-else>
          <component
            :is="subWidget.type + '-widget'"
            :field="subWidget"
            :designer="null"
            :key="swIdx"
            :parent-list="widget.widgetList"
            :index-of-parent-list="swIdx"
            :parent-widget="widget"
          ></component>
        </template>
      </template>
    </el-drawer>
  </container-item-wrapper>
</template>

<script>
import emitter from 'element-ui/lib/mixins/emitter';
import i18n from '@/utils/i18n';
import refMixin from '@/components/form-render/refMixin';
import ContainerItemWrapper from '@/components/form-render/container-item/container-item-wrapper';
import containerItemMixin from '@/components/form-render/container-item/containerItemMixin';
import FieldComponents from '@/components/form-designer/form-widget/field-widget/index';

export default {
  name: 'drawer-item',
  componentName: 'ContainerItem',
  mixins: [emitter, i18n, refMixin, containerItemMixin],
  components: {
    ContainerItemWrapper,
    ...FieldComponents,
  },
  props: {
    widget: Object,
  },
  data() {
    return {
      dialogVisible: false,
      callbackFlag: null,
    };
  },
  inject: ['refList', 'sfRefList', 'globalModel'],
  computed: {
    customClass() {
      return this.widget.options.customClass || '';
    },
  },
  created() {
    this.initRefList();
  },
  beforeDestroy() {
    this.unregisterFromRefList();
  },
  methods: {
    setDrawerVisible(flag, callback) {
      this.dialogVisible = flag;
      this.clearValidate();
      if (!flag && this.widget.options.destroyOnClose) {
        this.clear();
      }

      if (!flag) {
        callback && callback();
        return;
      }
      if (callback) {
        let timer = null;
        try {
          timer = setInterval(() => {
            // console.log('弹窗组件循环')
            if (this.callbackFlag) {
              this.callbackFlag = false;
              clearInterval(timer);
              let row = callback();
              if (row && Object.keys(row).length) this.setCategoryValue(row);
            }
          }, 100);
        } catch (error) {
          clearInterval(timer);
        }
      }
    }

  },
};
</script>

<style lang="scss">
.v-modal {
  z-index: 2000 !important;
}
</style>
