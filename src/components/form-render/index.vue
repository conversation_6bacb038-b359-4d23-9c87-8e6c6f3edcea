<template>
  <el-form
    ref="renderForm"
    :label-position="labelPosition"
    :size="size"
    :class="[customClass]"
    class="render-form"
    :label-width="labelWidth"
    :validate-on-rule-change="false"
    :model="formDataModel"
    @submit.native.prevent
  >
    <div v-if="status === 'authority'" class="d-flex j-end mr-b10">
      <el-button-group class="maskingButton">
        <el-button type="primary" @click="setMaskingHidden">一键全隐</el-button>
        <el-button @click="setMaskingShow">一键全显</el-button>
      </el-button-group>
    </div>
    <template v-for="(widget, index) in widgetList">
      <template v-if="checkFormPermission(widget)">
        <component
          :is="getContainerWidgetName(widget)"
          :key="widget.id"
          :widget="widget"
          :parent-list="widgetList"
          :index-of-parent-list="index"
          :parent-widget="null"
          :class="widget.id"
        />
      </template>
      <template v-else>
        <component
          :is="getWidgetName(widget)"
          :key="widget.id"
          :field="widget"
          :form-model="formDataModel"
          :designer="null"
          :parent-list="widgetList"
          :index-of-parent-list="index"
          :parent-widget="null"
          :class="widget.id"
        />
      </template>
    </template>
    <model-operation ref="modeOperation" />
  </el-form>
</template>

<script>
// import ElForm from 'element-ui/packages/form/src/form.vue'  /* 用于源码调试Element UI */
import emitter from "element-ui/lib/mixins/emitter";
import "./container-item/index";
import "./popup-item/index";
import FieldComponents from "@/components/form-designer/form-widget/field-widget/index";
import {
  deepClone,
  insertCustomCssToHead,
  insertGlobalFunctionsToHtml,
} from "../../utils/util";
import { uuid } from "@/utils/util";
import i18n, { changeLocale } from "../../utils/i18n";
import bus from "@/magic-editor/scripts/bus.js";
import request from "@/utils/request";
import { getToken, getEnv } from "@/utils/auth";
import ruleScript from "@/components/form-render/ruleScript.js";
import { executeDevById } from "@/api/interfaces/interfaces.js";
import modelOperation from "@/views/tool/variantform/modelOperation.vue";
import store from "@/store";
import user from "@/store/modules/user";
import { checkBusinessPermission } from "@/utils/permission";
import { getDevelop } from '../../utils/auth'
import intro from 'intro.js' // introjs库
import 'intro.js/introjs.css' // introjs默认css样式
import { getSelectTableList } from '@/api/tool/gen';
import { getDicts } from '@/api/system/dict/data.js';
import { newExecuteInterface, initializationBatchData } from '@/api/interfaces/interfaces';

export default {
  name: "VFormRender",
  mixins: [emitter, i18n],
  components: {
    // ElForm,
    modelOperation,
    ...FieldComponents,
  },
  provide() {
    return {
      refList: this.widgetRefList,
      sfRefList: this.subFormRefList, // 收集SubForm引用
      formConfig: this.formConfig,
      globalOptionData: this.optionData,
      globalModel: {
        formModel: this.formDataModel,
      },
      previewState: this.previewState,
      writeState: this.writeState,
      busUid: "ruleTrigger-" + this.eventUid,
      eventUid: this.eventUid, // 事件唯一Id
      status: this.status,
      previewDialogClose: this.previewDialogClose,
      flowNode: this.flowNode,
      hasRead: this.hasRead,
      allInterfaceData: this.allInterfaceData
    };
  },
  watch: {
    formDataModel: {
      immediate: true,
      handler(old, val) {
        for (let key in val) {
          if (key.includes("select") && val[key] instanceof Array) {
            val[key] = val[key].join();
          }
        }
        this.$emit("handelFormData", val);
        // 这里是为了实时回显查看数据功能展示的数据
        this.$emit("handleShowData")
      },
      deep: true,
    },
  },
  props: {
    formJson: Object, // prop传入的表单JSON配置
    formData: {
      // prop传入的表单数据
      Object,
      default: () => {},
    },
    optionData: {
      // prop传入的选项数据
      type: Object,
      default: () => {},
    },
    previewState: {
      // 是否表单预览状态
      type: Boolean,
      default: false,
    },
    writeState: {
      // 是否表单预览状态
      type: Boolean,
      default: true,
    },
    status: {
      // 是否权限配置界面
      type: String,
      default: null,
    },
    hasPer: {
      type: Boolean,
      default: true,
    },

    flowNode: {
      // 当前所在流程节点
      type: String,
      default: null,
    },
    // 事件唯一Id 主要解决多个vForm嵌套的时候内部触发的事件会重复触发 on监听没办法做到唯一消费
    eventUid: {
      type: String,
      default: uuid(),
    }

  },
  data() {
    return {
      popupName: "device-table",
      formJsonObj: this.formJson,
      openDialogTable: false,
      formPermission: [],
      formDataModel: {
        //
      },
      asData: {
        // 别名数据
      },
      asMapping: {},
      ruleScript: ruleScript,
      ruleScriptMap: {},
      widgetRefList: {},
      subFormRefList: {},
      dialogData: {
        fieldName: "",
        fieldModel: "",
      },
      dialogDataList: {},
      busUid: "ruleTrigger-" + this.eventUid,
      componentMap: {},
      hasRead: false,
      intro: intro,
      introOption: {
        prevLabel: '上一步',
        nextLabel: '下一步',
        skipLabel: '跳过',
        doneLabel: '完成',
        tooltipClass: 'intro-tooltip', /* 引导说明文本框的样式 */
        // highlightClass: 'intro-highlight', /* 说明高亮区域的样式 */
        exitOnEsc: true, /* 是否使用键盘Esc退出 */
        exitOnOverlayClick: false, /* 是否允许点击空白处退出 */
        keyboardNavigation: true, /* 是否允许键盘来操作 */
        showBullets: false, /* 是否使用点显示进度 */
        showProgress: false, /* 是否显示进度条 */
        scrollToElement: true, /* 是否滑动到高亮的区域 */
        overlayOpacity: 0.5, // 遮罩层的透明度 0-1之间
        positionPrecedence: ['bottom', 'top', 'right', 'left'], /* 当位置选择自动的时候,位置排列的优先级 */
        disableInteraction: false, /* 是否禁止与元素的相互关联 */
        hidePrev: true, /* 是否在第一步隐藏上一步 */
        // hideNext: true, /* 是否在最后一步隐藏下一步 */
        autoPosition: true, /* 是否自动选择位置 */
        helperElementPadding: 10, /* 设置辅助元素周围要使用的填充量 */
        steps: [], /* steps步骤,可以写个工具类保存起来 */
      },
      allInterfaceData: {
      }
    };
  },
  computed: {
    formConfig() {
      return this.formJsonObj.formConfig;
    },

    widgetList() {
      return this.formJsonObj.widgetList;
    },

    labelPosition() {
      if (!!this.formConfig && !!this.formConfig.labelPosition) {
        return this.formConfig.labelPosition;
      }

      return "left";
    },

    labelWidth() {
      if (!!this.formConfig && !!this.formConfig.labelWidth) {
        return this.formConfig.labelWidth + "px";
      }

      return "80px";
    },
    fieldName() {
      return this.dialogData.fieldName;
    },
    size() {
      if (!!this.formConfig && !!this.formConfig.size) {
        return this.formConfig.size;
      }

      return "medium";
    },

    customClass() {
      return !!this.formConfig && !!this.formConfig.customClass
        ? this.formConfig.customClass
        : "";
    },
  },
  created() {
    // 监听任务
    bus.$on("onOpenModelOperation-"+this.eventUid, this.openModelOperation);
    // console.log('dialogDataList.optionItems==',dialogDataList.optionItems)
    if (this.formJson) {
      let formId = this.formJson.formConfig.formId;
      this.formPermission = user && user.state && user.state.businessPermissions && user.state.businessPermissions[formId];
      if (!this.formPermission) {
        this.formPermission = [];
      }
    }
    this.buildFormModel(!this.formJsonObj ? null : this.formJsonObj.widgetList);
    this.initFormObject();
  },
  mounted() {
    this.initLocale();
    // 界面监听扫码
    this.emitScan();
    this.initRuleTrigger();
    // 延时一点，避免组件未渲染完
    setTimeout(() => {
      this.initGuide();
    }, 500);
    setTimeout(() => {
      this.initSummaryInterfaces()
    }, 50);

    this.handleOnMounted();
  },
  methods: {
    initSummaryInterfaces() {
      // 1. 遍历出有哪些组件是需要集中处理的
      let paramList = []

      this.$array.handleWidgetList(this.formJson.widgetList, (item) => {
        // 模型, getSelectTableList
        if(item.options.optionsModel) {
          paramList.push({
            id: item.id,
            type: 'model',
            optionsModel: item.options.optionsModel,
            value: '',    // value参数待定
            valueField: item.options.modelValue || item.options.selectValue || 'value'
          })
        }
        // 字典, getDicts
        else if(item.options.optionsDict) {
          paramList.push({
            id: item.id,
            type: 'dict',
            optionsDict: item.options.optionsDict
          })
        }
        // API, newExecuteInterface
        else if(item.options.api) {
          // 拼接接口参数
          let param = {}
          try {
            if (item.options.paramConfig) {
              JSON.parse(item.options.paramConfig).forEach((paramItem) => {
                if (paramItem.type === 'keyValue') {
                  if (paramItem.value.length && paramItem.value[1].includes('.')) {
                    let form = paramItem.value[1].split('.')[1];
                    // 当前点击的button的子表单中的索引
                    param[paramItem.key] =
                      this.formDataModel[form];
                  } else {
                    param[paramItem.key] = this.formDataModel[paramItem.value[1]] || '';
                  }
                } else {
                  // 取所有
                  let relation = {};
                  paramItem.subset.forEach((item2) => {
                    relation[item2.value[1].split('.')[1]] =
                      item2.key.split('.')[1];
                  });
                  let dataAssemble = (data) => {
                    let obj = {};
                    for (let i in data) {
                      obj[relation[i]] = data[i];
                    }
                    return obj;
                  };
                  this.formDataModel[paramItem.value[1]].forEach((item2) => {
                    param[paramItem.key].push(dataAssemble(item2));
                  });
                }
              });
            }
          } catch (error) {
            console.log(error)
          }

          // 参数配置

          let baseParam = {
            pageNum: 1,
            pageSize: 20,
            sort: { prop: '', order: '' },
            searchKey: '',
            searchParams: [],
            ordersList: [],
          };
          // 开启自动加载数据的才通过此处加载数据
          if(
            item.options.autoLoadData
          ) {
            paramList.push({
              id: item.id,
              type: 'api',
              apiId: item.options.api,
              body: { ...baseParam, ...param },    // body参数待定
            })
          }
        }

        // 特殊化出来表格
        if(item.type === 'data-table' && item.options?.cols.length) {
          let id = item.id
          this.$array.handleDFS(item.options?.cols, 'children', item => {
            let selectConfig = item?.selectConfig;
            if(selectConfig) {
              if(selectConfig.isModel && selectConfig.optionsModel) {
                paramList.push({
                  id: `${id}<--->${item.prop}`,
                  type: 'model',
                  optionsModel: selectConfig.optionsModel,
                })
              }
              else if (selectConfig.optionsDict) {
                 paramList.push({
                  id: `${id}<--->${item.prop}`,
                  type: 'dict',
                  optionsDict: selectConfig.optionsDict,
                })
              }
              else if (selectConfig.api &&
                (selectConfig?.paramConfig
                  ? !Object.keys(selectConfig?.paramConfig).length
                  : true)
              ) {
                paramList.push({
                  id: `${id}<--->${item.prop}`,
                  type: 'api',
                  apiId: selectConfig.api,
                  body: {},
                })
              }
            }
          })
        }

      })
      console.log(paramList)
      // 2.请求拿到数据
      if(paramList.length) {
        initializationBatchData(paramList).then(resList =>{
          paramList.forEach((item, index) => {
            this.$set(this.allInterfaceData, item.id, resList.data[item.id]);
          })
        })
      }
    },
    initGuide() {
      if (!this.formConfig.introSteps || this.formConfig.introSteps.length == 0) {
        return
      }
      this.introOption.steps = this.formConfig.introSteps.map(item => {
        return {
          ...item,
          element: '.' + item.element,
        };
      });
      this.intro().setOptions(this.introOption).start();
    },
    hasNode(nodeId) {
      return this.flowNode && this.flowNode === nodeId
    },
    buildUid() {
      return uuid();
    },
    setMaskingHidden() {
      this.formJsonObj.widgetList.forEach((item) => {
        item.options.hidden = true;
      });
    },
    setMaskingShow() {
      this.formJsonObj.widgetList.forEach((item) => {
        item.options.hidden = false;
      });
    },
    checkFormPermission(widget) {
      let authCode = widget.options.name;
      let authority = widget.options.authority;
      this.$set(widget, "formPermission", this.formPermission);
      if ("container" === widget.category) {
        return checkBusinessPermission(
          authority,
          authCode,
          this.formPermission
        );
      }
      return false;
    },
    openModelOperation(model, data, callback, status) {
      if(this.$refs.modeOperation){
        setTimeout(()=>{
          this.$refs.modeOperation.open(model, data, callback, status);
        },200)
      }
    },
    previewDialogClose() {},
    initRuleTrigger() {
      if (this.formConfig) {
        console.log("注册监听事件 监听ID:", this.busUid);
        //创建可触发事件的组件map
        let map = {};
        this.formConfig.linkRules.forEach((rule, index) => {
          rule.conditions.forEach((condition) => {
            condition.cd.forEach((e) => {
              map[e.component.at(-1)] = index;
            });
          });
        });
        this.componentMap = map;
        bus.$off(this.busUid);
        bus.$on(this.busUid, (args) => this.ruleTriggerFunc(args));
      }

      // this.ruleTriggerFunc([this, null, 'init'])
    },
    //全局触发事件 (触发规则引擎内容)
    ruleTriggerFunc(args) {
      if (this.componentMap.hasOwnProperty(args[1])) {
        this.formConfig.linkRules.forEach((rule) => {
          this.executeRuleResult(this.determineRule(args, rule), args, rule);
        });
      }
    },
    // 判断规则引擎条件是否成立
    determineRule(args, rule) {
      let allResult = null;
      console.log("条件", rule.conditions)
      for (let i = 0; i < rule.conditions.length; i++) {
        let cds = rule.conditions[i];
        let pGroup = null;
        if (i !== 0) {
          pGroup = rule.conditions[i - 1];
        }
        let result = null;
        for (let i = 0; i < cds.cd.length; i++) {
          let pcd = null;
          if (i !== 0) {
            pcd = cds.cd[i - 1];
          }
          let cd = cds.cd[i];
          let componentId = cd.component[cd.component.length - 1];
          let bol = null;
          // 事件条件
          if (cd.type === "event") {
            if (
              componentId === args[1] &&
              args[2] === "event" &&
              args[3] === cd.value.value
            ) {
              bol = true;
            } else {
              // 触发组件和条件组件不一致一定不符合条件 同时bus消息不是event类型并且value中传递的事件类型和条件不一致也一定不符合条件
              bol = false;
            }
          } else {
            // 值判断事件
            let value =
              componentId === args[1]
                ? args[0].getValue()
                : this.formDataModel[componentId];
            console.log("被动获取到的值", value)
            let cdValue =
              cd.value.type == "g"
                ? cd.value.value
                : this.$getValue(cd.value.value);
            if (cd.condition === "eq") {
              bol = value === cdValue;
            } else if (cd.condition === "neq") {
              bol = value !== cdValue;
            } else if (cd.condition === "inc") {
              bol = value.includes(cdValue);
            } else if (cd.condition === "ninc") {
              bol = !value.includes(cdValue);
            }
          }
          if (result == null || pcd == null) {
            result = bol;
          } else {
            result = pcd.leftLink === "and" ? result && bol : result || bol;
          }
        }
        if (allResult == null || pGroup == null) {
          allResult = result;
        } else {
          allResult =
            pGroup.link === "and" ? result && allResult : result || allResult;
        }
      }
      return allResult ? "ok" : "ng";
    },
    executeRuleResult(res, args, rule) {
      let than = this;
      if (rule[res]) {
        let fun = (arr) => {
          arr.forEach((item) => {
            // 脚本内容存在则执行内容
            if (
              than.ruleScriptMap[item.type.at(-1)] &&
              than.ruleScriptMap[item.type.at(-1)].func
            ) {
              try {
                let argsData = {};
                // 模型类操作
                if (item.type[0] === "model") {
                  // 打开模型弹窗
                  if (item.type[1] === "openModelDialog") {
                    // 需要判断触发事件给出的参数长度，非表格列操作时间下标不到4
                    let row = args.length >= 4 ? args[4]?.row : null;
                    let commitId, taskId, definitionId;
                    if (item.commitType === "widget") {
                      commitId = this.$getValue(item.commitId);
                    } else {
                      commitId = row && row[item.commitId];
                    }
                    if (item.definitionType === "widget") {
                      definitionId = this.$getValue(item.definitionId);
                    } else {
                      definitionId = row && row[item.definitionId];
                    }
                    if (item.taskType === "widget") {
                      taskId = this.$getValue(item.taskId);
                    } else {
                      taskId = row && row[item.taskId];
                    }

                    argsData = {
                      modelName: item.component,
                      args: {
                        than: args[0],
                        data: {
                          row,
                          commitId,
                          taskId,
                          definitionId,
                        },
                      },
                    };
                  }
                } else if (item.type[0] === "interfaces") {
                  argsData = {
                    apiId: item.interfacesUid,
                    params: item.param,
                  };
                } else {
                  argsData = {
                    componentId: item.component[item.component.length - 1],
                    value:
                      item.value.type == "g"
                        ? item.value.value
                        : this.$getValue(item.value.value),
                  };
                }
                console.debug("执行事件", res, args, than.ruleScriptMap[item.type.at(-1)].func);
                than.ruleScriptMap[item.type.at(-1)].func.call(
                  args[0],
                  argsData
                );
              } catch (e) {
                console.error("规则引擎任务失败", e);
              }
            }
            if (item.children && item.children.length > 0) {
              setTimeout(() => {
                fun(item.children);
              }, 200);
            }
          });
        };
        fun(rule[res]);
      }
    },
    $setInterval(name, callback, time) {
      let that = this;
      if (!window[name]) {
        window[name] = setInterval(() => {
          callback && callback.call(that);
        }, time);
        this.$once("hook:beforeDestroy", () => {
          if (window[name]) {
            clearInterval(window[name]);
            window[name] = undefined;
          }
        });
      }
    },
    $getValue(id) {
      if (this.getWidgetRef(id)) {
        return this.getWidgetRef(id).getValue();
      } else if (
        this?.formConfig?.store &&
        this?.formConfig?.store.hasOwnProperty(id)
      ) {
        return this.formConfig.store[id];
      } else if (
        this?.formConfig?.temp &&
        this?.formConfig?.temp.hasOwnProperty(id)
      ) {
        return this.formConfig.temp[id];
      }
    },
    $setValue(id, value) {
      if (this.getWidgetRef(id)) {
        this.getWidgetRef(id).setValue(value);
      } else if (this.formConfig.store) {
        this.$set(this.formConfig.store, id, value);
      }
    },
    getRequest(pp) {
      return request(pp);
    },

    $interface(apiId, param) {
      if (!apiId) {
        return;
      }
      for (let key in param) {
        param[key] = this.$getValue(param[key]);
      }
      executeDevById({
        apiId,
        body: param,
      });
    },
    http(interfacesId, data, awaitFlag = false) {
      if (awaitFlag) {
        let settings = {
          url: `${process.env.VUE_APP_BASE_API}/interfaces/ldfBusinessInfo/executeDevById`,
          type: "post",
          timeout: 0,
          data: JSON.stringify({ apiId: interfacesId, body: data }),
          dataType: "JSON",
          contentType: "application/json;charset=UTF-8",
          headers: {
            Accept: "application/json, text/plain, */*",
            Connection: "keep-alive",
          },
          async: !awaitFlag,
        };

        // 是否需要设置 token
        const isToken = (settings.headers || {}).isToken === false;
        // 请求携带来自终端类型
        settings.headers["terminalType"] = "PC";
        // 加入当前环境
        if (getEnv()) {
          settings.headers["env"] = getEnv();
        }
        // 加入当前客户端id
        if (localStorage.getItem("clientId")) {
          settings.headers["Magic-Request-Client-Id"] =
            localStorage.getItem("clientId");
        }
        if (getDevelop())
          settings.headers["develop"] = getDevelop();

        if (getToken() && !isToken) {
          settings.headers["Authorization"] = "Bearer " + getToken(); // 让每个请求携带自定义token 请根据实际情况自行修改
        }
        return $.ajax(settings).responseJSON;
      } else {
        return executeDevById({ apiId: interfacesId, body: data });
      }
    },
    waitHttp(apiId, data) {
      let settings = {
        url: `${process.env.VUE_APP_BASE_API}/interfaces/ldfBusinessInfo/executeDevById`,
        type: "post",
        timeout: 0,
        data: JSON.stringify({ apiId: apiId, body: data }),
        dataType: "JSON",
        contentType: "application/json;charset=UTF-8",
        headers: {
          Accept: "application/json, text/plain, */*",
          Connection: "keep-alive",
        },
        async: true,
      };

      // 是否需要设置 token
      const isToken = (settings.headers || {}).isToken === false;
      // 请求携带来自终端类型
      settings.headers["terminalType"] = "PC";

      if (getToken() && !isToken) {
        settings.headers["Authorization"] = "Bearer " + getToken(); // 让每个请求携带自定义token 请根据实际情况自行修改
      }
      return $.ajax(settings).responseJSON;
    },
    emitScan() {
      // 监听按键
      var code = "";
      var lastTime, nextTime; // 上次时间、最新时间
      var lastCode, nextCode; // 上次按键、最新按键
      document.onkeypress = (e) => {
        // 获取按键
        if (window.event) {
          // IE
          nextCode = e.keyCode;
        } else if (e.which) {
          // Netscape/Firefox/Opera
          nextCode = e.which;
        }
        // 如果触发了回车事件(扫码结束时间)
        if (nextCode === 13) {
          if (code.length < 3) return; // 手动输入的时间不会让code的长度大于2，所以这里只会对扫码枪有

          this.handleOnScanCommit(code); // 获取到扫码枪输入的内容，做别的操作

          code = "";
          lastCode = "";
          lastTime = "";
          return;
        }
        nextTime = new Date().getTime(); // 记录最新时间
        if (!lastTime && !lastCode) {
          // 如果上次时间和上次按键为空
          code += e.key; // 执行叠加操作
        }
        // 如果有上次时间及上次按键
        if (lastCode && lastTime && nextTime - lastTime > 30) {
          // 当扫码前有keypress事件时,防止首字缺失
          code = e.key;
        } else if (lastCode && lastTime) {
          code += e.key;
        }
        lastCode = nextCode;
        lastTime = nextTime;
      };
    },
    initFormObject() {
      this.insertCustomStyleAndScriptNode();
      // this.buildFormModel()
      this.addFieldChangeEventHandler();
      this.registerFormToRefList();
      this.handleOnCreated();
    },

    getContainerWidgetName(widget) {
      return widget.type + "-item";
    },
    getPopupWidgetName() {
      return this.popupName + "-popup";
    },
    getWidgetName(widget) {
      return widget.type + "-widget";
    },

    initLocale() {
      let curLocale = localStorage.getItem("v_form_locale") || "zh-CN";
      this.changeLanguage(curLocale);
    },

    insertCustomStyleAndScriptNode() {
      if (!!this.formConfig && !!this.formConfig.cssCode) {
        insertCustomCssToHead(this.formConfig.cssCode);
      }

      if (!!this.formConfig && !!this.formConfig.functions) {
        insertGlobalFunctionsToHtml(this.formConfig.functions);
      }
    },
    buildFormModel(widgetList) {
      // 将值设置给全局变量
      if (this.formConfig.store) {
        for (let item in this.formConfig.store) {
          this.$set(
            this.formConfig.store,
            item,
            deepClone(this.formData[item])
          );
        }
      }
      // 组件规则引擎
      this.ruleScript.forEach((e) => {
        e.children.forEach((el) => {
          if (el.script) {
            el.func = new Function("$env", el.script);
          }
          this.ruleScriptMap[el.type] = el;
        });
      });
      if (!!widgetList && widgetList.length > 0) {
        // 加载初始化数据方法
        widgetList.forEach((wItem) => {
          this.buildDataFromWidget(wItem);
        });
        this.$nextTick(()=> {
          setTimeout(() => {
            if (this.formJson.formConfig.onFormDataComplete) {
              let customFunc = new Function(
                this.formJson.formConfig.onFormDataComplete
              )
              customFunc.call(this)
            }
          }, 200);
        })
      }
      // 未加载字段放置到缓存中用于获取
      this.$set(
        this.formConfig,
        "temp",
        this.getDisjointFields(this.formData, this.formDataModel)
      );
    },
    // 获取不相交字段
    getDisjointFields(obj1, obj2) {
      const result = {};
      for (const key in obj1) {
        if (!(key in obj2)) {
          result[key] = obj1[key];
        }
      }
      for (const key in obj2) {
        if (!(key in obj1)) {
          result[key] = obj2[key];
        }
      }
      return result;
    },
    buildDataFromWidget(wItem) {
      if (wItem.category === "container") {
        if (wItem.type === "grid") {
          if (!!wItem.cols && wItem.cols.length > 0) {
            wItem.cols.forEach((childItem) => {
              this.buildDataFromWidget(childItem);
            });
          }
        } else if (wItem.type === "table") {
          if (!!wItem.rows && wItem.rows.length > 0) {
            wItem.rows.forEach((rowItem) => {
              if (!!rowItem.cols && rowItem.cols.length > 0) {
                rowItem.cols.forEach((colItem) => {
                  this.buildDataFromWidget(colItem);
                });
              }
            });
          }
        } else if (wItem.type === "tab") {
          if (!!wItem.tabs && wItem.tabs.length > 0) {
            wItem.tabs.forEach((tabItem) => {
              if (!!tabItem.widgetList && tabItem.widgetList.length > 0) {
                tabItem.widgetList.forEach((childItem) => {
                  this.buildDataFromWidget(childItem);
                });
              }
            });
          }
        } else if (wItem.type === "trends-tab") {
          let subFormName = wItem.options.name;
          if (!this.formData.hasOwnProperty(subFormName)) {
            let subFormDataRow = {};
            wItem.widgetList.forEach((subFormItem) => {
              if (subFormItem.category === "container") {
                subFormItem.cols.forEach((childFormItem) => {
                  childFormItem.widgetList.forEach((childChildFormItem) => {
                    subFormDataRow[childChildFormItem.options.name] =
                      childChildFormItem.options.defaultValue;
                  });
                });
              } else {
                subFormDataRow[subFormItem.options.name] =
                  subFormItem.options.defaultValue;
              }
            });
            subFormDataRow["title"] = wItem.options.label;
            this.$set(this.formDataModel, subFormName, [subFormDataRow]); //
          } else {
            let initialValue = this.formData[subFormName];
            this.$set(this.formDataModel, subFormName, deepClone(initialValue));
          }
        } else if (wItem.type === "sub-form") {
          let subFormName = wItem.options.name;
          if (!this.formData.hasOwnProperty(subFormName)) {
            let subFormDataRow = {};
            if (wItem.options.showBlankRow) {
              wItem.widgetList.forEach((subFormItem) => {
                if (subFormItem.formItemFlag) {
                  subFormDataRow[subFormItem.options.name] =
                    subFormItem.options.defaultValue;
                }
              });
              this.$set(this.formDataModel, subFormName, [subFormDataRow]); //
            } else {
              this.$set(this.formDataModel, subFormName, []); //
            }
          } else {
            let initialValue = this.formData[subFormName];
            this.$set(this.formDataModel, subFormName, deepClone(initialValue));
          }
        } else if (wItem.type === "grid-col" || wItem.type === "table-cell") {
          if (!!wItem.widgetList && wItem.widgetList.length > 0) {
            wItem.widgetList.forEach((childItem) => {
              this.buildDataFromWidget(childItem);
            });
          }
        } else if (wItem.type === "data-table") {
          let subFormName = wItem.options.name;
          this.$set(this.formDataModel, subFormName, []); //
        } else {
          // 自定义容器组件
          if (!!wItem.widgetList && wItem.widgetList.length > 0) {
            wItem.widgetList.forEach((childItem) => {
              this.buildDataFromWidget(childItem);
            });
          }
        }
      } else if (wItem.formItemFlag) {
        // if (wItem.type === "switch") {
        //   const { name, defaultValue, activeValue, inactiveValue } =
        //     wItem.options;
        //   if (activeValue || inactiveValue) {
        //     this.$set(
        //       this.formDataModel,
        //       name,
        //       defaultValue ? activeValue : inactiveValue
        //     );
        //   }
        //   const alias = wItem.options.alias ? wItem.options.alias : wItem.options.name;
        //   if (alias) {
        //     this.$set(this.asData, wItem.options.alias, defaultValue ? activeValue : inactiveValue)
        //   }
        // } else
        if (
          this.formData &&
          !this.formData.hasOwnProperty(wItem.options.name) &&
          !this.formData.hasOwnProperty(wItem.options.alias)
        ) {
          this.$set(
            this.formDataModel,
            wItem.options.name,
            wItem.options.defaultValue
          ); // 设置字段默认值
          const alias = wItem.options.alias ? wItem.options.alias : wItem.options.name;
          if (alias) {
            this.$set(this.asData, wItem.options.alias, wItem.options.defaultValue)
          }
        } else {
          if (this.formData) {
            let initialValue =
              this.formData[wItem.options.name] ||
              this.formData[wItem.options.alias];
            this.$set(
              this.formDataModel,
              wItem.options.name,
              deepClone(initialValue)
            );
            const alias = wItem.options.alias ? wItem.options.alias : wItem.options.name;
            if (alias) {
              this.$set(this.asData, wItem.options.alias, deepClone(initialValue))
            }
          }
        }
      }
    },

    addFieldChangeEventHandler() {
      this.$off("fieldChange"); // 移除原有事件监听

      this.$on(
        "fieldChange",
        function (fieldName, newValue, oldValue, subFormName, subFormRowIndex) {
          this.$emit("formDataChange", {
            fieldName: fieldName,
            newValue: newValue,
            oldValue: oldValue,
            formDataModel: this.formDataModel,
            subFormName: subFormName,
            subFormRowIndex: subFormRowIndex,
          });
          this.handleFieldDataChange(
            fieldName,
            newValue,
            oldValue,
            subFormName,
            subFormRowIndex
          );
          this.$emit(
            "formChange",
            fieldName,
            newValue,
            oldValue,
            this.formDataModel,
            subFormName,
            subFormRowIndex
          );
        }
      );
    },

    registerFormToRefList() {
      this.widgetRefList["v_form_ref"] = this;
      let vFormRenderList = this.$parent.$children.filter(
        (item) => item.$options.name === "VFormRender"
      );
      let objData = {};
      vFormRenderList.forEach((item) => {
        // 根据 refName 将其区分开来
        let key = item.formJsonObj.formConfig.refName;
        let refList = this.getComponentRefList(item);
        objData[key] =
          key in objData ? { ...objData[key], ...refList } : refList;
      });
      this.widgetRefList["all_form_ref"] = objData;
      this.$store["all_form_ref"] = objData;
    },
    getComponentRefList(componentTree) {
      let refList = null;
      const dfs = (root) => {
        if ("refList" in root) refList = root.refList;

        root.$children &&
          root.$children.forEach((item) => {
            dfs(item);
          });
      };


      dfs(componentTree);
      return refList;
    },
    handleFieldDataChange(
      fieldName,
      newValue,
      oldValue,
      subFormName,
      subFormRowIndex
    ) {
      if (!!this.formConfig && !!this.formConfig.onFormDataChange) {
        let customFunc = new Function(
          "fieldName",
          "newValue",
          "oldValue",
          "formModel",
          "subFormName",
          "subFormRowIndex",
          this.formConfig.onFormDataChange
        );
        customFunc.call(
          this,
          fieldName,
          newValue,
          oldValue,
          this.formDataModel,
          subFormName,
          subFormRowIndex
        );
      }
    },

    handleOnCreated() {
      if (!!this.formConfig && !!this.formConfig.onFormCreated && this.status !== 'authority' ) {
        let customFunc = new Function(this.formConfig.onFormCreated);
        customFunc.call(this);
      }
    },

    handleOnMounted() {
      if (!!this.formConfig && !!this.formConfig.onFormMounted && this.status !== 'authority') {
        let customFunc = new Function(this.formConfig.onFormMounted);
        customFunc.call(this);
      }
    },

    handleOnScanCommit(commitCode) {
      if (!!this.formConfig && !!this.formConfig.onScanCommit && this.status !== 'authority') {
        let customFunc = new Function(
          "commitCode",
          this.formConfig.onScanCommit
        );
        customFunc.call(this, commitCode);
      }
    },

    findWidgetAndSetDisabled(widgetName, disabledFlag) {
      let foundW = this.getWidgetRef(widgetName);
      if (foundW) {
        foundW.setDisabled(disabledFlag);
      }
    },

    findWidgetAndSetHidden(widgetName, hiddenFlag) {
      let foundW = this.getWidgetRef(widgetName);
      if (foundW) {
        foundW.setHidden(hiddenFlag);
      }
    },

    // --------------------- 以下为组件支持外部调用的API方法 begin ------------------//
    /* 提示：用户可自行扩充这些方法！！！ */

    changeLanguage(langName) {
      changeLocale(langName);
    },

    getNativeForm() {
      // 获取原生form引用
      return this.$refs["renderForm"];
    },

    getWidgetRef(widgetName, showError = false) {
      let foundRef = this.widgetRefList[widgetName];
      if (!foundRef && !!showError) {
        this.$message.error(this.i18nt("render.hint.refNotFound") + widgetName);
      }
      return foundRef;
    },

    clearFormDataModel() {
      for (let pkey in this.formDataModel) {
        delete this.formDataModel[pkey];
      }
    },

    /**
     * 动态加载表单JSON
     * @param newFormJson
     */
    setFormJson(newFormJson) {
      if (newFormJson) {
        if (
          typeof newFormJson === "string" ||
          newFormJson.constructor === Object
        ) {
          let newFormJsonObj = null;
          if (typeof newFormJson === "string") {
            newFormJsonObj = JSON.parse(newFormJson);
          } else {
            newFormJsonObj = newFormJson;
          }

          if (!newFormJsonObj.formConfig || !newFormJsonObj.widgetList) {
            this.$message.error("Invalid format of form json.");
            return;
          }

          /* formDataModel必须在widgetList赋值完成初始化，因为widgetList赋值意味着子组件开始创建！！！ */
          // this.formDataModel = {}  //清空表单数据对象（有bug，会导致表单校验失败！！）
          this.clearFormDataModel(); // 上行代码有问题，会导致表单校验失败，故保留原对象引用只清空对象属性！！
          this.buildFormModel(newFormJsonObj.widgetList);

          this.$set(this.formJsonObj, "formConfig", newFormJsonObj.formConfig);
          this._provided.formConfig = newFormJsonObj.formConfig; // 强制更新provide的formConfig对象
          this.$set(this.formJsonObj, "widgetList", newFormJsonObj.widgetList);

          this.initFormObject();
          this.handleOnMounted();
        } else {
          this.$message.error("Set form json failed.");
        }
      }
    },
    getFormJson() {
      return deepClone(this.formJsonObj);
    },
    /**
     * 重新加载选项数据
     * @param widgetNames 指定重新加载的组件名称或组件名数组，不传则重新加载所有选项字段
     */
    reloadOptionData(widgetNames) {
      let eventParams = [];
      if (!!widgetNames && typeof widgetNames === "string") {
        eventParams = [widgetNames];
      } else if (!!widgetNames && Array.isArray(widgetNames)) {
        eventParams = [...widgetNames];
      }
      this.broadcast("FieldWidget", "reloadOptions", [eventParams]);
    },
    getMyFormData(Validation) {
      this.getFormData(Validation);
    },
    getFormData(needValidation = true) {
      if (this.formConfig.store) {
        for (let item in this.formConfig.store) {
          this.$set(this.formDataModel, item, this.formConfig.store[item]);
        }
      }
      if (!needValidation) {
        return this.formDataModel;
      }
      let callback = function nullFunc() {};
      let promise = new window.Promise(function (resolve, reject) {
        callback = function (formData, error) {
          !error ? resolve(formData) : reject(error);
        };
      });

      // // 图片上传接口
      // this.$refs['fieldEditor']

      // 对容器中的字段进行重复性校验
      let validateRepeat = () => {
        // 筛选出所有的key且需要进行做校验的(当前只校验subform(子表单)和trendstab(动态选项卡))
        let keyList = Object.keys(this.formDataModel).filter(
          (item) => item.includes("subform") || item.includes("trendstab")
        );

        for (let i = 0; i < keyList.length; i++) {
          let item = keyList[i];
          // 大于1才需要进行重复性校验
          if (this.formDataModel[item].length > 1) {
            // 取出所有的需要进行重复校验的key
            let keys = getRepeatWidget(item);
            if (keys) {
              // 遍历数据
              for (let j = 0; j < keys.length; j++) {
                // 去重后的数组
                let removalArr = [
                  ...new Set(
                    this.formDataModel[item].map((item) => item[keys[j]])
                  ),
                ];

                if (removalArr.length === 1 && removalArr[0] !== "") {
                  return {
                    data: false,
                    message: "数据项不能出现重复,提示:" + removalArr[0],
                  };
                }
              }
            }
          }
          // 必填校验(每一行不能全是空)
          let widget =
            this.widgetRefList[item] &&
            this.widgetRefList[item]["_props"]["widget"].options;
          if (widget && widget.required && !this.formDataModel[item].length) {
            return {
              data: false,
              message:
                widget.validationHint || widget.label + "至少要有一条数据",
            };
          }
        }

        return {
          data: true,
        };
      };

      // 根据容器id找出其中需要做重复性校验的字段
      let getRepeatWidget = (id) => {
        let widgetList = [];
        let dfs = (root) => {
          root.forEach((item) => {
            widgetList.push(item);
            if ("widgetList" in item) {
              dfs(item.widgetList);
            }
          });
        };
        dfs(this.widgetList);
        // 需要校验重复的字段
        return widgetList
          .find((widgetItem) => widgetItem.id === id)
          ?.widgetList.filter((item) => item.options.validation === "noRepeat")
          .map((item) => item.id);
      };

      this.$refs["renderForm"].validate((valid) => {
        if (valid) {
          let result = validateRepeat();
          if (!result.data) {
            callback(this.formDataModel, result.message);
            return;
          }
          callback(this.formDataModel);
        } else {
          callback(
            this.formDataModel,
            this.i18nt("render.hint.validationFailed")
          );
        }
      });

      return promise;
    },

    setFormData(formData) {
      // 将值设置给全局变量
      if (this.formConfig.store) {
        for (let item in this.formConfig.store) {
          this.$set(this.formConfig.store, item, deepClone(formData[item]));
        }
      }

      // 设置表单数据
      // this.formDataModel = formData //inject注入的formModel不是响应式的，直接赋值在其他组件拿不到最新值！！

      Object.keys(this.formDataModel).forEach((propName) => {
        if (!!formData && formData.hasOwnProperty(propName)) {
          this.formDataModel[propName] = deepClone(formData[propName]);
        }
      });
      // this.formDataModel = formData
      // this._provided.globalModel.formModel = formData  /* 这种写法可使inject的属性保持响应式更新！！ */
      //

      // 通知SubForm组件：表单数据更新事件！！
      // this.broadcast('ContainerItem', 'setFormData', formData)
      this.broadcast("ContainerItem", "setFormData", this.formDataModel);

      // 通知FieldWidget组件：表单数据更新事件！！
      // this.broadcast('FieldWidget', 'setFormData', formData)
      this.broadcast("FieldWidget", "setFormData", this.formDataModel);
    },

    getFieldValue(fieldName) {
      // 单个字段获取值
      let fieldRef = this.getWidgetRef(fieldName);
      if (!!fieldRef && !!fieldRef.getValue) {
        fieldRef.getValue();
      }
    },

    setFieldValue(fieldName, fieldValue) {
      // 单个更新字段值
      let fieldRef = this.getWidgetRef(fieldName);
      if (!!fieldRef && !!fieldRef.setValue) {
        fieldRef.setValue(fieldValue);
      }
    },

    getSubFormValues(subFormName, needValidation = true) {
      let foundSFRef = this.subFormRefList[subFormName];
      // if (!foundSFRef) {
      //   return this.formDataModel[subFormName]
      // }
      return foundSFRef.getSubFormValues(needValidation);
    },

    disableForm() {
      // 禁用整个表单
      let wNameList = Object.keys(this.widgetRefList);
      wNameList.forEach((wName) => {
        let foundW = this.getWidgetRef(wName);
        if (foundW) {
          // if (!!foundW.setDisabled) {
          //   foundW.setDisabled(true)
          // }

          !!foundW.setDisabled && foundW.setDisabled(true);
        }
      });
      this.hasRead = true;
    },

    enableForm() {
      // 启用整个表单
      let wNameList = Object.keys(this.widgetRefList);
      wNameList.forEach((wName) => {
        let foundW = this.getWidgetRef(wName);
        if (foundW) {
          // if (!!foundW.setDisabled) {
          //   foundW.setDisabled(false)
          // }

          !!foundW.setDisabled && foundW.setDisabled(false);
        }
      });
      this.hasRead = false;
    },

    resetForm() {
      // 重置表单
      let subFormNames = Object.keys(this.subFormRefList);
      subFormNames.forEach((sfName) => {
        if (this.subFormRefList[sfName].resetSubForm) {
          this.subFormRefList[sfName].resetSubForm();
        }
      });

      let wNameList = Object.keys(this.widgetRefList);
      wNameList.forEach((wName) => {
        let foundW = this.getWidgetRef(wName);
        if (!!foundW && !!foundW.resetField) {
          foundW.resetField();
        }
      });

      this.$nextTick(() => {
        this.clearValidate(); /* 清除resetField方法触发的校验错误提示 */
      });
    },

    clearValidate(props) {
      this.$refs.renderForm.clearValidate(props);
    },

    validateForm() {
      //
    },

    validateFields() {
      //
    },

    disableWidgets(widgetNames) {
      if (widgetNames) {
        if (typeof widgetNames === "string") {
          this.findWidgetAndSetDisabled(widgetNames, true);
        } else if (Array.isArray(widgetNames)) {
          widgetNames.forEach((wn) => {
            this.findWidgetAndSetDisabled(wn, true);
          });
        }
      }
    },

    enableWidgets(widgetNames) {
      if (widgetNames) {
        if (typeof widgetNames === "string") {
          this.findWidgetAndSetDisabled(widgetNames, false);
        } else if (Array.isArray(widgetNames)) {
          widgetNames.forEach((wn) => {
            this.findWidgetAndSetDisabled(wn, false);
          });
        }
      }
    },

    hideWidgets(widgetNames) {
      if (widgetNames) {
        if (typeof widgetNames === "string") {
          this.findWidgetAndSetHidden(widgetNames, true);
        } else if (Array.isArray(widgetNames)) {
          widgetNames.forEach((wn) => {
            this.findWidgetAndSetHidden(wn, true);
          });
        }
      }
    },

    showWidgets(widgetNames) {
      if (widgetNames) {
        if (typeof widgetNames === "string") {
          this.findWidgetAndSetHidden(widgetNames, false);
        } else if (Array.isArray(widgetNames)) {
          widgetNames.forEach((wn) => {
            this.findWidgetAndSetHidden(wn, false);
          });
        }
      }
    },

    // --------------------- 以上为组件支持外部调用的API方法 end ------------------//
    closePreviewDialog() {
      let that = this
      while (that.$options?.name !== 'previewDialog') {
        if (that.$options?.name === 'App') {
          break
        }
        that = that.$parent;
      }
      that.close()
    }
  },
};
</script>

<style lang="scss" scoped>
.el-form ::v-deep .el-row {
  padding: 8px;
}

::v-deep .el-select {
  width: 100%;
}

// ::v-deep .el-textarea.is-disabled .el-textarea__inner {
//   color: black !important;
// }

// ::v-deep .el-input.is-disabled .el-input__inner {
//   color: black !important;
// }
</style>
<style lang="scss">
  @import url(../../assets/styles/introjs.scss);
</style>
