<template>
  <div>
    <el-dialog title="查看" :visible.sync="lonTextDialogVisible" width="40%">
      <div style="height: 400px;padding: 20px 20px 0 20px;overflow-y: auto;" id="proxyImage" @click="proxyImage">
        <span v-html="longText"></span>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="lonTextDialogVisible = false">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog @close="fileDialogClose" v-drag-dialog :style="[{height:dialogHeight + 'px'},{'z-index':999999999}]" :visible.sync="show" :close-on-click-modal="false" :width="dialogWidth + 'px'" class="dialog" center append-to-body>
      <div slot="title" class="images-prview-header">
        {{ title || '文件预览' }}
      </div>
      <div id="prviewArea" element-loading-background="transparent">
        <div id="actionArea" v-loading="loading" style="width: 100%;height: 100%;background-color: #fff;">
          <el-tabs type="border-card" v-if="fileArray.length > 0" style="width: 100%;height: 100%" @tab-click="handleClick">
            <el-tab-pane :key="index" v-for="(item, index) in fileArray" :label="item.fileName">
              <iframe :src="item.url" v-if="index === tabIndex" style="width: 100%;height:100%;border: 0;margin:0;"></iframe>
            </el-tab-pane>
          </el-tabs>

          <el-empty :image-size="300" v-else></el-empty>
        </div>
      </div>
    </el-dialog>

    <el-image-viewer style="z-index: 2222;" v-if="imgViewerVisible" :on-close="()=>{imgViewerVisible=false}" :url-list="imgList" />
  </div>
</template>

<script>
import { getFile } from '@/api/file/file'
import { Base64 } from "js-base64";
import store from "@/store"
export default {
  components: { 'el-image-viewer': () => import('element-ui/packages/image/src/image-viewer') },
  data() {
    return {
      fileArray: [],
      activeName: 'second',
      dialogWidth: 950,
      dialogHeight: 800,
      show: false,
      tabIndex: 0,
      scale: 100,
      mouseDownFlag: false,
      direction: 0, // 图片方向
      currIndex: 0,
      images: [],
      loading: false,
      loadingFail: false,
      title: '文件预览',
      isAsync: false, // 是否进行异步请求图片
      urlFunc: [], // 异步获取方法
      longText: "",
      lonTextDialogVisible: false,
      imgViewerVisible: false,
      imgList: []
    }
  },
  methods: {
    handleClick(tab, event) {
      this.tabIndex = parseInt(tab.index);
    },
    setTitle(title) {
      this.title = title
    },
    proxyImage: function (e) {
      if (e.target.tagName.toUpperCase() === 'IMG') {
        this.imgList = [];
        this.imgList.push(e.target.src)
        this.imgViewerVisible = true
      }
    },
    openUid(uid, width, height) {
      this.dialogWidth = width || window.innerWidth;
      this.dialogHeight = height || window.innerHeight;
      this.fileArray = []
      this.getData({ "uid": uid })
      return this
    },
    open(fileId, width, height) {
      this.dialogWidth = width || window.innerWidth;
      this.dialogHeight = height || window.innerHeight;

      this.fileArray = []
      this.getData({ "fileId": fileId })

      return this
    },
    previewLongText(text){
      this.longText = text;
      this.lonTextDialogVisible = true;
    },
    fileDialogClose() {
      this.$emit("close")
      this.show = false
    },
    openByUrl(pathName, fileName, width, height){
      this.fileArray = []
      this.dialogWidth = width || window.innerWidth;
      this.dialogHeight = height || window.innerHeight;
      let uploadUrl = store.state.app.uploadUrl + pathName;
      let param = encodeURIComponent(Base64.encode(uploadUrl))
      let url = `${store.state.app.uploadUrl}/file/onlinePreview?url=${param}`

      // ------ 本地环境
      // let uploadUrl = `http://127.0.0.1:8080${pathName}`;
      // let param = encodeURIComponent(Base64.encode(uploadUrl))
      // let url = `http://127.0.0.1:8080/file/onlinePreview?url=${param}`
      console.log("kk")
      this.fileArray.push({ url: url, fileName: fileName || '文件预览'})
      this.show = true
    },
    getData(param) {
      getFile(param).then((res) => {
        if (res.code === 200 && res.data.length) {
          let files = res.data.filter((item, index, self) => {
            if (!item.fileMdFive) {
              return true; // 保留空的项
            } else {
              return index === self.findIndex(t => t.fileMdFive === item.fileMdFive);
            }
          });

          files.forEach(item => {
            let uploadUrl = store.state.app.uploadUrl + item.pathName;
            let param = encodeURIComponent(Base64.encode(uploadUrl))
            let url = `${store.state.app.uploadUrl}/file/onlinePreview?url=${param}`

            // ------ 本地环境
            // let uploadUrl = `http://127.0.0.1:8080${item.pathName}`;
            // let param = encodeURIComponent(Base64.encode(uploadUrl))
            // let url = `http://127.0.0.1:8080/file/onlinePreview?url=${param}`
            console.log("kk")
            this.fileArray.push({ url: url, fileName: item.fileName })
          })
          this.show = true
        } else {
          this.show = true
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.image-name {
  font-size: 14px;
  // margin-left:10px;
}

::v-deep #proxyImage img {
  width: 300px;
  cursor: pointer;
}

#prviewArea {
  background-color: #f5f5f5;
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;

  #actionArea {
    position: relative;
    user-select: none;
    transition: transform 0.3s;

    #actionImg {
      cursor: url(./icons/mouseOpen.svg), auto;
    }
  }

  &:hover {
    .tools {
      transition-delay: 0.3s;
      top: 5px;
    }

    .pre-img,
    .next-img {
      opacity: 1;
      display: flex;
    }
  }

  .mark {
    position: absolute;
    bottom: 10px;
    display: inline-block;
    margin: 0 auto;
    box-shadow: 0px 0px 7px rgba(0, 0, 0, 0.2),
      0px 0px 0px 1px rgba(188, 188, 188, 0.1);
    // height:30px;
    padding: 3px 10px;
    font-size: 12px;
    border-radius: 12px;
    width: auto;
    background: #fff;
  }

  .pre-img,
  .next-img {
    position: absolute;
    opacity: 0;
    display: none;
    transition: opacity 0.2s;
    top: calc(50% - 20px);
  }

  .pre-img {
    left: 5px;
  }

  .next-img {
    right: 5px;
  }

  .icon {
    float: left;
    color: #fff;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    user-select: none;
    // margin-right: 10px;
    transform: scale(0.8);
    background: rgba(0, 0, 0, 0.3);

    span {
      font-size: 24px;
    }

    &:hover {
      background-color: rgba(80, 77, 77, 0.6);
      box-shadow: 0px 0px 3px rgba(80, 77, 77, 0.6);
    }

    &:active {
      box-shadow: 0px 0px 1px rgba(80, 77, 77, 0.6);
      background-color: rgba(80, 77, 77, 0.75);
      transform: scale(0.79) translate3d(1px, 1px, 0px) !important;
    }

    .scale-icon {
      font-size: 12px;
    }
  }

  .tools {
    position: absolute;
    display: inline-block;
    height: 40px;
    top: -50px;
    width: auto;
    right: 10px;
    transition: top 0.2s;

    z-index: 999999;
  }
}

::v-deep .el-tabs--border-card > .el-tabs__content {
  padding: 15px;
  height: 100% !important;
}

::v-deep .el-tab-pane {
  height: 94% !important;
}

::v-deep .el-dialog__body {
  overflow: hidden;
  width: 100%;
  height: 96%;
  flex: inherit !important;
  padding-top: 0 !important;
  padding-right: 0 !important;
  padding-bottom: 0 !important;
  padding-left: 0 !important;
}

::v-deep .el-dialog--center {
  overflow-y: hidden;
  width: 950px;
  height: 98% !important;
}

.images-prview-header {
  text-align: left;
}

::v-deep .el-dialog__headerbtn {
  top: 15px;
}

::v-deep .el-dialog__header {
  padding: 10px 20px 10px;
}
</style>
