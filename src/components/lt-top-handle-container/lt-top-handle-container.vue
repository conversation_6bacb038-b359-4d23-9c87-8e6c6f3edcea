<template>
  <div class="lt-container">
    <div class="header">
      <div class="left pointer" style="width: 100px;" @click="back">
        <span class="iconfont">&#xe610;</span>
        <span class="text">返回</span>
      </div>
      <div class="flex-1 text-center font-14" style="font-weight: 600;">
        {{ stateTitleComp }}{{ title }}
      </div>
      <div :style="{width:rightWidth}" class="d-flex j-end">
        <div v-if="!('right' in $scopedSlots)" class="defaultSlot pointer" @click="$emit('rightClick')">
          <span class="iconfont font-14">&#xe60b;</span>
          <span class="text">保存</span>
        </div>
        <slot v-else name="right" :state="state" :title="title" />
      </div>
    </div>
    <div class="pd-15">
      <el-scrollbar wrap-style="overflow-x:hidden;">
        <div v-if="isWhole" class="bg-white radius-4" style="min-height: calc(100vh - 160px); ">
          <slot />
        </div>
        <slot v-else />
      </el-scrollbar>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    // 是否为一个整体
    isWhole: {
      type: Boolean,
      default: true
    },
    // 中间标题
    title: {
      type: String,
      default: ''
    },
    // 组件状态,info查看 edit编辑 add添加
    state: {
      type: String,
      default: ''
    },
    // 右侧插槽宽度
    rightWidth: {
      type: String,
      default: '100px'
    }
  },
  computed: {
    stateTitleComp() {
      if (this.state === 'info') return '查看'
      if (this.state === 'edit') return '编辑'
      if (this.state === 'add') return '添加'
      return ''
    }
  },
  methods: {
    back() {
      this.$emit('back', false)
    }
  }
}
</script>

<style lang="scss" scoped="scoped">
  .header {
    color: #364760;
    display: flex;
    align-items: center;
    background-color: #FFF;
    height: 45px;
    padding: 0 15px;
    .left, .defaultSlot{
      &:hover {
        color: var(--primary-color);
      }
    }

    .text {
      font-size: 14px;
      padding-left: 5px;
    }
  }
  ::v-deep .el-form-item__label{
    font-weight: normal;
  }
  .pd-15{
    height:calc(100vh - 130px)
  }
  .el-scrollbar{
    height: 100%;
  }
  .el-scrollbar__wrap {
    // overflow: scroll;
    // width: 110%;
    // height: 120%;
  }
  ::v-deep.el-scrollbar__thumb{
    display: none !important;
  }
</style>
