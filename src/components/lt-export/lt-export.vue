<template>

    <JsonExcel
      :fields="fields"
      :fetch="exportDataUpdate"
      :name="xlsNameCom"
    >
      <slot>
        <el-button    icon="el-icon-upload2" type="primary">
          {{ title }}
        </el-button>
      </slot>
    </JsonExcel>
</template>

<script>
// https://blog.csdn.net/snsHL9db69ccu1aIKl9r/article/details/109212910
// https://www.npmjs.com/package/vue-json-excel
import JsonExcel from 'vue-json-excel'
export default {
  name: 'LtExport',
  components: {
    JsonExcel
  },
  props: {
    data: {
      type: Array,
    },
    fields: {
      type: Object
    },
    title: {
      type: String,
      default: '导出'
    },
    xlsName: {
      type: String,
      default:''
    },
    updateExportData: {
      type: Function,
      default: null
    }
  },
  computed: {
    xlsNameCom(){
      if(!this.xlsName){
        let result = this.xlsName
        var menus = this.$store.state.tagsView.visitedViews
        menus.forEach(item => {
          if (item.fullPath === this.$route.fullPath) {
            result = item.title + '数据导出'
          }
        })
        return result
      } else {
        return this.xlsName
      }
    }
  },
  methods:{
    async exportDataUpdate(){
      if (typeof this.updateExportData === 'function') {
        return this.updateExportData()
      }
      return this.data;
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
