<template>
  <div :class="className" :style="{ height: height, width: width }" />
</template>

<script>
import * as echarts from 'echarts';
import resize from "./mixins/resize";
import chartNoData from "@/assets/images/chartNoData.png";
import elementResizeDetectorMaker from "element-resize-detector";
import { debounce } from "@/utils/decorator.js";
export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: "chart",
    },
    width: {
      type: String,
      default: "100%",
    },
    height: {
      type: String,
      default: "300px",
    },
    loadingText: {
      type: String,
      default: "数据正在努力加载...",
    },
    theme: {
      type: String,
      default: "macarons",
    },
  },
  // watch: {
  //   theme(val) {
  //     this.initChart();
  //   },
  // },
  data() {
    return {
      chart: null,
    };
  },
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.dispose();
    this.chart = null;
  },
  mounted() {
    document.querySelector(".chart").style.width = this.width;
    // this.chart = echarts.init(this.$el, this.theme);
    this.showEmpty();

    const _this = this;
    elementResizeDetectorMaker().listenTo(
      document.getElementsByClassName(this.className),
      (element) => {
        _this.$nextTick(() => {
          _this.changeReSize();
        });
      }
    );
  },
  methods: {
    getChartObj() {
      return this.chart;
    },
    setOption(option) {
      this.chart.setOption(option, true);
    },
    /**
     * 初始化
     * @param {Object} option
     */
    initChart(option) {
      try {
        require(`echarts/theme/${this.theme}`);
        console.log('echarts',echarts)
        this.chart = echarts.init(this.$el, this.theme);
        // this.chart.clear();
        this.hideLoading();

        this.chart.setOption(option, true);
        this.$nextTick(() => {
          this.chart.resize();
        });
      } catch (e) {console.log(e)}
    },
    /**
     * 显示loading
     */
    showLoading() {
      this.chart.showLoading({
        text: this.loadingText,
        color: this.$store.state.settings.theme,
        lineWidth: 2,
      });
    },
    /**
     * 隐藏loading
     */
    hideLoading() {
      this.chart.hideLoading();
    },
    /**
     * 显示空布局
     */
    showEmpty() {
      this.initChart({
        title: {
          text: ["{a|}", "{b|暂无数据}"].join("\n"),
          x: "center",
          y: "center",
          textStyle: {
            rich: {
              a: {
                backgroundColor: {
                  image: chartNoData,
                },
                height: 40,
              },
              b: {
                color: "#C2C7CF",
              },
            },
          },
        },
      });
    },
    @debounce(100) // 防抖
    changeReSize() {
      // 监听到事件后执行的业务逻辑
      this.resize();
    },
  },
};
</script>
