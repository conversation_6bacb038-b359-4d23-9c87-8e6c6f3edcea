// 全局安装文件
import Vue from 'vue'

import printBarcodeTagDialog from '@/views/tool/excelreport/viewer/printBarcodeTagDialog.vue'

setTimeout(() => {
  const codePrint = Vue.extend(printBarcodeTagDialog)
  // eslint-disable-next-line new-cap
  const instance = new codePrint()
  instance.vm = instance.$mount()
  document.body.appendChild(instance.vm.$el)
  Vue.prototype.$barcodePrint = instance.vm
}, 500)
