<template>
  <el-table
    v-bind="$attrs"
    :data="value.filter(e=>(filterProp&&filterString)?(filterProp.includes('.')?e[filterProp.split('.')[0]][filterProp.split('.')[1]]:e[filterProp]).includes(filterString):true)"
    :cust-sort-table-id="id"
    v-on="$listeners"
    ref="tableRef"
    :key="custKey"
  >
    <template slot="empty">
      <lt-empty />
    </template>
    <el-table-column
      class-name="wards-move"
      label="顺序"
      width="60"
      v-if="isEnable"
    >
      <span class="iconfont" style="cursor: move">&#xed41;</span>
    </el-table-column>
    <slot />
  </el-table>
</template>

<script>
import Sortable from 'sortablejs';
// import * as API from '@/api/columnEditApi.js'
export default {
  name: 'SortTable',
  data() {
    return {
      id: 'sortTable-' + this.$string.getUUID(),
    };
  },
  props: {
    value: {
      type: Array,
      default: () => [],
    },
    // id: {
    //   type: String,
    //   default: 'sortTable'
    // },
    // 排序字段
    sortField: {
      type: String,
      default: 'sortId',
    },
    // 是否启用排序功能
    isEnable: {
      type: Boolean,
      default: false,
    },
    custKey: {
      type: Number | String,
      default: '',
    },
    filterProp:{
      type:String,
      default: '',
    },
    filterString:{
      type:String,
      default: '',
    },
  },
  watch: {
    isEnable: {
      immediate: true,
      handler() {
        this.rowDrop();
      },
      deep: true,
    },
    custKey: {
      immediate: true,
      handler() {
        this.rowDrop();
      },
      deep: true,
    },
  },
  mounted() {
    this.rowDrop();
  },
  methods: {
    doLayout() {
      this.$nextTick(() => {
        this.$refs.tableRef && this.$refs.tableRef.doLayout();
      });
    },
    rowDrop() {
      if (this.isEnable) {
        this.$nextTick(() => {
          const tbody = $(`div[cust-sort-table-id=${this.id}] tbody`)[0];
          let oldData = JSON.parse(JSON.stringify(this.value));
          Sortable.create(tbody, {
            handle: '.wards-move',
            animation: 150,
            onEnd: ({ oldIndex, newIndex }) => {
              // 缓存数据数组赋值给实际数据数组
              this.value.splice(newIndex, 0, this.value.splice(oldIndex, 1)[0]);
              const tableData = this.value.slice(0);
              // this.$emit('input', [])
              this.$nextTick(() => {
                tableData.forEach((item, index) => {
                  this.$set(item, this.sortField, index + 1);
                });
                this.$emit('input', tableData);
                this.$emit('change', oldData, tableData);
              });
            },
          });
        });
      }
    },
  },
};
</script>

<style></style>
