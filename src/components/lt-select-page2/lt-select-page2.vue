<template>
  <el-select
    v-model="dataValue"
    :loading="loading"
    reserve-keyword
    :filter-method="filterMethod"
    filterable
    v-bind="$attrs"
    :multiple="multiple"
    @change="change"
  >
    <el-option
      v-for="item in selectList"
      :key="item[selectLabel] + item[selectValue]"
      :label="item[selectLabel]"
      :value="item[selectValue]"
    />
    <div class="d-flex pd-lr20 a-center">
      <el-button
        :disabled="current === 1"
        type="text"
        icon="el-icon-arrow-left"
        @click="switchPages('up')"
      >
        上一页
      </el-button>
      <div class="flex-1 font-13 d-flex a-center j-center">
        <span style="color: #606266;">{{ current }}</span>/<span style="color: var(--primary-color);">{{ totalPages }}</span>
      </div>
      <el-button
        :disabled="totalPages === current"
        type="text"
        @click="switchPages('down')"
      >
        下一页<i class="el-icon-arrow-right el-icon--right" />
      </el-button>
    </div>
  </el-select>
</template>

<script>
import { executeInterface } from '@/api/interfaces/interfaces'
export default {
  name: 'LtSelectPage',
  props: {
    value: null,
    // select的label
    selectLabel: {
      type: String,
      default: 'label'
    },
    // select的value
    selectValue: {
      type: String,
      default: 'value'
    },
    // 筛选项传递给后端的key值
    searchKey: {
      type: String,
      default: 'name'
    },
    // 当前选中的id
    choiceId: {
      type: [Number, Array, String]
    },
    // 自动加载
    auto: {
      type: Boolean,
      default: true
    },
    // 模式,分为2种,钟宇星(new)写的取data.pages, 周敏(old)写的取total(后面他会改)
    mode: {
      type: String,
      default: 'new'
    },
    apiId: {
      type: String
    },
    multiple: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dataValue: null,
      loading: false,
      selectList: [], // select数据
      current: 1, // 当前页码
      size: 10, // 每页的数量
      itemName: '', // 筛选关键字
      totalPages: 1 // 总页数
    }
  },
  watch: {
    value: {
      handler(val) {
        this.dataValue = val
      },
      immediate: true
    },
    apiId: {
      handler(val) {
        this.current = 1
        this.getSelectPage()
      },
      immediate: true
    }
  },
  mounted() {
    // if (this.auto && this.apiId) this.getSelectPage()
  },
  methods: {
    change(val) {
      console.log(val)
      this.$emit('input', val)
      this.$emit('change', this.multiple ? val : this.selectList.filter(item => item[this.selectValue] === val)[0])
    },
    /**
     * 数据的模糊查询
     */
    filterMethod(query) {
      this.loading = true
      this.current = 1
      this.size = 10
      this.itemName = query
      this.getSelectPage()
    },
    /**
     * 查询下拉分页的数据
     * @param {Object} choiceId   当前选中的id
     */
    getSelectPage(choiceId = '') {
      if (!this.apiId) {
        this.selectList = []
        this.totalPages = 0
        return
      }
      this.loading = true

      let getChoiceId = () => {
        let temp = choiceId || this.choiceId
        if (this.multiple) {
          return Array.isArray(temp) ? temp.join(',') : temp
        } else {
          return temp
        }
      }

      let data = {
        pageNum: this.current,
        pageSize: this.size,
        choiceId: this.current === 1 ? getChoiceId() : '',
        itemName: this.itemName
      }
      executeInterface({
        apiId: this.apiId,
        body: data
      }).then(res => {
        if (this.mode === 'new') {
          this.selectList = res.data.records
          this.totalPages = res.data.pages
        }
        if (this.mode === 'old') {
          this.selectList = res.rows
          this.totalPages = Math.ceil(res.total / this.size)
        }
        // if (this.dataValue) this.change(this.dataValue)
        this.valueToStr()
        this.loading = false
      }).catch(e => {
        this.loading = false
      })
    },
    /**
     * 切换页面,
     * upper为上一页
     * lower为下一页
     */
    switchPages(direction) {
      direction === 'up' ? this.current-- : this.current++
      this.getSelectPage()
    },
    // 将item选项中的value的类型转换成String类型
    valueToStr() {
      if (this.selectList && this.selectList.length > 0) {
        this.selectList.forEach(item => {
          if (item[this.field.options.selectValue] && typeof item[this.field.options.selectValue] == 'number') {
            item[this.field.options.selectValue] = item[this.field.options.selectValue].toString()
          }
        })

      }
    },
  }
}
</script>

<style lang="scss" scoped>
</style>
