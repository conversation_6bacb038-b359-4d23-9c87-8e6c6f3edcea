<template>
  <div class="ace-container" style="position: relative">
    <el-tooltip content="代码格式化(Ctrl+Shift+F)">
      <svg-icon
        icon-class="code"
        style="
          position: absolute;
          top: 5px;
          right: 5px;
          z-index: 999;
          cursor: pointer;
        "
        @click.native="format"
      ></svg-icon>
    </el-tooltip>

    <el-tooltip content="保存">
      <svg-icon
        icon-class="提交"
        v-if="showSave"
        style="
          position: absolute;
          top: 25px;
          right: 5px;
          z-index: 999;
          cursor: pointer;
        "
        @click.native="$emit('save')"
      ></svg-icon>
    </el-tooltip>

    <!-- 官方文档中使用id，这里禁止使用，在后期打包后容易出现问题，使用 ref 或者 DOM 就行 -->
    <div
      class="ace-editor"
      :style="{ minHeight: height }"
      ref="ace"
      @keydown.ctrl.shift.70="format"
    ></div>
  </div>
</template>

<script>
import ace from "ace-builds";
/* 启用此行后webpack打包回生成很多动态加载的js文件，不便于部署，故禁用！！
     特别提示：禁用此行后，需要调用ace.config.set('basePath', 'path...')指定动态js加载URL！！
   */
//import 'ace-builds/webpack-resolver'

// import 'ace-builds/src-min-noconflict/theme-monokai' // 默认设置的主题
import "ace-builds/src-min-noconflict/theme-sqlserver"; // 新设主题
import "ace-builds/src-min-noconflict/mode-javascript"; // 默认设置的语言模式
import "ace-builds/src-min-noconflict/mode-json"; //
import "ace-builds/src-min-noconflict/mode-css"; //
import "ace-builds/src-min-noconflict/mode-sql"; //
import "ace-builds/src-min-noconflict/ext-language_tools";
import { ACE_BASE_PATH } from "@/utils/config";
import beautify from "js-beautify";
export default {
  name: "CodeEditor",
  props: {
    value: {
      type: String,
      required: true,
    },
    readonly: {
      type: Boolean,
      default: false,
    },
    mode: {
      type: String,
      default: "javascript",
    },
    userWorker: {
      //是否开启语法检查，默认开启
      type: Boolean,
      default: true,
    },
    maxLines: {
      type: Number,
      default: 36,
    },
    height: {
      type: String,
      default: "500px",
    },
    showSave: {
      type: Boolean,
      default: false,
    },
  },
  mounted() {
    //ace.config.set('basePath', 'https://ks3-cn-beijing.ksyun.com/vform2021/ace')
    ace.config.set("basePath", ACE_BASE_PATH);
    //path 这个文件在启动服务后,需要你直接在浏览器里面能访问到，否则任然报错

    ace.config.setModuleUrl("ace/mode/javascript_worker", "/worker.js");

    this.aceEditor = ace.edit(this.$refs.ace, {
      maxLines: this.maxLines, // 最大行数，超过会自动出现滚动条
      minLines: 5, // 最小行数，还未到最大行数时，编辑器会自动伸缩大小
      fontSize: 12, // 编辑器内字体大小
      theme: this.themePath, // 默认设置的主题
      mode: this.modePath, // 默认设置的语言模式
      tabSize: 2, // 制表符设置为2个空格大小
      readOnly: this.readonly,
      highlightActiveLine: true,
      value: this.codeValue,
    });

    this.aceEditor.setOptions({
      // userWorker: this.userWorker,
      enableBasicAutocompletion: true,
      enableSnippets: true, // 设置代码片段提示
      enableLiveAutocompletion: true, // 设置自动提示
      // enableBehaviours: true,
    });
    this.addAutoCompletion(ace); //添加自定义代码提示！！
    if (this.mode === "json") {
      this.setJsonMode();
    } else if (this.mode === "css") {
      this.setCssMode();
    }

    //编辑时同步数据
    this.aceEditor.getSession().on("change", (ev) => {
      //this.$emit('update:value', this.aceEditor.getValue())  // 触发更新事件, 实现.sync双向绑定！！
      this.$emit("input", this.aceEditor.getValue());
    });
  },
  data() {
    return {
      aceEditor: null,
      themePath: "ace/theme/sqlserver", // 不导入 webpack-resolver，该模块路径会报错
      modePath: `ace/mode/${this.mode}`, // 同上
      codeValue: this.value,
      ids: "11",
    };
  },
  methods: {
    format() {
      let code = beautify(this.aceEditor.getValue(), {
        indent_size: 2, //缩进两个空格
        space_in_empty_paren: true,
      });
      this.aceEditor.setValue(code);
    },
    addAutoCompletion(ace) {
      let acData = [
        {
          meta: "获取指定组件Vue对象",
          caption: "getWidgetRef",
          value: "getWidgetRef()",
          score: 2,
        },
        {
          meta: "获取指定组件ref对象",
          caption: "getFormRef",
          value: "getFormRef()",
          score: 2,
        },
        {
          meta: "数据表格刷新数据",
          caption: "updateRefresh",
          value: "updateRefresh()",
          score: 1,
        },
        {
          meta: "获取组件值",
          caption: "getValue",
          value: "getValue()",
          score: 1,
        },
        {
          meta: "获取组件值",
          caption: "$getValue",
          value: "$getValue()",
          score: 1,
        },
        {
          meta: "设置组件值",
          caption: "$setValue",
          value: "$setValue()",
          score: 1,
        },
        {
          meta: "设置组件值(不会触发onChange)",
          caption: "setValue",
          value: "setValue()",
          score: 1,
        },
        {
          meta: "改变组件值(触发onChange)",
          caption: "changeValue",
          value: "changeValue()",
          score: 1,
        },
        {
          meta: "发送http请求",
          caption: "getRequest",
          value: `
this.getRequest({
  url: '/interfaces/*****',
  method: 'get/post/delete/put',
  data: {
    name: 'xxxxx'
  },
  params: {
    name: '12312312'
  }
}).then(res => {
  console.log(res)
}).catch(err => {
  console.log(err)
})
          `,
          score: 1,
        },
        {
          meta: "初始化图表数据",
          caption: "initChart",
          value: "initChart()",
          score: 3,
        },
        {
          meta: "设置弹窗是否显示",
          caption: "setDialogVisible",
          value: "setDialogVisible()",
          score: 1,
        },
        {
          meta: "获取循环组件当前列的下标和列表数据",
          caption: "message",
          value: "getParentIndexItem()",
          score: 1,
        },
        {
          meta: "获取组织树当前点击的节点",
          caption: "getClickNode",
          value: "getClickNode()",
          score: 1,
        },
        {
          meta: "获取组织树当前选中的节点",
          caption: "getCheckNodes",
          value: "getCheckNodes()",
          score: 1,
        },
        { meta: "打印", caption: "console", value: "console.log()", score: 1 },
        {
          meta: "请求接口(异步)",
          caption: "asyncHttp",
          value: `this.asyncHttp(apiId, {}).then(res => {

}).catch(err => {
  console.log(err)
})`,
          score: 1,
        },
        {
          meta: "请求接口(同步)",
          caption: "waitHttp",
          value: `// 同步请求,会阻塞代码继续向下执行
let res = this.waitHttp(apiId, {})`,
          score: 1,
        },
        {
          meta: "成功提示",
          caption: "this.$message.success",
          value: `this.$message.success()`,
          score: 1,
        },
        {
          meta: "错误提示",
          caption: "this.$message.error",
          value: `this.$message.error()`,
          score: 1,
        },
        {
          meta: "警告提示",
          caption: "this.$message.warning",
          value: `this.$message.warning()`,
          score: 1,
        },
        {
          meta: "信息提示",
          caption: "this.$message.info",
          value: `this.$message.info()`,
          score: 1,
        },
         {
          meta: "加载方法",
          caption: "this.$setLoading",
          value: `/**
 * @param { id }    String   传入组件id则为局部加载，否则为全屏加载，该方法返回一个实例，可调用该实例的close方法关闭加载
 */
this.$setLoading(id)`,
          score: 1,
        },
        {
          meta: "轮询任务",
          caption: "this.$setInterval",
          value: `/**
 * @param { name }    String   定时器名称，当有多个定时器时保证每个名称唯一，如果定时器在弹出层内使用，传入弹出层组件的id！
 * @param { callback }  Function 回调函数
 * @param { time }  Number   定时器时间，单位为毫秒
 */
this.$setInterval(name,callback,time)`,
          score: 1,
        },
        //TODO: 待补充！！
      ];
      let langTools = ace.require("ace/ext/language_tools");
      langTools &&
        langTools.addCompleter({
          getCompletions: function (editor, session, pos, prefix, callback) {
            if (prefix.length === 0) {
              return callback(null, []);
            } else {
              return callback(null, acData);
            }
          },
        });
    },

    setJsonMode() {
      this.aceEditor.getSession().setMode("ace/mode/json");
    },

    setCssMode() {
      this.aceEditor.getSession().setMode("ace/mode/css");
    },
  },
};
</script>

<style lang="scss" scoped>
// ::v-deep .ace-editor {
//   height: 504px !important;
// }
// .ace-editor {
//  min-height: 500px;
// }
</style>
