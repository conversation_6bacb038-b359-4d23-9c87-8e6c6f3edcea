<template>
  <div class="el-x-conversations-container" :style="styleConfig">
    <div class="el-x-conversations-list">
      <div class="el-x-conversations-scroll-wrapper">
        <div class="scroll-content">
          <div
            v-for="(item, index) in items"
            :key="item.id || index"
            :class="[
              'conversation-item',
              { 'active': item.id === active },
              { 'group-header': item.isGroupHeader }
            ]"
            @click="handleItemClick(item)"
            @contextmenu.prevent="handleContextMenu($event, item)"
          >
            <!-- 分组标题 -->
            <div v-if="item.isGroupHeader" class="group-title">
              {{ item.label }}
            </div>
            
            <!-- 普通对话项 -->
            <div v-else class="conversation-content">
              <div class="conversation-main">
                <i v-if="item.prefixIcon" :class="item.prefixIcon" class="prefix-icon"></i>
                <span class="conversation-label" :style="{ maxWidth: labelMaxWidth + 'px' }">
                  {{ item.label }}
                </span>
              </div>
              
              <!-- 菜单按钮 -->
              <div v-if="showBuiltInMenu" class="conversation-menu">
                <el-dropdown 
                  @command="handleMenuCommand"
                  trigger="click"
                  placement="bottom-end"
                >
                  <span class="menu-trigger">
                    <i class="el-icon-more"></i>
                  </span>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item
                      v-for="menuItem in menu"
                      :key="menuItem.key"
                      :command="{ command: menuItem.command, item: item }"
                      :style="menuItem.menuItemHoverStyle"
                    >
                      <i v-if="menuItem.icon" :class="menuItem.icon"></i>
                      {{ menuItem.label }}
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ElXConversations',
  props: {
    items: {
      type: Array,
      default: () => []
    },
    active: {
      type: [String, Number],
      default: null
    },
    styleConfig: {
      type: Object,
      default: () => ({})
    },
    showBuiltInMenu: {
      type: Boolean,
      default: false
    },
    labelMaxWidth: {
      type: Number,
      default: 200
    },
    menu: {
      type: Array,
      default: () => []
    },
    groupable: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    handleItemClick(item) {
      if (!item.isGroupHeader) {
        this.$emit('change', item.id);
      }
    },
    
    handleContextMenu(event, item) {
      // 处理右键菜单
      event.preventDefault();
    },
    
    handleMenuCommand(command) {
      this.$emit('menu-command', command.command, command.item);
    }
  }
};
</script>

<style lang="scss" scoped>
.el-x-conversations-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
  width: fit-content;
  box-sizing: border-box;
  overflow: hidden;
}

.el-x-conversations-list {
  list-style: none;
  margin: 0;
  padding: 0;
  flex: 1;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

.el-x-conversations-scroll-wrapper {
  flex: 1;
  overflow-y: auto;
  position: relative;
}

.conversation-item {
  padding: 8px 12px;
  cursor: pointer;
  transition: background-color 0.2s;
  
  &:hover {
    background-color: #f5f7fa;
  }
  
  &.active {
    background-color: #e6f7ff;
    border-right: 3px solid #1890ff;
  }
  
  &.group-header {
    cursor: default;
    background-color: #fafafa;
    
    &:hover {
      background-color: #fafafa;
    }
  }
}

.group-title {
  font-size: 12px;
  color: #909399;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.conversation-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.conversation-main {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
}

.prefix-icon {
  margin-right: 8px;
  color: #909399;
  font-size: 14px;
}

.conversation-label {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 14px;
  color: #303133;
}

.conversation-menu {
  opacity: 0;
  transition: opacity 0.2s;
  
  .conversation-item:hover & {
    opacity: 1;
  }
}

.menu-trigger {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 4px;
  color: #909399;
  
  &:hover {
    background-color: #e6f7ff;
    color: #1890ff;
  }
}
</style>
