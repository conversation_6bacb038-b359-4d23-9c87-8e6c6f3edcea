<template>
  <transition name="fade">
    <div
      class="app-selector"
      v-show="visible"
      :style="selectorStyle"
    >
      <!-- 遮罩层可能阻碍了应用选择器的显示和交互，暂时注释掉 -->
      <!-- <div class="app-selector-overlay" @click="handleOverlayClick"></div> -->
      <div class="app-selector-dropdown" >
        <div class="app-selector-header">
          <div class="header-title">选择应用</div>
        </div>

        <div class="app-list">
          <div v-if="filteredApps.length === 0" class="no-results">
            <i class="el-icon-warning"></i>
            <span>未找到匹配的应用</span>
          </div>

          <div
            v-for="(app, index) in filteredApps"
            :key="app.id"
            :class="['app-item', { active: index === selectedIndex }]"
            @click="selectApp(app)"
            @mouseenter="selectedIndex = index"
          >
            <div class="app-icon">
              <i :class="app.icon"></i>
            </div>
            <div class="app-info">
              <div class="app-name">{{ app.name }}</div>
              <div class="app-description">{{ app.description }}</div>
            </div>
          </div>
        </div>

        <div class="app-selector-footer">
          <div class="tip">
            <i class="el-icon-info"></i>
            <span>使用 ↑/↓ 键选择，Enter 键确认</span>
          </div>
        </div>
      </div>
    </div>
  </transition>
</template>

<script>
export default {
  name: 'AppSelector',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    position: {
      type: Object,
      default: () => ({ left: 0, top: 0 })
    },
    apps: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      selectedIndex: 0,
      filteredApps: [],
      allApps: []
    };
  },
  computed: {
    /**
     * 计算选择器的定位样式
     */
    selectorStyle() {
      const style = {
        position: 'absolute',
        left: `${this.position.left}px`,
        top: `${this.position.top}px`,
        zIndex: 10000
      };
      console.log('AppSelector selectorStyle:', style, 'visible:', this.visible);
      return style;
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.initSelector();
      } else {
        this.resetSelector();
      }
    },
    apps: {
      handler(newApps) {
        this.loadApplications();
      },
      immediate: true
    }
  },
  mounted() {
    this.loadApplications();
    this.bindGlobalEvents();
  },
  beforeDestroy() {
    this.unbindGlobalEvents();
  },
  methods: {
    /**
     * 加载应用列表
     */
    loadApplications() {
      // 直接使用传入的应用列表
      this.allApps = this.apps || [];
      this.filteredApps = [...this.allApps];
    },

    /**
     * 初始化选择器
     */
    initSelector() {
      this.selectedIndex = 0;
      this.filteredApps = [...this.allApps];
    },

    /**
     * 重置选择器
     */
    resetSelector() {
      this.selectedIndex = 0;
      this.filteredApps = [];
    },

    /**
     * 处理键盘事件
     */
    handleKeydown(event) {
      switch (event.key) {
        case 'ArrowDown':
          event.preventDefault();
          this.moveSelection(1);
          break;
        case 'ArrowUp':
          event.preventDefault();
          this.moveSelection(-1);
          break;
        case 'Enter':
          // 阻止事件传播，防止触发表单提交
          event.preventDefault();
          event.stopPropagation();
          if (this.filteredApps.length > 0) {
            this.selectApp(this.filteredApps[this.selectedIndex]);
          }
          break;
        case 'Escape':
          event.preventDefault();
          this.closeSelector();
          break;
      }
    },

    /**
     * 移动选择
     */
    moveSelection(direction) {
      if (this.filteredApps.length === 0) return;

      this.selectedIndex += direction;

      if (this.selectedIndex < 0) {
        this.selectedIndex = this.filteredApps.length - 1;
      } else if (this.selectedIndex >= this.filteredApps.length) {
        this.selectedIndex = 0;
      }
    },

    /**
     * 选择应用
     */
    selectApp(app) {
      // 先发送选择的应用，然后再关闭选择器
      // 这样可以确保父组件在处理选择事件时，选择器仍处于可见状态
      this.$emit('app-selected', app);
      
      // 使用nextTick延迟关闭选择器，确保选择事件被完全处理
      this.$nextTick(() => {
        this.closeSelector();
      });
    },

    /**
     * 处理遮罩层点击
     */
    handleOverlayClick() {
      this.closeSelector();
    },

    /**
     * 关闭选择器
     */
    closeSelector() {
      this.$emit('close');
    },

    /**
     * 绑定全局事件
     */
    bindGlobalEvents() {
      document.addEventListener('keydown', this.handleGlobalKeydown);
    },

    /**
     * 解绑全局事件
     */
    unbindGlobalEvents() {
      document.removeEventListener('keydown', this.handleGlobalKeydown);
    },

    /**
     * 处理全局键盘事件
     */
    handleGlobalKeydown(event) {
      if (!this.visible) return;

      switch (event.key) {
        case 'ArrowDown':
        case 'ArrowUp':
        case 'Enter':
        case 'Escape':
          this.handleKeydown(event);
          // 阻止事件传播，防止触发表单提交
          event.stopPropagation();
          break;
      }
    }
  }
};
</script>

<style lang="scss" scoped>
/* 过渡动画 */
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.2s, transform 0.2s;
}
.fade-enter, .fade-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

.app-selector {
  background: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  max-width: 300px;
  min-width: 190px;
  max-height: 260px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  /* 确保组件可见 */
  opacity: 1;
  visibility: visible;
}

.app-selector-dropdown {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.app-selector-header {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
  border-radius: 8px 8px 0 0;

  .header-title {
    font-size: 14px;
    font-weight: 500;
    color: #303133;
  }
}

.app-list {
  flex: 1;
  overflow-y: auto;
  max-height: 180px;
}

.app-selector-footer {
  padding: 8px 16px;
  border-top: 1px solid #f0f0f0;
  background: #fafafa;
  border-radius: 0 0 8px 8px;

  .tip {
    display: flex;
    align-items: center;
    font-size: 12px;
    color: #909399;

    i {
      margin-right: 4px;
    }
  }
}

.app-selector-content {
  flex: 1;
  overflow-y: auto;
  max-height: 280px;
}

.no-results {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px 16px;
  color: #909399;
  font-size: 14px;

  i {
    margin-right: 8px;
    font-size: 16px;
  }
}

.app-item {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  cursor: pointer;
  transition: all 0.2s;
  border-left: 3px solid transparent;

  &:hover,
  &.active {
    background-color: #f5f7fa;
    border-left-color: var(--primary-color, #409EFF);
  }
}

.app-icon {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  flex-shrink: 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  i {
    color: #fff;
    font-size: 16px;
  }
}

.app-info {
  flex: 1;
  min-width: 0;
}

.app-name {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 2px;
  line-height: 1.2;
}

.app-description {
  font-size: 12px;
  color: #909399;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 滚动条样式
.app-selector-content::-webkit-scrollbar {
  width: 4px;
}

.app-selector-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 2px;
}

.app-selector-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;

  &:hover {
    background: #a8a8a8;
  }
}

</style>
