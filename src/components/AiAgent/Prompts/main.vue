<template>
  <div class="el-x-prompts" :style="styles">
    <h3 v-if="title" class="el-x-prompts-title">{{ title }}</h3>
    
    <div 
      :class="[
        'el-x-prompts-list',
        { 'el-x-prompts-list-wrap': wrap },
        { 'el-x-prompts-list-vertical': !wrap }
      ]"
      :style="listStyles"
    >
      <div
        v-for="(item, index) in items"
        :key="item.id || index"
        :class="[
          'el-x-prompts-item',
          { 'el-x-prompts-item-loading': loading }
        ]"
        :style="[styles.item, item.style]"
        @click="handleItemClick(item, index)"
      >
        <!-- 图标插槽 -->
        <div v-if="$slots.icon || item.icon" class="el-x-prompts-icon">
          <slot name="icon" :item="item" :index="index">
            <i v-if="item.icon" :class="item.icon" :style="item.iconStyle"></i>
          </slot>
        </div>
        
        <!-- 内容区域 -->
        <div class="el-x-prompts-content">
          <!-- 标题 -->
          <div v-if="item.label" class="el-x-prompts-label">
            {{ item.label }}
          </div>
          
          <!-- 描述 -->
          <div v-if="item.description" class="el-x-prompts-desc">
            {{ item.description }}
          </div>
        </div>
        
        <!-- 加载状态 -->
        <div v-if="loading" class="el-x-prompts-loading">
          <i class="el-icon-loading"></i>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ElXPrompts',
  props: {
    items: {
      type: Array,
      default: () => []
    },
    title: {
      type: String,
      default: ''
    },
    wrap: {
      type: Boolean,
      default: false
    },
    styles: {
      type: Object,
      default: () => ({})
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    listStyles() {
      const baseStyles = {
        display: 'flex',
        gap: '8px'
      };
      
      if (this.wrap) {
        baseStyles.flexWrap = 'wrap';
      } else {
        baseStyles.flexDirection = 'column';
      }
      
      if (this.styles.justifyContent) {
        baseStyles.justifyContent = this.styles.justifyContent;
      }
      
      return baseStyles;
    }
  },
  methods: {
    handleItemClick(item, index) {
      if (!this.loading) {
        this.$emit('on-item-click', { data: item, index });
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.el-x-prompts {
  max-width: 100%;
  
  .el-x-prompts-title {
    font-weight: normal;
    font-size: 16px;
    color: #606266;
    margin-top: 0;
    margin-bottom: 0.5em;
  }
  
  .el-x-prompts-list {
    display: flex;
    gap: 8px;
    
    &.el-x-prompts-list-wrap {
      flex-wrap: wrap;
    }
    
    &.el-x-prompts-list-vertical {
      flex-direction: column;
    }
  }
  
  .el-x-prompts-item {
    flex: none;
    display: flex;
    gap: 8px;
    height: auto;
    padding: 12px 16px;
    align-items: flex-start;
    justify-content: flex-start;
    background: #ffffff;
    border-radius: 8px;
    transition: border 0.3s, background 0.3s;
    border: 1px solid #ebeef5;
    cursor: pointer;
    position: relative;
    
    &:hover {
      background: #f5f7fa;
      border-color: #c0c4cc;
    }
    
    &:active {
      background: #ecf5ff;
      border-color: #409eff;
    }
    
    &.el-x-prompts-item-loading {
      cursor: not-allowed;
      opacity: 0.6;
    }
  }
  
  .el-x-prompts-icon {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    
    i {
      font-size: 16px;
      color: #909399;
    }
  }
  
  .el-x-prompts-content {
    flex: auto;
    min-width: 0;
    display: flex;
    gap: 4px;
    flex-direction: column;
    align-items: flex-start;
  }
  
  .el-x-prompts-label {
    margin: 0;
    padding: 0;
    font-size: 14px;
    line-height: 1.5;
    text-align: start;
    white-space: normal;
    color: #303133;
    font-weight: 500;
  }
  
  .el-x-prompts-desc {
    margin: 0;
    padding: 0;
    font-size: 12px;
    line-height: 1.4;
    text-align: start;
    white-space: normal;
    color: #606266;
  }
  
  .el-x-prompts-loading {
    position: absolute;
    top: 50%;
    right: 12px;
    transform: translateY(-50%);
    
    i {
      font-size: 14px;
      color: #409eff;
      animation: rotating 2s linear infinite;
    }
  }
}

@keyframes rotating {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// 移动端响应式
@media screen and (max-width: 768px) {
  .el-x-prompts {
    .el-x-prompts-list-wrap {
      .el-x-prompts-item {
        width: calc(50% - 4px);
        min-width: 150px;
        padding: 10px 12px;
      }
    }
    
    .el-x-prompts-list-vertical {
      .el-x-prompts-item {
        width: 100%;
        padding: 12px 16px;
      }
    }
    
    .el-x-prompts-label {
      font-size: 13px;
    }
    
    .el-x-prompts-desc {
      font-size: 11px;
    }
  }
}
</style>
