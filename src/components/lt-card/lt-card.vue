<template>
  <div class="lt-card radius-4" :style="{'border':  '1px solid #eaecef'  }">
    <div class="header pd-l12 font-14">{{ title }}</div>
    <div class="pd-10">
      <slot />
    </div>
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: ''
    },
    border: {
      type: Boolean,
      default: false
    }
  }
}
</script>

<style lang="scss" scoped>
  .lt-card{
    .header{
      color: #364760;
      border-bottom: 1px solid #eaecef;
      height: 45px;
      line-height: 45px;
      font-weight: 600;
    }
  }
</style>
