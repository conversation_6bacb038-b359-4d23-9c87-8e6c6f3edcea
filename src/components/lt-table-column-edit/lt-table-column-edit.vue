<template>
  <el-table-column v-bind="$attrs" :label="label" :width="width">
    <template #header>
      <slot name="header">
        {{ label }}
      </slot>
    </template>
    <template slot-scope="{ row, $index }">
      <!-- 预览部分 -->
      <div v-show="!row.isEdit || editDisable">
        <slot name="preview" :row="row" :$index="$index">
          <!-- <template>{{ previewText(row) }}</template> -->
          <el-switch v-if="state === 'switch' && isSwitchPreview" v-model="row[prop]" v-bind="$attrs"
            @change="switchChange(row, $index)" />
          <template v-else>{{ previewText(row) }}</template>
        </slot>
      </div>

      <!-- 编辑部分 -->
      <template v-if="!editDisable">
        <div v-show="row.isEdit" :class="{
          'bg-white': ['input', 'select', 'date', 'time'].includes(state),
        }">
          <slot name="edit" :row="row" :$index="$index">
            <el-input v-if="state === 'input' && type === 'text'" v-model.trim="row[prop]" v-bind="$attrs"
              :cust-id="prop + '-' + $index + '-' + $parent._uid" :placeholder="placeholderComp" :type="type"
              @input="numberInput($event, $index)" />

            <el-input v-if="state === 'input' && type === 'number'" v-model.number="row[prop]" v-bind="$attrs"
              :cust-id="prop + '-' + $index + '-' + $parent._uid" :placeholder="placeholderComp" :type="type" />

            <el-select v-if="state === 'select'" v-model="row[prop]" clearable filterable :placeholder="placeholderComp"
              v-bind="$attrs" @change="selectChange($event, $index)">
              <el-option v-for="item in selectOption.options" :key="item + Math.random()"
                :label="item[selectOption.label]" :value="item[selectOption.value]" />
            </el-select>
            <el-switch v-if="state === 'switch'" v-model="row[prop]" v-bind="$attrs"
              @change="switchEditChange($event, $index)" />
            <el-radio-group v-if="state === 'radio'" v-model="row[prop]" v-bind="$attrs">
              <el-radio v-for="(item, index) in radioOption.options" :key="index + Math.random()"
                :label="item[radioOption.value]">{{ item[radioOption.label] }}</el-radio>
            </el-radio-group>
            <el-date-picker v-if="state === 'date'" v-model="row[prop]" v-bind="$attrs" type="date"
              :placeholder="placeholderComp" />
            <!--时间控件-->
            <el-time-picker v-if="state === 'time'" v-model="row[prop]" v-bind="$attrs" :placeholder="placeholderComp" />
          </slot>
        </div>
      </template>
    </template>
  </el-table-column>
</template>

<script>
export default {
  name: 'LtTableColumnEdit',
  props: {
    prop: {
      type: String,
      default: '',
      required: true,
    },
    label: {
      type: String,
      default: '',
      required: true,
    },
    placeholder: {
      type: String,
      default: '',
    },
    // 禁用编辑项
    editDisable: {
      type: Boolean,
      default: false,
    },
    // 编辑项的类型
    state: {
      type: String,
      default: 'input',
      validator: (value) => {
        // 这个值必须匹配下列字符串中的一个
        return [
          'input',
          'select',
          'radio',
          'switch',
          'date',
          'time',
          'none',
        ].includes(value);
      },
    },
    // el-select的配置参数
    selectOpt: {
      type: Object,
      default: () => ({}),
    },
    // el-radio的配置参数
    radioOpt: {
      type: Object,
      default: () => ({}),
    },
    // el-switch的配置参数
    switchOpt: {
      type: Object,
      default: () => ({}),
    },
    type: {
      type: String,
      default: 'text',
    },
    // 不能出现重复项
    noRepeat: {
      type: Boolean,
      default: false,
    },
    width: {
      type: String,
      default: '',
    },
    // 添加或编辑时是否自动聚焦
    focus: {
      type: Boolean,
      default: false,
    },
    // 在预览时就将switch展示出来
    isSwitchPreview: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      // el-select的配置参数
      selectOption: {
        previewLabel: '', // 用于回显
        options: [], // el-select的选项
        label: 'dictLabel',
        value: 'dictCode',
      },
      // el-radio的配置参数
      radioOption: {
        options: [],
        label: 'dictLabel',
        value: 'dictValue',
      },
      // el-switch的配置参数
      switchOption: {
        openText: '启用', // 开启状态下预览的文字
        closeText: '停用', // 关闭状态下预览的文字
      },
    };
  },
  computed: {
    placeholderComp() {
      return this.placeholder
        ? this.placeholder
        : `请${this.state === 'input' ? '输入' : '选择'}${this.label}`;
    },
  },
  watch: {
    selectOpt: {
      immediate: true,
      handler(val) {
        this.selectOption = { ...this.selectOption, ...this.selectOpt };
        if (!this.selectOption.options) this.selectOption.options = [];
      },
      deep: true,
    },
    radioOpt: {
      immediate: true,
      handler(val) {
        this.radioOption = { ...this.radioOption, ...this.radioOpt };
      },
      deep: true,
    },
    switchOpt: {
      immediate: true,
      handler(val) {
        this.switchOption = { ...this.switchOption, ...this.switchOpt };
      },
      deep: true,
    },
  },
  methods: {
    /**
     * 预览显示的文字
     * @param {Object} row
     */
    previewText(row) {
      // el-select时的预览
      if (this.state === 'select' && row[this.prop]) {
        /**
         * 优先根据value去数组查找并取值,取不到则取label,若都为空则返回空
         */
        const options = this.selectOption.options;
        let result = [];
        if (options.length)
          result = options.filter(
            (item) => item[this.selectOption.value] === row[this.prop],
          );
        return result.length
          ? result[0][this.selectOption.label]
          : row[this.selectOption.previewLabel] || '';
      }
      // el-radio时的预览
      if (this.state === 'radio') {
        const options = this.radioOption.options;
        let result = [];
        if (options.length)
          result = options.filter(
            (item) => item[this.radioOption.value] === row[this.prop],
          );
        return result.length
          ? result[0][this.radioOption.label]
          : row[this.radioOption.previewLabel] || '';
      }
      // el-switch时的预览
      if (this.state === 'switch') {
        return row[this.prop] === (this.$attrs['active-value'] || true)
          ? this.switchOption.openText
          : this.switchOption.closeText;
      }

      return row[this.prop];
    },
    /**
     * switch变化的时候触发
     * @param {Object} row
     * @param {Object} index
     */
    switchChange(row, index) {
      // 调用lt-table-column-operation组件的saveMain方法
      let LtTableColumnOperation = this.$parent.$children.filter(
        (item) => item.$options.name === 'LtTableColumnOperation',
      );
      if (LtTableColumnOperation && LtTableColumnOperation.length) {
        LtTableColumnOperation[0].saveMain(row, index);
      }
    },
    selectChange(e, index) {
      this.$emit('change', {
        data: this.selectOption.options.filter(
          (item) => item[this.selectOption.value] === e,
        )[0],
        index: index,
      });
    },
    numberInput(e, index) {
      this.$emit('input', { data: e, index: index });
    },
    switchEditChange(e, index) {
      this.$emit('change', { data: e, index: index });
    },
  },
};
</script>

<style scoped="scoped">
.bg-white {
  background-color: #ffffff !important;
}

::v-deep .el-radio {
  margin-right: 10px;
}

::v-deep .el-radio__label {
  font-size: 13px;
}

::v-deep .el-input__prefix {
  top: -8px;
}

.el-date-editor ::v-deep .el-input__inner {
  padding-left: 0;
}

.el-input--prefix ::v-deep .el-input__inner {
  padding-left: 12px;
}

::v-deep .el-input__suffix {
  /* right: 0px; */
  height: 23px;
  line-height: 23px;
  align-items: center;
  justify-content: center;
  display: flex;
}

::v-deep th>.cell {
  color: #364760;
}

::v-deep .el-input__inner {
  height: 23px !important;
  line-height: 23px !important;
}

::v-deep .el-form-item {
  margin-bottom: 15px;
}

::v-deep .el-input__inner {
  width: 100% !important;
  border: none;
  padding-left: 10px;
  color: black;
}

::v-deep .el-date-editor {
  width: 100%;
  padding-left: 20px;
}

::v-deep .el-date-editor .el-input__prefix {
  top: -6px;
}

::v-deep .el-select.el-select--medium {
  width: 100%;
}
</style>
