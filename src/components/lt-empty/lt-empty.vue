<template>
  <div class="pd-20 d-flex flex-column a-center">
    <img src="@/assets/images/noData.svg" class="image">
    <p class="text-center mr-t5" style="color: #C2C7CF;" :style="{fontSize:fontSize}">{{ text }}</p>
  </div>
</template>

<script>
export default {
  props: {
    fontSize: {
      type: String,
      default: '12px'
    },
    text: {
      type: String,
      default: '还没有数据哦!'
    }
  }
}
</script>

<style lang="scss" scoped="scoped">
  .image{
    width: 70px;
  }
</style>