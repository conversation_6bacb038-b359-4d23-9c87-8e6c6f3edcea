<template>
  <div>
    <template v-if="isSetting">
      <el-row v-for="(rowItem, rowIndex) in tabData" :key="rowIndex">
        <el-col
          v-for="(colItem, colIndex) in rowItem"
          :key="colIndex"
          :span="rowItem.length === 1 ? 24 : 12"
          style="padding-bottom: 10px"
          :style="{ paddingRight: !colIndex ? '10px' : 0 }"
        >
          <el-card :title="colItem.title">
            <div slot="header">
              {{ colItem.title }}
            </div>
            <el-tabs
              v-model="colItem.currentTab"
              @tab-click="
                tabClick($event, colItem.currentTab, rowIndex, colIndex)
              "
            >
              <el-tab-pane label="单值控制图" name="1" />
              <el-tab-pane label="移动极差Rm控制图" name="2"> </el-tab-pane>
              <el-tab-pane label="数据表" name="3"> </el-tab-pane>
            </el-tabs>
            <div style="height: 300px">
              <lt-chart
                v-show="colItem.currentTab === '1'"
                :ref="`discriminate-${rowIndex}-${colIndex}`"
              />
              <lt-chart
                v-show="colItem.currentTab === '2'"
                :ref="`difference-${rowIndex}-${colIndex}`"
              />
              <el-descriptions
                border
                :column="3"
                v-show="colItem.currentTab === '3'"
              >
                <el-descriptions-item label="极差">{{
                  colItem.describe.range
                }}</el-descriptions-item>
                <el-descriptions-item label="平均值">{{
                  colItem.describe.avg
                }}</el-descriptions-item>
                <el-descriptions-item label="最大值">{{
                  colItem.describe.max
                }}</el-descriptions-item>
                <el-descriptions-item label="最小值">{{
                  colItem.describe.min
                }}</el-descriptions-item>
                <el-descriptions-item label="标准差">{{
                  colItem.describe.stddev
                }}</el-descriptions-item>
                <el-descriptions-item label="CP">{{
                  colItem.describe.cp
                }}</el-descriptions-item>
                <el-descriptions-item label="CPK">{{
                  colItem.describe.cpk
                }}</el-descriptions-item>
              </el-descriptions>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <el-table :data="tableData">
        <el-table-column prop="index" label="序号" width="80" />
        <el-table-column prop="batchNum" label="批次号" />
        <el-table-column prop="resultAt" label="检验时间" />
        <el-table-column
          v-for="item in columnList"
          :prop="item"
          :label="item"
          :key="item"
        />
      </el-table>
    </template>
    <lt-empty v-else text="未设置控制规格或控制上下限" />
  </div>
</template>

<script>
export default {
  name: 'LtSpcItem',
  data() {
    return {
      isSetting: true,
      tabData: [],
      tableData: [],
      columnList: [],
    };
  },
  methods: {
    init(res) {
      let { tab, table } = res.data;
      if (!tab && !table) {
        this.isSetting = false;
        this.$message.error('未设置控制规格或控制上下限');
        return;
      }
      this.isSetting = true;
      tab.forEach((tabItem) => {
        tabItem.forEach((item) => {
          let keys = Object.keys(item.standard);
          let values = Object.values(item.standard);

          item.standard = keys.map((keyItem, index) => {
            return {
              key: keyItem,
              value: values[index],
            };
          });

          item.standard = item.standard.filter(
            (item) => typeof item.value === 'number',
          );

          item.data.forEach((dataItem, index) => {
            // 计算出在table中出现的位置
            dataItem['xValue'] =
              table.findIndex(
                (tableItem) => tableItem.batchNum === dataItem.batchNum,
              ) + 1;
            dataItem['isDimension'] =
              item.ruleSet && item.ruleSet[index] && item.ruleSet[index].length;
          });

          item.currentTab = '1';
        });
      });

      let columnList = [];
      let tableData = [];
      table.forEach((item, index) => {
        let temp = {};
        item.data.forEach((dataItem) => {
          columnList.push(dataItem.name);
          temp[dataItem.name] = dataItem.testResult;
        });
        temp['index'] = index + 1;

        if (item.data.length) {
          temp['batchNum'] = item.data[0].batchNum;
          temp['resultAt'] =
            item.data[0]?.resultAt && item.data[0]?.resultAt.replace('T', ' ');
        }
        tableData.push(temp);
      });
      columnList = [...new Set(columnList)];

      this.tabData = tab;
      this.tableData = tableData;
      this.columnList = columnList;
      this.$nextTick(() => {
        tab.forEach((rowItem, rowIndex) => {
          rowItem.forEach((colItem, colIndex) => {
            this.discriminateInit(rowIndex, colIndex);
          });
        });
      });
    },
    /**
     * tab切换
     * @param {*} e
     * @param {*} tab
     * @param {*} rowIndex
     * @param {*} colIndex
     */
    tabClick(e, tab, rowIndex, colIndex) {
      // console.log(e, tab, rowIndex, colIndex);
      if (tab === '1') {
        this.discriminateInit(rowIndex, colIndex);
      }
      if (tab === '2') this.differenceInit(rowIndex, colIndex);
    },
    /**
     * 判异图初始化
     * @param {*} rowIndex
     * @param {*} colIndex
     */
    discriminateInit(rowIndex, colIndex) {
      let widgetRef = this.$refs[`discriminate-${rowIndex}-${colIndex}`];
      widgetRef = widgetRef.length ? widgetRef[0] : widgetRef;
      widgetRef.showLoading();
      let data = this.tabData[rowIndex][colIndex];
      let labelList = data.data.map((item) => item.xValue);

      let colorList = [
        '#ff0000',
        '#28B5EB',
        '#ffc000',
        '#00b050',
        this.$store.state.settings.theme,
        '#67C23A',
        '#E6A23C',
        '#F56C6C',
        '#909399',
      ];
      let series = [
        {
          name: 'data',
          data: data.data.map((item) => {
            let value = item.testResult ? parseFloat(item.testResult) : 0;
            if (item.isDimension) {
              return {
                value,
                itemStyle: {
                  normal: {
                    color: '#FFA500',
                  },
                },
                symbol: 'circle',
                symbolSize: 7,
              };
            }
            return { value };
          }),
          type: 'line',
          lineStyle: {
            color: '#28B5EB',
          },
          smooth: false,
          markLine: {
            data: data.standard.map((item, index) => {
              return {
                label: {
                  position: 'end', // 表现内容展示的位置
                  formatter: item.key, // 标线展示的内容
                  color: colorList[index], // 展示内容颜色
                },
                lineStyle: {
                  type: 'dashed',
                  color: colorList[index],
                  width: 1,
                },
                yAxis: item.value,
              };
            }),
          },
        },
      ];

      let tMin = Math.min(...data.standard.map((item) => item.value));
      let tMax = Math.max(...data.standard.map((item) => item.value));
      const map = {
        1: '①  任何1个点落在A区以外',
        2: '②  连续9个点落在中心线的同一侧',
        3: '③  连续6个点递增或递减',
        4: '④  连续14个点中相邻点交替上下',
        5: '⑤  连续3个点中有2个点落在中心线同一侧的B 区以外',
        6: '⑥  连续5个点中有4个点落在中心线同一侧的C 区以外',
        7: '⑦  连续15个点落在中心线两侧的C区以内',
        8: '⑧  连续8个点落在中心线两侧且无一在C区内',
      };
      let option = {
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: labelList,
        },
        color: series.map((item) => item.lineStyle.color),
        grid: {
          x: 45,
          y: 30,
          x2: 30,
          y2: 30,
        },
        yAxis: {
          type: 'value',
          axisTick: {
            show: false,
          },
          splitLine: {
            show: false,
          },
          min:
            (tMin - (tMax - tMin) / 8).toFixed(3) > 0
              ? (tMin - (tMax - tMin) / 8).toFixed(3)
              : 0,
          max: (tMax + (tMax - tMin) / 8).toFixed(3),
        },
        tooltip: {
          trigger: 'axis',
          formatter: (params) => {
            let index = parseInt(params[0].dataIndex);
            let str = `结果值: ${params[0].value.toFixed(3)}-${
              params[0].dataIndex
            }`;

            if (this.tableData && this.tableData.length) {
              if (index <= this.tableData.length) {
                str += `<br/>${this.tableData[index].batchNum}<br/>${this.tableData[index].resultAt}`;
              }
            }
            if (
              data.ruleSet &&
              data.ruleSet[index] &&
              data.ruleSet[index].length
            ) {
              str += '<br> 异常: <br>';
              data.ruleSet[index].forEach((item) => {
                str += map[item] + ' <br> ';
              });
            }

            return str;
          },
        },
        series,
        dataZoom: [
          {
            type: 'inside', // 放大和缩小
            orient: 'vertical',
          },
        ],
      };

      if (labelList.length > 20) {
        option.dataZoom = [
          {
            type: 'inside', // 放大和缩小
            orient: 'vertical',
          },
          {
            start: 0,
            end: 100,
            bottom: -4,
          },
        ];
      }
      setTimeout(() => {
        console.log(JSON.stringify(option));
        widgetRef.initChart(option);
      }, 300);
    },
    /**
     * 差异图初始化
     * @param {*} rowIndex
     * @param {*} colIndex
     */
    differenceInit(rowIndex, colIndex) {
      let widgetRef = this.$refs[`difference-${rowIndex}-${colIndex}`][0];
      widgetRef.showLoading();
      let data = this.tabData[rowIndex][colIndex];
      let labelArr = data.differences.map((item, index) => index + 1);
      let option = {
        xAxis: {
          type: 'category',
          data: labelArr,
        },
        grid: {
          x: 40,
          x2: 20,
          y: 20,
          y2: 30,
        },
        yAxis: {
          type: 'value',
        },
        tooltip: {
          trigger: 'axis',
          formatter: function (params) {
            return `${params[0].name} : ${params[0].value.toFixed(4)}`;
          },
        },
        series: [
          {
            data: data.differences.map((item) => item.testResult),
            type: 'line',
            // markLine: {
            //   symbol: ["none", "none"],
            //   itemStyle: {
            //     normal: {
            //       lineStyle: {
            //         type: "dashed",
            //       },
            //       label: {
            //         show: true,
            //         position: "end",
            //         distance: 20,
            //         textStyle: {
            //           fontSize: 14,
            //         },
            //       },
            //     },
            //   },
            //   data: lineArr
            // },
          },
        ],
      };

      if (labelArr.length > 20) {
        option.dataZoom = [
          {
            start: 80,
            end: 100,
            bottom: -4,
          },
        ];
      }
      setTimeout(() => {
        console.log(JSON.stringify(option));
        widgetRef.initChart(option);
      }, 300);
    },
  },
};
</script>
