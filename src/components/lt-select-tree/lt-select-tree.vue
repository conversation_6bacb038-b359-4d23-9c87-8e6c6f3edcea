<template>
  <!--
    v-bind="$attrs"实现prop的传递
    v-on="$listeners"实现事件的传递
  -->
  <Treeselect v-bind="$attrs" v-on="$listeners" />
</template>

<script>
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
export default {
  components: { Treeselect }
}
</script>

<style lang="scss" scoped>
  ::v-deep .vue-treeselect__menu-container{
    font-size: 14px !important;
  }
  ::v-deep .vue-treeselect__value-container{
    font-size: 14px !important;
  }
</style>
