<template>
  <div v-if="visible">
    <el-dialog :visible.sync="apiInfo.dialogShow" :title="apiInfo.title" width="85vw"
      class="small-padding-dialog interfaces-dialog">
      <interfaces-design ref="interfacesDesign" />
    </el-dialog>
    <el-dialog :title="title" :visible.sync="visible" :show-close="true" width="96vw" @close="eventDialogClosed"
      class="interfaces-dialog" :close-on-click-modal="false" :close-on-press-escape="false" :destroy-on-close="true">
      <el-row style="background-color: rgb(249, 250, 252);">
        <el-col :span="3">
          <ul class="interfaces-list">
            <li style="margin-bottom: 0;">
              <el-input placeholder="搜索接口" v-model="apiInfo.interfacesSearch" suffix-icon="el-icon-search"
                size="mini"></el-input>
            </li>
            <li
              v-for="(item, index) in interfacesInfos.filter((item) => !apiInfo.interfacesSearch || item.interfacesContent.name.includes(apiInfo.interfacesSearch))"
              :key="index" @click="interfacesEvent(item, index)">
              <span class='icon-api'>
                {{ item.interfacesContent.name }}
              </span>
            </li>
          </ul>
        </el-col>
        <el-col :span="21">
          <div class="d-flex">
            <componentTree ref="componentTreeRef" height="calc(100vh - 200px)" />
            <div class="flex-1" height="calc(100vh - 200px)">
              <el-alert type="info" :closable="false" :title="eventHeader" v-if="eventHeader"/>
              <code-editor v-model="eventCode" :mode="'javascript'" :readonly="false"
                height="calc(100vh - 300px)" :key="codeEditorKey" />
              <el-alert type="info" :closable="false" title="}" v-if="eventHeader"/>
            </div>
          </div>
        </el-col>
      </el-row>
      <div slot="footer" class="dialog-footer">
        <el-button type="info" @click="showDoc" v-if="docFlag">
          {{ i18nt("designer.hint.document") }}</el-button>
        <el-button @click="visible = false">
          {{ i18nt("designer.hint.cancel") }}</el-button>
        <el-button type="primary" @click="saveEventHandler">
          {{ i18nt("designer.hint.confirm") }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import InterfacesDesign from "@/views/admin/layout/InterfacesDesign";
import i18n from "@/utils/i18n";
import CodeEditor from "@/components/code-editor/index";
import componentTree from "@/components/form-designer/setting-panel/components/componentTree.vue";
export default {
  name: "EventEditor",
  mixins: [i18n],
  components: {
    InterfacesDesign,
    CodeEditor,
    componentTree,
  },
  props: {
    designer: Object,
    title: {
      type: String,
      default: "组件事件处理",
    },
    eventHeader: {
      type: String,
      default: "",
    },
    eventHandlerCode: {
      type: String,
      default: "",
    },
    docFlag: {
      type: Boolean,
      default: false,
    }
  },
  computed: {
    interfacesInfos() {
      return this.$store.state.design.interfacesInfos
    },
  },
  data() {
    return {
      apiInfo:{
        interfacesSearch: '',
        dialogShow: false,
        title: "",
        selectedInterfaceIndex: 0,
      },
      visible: false,
      eventCode: "",
      codeEditorKey: "",
    }
  },
  methods: {
    open(flag) {
      this.codeEditorKey = new Date().getTime();
      this.visible = flag
      if (flag) {
        this.eventCode = this.eventHandlerCode;
        this.$nextTick(() => {
          this.$refs.componentTreeRef.init(this.designer.widgetList);
        });
      }
    },
    showDoc(){
      this.$emit("showDoc");
    },
    saveEventHandler() {
      this.visible = false;
      this.$emit("saveEventHandler", this.eventCode);
    },
    interfacesEvent(api, index){
      this.apiInfo.selectedInterfaceIndex = index;
      this.apiInfo.dialogShow = true;
      this.apiInfo.title = api.interfacesContent.name;
      let headValue = {
        label: api.interfacesContent.name,
        value: api.interfacesId,
        type: 'interface'
      }
      this.$nextTick(() => {
        this.$refs.interfacesDesign.changeItem(headValue)
      })
    },
    eventDialogClosed() {
      this.$emit("widgetEventDialogClosed");
    },
  }
}
</script>
<style lang="scss" scoped>
.interfaces-dialog {
  ::v-deep .el-dialog__body {
    overflow: hidden !important
  }

  ::v-deep .ma-bottom-container {
    margin-bottom: 10px !important;
  }
}
.d-flex {
  .componentTree, .flex-1{
    padding: 10px 5px;
    margin: 10px 15px 10px 0;
    background-color: #fff;
    box-shadow: 0 4px 10px #0c1f5014;
    border-radius: 10px;
  }
}
.interfaces-list {
  list-style-type: none; /* 去除默认的列表样式 */
  padding: 10px 0;
  margin: 10px 15px 10px 10px;
  background-color: #fff;
  box-shadow: 0 4px 10px #0c1f5014;
  border-radius: 10px;
  // height: 44vw;
  height: calc(100vh - 200px);
  overflow: auto;
  li {
    width: 90%;
    margin: auto;
    padding: 8px 8px;
    margin-bottom: 6px;
    border-radius: 4px;
    color: #494f57;
    font-family: iconfont !important;
    font-size: 14px;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    transition: background-color 0.3s ease;
    cursor: pointer;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

    &:hover, &.active {
      background-color: rgba(24, 144, 255, 0.1);
      border-left: 3px solid #1890FF;
      border-radius: 4px;
    }
  }

  ::-webkit-scrollbar-track {
    background-color: #f1f1f1;
  }

  ::-webkit-scrollbar{
    width: 2px;
  }

  ::-webkit-scrollbar-thumb {
    background-color: #888;
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: #555;
  }
}
.small-padding-dialog {
  ::v-deep .el-dialog__body {
    padding: 6px 15px 0 15px;
  }
}
</style>