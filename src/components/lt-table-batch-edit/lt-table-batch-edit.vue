<template>
  <table class="editTable">
    <!-- 列宽 -->
    <colgroup>
      <slot name="colBeforeSlot" />
      <col v-for="item in dateData" :width="colWidth" :key="item + 'col'" />
      <slot name="colAfterSlot" />
      <col v-if="rowTotal" width="{80}" />
    </colgroup>
    <!-- 表头 -->
    <thead>
      <tr>
        <slot name="theadBeforeSlot" />
        <th v-for="item in dateData" :key="item + 'th'">
          <slot name="header" :item="item">{{ item }}</slot>
        </th>
        <slot name="theadAfterSlot" />
        <th v-if="rowTotal">合计</th>
      </tr>
    </thead>
    <!-- 表体 -->
    <tbody
      @mousedown.stop="mouseDown"
      @mousemove.stop="mouseMove"
      @mouseup.stop="mouseUp"
      @mouseleave="mouseUp"
      @click.stop="() => {}"
    >
      <tr v-for="(item, rowIndex) in tableData" :key="rowIndex + 'tr'">
        <slot name="tdBeforeSlot" :rowIndex="rowIndex" />

        <td
          v-for="(item2, colIndex) in item"
          :key="rowIndex + '-' + colIndex + 'td'"
          :row="rowIndex"
          :col="colIndex"
          :class="{
            select:
              selectCells.includes(`${rowIndex}-${colIndex}`) ||
              startNode === `${rowIndex}-${colIndex}`,
          }"
          :style="{ height: colHeight + 'px' }"
          @click="$emit('tdClick', rowIndex, colIndex)"
        >
          <slot
            name="tdRender"
            :rowIndex="rowIndex"
            :colIndex="colIndex"
            :item="item2"
            >{{ item2.val }}</slot
          >
        </td>
        <slot name="tdAfterSlot" />

        <td v-if="rowTotal">{{ renderCount(item) }}</td>
      </tr>

      <slot name="trAfterSlot" :tableData="tableData" />
    </tbody>
  </table>
</template>

<script>
import moment from 'moment';

export default {
  name: 'LtTableBatchEdit',
  props: {
    // 行合计
    rowTotal: {
      type: Boolean,
      default: false,
    },
    // 表格生成几行 number
    rowNumber: {
      type: Number,
      default: 8,
    },
    startDate: {
      type: String,
      default: '',
    },
    endDate: {
      type: String,
      default: '',
    },
    // 是否禁用,开启则无法选中单元格  boolean
    disabled: {
      type: Boolean,
      default: false,
    },
    // 列宽  number
    colWidth: {
      type: Number,
      default: 100,
    },
    // 列高  number
    colHeight: {
      type: Number,
      default: 35,
    },
    // 在哪一行进行列合计(逻辑为向上追溯),根据下标来的
    totalColumns: {
      type: Number,
      default: -1,
    },
    // 自定义数据处理
    custHandleData: {
      type: [String, Function],
      default: '',
    },
  },
  data() {
    return {
      dateData: [], // 日期数据
      tableData: [], // 表格数据(二维数组)
      startNode: '', // 起始点
      selectCells: [], // 表格选中的数据
      mouseDowFlag: false, // 是否按下了左键
      keyCount: 0, // 记录按键的次数
    };
  },
  watch: {
    rowNumber: function (newV, oldV) {
      this.startInit();
    },
  },
  mounted() {
    if (!this.disabled) {
      // 点击别的区域取消选中
      document.addEventListener('click', (e) => {
        this.selectCells = [];
        this.startNode = '';
      });
      // 键盘监听事件
      document.addEventListener('keydown', this.handleKeyDown);
    }
    this.startInit();
  },
  beforeDestroy() {
    document.removeEventListener('keydown', this.handleKeyDown);
  },
  methods: {
    /**
     * 首度初始化
     */
    startInit() {
      let dateData = this.enumerateDaysBetweenDates(
        this.startDate,
        this.endDate,
      );
      if (dateData.length) {
        let tableData = new Array(this.rowNumber).fill(
          new Array(dateData.length).fill({ val: '' }),
        );
        this.init(dateData, tableData, true);
      }
    },
    /**
     * 数据初始化
     * @param {*} dateData    日期数据
     * @param {*} tableData   表格数据
     * @param {*} flag        合计列是否初始化
     */
    init(dateData, tableData, flag = false) {
      this.dateData = JSON.parse(JSON.stringify(dateData));
      this.tableData = JSON.parse(JSON.stringify(tableData));

      // 列合计补充
      if (this.totalColumns > 0 && flag) {
        this.tableData[this.totalColumns].forEach((item) => {
          item.val = 0;
        });
      }
    },
    /**
     * 合计的计算
     * @param {*} data
     * @returns
     */
    renderCount(data) {
      let count = 0;
      data.forEach((item) => {
        if (item) count += parseFloat(item);
      });
      return this.$number.toFixed(count, 1);
    },
    /**
     * 鼠标按下
     * @param {*} e
     */
    mouseDown(e) {
      let startNode = this.getActiveCell(e);
      // 只处理左键
      if (
        e.button === 0 &&
        !this.disabled &&
        this.tableData[startNode.split('-')[0]][startNode.split('-')[1]]
          .isSelect
      ) {
        this.mouseDowFlag = true;
        this.selectCells = [];
        this.startNode = startNode;
        this.keyCount = 0;
      }
    },
    /**
     * 鼠标移动
     * @param {*} e
     */
    mouseMove(e) {
      if (this.mouseDowFlag && !this.disabled) {
        this.selectCelling(this.startNode, this.getActiveCell(e));
      }
    },
    /**
     * 鼠标抬起
     * @param {*} e
     */
    mouseUp(e) {
      if (!this.disabled) {
        this.getActiveCell(e);
        this.mouseDowFlag = false;
      }
    },
    /**
     * 获取当前鼠标所在的单元格
     * @param {*} e
     */
    getActiveCell(e) {
      let temp = '';
      let target = e.target;
      while (target.tagName !== 'TBODY' && !temp) {
        let row = target.getAttribute('row');
        let col = target.getAttribute('col');
        if (row !== null && col !== null) {
          temp = `${row}-${col}`;
        }
        target = target.parentNode;
      }

      return temp;
    },
    /**
     * 设置选中的单元格
     * @param {*} startNode  开始节点
     * @param {*} endNode    结束节点
     */
    selectCelling(startNode, endNode) {
      if (startNode === endNode) {
        this.selectCells = [startNode];
        return;
      }
      if (!startNode || !endNode) {
        return;
      }
      // 根据起始点和当前点计算得出所有单元格的信息
      let startInfo = {
        row: startNode.split('-')[0],
        col: startNode.split('-')[1],
      };
      let endInfo = {
        row: endNode.split('-')[0],
        col: endNode.split('-')[1],
      };
      // 得到行列首尾值
      let [startRow, endRow, startCol, endCol] = [
        Math.min(startInfo.row, endInfo.row),
        Math.max(startInfo.row, endInfo.row),
        Math.min(startInfo.col, endInfo.col),
        Math.max(startInfo.col, endInfo.col),
      ];

      // 计算中间到底有哪些单元格
      let data = [];
      for (let i = startRow; i <= endRow; i++) {
        for (let j = startCol; j <= endCol; j++) {
          if (this.tableData[i][j].isSelect) {
            data.push(`${i}-${j}`);
          }
        }
      }
      this.selectCells = data;
    },

    /**
     * 获取两日期中间的日期集合(假定你已经保证了startDate 小于endDate，且二者不相等)
     * @param {*} startDate
     * @param {*} endDate
     * @returns
     */
    enumerateDaysBetweenDates(startDate, endDate) {
      if (!startDate && !endDate) {
        return [];
      }
      let daysList = [];
      let SDate = moment(startDate);
      let EDate = moment(endDate);

      daysList.push(SDate.format('YYYY-MM-DD'));
      while (SDate.add(1, 'days').isBefore(EDate)) {
        // 注意这里add方法处理后SDate对象已经改变。
        daysList.push(SDate.format('YYYY-MM-DD'));
      }
      daysList.push(EDate.format('YYYY-MM-DD'));
      return daysList;
    },

    /**
     * 按键事件处理
     * @param {*} e
     */
    handleKeyDown(e) {
      const { selectCells, isAllow } = this.getSelectInfo();

      if (isAllow) {
        // 允许的按键
        let permitKeys = [
          '1',
          '2',
          '3',
          '4',
          '5',
          '6',
          '7',
          '8',
          '9',
          '0',
          '.',
          'Backspace',
        ];
        let data = JSON.parse(JSON.stringify(this.tableData));
        if (permitKeys.includes(e.key)) {
          this.keyCount++;
          // 如果是第一次按则需要先置空
          if (this.keyCount === 1) {
            selectCells.forEach((item) => {
              let row = item.split('-')[0];
              let col = item.split('-')[1];

              data[row][col].val = '';
            });
          }

          selectCells.forEach((item) => {
            let row = item.split('-')[0];
            let col = item.split('-')[1];
            if (e.key === 'Backspace') {
              if (data[row][col].val) {
                data[row][col].val = data[row][col].val.substr(
                  0,
                  data[row][col].val.length - 1,
                );
              }
            } else {
              // 第一次输入.前面自动补0
              if (e.key === '.' && !data[row][col].val) {
                data[row][col].val = '0.';
                return;
              }
              // 不允许有多个.
              if (e.key === '.' && data[row][col].val.includes('.')) {
                return;
              }
              // 为null则直接赋值
              if (data[row][col].val === null) {
                data[row][col].val = e.key;
                return;
              }
              // 否则拼接
              data[row][col].val += e.key;
            }
          });

          // 列合计的计算
          if (this.totalColumns > 0) {
            let dataSource = data.filter(
              (item, index) => index < this.totalColumns,
            );
            if (dataSource.length) {
              for (let i = 0; i < dataSource[0].length; i++) {
                data[this.totalColumns][i].val = this.renderCount(
                  dataSource.map((item) => item[i].val),
                );
              }
            }
          }
          if (this.custHandleData)
            this.custHandleData(data, [...this.dateData]);
          this.tableData = data;
        }
      }
    },

    getTableData() {
      return JSON.parse(
        JSON.stringify({ tableData: this.tableData, dateData: this.dateData }),
      );
    },
    /**
     * 是否允许进行输入
     */
    getSelectInfo() {
      let selectCells = [];
      if (this.startNode) selectCells = [this.startNode];
      if (this.selectCells.length) selectCells = this.selectCells;

      let selectData = selectCells.map((item) => {
        item = item.split('-').map((item) => parseInt(item));
        return this.tableData[item[0]][item[1]];
      });
      let isAllow = selectData.some((item) => item.isInput);

      return {
        selectCells,
        isAllow,
        selectData,
      };
    },
    /**
     * 清空选中
     */
    clearSelect() {
      this.startNode = '';
      this.selectCells = [];
    },
  },
};
</script>

<style scoped lang="scss">
.editTable {
  width: 100%;
  border-collapse: collapse;
  table-layout: fixed;
  font-size: 14px;
  th {
    height: 35px;
    border: 1px solid #e8e8e8;
    text-align: center;
    white-space: nowrap;
    user-select: none;
  }
  td {
    /* height: 35px; */
    text-align: center;
    user-select: none;
    border-left: 1px solid #e8e8e8;
    border-bottom: 1px solid #e8e8e8;
    &.select {
      background: rgba(217, 217, 217, 0.3);
    }
  }

  thead tr th:last-child {
    border-right: 1px solid #e8e8e8;
  }

  tbody tr {
    td:last-child {
      border-right: 1px solid #e8e8e8;
    }
    &:last-of-type td {
      border-bottom-width: 1px;
    }
    // &:first-child td {
    //   border-top: 3px solid #e8e8e8;
    // }
    // &:nth-child(even) td {
    //   border-bottom-width: 3px;
    // }
  }
}
</style>
