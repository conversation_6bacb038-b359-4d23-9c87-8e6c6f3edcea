<template>
  <div class="upload-file">
    <el-upload v-if="(!disabled && !readonly) || designState"
               action="#"
               :before-upload="handleBeforeUpload"
               :name="uploadId"
               :ref="`Uploader-${uploadId}`"
               :file-list="fileList"
               :limit="limit"
               :auto-upload="false"
               :on-change="changeFile"
               :on-exceed="handleExceed"
               :multiple="multipleSelect"
               :show-file-list="false"
               :headers="headers"
               :accept="fileType.join(',.')"
               :disabled="isDisabled"
               class="upload-file-uploader" :drag="drag == 'drag'">
      <template v-if="drag == 'drag'">
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
      </template>
      <template v-else>
        <el-button type="primary">{{ buttonLabel || "选取文件"
          }}<i class="el-icon-upload el-icon--right"></i></el-button>
      </template>
      <template v-if="showTip">
        <div class="el-upload__tip" slot="tip">
          只能上传格式为
          <template>
            <b style="color: #f56c6c">{{ fileType.join("/") }}</b> </template>的文件，大小不超过 <b style="color: #f56c6c" />{{
            fileSize }}MB
        </div>
      </template>
    </el-upload>
    <!-- 文件列表 -->
    <transition-group v-if="!((!disabled && !readonly) || designState) || showFileList"
                      class="upload-file-list el-upload-list el-upload-list--text" name="el-fade-in-linear" tag="ul">
      <li :key="file.uid" class="el-upload-list__item ele-upload-list__item-content" v-for="(file, index) in fileList">
        <el-link :underline="false" @click="handleView(index)" target="_blank">
          <span class="el-icon-document"> {{ file.name }} </span>
        </el-link>
        <div class="ele-upload-list__item-content-action">
          <el-link :href="file.url" type="success" icon="el-icon-download">下载</el-link>
          <el-link v-if="(!disabled && !readonly)" :underline="false" @click="handleDelete(index)" type="danger">删除</el-link>
        </div>
      </li>
      <li :key="file.uid" v-for="(file) in progressList" style="position: relative;margin-bottom: 10px;">
        <el-progress :text-inside="true" :stroke-width="24" :percentage="file.progress" :color="customColorMethod"></el-progress>
        <span class="el-icon-document progressSpan"> {{ file.fileName }} </span>
      </li>
    </transition-group>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";
import { delFile, getFile, sliceUploadFile} from "@/api/file/file";
import LtEmpty from "@/components/lt-empty/lt-empty.vue";
export default {
  name: "FileUpload",
  components: { LtEmpty },
  props: {
    // 值
    value: [String, Object, Array],
    // 数量限制
    limit: {
      type: Number,
      default: 5,
    },
    uploadURL: {
      type: String,
    },
    // 大小限制(MB)
    fileSize: {
      type: Number,
      default: 5,
    },
    // 文件类型, 例如['png', 'jpg', 'jpeg']
    fileType: {
      type: Array,
      default: () => [
        "doc",
        "xls",
        "ppt",
        "txt",
        "pdf",
        "png",
        "jpg",
        "csv",
        "tif",
      ],
    },
    // 是否显示提示
    isShowTip: {
      type: Boolean,
      default: true,
    },
    showFileList: {
      type: Boolean,
      default: true,
    },
    multipleSelect: {
      type: Boolean,
      default: false,
    },
    drag: {
      type: String,
    },
    buttonLabel: {
      type: String,
    },
    disabled: {
      type: Boolean,
      default: true,
    },
    readonly: {
      type: Boolean,
      default: true,
    },
    designState: {
      type: Boolean,
      default: true,
    },
  },

  data() {
    return {
      // uploadFileUrl:  "http://localhost:1024/dev-api/file/upload", // 上传的图片服务器地址
      uploadFileUrl: process.env.VUE_APP_BASE_API + "/file/upload",
      headers: {
        Authorization: "Bearer " + getToken(),
        // 请求携带来自终端类型
        terminalType: "PC",
      },
      uploadId: Math.random().toString(36).slice(2).toUpperCase(),
      fileList: [],  // 上传成功后的
      flag: true,
      progressList: [], // 进度条
      intervalMap: {},  // 定时器
      uploadFileList: [], // 待上传集合
      fileTotal: 0,
      isDisabled: false,
      fm: new FormData(),
      chunkSize: 2 * 1024 * 1024, // 切片大小，这里设置为5MB
      chunks: [],
    };
  },
  watch: {
    value: {
      handler(val) {
        if (val) {
          // 获取文件列表
          this.fileList = []
          getFile({ fileId: val }).then((files) => {
            for (let i = 0; i < files.data.length; i++) {
              this.fileList.push({
                name: files.data[i].fileName,
                url: this.$store.state.app.uploadUrl + files.data[i].pathName,
                uid: files.data[i].uid,
              });
            }
          });
        }
      },
      deep: true,
      immediate: true,
    },
  },
  computed: {
    // 是否显示提示
    showTip() {
      return this.isShowTip && (this.fileType || this.fileSize);
    },
  },
  methods: {
    customColorMethod(percentage) {
      console.log("计算比例",percentage)
      if (percentage < 30) {
        return '#909399';
      } else if (percentage < 70) {
        return '#e6a23c';
      } else {
        return '#67c23a';
      }
    },
    // 切割
    sliceFile(file) {
      const chunks = [];
      let current = 0;
      while (current < file.size) {
        chunks.push(file.slice(current, current + this.chunkSize));
        current += this.chunkSize;
      }
      return chunks;
    },

    async changeFile(file, fileList) {
      console.log(fileList)
      // 选择的个数
      this.fileTotal = document.getElementsByName(this.uploadId)[0].files.length;
      // 上传前校检格式和大小
      let flag = this.handleBeforeUpload(file)
      if (flag && file.status === 'ready') {
        // 添加上传列表
        this.uploadFileList.push(file);
        let fileId = this.value;
        // 已加载全部文件 进行顺序上传 已上传个数加上待上传个数等于总数
        if (this.uploadFileList.length === this.fileTotal) {
          this.isDisabled = true;
          for (let upFileIndex = 0; upFileIndex < this.uploadFileList.length; upFileIndex++) {
            let upFile = this.uploadFileList[upFileIndex]
            console.log(upFile)

            let chunks = this.sliceFile(upFile.raw);

            let uid = null;
            for (let i = 0; i < chunks.length; i++) {
              const formData = new FormData();
              formData.append("file", chunks[i]);
              formData.append("chunk", i);
              formData.append("chunks", chunks.length);
              formData.append("filename", upFile.name);
              if (fileId) {
                formData.append("fileId", fileId);
              }
              if (uid) {
                formData.append("uid", uid);
              }
              try {
                let res = await sliceUploadFile(formData);
                // 上传成功
                if (res.code === 200){
                  if (res.data.fileId) {
                    fileId = res.data.fileId;
                    this.value = fileId
                  }
                  if (res.data.uid) {
                    uid = res.data.uid;
                  }
                  if (i === (chunks.length - 1)){
                    this.fileList.push({
                      name: res.data.fileName,
                      url: this.$store.state.app.uploadUrl + res.data.pathName,
                      uid: res.data.uid,
                    });

                    // 所有文件上传完成
                    if (upFileIndex === (this.uploadFileList.length - 1)) {
                      this.$message.success("上传成功");
                      // 清空待上传列表
                      this.uploadFileList = []
                      this.value = res.data.fileId
                      this.isDisabled = false;
                      this.$emit("handleFileUpload", res, file, this.uploadFileList);
                    }
                  }
                  // 如果是第一次提交添加进度内容
                  if (i === 0) {
                    this.progressList.push({
                      uid: uid,
                      progress: 0,
                      fileName: upFile.name
                    })
                  }
                  // 计算上传比例
                  let progress = this.progressList.find(e => e.uid == uid)
                  progress.progress = parseInt(((i + 1) / chunks.length) * 100);
                  if(progress.progress >= 100 ){
                    // 清除进度条
                    this.progressList.splice(this.progressList.findIndex(e => e.uid == uid), 1)
                  }
                } else {
                  // 上传失败 消息提示清除文件列表 进度条等数据
                  this.$message.error(res.msg);
                  this.fileList.splice( fileList.findIndex(e => e.uid === file.uid), 1)
                  this.uploadFileList.splice( this.uploadFileList.findIndex(e => e.uid === file.uid), 1)
                  this.isDisabled = false;
                  // 清除进度条
                  this.progressList = this.progressList.filter(e => e.uid != uid)
                  this.$refs[`Uploader-${this.uploadId}`].clearFiles()
                }
              } catch (e) {
                // 清除进度条
                this.progressList = this.progressList.filter(e => e.uid != uid)
                this.uploadFileList.splice( this.uploadFileList.findIndex(e => e.uid === file.uid), 1)
                this.fileList.splice( fileList.findIndex(e => e.uid === file.uid), 1)
                this.$refs[`Uploader-${this.uploadId}`].uploadFiles = this.$refs[`Uploader-${this.uploadId}`].uploadFiles.filter(item => item.name !== file.name);
                this.$refs[`Uploader-${this.uploadId}`].clearFiles()
                this.handleUploadError()
                console.error("文件上传失败", e)
              }
            }
          }
        }
      } else {
        this.uploadFileList.splice( this.uploadFileList.findIndex(e => e.uid === file.uid), 1)
        this.fileList.splice( fileList.findIndex(e => e.uid === file.uid), 1)
        this.progressList = this.progressList.filter(e => e.uid != uid)
        this.$refs[`Uploader-${this.uploadId}`].uploadFiles = this.$refs[`Uploader-${this.uploadId}`].uploadFiles.filter(item => item.name !== file.name);
      }
    },
    // 上传前校检格式和大小
    handleBeforeUpload(file) {
      // 校检文件类型
      if (this.fileType) {
        let fileExtension = "";
        if (file.name.lastIndexOf(".") > -1) {
          fileExtension = file.name.slice(file.name.lastIndexOf(".") + 1);
        }
        const isTypeOk = this.fileType.some((type) => {
          // if (file.type.indexOf(type) > -1) return true;
          if (fileExtension && fileExtension.indexOf(type) > -1) return true;
          return false;
        });
        if (!isTypeOk) {
          this.$message.error(
            `文件格式不正确, 请上传${this.fileType.join("/")}格式文件!`
          );
          return false;
        }
      }
      // 校检文件大小
      if (this.fileSize) {
        const isLt = file.size / 1024 / 1024 < this.fileSize;
        if (!isLt) {
          this.$message.error(`上传文件大小不能超过 ${this.fileSize} MB!`);
          return false;
        }
      }
      // let timer = setInterval(() => {
      //   getRedisByKey(base64Name).then((res) => {
      //     for (let i = 0; i < this.progressList.length; i++) {
      //       const item = this.progressList[i];
      //       const floatValue = parseInt(res.data);
      //       if (base64Name == item.id && floatValue > 0) {
      //         item.progress = floatValue
      //       }
      //       if(floatValue <= 0){
      //         item.zeroCount++
      //       }
      //       if(floatValue >= 100 || item.zeroCount >= 20){
      //         clearInterval(this.intervalMap[base64Name])
      //
      //         if(item.zeroCount >= 20){
      //           this.progressList.splice(i, 1);
      //           i--;
      //         }
      //       }
      //     }
      //   })
      // }, 1000)

      // this.intervalMap[base64Name] = timer;
      this.$emit("handleOnBeforeUpload", file);
      return true;
    },
    // 文件个数超出
    handleExceed() {
      this.$message.error(`上传文件数量不能超过 ${this.limit} 个!`);
    },
    // 上传失败
    handleUploadError(err, file, fieldList) {
      this.$message.error("上传失败, 请重试");
      this.isDisabled = false;
      this.$emit("handelUploadError", err, file, fieldList);
    },
    handleView(index) {
      this.$ltKKFilePreview.open(this.value);
    },
    // 删除文件
    handleDelete(index) {
      delFile({ fileId: this.value, uid: this.fileList[index].uid })
        .then((e) => {
          this.fileList.splice(index, 1);
          this.$emit("input", this.fileList.length ? this.value : '');
          this.$message.success("删除成功");
        })
        .catch((e) => {
          this.$message.error("删除失败");
        });
    },
    // 获取文件名称
    getFileName(name) {
      if (name && name.lastIndexOf("/") > -1) {
        return name.slice(name.lastIndexOf("/") + 1).toLowerCase();
      } else {
        return "";
      }
    },

    clear() {
      this.fileList = [];
    },
  },
};
</script>

<style scoped lang="scss">
.upload-file-uploader {
  margin-bottom: 5px;
}
.upload-file-list .el-upload-list__item {
  border: 1px solid #e4e7ed;
  line-height: 2;
  margin-bottom: 10px;
  position: relative;
  &:first-child {
    margin-top: 5px;
  }
}
.upload-file-list .ele-upload-list__item-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: inherit;
}
.ele-upload-list__item-content-action .el-link {
  margin-right: 10px;
}

::v-deep .el-progress-bar__outer{
  background-color: #ffffff;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  height: 31.38px !important;
}
::v-deep .el-progress-bar__inner{
  border-radius: 4px;
  //background-color: #00c250 !important;
}

::v-deep .el-progress-bar__outer{
  background-color: #ffffff !important;
}
.progressSpan{
  position: absolute;
  top: 8px;
  color: #ffffff;
}

.el-icon-document{
  padding-left: 8px;
}
</style>
