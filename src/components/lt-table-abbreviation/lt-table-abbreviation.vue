<template>
  <div
    v-if="isScroll && (relation || tableRef)"
    class="table-abbreviation-wapper noPrint"
    :class="{ retract: isRetract }"
    :id="identification"
    :style="{ bottom: 10 + (total - index - 1) * 100 + 'px' }"
    @mouseout="autoRetract"
    @mouseover="clearTimer"
    @click.stop
  >
    <div class="table-abbreviation-switch" @click="switchAbbreviation">
      <img class="table-abbreviation-arrow" src="./icons/arrow.svg" />
    </div>
    <div class="table-abbreviation-main">
      <div class="table-abbreviation-body" />
      <!-- 遮罩层 挡住表格原本的悬浮等效果 -->
      <div class="table-abbreviation-mask" />
      <div
        ref="tableCurrentArea"
        class="table-current-area"
        @mousedown.stop="mouseDownEvent"
      />
    </div>
  </div>
</template>

<script>
/**
 * 创建节点
 * 传入 节点名称 样式 属性
 */
function createDom(name, styles = {}, attr = {}) {
  const t = document.createElement(name);
  setDomStyle(t, styles);
  for (const key in attr) {
    t[key] = attr[key];
  }
  return t;
}
function setDomStyle(dom, styles) {
  for (const key in styles) {
    dom.style[key] = styles[key];
  }
}

export default {
  props: {
    relation: {
      type: String,
      default: '',
    },
    tableRef: null,
    // sync: { // 同步初始化
    //   type: Boolean,
    //   default: false
    // },
    isAutoRetract: {
      type: Boolean,
      default: true,
    },
    appIsSeetingMouseDown: {
      type: Boolean,
      default: true,
    },
    tableData: {
      type: Array,
      default: () => [],
    },
    offsetWidth: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      innerWidth: 0, // 预览窗表格视图 宽度
      wapperWidth: 0, // 预览窗整体表格视图 宽度
      privewInnerWidth: 0, // 预览可视区宽度
      privewWapperWidth: 0, // 预览总体宽度
      moveInfo: {}, // 拖拽相关信息
      table: null,
      slider: null, // 滑块
      isRetract: false, // 是否收起,
      isScroll: false, // 表格是否滚动
      timer: null,
      updateTimer: null,
      clientWidth: document.body.clientWidth,
      identification: this.$string.getUUID(10),
      index: 0, // 当前 abbreviation 的索引
      total: 0, // 界面中 abbreviation 的总数
    };
  },
  watch: {
    tableData: {
      immediate: true,
      handler(val) {
        this.$nextTick(() => {
          this.update();
        });
      },
      deep: true,
    },
    clientWidth(newVal, oldVal) {
      this.update();
    },
  },
  mounted() {
    this.initWidth();
    setTimeout(() => {
      let list = $.makeArray($('.table-abbreviation-wapper'));
      this.total = list.length;
      this.index = list.findIndex((item) => item.id === this.identification);
    }, 100);
  },
  destroyed() {
    document.removeEventListener('mousemove', this.mouseMoveEvent, false);
    document.removeEventListener('mouseup', this.mouseUpEvent, false);
    window.removeEventListener('resize', this.update, false);
  },
  methods: {
    // 监听窗口缩小
    initWidth() {
      const that = this;
      window.onresize = () => {
        return (() => {
          window.clientWidth = document.body.clientWidth;
          that.clientWidth = window.clientWidth;
        })();
      };
    },
    clearTimer() {
      if (this.timer) {
        clearTimeout(this.timer);
      }
    },
    autoRetract(e) {
      this.clearTimer();
      if (this.isAutoRetract) {
        this.timer = setTimeout(() => {
          this.isRetract = true;
          this.mouseUpEvent(e);
        }, 3000);
      }
    },
    update(callback) {
      let tableRefObj = this.tableRef || this.$parent.$refs[this.relation];
      if (tableRefObj) {
        if (this.updateTimer) {
          clearTimeout(this.updateTimer);
        }
        this.$nextTick(() => {
          // 由略缩图组件托管表格 将会隐藏表格原有滚动条 可以调用 tableShowScroller方法 显示
          if (tableRefObj.$el.className.indexOf(' hide-scroller') === -1) {
            tableRefObj.$el.className += ' hide-scroller';
          }
        });
        this.updateTimer = setTimeout(() => {
          this.init(tableRefObj);
          if (callback && typeof callback === 'function') {
            callback();
          }
        }, 30);
      }
    },
    /**
     * 表格宽度变化重新生成
     */
    headerDragend() {
      setTimeout(() => {
        this.$nextTick(() => {
          this.$refs.evolTableAbbreviation.update();
        });
      }, 100);
    },
    switchAbbreviation() {
      this.isRetract = !this.isRetract;
      if (this.isRetract) {
        this.clearTimer();
      }
    },
    /**
     * 传入 elelmt table
     * @param {Object} table
     * 初始化
     */
    init(table) {
      // console.log(table.$parent);
      if (!(table && table.layout && table.layout.scrollX)) {
        this.isScroll = false;
        return;
      }
      this.isScroll = table.layout.scrollX;
      setTimeout(() => {
        this.table = table;
        this.innerWidth = this.offsetWidth || table.$el.offsetWidth;
        let tableRefObj = this.tableRef || this.$parent.$refs[this.relation];

        if (tableRefObj.$el.className.indexOf('el-table--scrollable-y') > -1)
          this.innerWidth = this.innerWidth - 16;
        this.wapperWidth = parseInt(table.bodyWidth) + 1;
        // 由略缩图组件托管表格 将会隐藏表格原有滚动条 可以调用 tableShowScroller方法 显示
        if (table.$el.className.indexOf(' hide-scroller') === -1) {
          table.$el.className += ' hide-scroller';
        }
        const dom = table.$parent.$el.cloneNode(true);
        // const dom = table;

        dom.id = '';
        setDomStyle(dom, {
          width: this.wapperWidth + 'px',
          position: 'absolute',
          left: '0px',
          'min-height': '500px',
          'max-height': '600px',
          'overflow-y': 'hidden',
          margin: '0',
          top: '0px',
          'max-width': 'inherit',
          transformOrigin: '0px 0px',
        });
        const previewDom = $('.table-abbreviation-body')[this.index];
        this.privewWapperWidth = previewDom.offsetWidth;
        const scale = this.privewWapperWidth / this.wapperWidth;
        const scaleY = 50 / 320;
        dom.style.transform = `scaleX(${scale}) scaleY(${scaleY})`;
        // 得到略缩图可视区宽度
        this.$refs.tableCurrentArea.style.width =
          this.innerWidth * scale + 'px';
        this.privewInnerWidth = this.innerWidth * scale; // 加上 2px 边框宽度
        // 计算缩放比例
        previewDom.innerHTML = '';
        previewDom.appendChild(dom);
        this.$nextTick(() => {
          this.slider = this.$refs.tableCurrentArea;
          setDomStyle(previewDom, {
            height: dom.getBoundingClientRect().height + 'px',
          });
          window.removeEventListener('resize', this.update, false);
          document.removeEventListener('mousemove', this.mouseMoveEvent, false);
          document.removeEventListener('mouseup', this.mouseUpEvent, false);
          document.addEventListener('mouseup', this.mouseUpEvent, false);
          document.addEventListener('mousemove', this.mouseMoveEvent, false);
          window.addEventListener('resize', this.update, false);
        });
        // this.autoRetract()
      }, 100);
    },
    mouseDownEvent(e) {
      if (this.appIsSeetingMouseDown)
        document.getElementById('app').setAttribute('mouseDown', 'mouseDown');
      this.moveInfo = {
        mouseX: e.clientX,
        mouseDownFlag: true,
        scrollX: parseInt(this.slider.style.left || '0'),
      };
    },
    mouseUpEvent(e) {
      if (this.appIsSeetingMouseDown) {
        setTimeout(() => {
          document.getElementById('app').setAttribute('mouseDown', '');
        }, 50);
      }
      this.moveInfo.mouseDownFlag = false;
    },
    mouseMoveEvent(e) {
      if (!this.moveInfo.mouseDownFlag) {
        return;
      }
      this.$nextTick(() => {
        // 移动略缩图滑块
        let scrollX =
          this.moveInfo.scrollX + (e.clientX - this.moveInfo.mouseX);
        if (scrollX < 0) {
          // 可移动区域限制
          scrollX = 0;
        } else if (scrollX > this.privewWapperWidth - this.privewInnerWidth) {
          scrollX = this.privewWapperWidth - this.privewInnerWidth;
        }
        this.slider.style.left = scrollX + 'px';
        // 计算移动比例
        let temp = (scrollX / this.privewWapperWidth) * this.wapperWidth;
        this.tableRef.bodyWrapper.scrollLeft = temp;
        this.$emit('scrollChange', temp);
      });
    },
  },
  // 显示表格滚动条
  tableShowScroller() {
    this.table.$el.className = this.table.$el.className.replace(
      ' hide-scroller',
      '',
    );
  },
};
</script>

<style lang="scss">
.hide-scroller {
  .el-table__body-wrapper::-webkit-scrollbar {
    height: 0px !important;
  }
}
</style>

<style lang="scss" scoped>
.retract {
  width: 0px !important;
  border-top-right-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
  .table-abbreviation-arrow {
    transform: rotateY(180deg);
  }
}
.table-abbreviation-wapper {
  overflow: hidden;
  padding: 5px;
  position: fixed;
  padding-left: 25px;
  right: 20px;
  // bottom: 10px;
  width: 290px;
  $borderRadius: 5px;
  border-radius: $borderRadius;
  background-color: rgba(0, 0, 0, 0.4);
  opacity: 0.45;
  transition: opacity 0.4s ease 0.1s, width 0.2s ease;
  z-index: 1500;
  &:hover {
    opacity: 1;
    transition-delay: 0.1s;
  }
  .table-abbreviation-switch {
    cursor: pointer;
    position: absolute;
    left: 0px;
    top: 0;
    width: 25px;
    height: 100%;
    padding-left: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    .table-abbreviation-arrow {
      max-height: 30px;
      min-height: 10px;
      height: 95%;
      transition: transform 0.1s ease;
      margin-left: 2px;
    }
  }
  .table-abbreviation-main {
    position: relative;
    width: 250px;
    margin-left: 5px;
    user-select: none;
    .table-abbreviation-body {
      position: relative;
    }
    .table-abbreviation-mask {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
    }
    .table-current-area {
      width: 20px;
      height: 100%;
      position: absolute;
      left: 0;
      top: 0;
      border: 1px solid orangered;
    }
  }
}
</style>
