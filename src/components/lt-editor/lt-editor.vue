<template>
  <!-- https://www.wangeditor.com/doc/ -->
  <div ref="editorElem" />
</template>

<script>
import E from 'wangeditor'
import store from "@/store"
import { uploadsImage } from '@/api/file/file.js'
export default {
  name: 'LtEditor',
  props: {
    value: String, // 组件接受一个 value 属性用来在 div 中展示
    config: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      defaultConfig: {
        height: 50
      },
      editor: null
    }
  },
  watch: {
    value(val) {
      this.editor.txt.html(decodeURI(val))
    }
  },
  mounted() {
    let editor = new E(this.$refs.editorElem)
    editor.config = { ...editor.config, ...this.defaultConfig, ...this.config }

    // 变化时向外抛出
    editor.config.onchange = (newHtml) => {
      this.$emit('input', encodeURI(newHtml))
    }

    // 自定义文件上传
    editor.config.customUploadImg = function (resultFiles, insertImgFn) {
      // resultFiles 是 input 中选中的文件列表
      // insertImgFn 是获取图片 url 后，插入到编辑器的方法

      let formData = new FormData()
      for (let item of resultFiles) {
        formData.append('files', item)
        formData.append('Paths', 'editor')
      }

      uploadsImage(formData).then(res => {
        // 上传图片，返回结果，将图片插入到编辑器中
        for (let item of res.data) {
          let url = `${store.state.app.uploadUrl}/file${item.url.substr(item.url.indexOf("/statics/editor"))}`
          insertImgFn(url)
        }
      })
    }

    editor.create() // 创建富文本实例
    this.editor = editor
  },

  methods: {
    /**
     * 禁用编辑器
     */
    disableRichText() {
      this.editor.disable()
    }
  }
}
</script>
