<template>
  <div class="userAvatar" :style="{ width: `${width}px`, height: `${width}px` }">
    <img
      v-if="isImg"
      @load="isImg = true"
      @error="isImg = false"
      :src="
        info.avatar && info.avatar.includes($store.state.app.uploadUrl)
          ? info.avatar
          : $store.state.app.uploadUrl + info.avatar
      "
      alt=""
    />
    <div v-else class="text">{{ info.nickName.slice(0, 1) }}</div>
  </div>
</template>

<script>
export default {
  name: 'LtuserAvatar',
  props: {
    width: {
      type: Number,
      default: 35,
    },
    info: {
      type: Object,
      default: {
        nickName: '',
        avatar: '',
      },
    },
  },
  data() {
    return {
      isImg: true,
    };
  },
};
</script>

<style lang="scss" scoped>
.userAvatar {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  border-radius: 10px;
  overflow: hidden;
  background: var(--primary-color);
  img {
    width: 100%;
    height: 100%;
  }
  .text {
    font-size: 20px;
    color: #fff;
    font-weight: bold;
  }
}
</style>
