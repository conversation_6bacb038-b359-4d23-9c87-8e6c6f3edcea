import Vue from 'vue'

// 全局导入组件 @/components/lt-$1/lt-$1
const requireComponent = require.context('./', true, /(.*?)\/\1.vue$/)
requireComponent.keys().forEach(fileName => {
  const componentConfig = requireComponent(fileName)
  // 获取组件的 PascalCase 命名
  const componentName = fileName.split('/')[1].replace(/^\.\/(.*)\.\w+$/, '$1')
  Vue.component(
    componentName,
    // 如果这个组件选项是通过 `export default` 导出的，
    // 那么就会优先使用 `.default`，否则回退到使用模块的根。
    componentConfig.default || componentConfig
  )
})
