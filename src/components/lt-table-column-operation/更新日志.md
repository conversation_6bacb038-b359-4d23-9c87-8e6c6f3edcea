## 2021-09-05

- 内置select、radio、switch、input编辑项
- 回显和编辑支持自定义插槽
- 操作按钮自定义
- 内置保存(带非空以及正则校验，可配置)和删除,可自定义
- 取消逻辑编写完成

## 2021-09-23

- 数据监听更改为深度监听
- 编辑项新增date类型
- 自动聚焦,由唯一的id更改为自定义属性cust-id已防止id可能一致产生的报错
- v-for的key更改为key+随机数

## 2021-09-24

- 校验层逻辑优化，支持更灵活的非空校验配置

## 2021-09-25

- prop字段增加校验使其对入参更加严格
- 自定义显示编辑插槽内部实现优化, $scopedSlots的形式更改为插槽的后备内容(默认的)

## 2021-09-28
- 点击保存不能回到预览模式的bug解决
- validateProp新增可传入逗号分号分隔的字符串来进行配置

## 2021-09-29
- 内置删除操作删除当前行数据
- el-switch支持多类型
- 新增编辑api的配置参数

## 2021-10-09
- 新增validateRange校验数值范围
- select回显逻辑优化(优先根据value去数组查找并取值,取不到则取label,若都为空则返回空)

## 2021-10-11
- validateProp指定校验列无效的bug修复

## 2021-10-12
- 新增editDisable指定列不会进入编辑状态
- 若不指定editApi,则编辑和新增统一使用saveApi
- 废弃switchOpt的配置项,在预览模式下可直接对switch进行修改并调用接口

## 2021-10-13
- 添加操作默认追加到最前面
- 针对number类型的input使用.number修饰符
- 新增custValidate自定校验使其更加灵活
- 判空逻辑优化,空字符串、null、undefined都为空

## 2021-10-15
- 新增noRepeat参数,当前列数据不能出现重复(保存时会校验别的列)
- 内置select默认带filterable参数

## 2021-10-22
- 新增editConfirm事件,在点编辑行数据进入编辑状态后触发

## 2021-11-02
- 新增成功后将 后台返回的数据 的主键 赋值给 当前数据,用于后续的编辑

## 2021-11-03
- 新增addBefore时间,用于添加之前对row数据进行更改
- 重复值校验校验提示增加具体哪一个值
## 2021-11-11
- 操作列继承header插件,用于对操作列头部的自定义

## 2021-11-18
- 新增editConfirm事件,点击编辑界面渲染完以后的回调

## 2021-11-23
- 新增before、after插槽,更灵活的配置操作列

## 2021-11-24
- 重复性校验优化select项的提示

## 2021-11-26
- 废除操作列focusProp参数,编辑列新增focus参数来指定自动聚焦的列,默认会自动聚焦第一个input列

## 2021-12-06
- 编辑列新增input事件
- 默认不能无限添加
- 数据还原的逻辑优化(新增时不会备份数据从而导致取消有bug)

## 2021-12-08
- 自定义校验增加row参数返回

## 2021-12-13
- select列重复项校验bug修改

## 2021-12-15
- 取消逻辑bug修改,改用对象备用数据,key为主键,value为行数据,还原赋值更改为单个赋值
- 取消逻辑对可能存在的排序字段进行剔除

## 2021-12-30
- 新增custOperDisplay传入一个方法更灵活的控制编辑删除的显示隐藏

## 2022-04-12
- sync传值更改为v-model
- validateProp字符串情况下逗号可以出现空格
- 新增isSwitchPreview参数控制预览下是否显示switch

## 2022-08-16
- 新增saveComplete以及deleteComplete供自定义保存删除外部调用