<!-- <AUTHOR> -->
<template>
  <div class="icon-body">
    <el-input
      v-model="name"
      style="position: relative"
      clearable
      placeholder="请输入图标名称"
      @clear="filterIcons"
      @input="filterIcons"
    >
      <i slot="suffix" class="el-icon-search el-input__icon" />
      <el-button slot="append"  icon="el-icon-close" @click="clear">清空</el-button>
    </el-input>
    <div  style="height: 200px;
    overflow-y: scroll;">
      <p style="margin:0 0 1px 0;color:rgba(64, 158, 255, 1)">常用</p>
      <div class="icon-list">
        <div
        v-for="(item, index) in iconList.filter(item=>commonList.includes(item))"
        :key="index"
        @click="selectedIcon(item)"
      >
        <svg-icon :icon-class="item" style="height: 30px; width: 16px" />
        <span>{{ item }}</span>
      </div>
      </div>
      
      <p style="margin:0 0 1px 0;color:rgba(64, 158, 255, 1)">其他</p>
      <div class="icon-list">
        <div
        v-for="(item, index) in iconList.filter(item=>!commonList.includes(item))"
        :key="index"
        @click="selectedIcon(item)"
      >
        <svg-icon :icon-class="item" style="height: 30px; width: 16px" />
        <span>{{ item }}</span>
      </div>
      </div>
      
      <i :class="name" style="font-size:20px;cursor:pointer;margin:5px" v-if="name" @click="selectedIcon(name)"></i>
    </div>
  </div>
</template>

<script>
import icons from './requireIcons';
export default {
  name: 'IconSelect',
  data() {
    return {
      name: '',
      iconList: icons,
      commonList:['icon_添加','删除','修改','打印','工单确认','云端上传','关闭','发布色块','对勾']
    };
  },
  methods: {
    filterIcons() {
      this.iconList = icons;
      if (this.name) {
        this.iconList = this.iconList.filter((item) =>
          item.includes(this.name),
        );
      }
    },
    selectedIcon(name) {
      this.$emit('selected', name);
      document.body.click();
    },
    clear(){
      this.$emit('selected', '');
      document.body.click();
    },
    reset() {
      this.name = '';
      this.iconList = icons;
    },
  },
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.icon-body {
  width: 100%;
  padding: 10px;
  .icon-list {
    display: flex;
    flex-wrap: wrap;
    div {
      height: 30px;
      line-height: 30px;
      margin-bottom: -5px;
      cursor: pointer;
      width: 33%;
    }
    span {
      display: inline-block;
      vertical-align: -0.15em;
      fill: currentColor;
      overflow: hidden;
    }
  }
}
</style>
