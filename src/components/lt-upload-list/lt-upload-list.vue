<template>
  <el-upload
    ref="uploadRef"
    action=""
    list-type="picture-card"
    :file-list="fileList"
    v-bind="$attrs"
    :on-change="changeFile"
    :before-remove="beforeRemove"
    :on-preview="handlePictureCardPreview"
    :auto-upload="false"
    :accept="accept"
    :disabled="disabled"
    :class="{'hide': (disabled&&fileList.length) || fileList.length>=limit}"
  >
    <i class="el-icon-plus" />
  </el-upload>
</template>

<script>
import { uploads, getFile, delFile } from '@/api/file/file.js';

export default {
  props: {
    // 文件id
    fileId: {
      type: String,
      default: '',
    },
    accept: {
      type: String,
      default: '',
    },
    maxSize: {
      type: Number,
      default: -1,
    },
    onSuccess: {
      type: Function | String,
      default: '',
    },
    onError: {
      type: Function | String,
      default: '',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    fileTypes:{
      type:Array,
      default:()=>[]
    },
    limit:{
      type:Number
    }
  },
  data() {
    return {
      fileList: [], // 用于展示的fileList
      formFileList: [], // 传递给后端的文件列表
      delFileList: [], // 已删除的文件url列表, 用于预览的筛选数据
      timer: null
    };
  },
  watch: {
    fileId: {
      immediate: true,
      handler(val, oldVal) {
        this.fileList = [];
        // 文件列表初始化
        if (this.fileId && (!this.fileList.length || val !== oldVal)) {
          getFile({
            fileId: this.fileId,
          }).then((res) => {
            this.initImageList(res.data);
          });
        }
        // else {
        //   this.fileList = [];
        // }
      },
      deep: true,
    },
  },
  methods: {
    /**
     * 图片列表初始化
     * @param {Object} data
     */
    initImageList(data) {
      data.forEach((item) => {
        this.fileList.push({
          name: item.fileName, // name就是文件显示的名字,这个是可以显示出来的,需要改下css布局才行
          fileId: item.fileId,
          uid: item.uid,
          fileType: item.fileType.toLowerCase(),
          url: this.$enum.imgFormat.includes(item.fileType.toLowerCase())
            ? this.$store.state.app.uploadUrl + item.pathName
            : require(`@/assets/images/fileIcon/${item.fileType.toLowerCase()}.png`),
          fileUrl: this.$store.state.app.uploadUrl + item.pathName, // 文件的真实路径
        });
      });
    },
    /**
     * 添加文件
     * @param {Object} file
     */
    changeFile(file, fileList, scope) {
      if(fileList.length>this.limit){
        this.$message.error('最多只能上传'+this.limit+'个文件');
        fileList.splice(fileList.length - 1, 1)
        return;
      }

      if(this.fileTypes.length && !this.fileTypes.some(item=>file.raw.type === 'image/' + item)){
        this.$message.error(`请上传${this.fileTypes.join('/')}格式`)
        this.fileList.splice()
        return
      }
      // 限制文件大小
      if (this.maxSize > 0 && Number(file.size / 1024 / 1024) > this.maxSize) {
        this.$refs.uploadRef.uploadFiles.splice(
          this.$refs.uploadRef.uploadFiles.length - 1,
          1,
        );
        this.$message({
          message: `文件大小不能超过${this.maxSize}MB, 请重新选择`,
        });
        return;
      }



      // 拼接文件名后缀
      let arr = file.raw.name.split('.');
      file.raw.fileType = arr[arr.length - 1].toLowerCase();

      try {
        file.url = this.$enum.imgFormat.includes(
          file.raw.fileType.toLowerCase(),
        )
          ? file.url
          : require(`@/assets/images/fileIcon/${file.raw.fileType.toLowerCase()}.png`);
      } catch (e) {
        // TODO handle the exception
        file.url = require(`@/assets/images/fileIcon/default.png`);
      }

      this.formFileList.push(file.raw);

      if(this.timer){
        clearTimeout(this.timer);
      }
      this.timer=setTimeout(()=>{
        this.$emit('onChange');
        this.timer=null;
      },200)

    },
    /**
     * 删除文件
     * @param {Object} file
     */
    async beforeRemove(file, fileList) {
      await this.$confirm(
        '您确定删除这个文件吗?',
        '提示',
        await {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        },
      ).then((res) => {
        // 根据raw字段判断前端删除还是后端删除
        if (!file.raw) {
          delFile({
            fileId: file.fileId,
            uid: file.uid,
          })
            .then((res) => {
              this.delFileList.push(file.url);
              // console.log(this.fileList)
              // console.log(file)
              this.fileList.splice(fileList.findIndex(item => item.uid === file.uid), 1)
              return true;
            })
            .catch(() => {
              return false;
            });
        } else {
          this.formFileList.splice(
            this.formFileList.findIndex((item) => item.uid === file.uid),
            1,
          );
          this.$emit('onChange', this.fileList);
        }
      });
    },
    /**
     * 文件预览,默认是图片预览,如果是别的文件,则进行下载
     * @param {Object} e
     */
    handlePictureCardPreview(e) {
      // 图片预览
      if (this.$enum.imgFormat.includes(e.fileType)) {
        let imgList = [...this.fileList, ...this.formFileList]
          .filter((item) => this.$enum.imgFormat.includes(item.fileType))
          .map((item) => item.fileUrl);

        this.$ltImgsPreview.open(
          imgList,
          imgList.findIndex((item) => item === e.fileUrl),
        );
        return;
      }

      // 文件下载
      window.open(e.fileUrl);
    },
    /**
     * 文件上传
     */
    upload() {
      return new Promise((resolve, reject) => {
        try {
          if (this.formFileList.length) {
            // 处理图片的文件上传
            let formData = new FormData();
            this.formFileList.forEach((item) => {
              formData.append('files', item);
            });
            if (this.fileId) formData.append('fileId', this.fileId);
            uploads(formData)
              .then((res) => {
                if (this.onSuccess) {
                  this.onSuccess(
                    res,
                    this.formFileList.at(-1),
                    this.formFileList,
                  );
                }
                this.formFileList = [];

                resolve(res.data.length ? res.data[0].fileId : this.fileId);
                // 对 fileList 做追加操作
                // this.initImageList(res.data)
                // 重新渲染
                this.fileList = [];
                getFile({
                  fileId: this.fileId,
                }).then((res) => {
                  this.initImageList(res.data);
                });
              })
              .catch((e) => {
                this.onError &&
                  this.onError(e, this.formFileList.at(-1), this.formFileList);
              });

            return;
          }
          resolve(this.fileId);
        } catch (e) {
          this.onError &&
            this.onError(e, this.formFileList.at(-1), this.formFileList);
        }
      });
    },
    clear() {
      this.fileId=null
      this.fileList = []; // 用于展示的fileList
      this.formFileList = []; // 传递给后端的文件列表
      this.delFileList = []; // 已删除的文件url列表, 用于预览的筛选数据
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-upload-list--picture-card .el-upload-list__item,
::v-deep .el-upload--picture-card {
  width: 120px;
  height: 120px;
}

::v-deep .el-upload--picture-card {
  line-height: 122px !important;
}

::v-deep.hide .el-upload--picture-card {
  display: none;
}
</style>
