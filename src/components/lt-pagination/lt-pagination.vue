<template>
  <div v-if="total > 0" :class="{ 'hidden': hidden }" class="pagination-container">
    <el-pagination :background="background" :current-page.sync="currentPage" :page-size.sync="pageSize" :layout="layout"
      :page-sizes="pageSizes" :total="total" v-bind="$attrs" @size-change="handleSizeChange"
      @current-change="handleCurrentChange" :pager-count="Number(pagerCount)" />
  </div>
</template>

<script>
import { scrollTo } from '@/utils/scroll-to'

export default {
  props: {
    total: {
      required: true,
      type: Number
    },
    page: {
      type: Number,
      default: 1
    },
    limit: {
      type: Number,
      default: 20
    },
    pageSizes: {
      type: Array,
      default() {
        return [10, 20, 30, 50]
      }
    },
    layout: {
      type: String,
      default: 'total, sizes, prev, pager, next, jumper'
    },
    background: {
      type: Boolean,
      default: true
    },
    autoScroll: {
      type: <PERSON><PERSON>an,
      default: true
    },
    hidden: {
      type: <PERSON><PERSON><PERSON>,
      default: false
    },
    auto: {
      type: <PERSON>olean,
      default: true
    },
    pagerCount: {
      type: Number,
      default: 7
    }
  },
  computed: {
    currentPage: {
      get() {
        return this.page
      },
      set(val) {
        this.$emit('update:page', val)
      }
    },
    pageSize: {
      get() {
        return this.limit
      },
      set(val) {
        this.$emit('update:limit', val)
      }
    }
  },
  created() {
    if (this.auto) this.$emit('pagination', { page: this.currentPage, limit: this.pageSize })
  },
  methods: {
    handleSizeChange(val) {
      this.$emit('pagination', { page: this.currentPage, limit: val })
      if (this.autoScroll) {
        scrollTo(0, 800)
      }
    },
    handleCurrentChange(val) {
      this.$emit('pagination', { page: val, limit: this.pageSize })
      if (this.autoScroll) {
        scrollTo(0, 800)
      }
    }
  }
}
</script>

<style scoped >
.pagination-container {
  margin-top: 10px;
  display: flex;
  justify-content: center;
}

.pagination-container.hidden {
  display: none;
}

::v-deep .el-pagination.is-background .el-pager li:not(.disabled).active {
  background-color: var(--primary-color);
}
</style>
