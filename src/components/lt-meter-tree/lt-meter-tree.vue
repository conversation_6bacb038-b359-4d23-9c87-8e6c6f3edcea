<template>
  <div
    class="left bg-white radius-6"
    :style="{
      width: isOpen ? width : '0px',
      marginRight: isOpen ? '30px' : '15px',
      ...cssVar,
    }"
  >
    <div class="pd-t10 searchBox">
      <el-input v-model.trim="searchKey" :placeholder="placeholder">
        <i
          slot="suffix"
          class="el-input__icon el-icon-search pointer"
          @click="$refs.deviceTree.filter(searchKey)"
        />
      </el-input>
    </div>
    <el-scrollbar v-loading="loading" class="scrollbar" :style="cssVar">
      <vue-easy-tree
        ref="deviceTree"
        v-bind="$attrs"
        :data="treeData"
        node-key="id"
        :expand-on-click-node="false"
        highlight-current
        default-expand-all
        :filter-node-method="filterNode"
        :check-strictly="checkStrictly"
        :height="height"
        @check="getCheckedKeys"
        v-on="$listeners"
        @node-click="nodeClick"
      >
        <!-- text-ellipsis -->
        <div
          slot-scope="{ node, data }"
          class="flex-1 font-14"
          :class="{ noMinimumLevel: node.data.noMinimumLevel }"
        >
          <i
            v-if="data.dataType == 1"
            class="el-icon-set-up"
            style="color: var(--primary-color)"
          />
          <i
            v-if="data.dataType == 2"
            class="el-icon-odometer"
            style="color: #67c23a"
          />
          <el-tooltip effect="dark" :content="node.label" placement="bottom">
            <span>{{ node.label }}</span>
          </el-tooltip>
          <el-button
            v-if="
              data.children &&
              data.children.length !== 0 &&
              node.expanded == true
            "
            type="text"
            class="node-tree-node__expand-icon"
            size="mini"
            @click.stop="node.expanded = false"
            >缩收</el-button
          >
          <el-button
            v-if="
              data.children &&
              data.children.length !== 0 &&
              node.expanded == false
            "
            type="text"
            class="node-tree-node__expand-icon"
            size="mini"
            @click.stop="node.expanded = true"
            >展开</el-button
          >
        </div>
      </vue-easy-tree>
    </el-scrollbar>
    <div
      v-if="isShrink"
      class="bd-rd d-flex a-center j-center pointer"
      :style="{ right: isOpen ? '-20px' : '-5px' }"
      @click="isOpen = !isOpen"
    >
      <i
        class="el-icon-arrow-left"
        style="color: #ffffff"
        :class="{ clear: !isOpen }"
      />
    </div>
  </div>
</template>

<script>
import * as API from '@/api/system/dept.js';
// import { listMenu } from '@/api/system/menu'
import VueEasyTree from '@wchbrad/vue-easy-tree';
export default {
  components: {
    VueEasyTree,
  },
  props: {
    // 宽度
    width: {
      type: String,
      default: '280px',
    },
    // 高度
    height: {
      type: String,
      default: '',
    },
    // 占位符
    placeholder: {
      type: String,
      default: '请选择部门或仪表名称',
    },
    // 是否需要收缩功能
    isShrink: {
      type: Boolean,
      default: true,
    },
    // 查询粒度,device:设备  meter:仪表
    grain: {
      type: String,
      default: 'device',
    },
    // 默认选择第一个meter子节点,grain为meter时生效
    meterFirstNode: {
      type: Boolean,
      default: false,
    },
    // 默认选择第一个device子节点,grain为device时生效
    deviceFirstNode: {
      type: Boolean,
      default: false,
    },
    // 默认选择第一个部门
    deptFirstNode: {
      type: Boolean,
      default: true,
    },
    // 控制最大选中数量
    maxCheck: {
      type: Number,
      default: -1,
    },
    // 是否选中第一个节点
    checkFirstNode: {
      type: Boolean,
      default: false,
    },
    checkStrictly: {
      type: Boolean,
      default: false,
    },
    checkFirstChild: {
      type: Boolean,
      default: false,
    },
    /*
    // 隐藏不是最小层级的仪表
    hideNoMinLevel: {
      type: Boolean,
      default: false
    },
    */
  },
  data() {
    return {
      loading: true,
      treeData: [],
      typeDict: {
        // 类型字典,设备为1,仪表为2,用于取出当前节点的所有子节点类型与grain一致的数据
        device: 1,
        meter: 2,
      },
      isOpen: true, // 是否展开
      checkedKeys: [], // 当前选中的所有节点
      searchKey: '', // 搜索关键字
    };
  },
  computed: {
    cssVar() {
      // css中访问js的变量 https://www.cnblogs.com/coder--wang/p/15179784.html
      return {
        '--height': this.height,
      };
    },
    apiComp() {
      if (this.grain === 'device') return 'getTreeDevice';
      if (this.grain === 'meter') return 'getCdTreeMeter';
      return 'getTreeDevice';
    },
  },
  mounted() {
    this.getDeviceTreeData();
  },
  methods: {
    // getExpand() {
    //   let keys = [];
    //   const fun = function (level, arr) {
    //     if (level < 2) return;
    //     arr.forEach((item) => {
    //       keys.push(item.nodeId);
    //       if (item.children) {
    //         fun(level - 1, item.children);
    //       }
    //     });
    //   };
    //   fun(3, this.treeData);
    //   return keys;
    // },
    getFirstChild() {
      let res = null;
      const fun = function (arr) {
        let item = arr[0];
        if (!item.children || item.children.length == 0) {
          res = item;
          return;
        } else {
          fun(item.children);
        }
      };
      fun(this.treeData);
      return res;
    },
    /**
     * 获取设备树的数据
     */
    getDeviceTreeData() {
      this.loading = true;
      API[this.apiComp]({
        menuId: this.$route.meta.menuId,
        deptName: this.searchKey,
      })
        .then((res) => {
          this.treeData = res.data || {};
          let firstNode = {};
          if (
            this.deptFirstNode &&
            !(this.meterFirstNode || this.deviceFirstNode)
          )
            firstNode = this.treeData[0];
          if (this.meterFirstNode || this.deviceFirstNode) {
            // 查找树下的第一个节点(设备||仪表)
            const dfs = (root) => {
              // 1.先访问根节点
              console.log(this.grain)
              if (
                ['meter', 'device'].includes(this.grain) &&
                root.dataType == this.typeDict[this.grain] &&
                !Object.keys(firstNode).length
              ) {
                firstNode = root;
              }

              if (!Object.keys(firstNode).length) {
                // 2.对根节点的children挨个进行深度优先遍历
                root.children &&
                  root.children.forEach((item) => {
                    dfs(item);
                  });
              }
            };
            dfs(this.treeData[0]);
          }

          // 选择第一个节点
          if (Object.keys(firstNode).length) {
            this.$nextTick(() => {
              this.$refs.deviceTree.setCurrentKey(firstNode.nodeId);
              if (this.meterFirstNode || this.deviceFirstNode) {
                this.$emit('click', {
                  data: [firstNode.id],
                  type: 'node-click',
                });
              }

              if (
                this.deptFirstNode &&
                !(this.meterFirstNode || this.deviceFirstNode)
              ) {
                this.nodeClick(firstNode);
              }
            });
          }
          // 默认选中第一个节点
          if (this.checkFirstNode) {
            this.checkedKeys.push(this.treeData[0]);
            this.$refs.deviceTree.setCheckedKeys([this.treeData[0].nodeId]);
            this.$emit('checkChange', this.checkedKeys);
          }
          if (this.checkFirstChild) {
            let item = this.getFirstChild();
            this.checkedKeys.push(item);
            this.$refs.deviceTree.setCheckedKeys([item.nodeId]);
            this.$emit('checkChange', this.checkedKeys);
          }

          this.$emit('requestComplete', {
            data: this.treeData,
          });

          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
        });
      /*
      API[this.apiComp]().then(res => {
        this.treeData = res.data || {}
        let firstNode = res.data || {}

        if (this.grain === 'meter') {
          // 数据拼接
          const dfs = (root) => {
            // 1.先访问根节点
            if ('childMeter' in root && root.childMeter.length) {
              root.childMeter.forEach(item => {
                item.id = 'cust-' + item.id
              })
              // 设置第一个子节点
              if (typeof firstNode[0].id === 'number' && this.meterFirstNode && this.grain === 'meter') {
                firstNode = [root.childMeter[0]]
              }

              if (!root.children) {
                root.children = []
              } else {
                // 不是最小层级的仪表
                root.childMeter.forEach(item => {
                  item.noMinimumLevel = true
                })
              }
              root.children = [...root.children, ...root.childMeter]
            }

            // 2.对根节点的children挨个进行深度优先遍历
            root.children && root.children.forEach(item => {
              dfs(item)
            })
          }
          dfs(res.data[0])
        }
        this.$nextTick(() => {
          this.$refs.deviceTree.setCurrentKey(firstNode[0].id)

          // eslint-disable-next-line no-undef
          if (this.hideNoMinLevel) $('.noMinimumLevel').parent().hide()
        })
        this.$emit('node-click', firstNode[0])

        // 默认选中第一个节点
        if (this.checkFirstNode) {
          this.checkedKeys.push(this.treeData[0])
          this.$refs.deviceTree.setCheckedKeys([this.treeData[0].id])
          this.$emit('loadComplete', this.checkedKeys)
        }
        this.$emit('requestComplete', this.treeData)
        this.loading = false
      })
      */
    },

    /**
     * 节点点击
     * @param {Object} e
     */
    nodeClick(e) {
      let paramIds = [];

      // 根据点击的节点,获取其部门、工序、功能位置
      // 263工序，264功能位置，260，261，262，267，268，269部门
      // 工序: 返回工序以及第一个父级部门
      // 部门: 直接返回部门
      // 功能位置: 返回功能位置、第一个父级工序、第一个父级部门
      // 拼接参数,取当前节点下所有子节点类型与 grain 参数一致的业务数据id
      this.getParentNode(e);

      const dfs = (root) => {
        // 1.先访问根节点
        if (root.dataType == this.typeDict[this.grain]) paramIds.push(root.meterId);
        // 2.对根节点的children挨个进行深度优先遍历
        root.children &&
          root.children.forEach((item) => {
            dfs(item);
          });
      };
      dfs(e);

      this.$emit('click', {
        data: paramIds,
        type: 'node-click',
      });
    },
    /**
     * 判断勾选的子节点是否大于自定义最大数量，如果大于自定义数量，则替换最早选中的节点
     */
    getCheckedKeys(data, isCheck) {
      if (!this.checkStrictly) {
        if (this.searchKey) {
          const findNode = (treeData, name) => {
            return treeData.filter((item) => {
              return filterItem(item, name);
            });
          };

          const filterItem = (item, name) => {
            if (item.label.indexOf(name) !== -1) return true;

            if (item.children) {
              item.children = item.children.filter((sonEle) => {
                return filterItem(sonEle, name);
              });

              if (item.children.length > 0) {
                return true;
              }
            }

            return false;
          };
          this.$emit(
            'checkChange',
            findNode(this.$refs.deviceTree.getCheckedNodes(), this.searchKey),
          );
        } else {
          this.$emit('checkChange', this.$refs.deviceTree.getCheckedNodes());
        }
        return;
      }
      // 判断是否选中， 使用数组checkedKeys维护节点选中时的顺序，如果选中则追加节点nodeId到数组中，如果是取消选中则移除
      isCheck
        ? this.checkedKeys.push(data)
        : this.checkedKeys.splice(
            this.checkedKeys.findIndex((item) => item.nodeId === data.nodeId),
            1,
          );

      // 判断选中的节点数量是否大于设置的最大值
      if (this.maxCheck !== -1 && this.checkedKeys.length > this.maxCheck) {
        // 如果大于最大值， 取消最早选中的节点状态
        this.$refs.deviceTree.setChecked(this.checkedKeys[0].nodeId, false);
        // 添加新的节点选中状态
        this.$refs.deviceTree.setChecked(data.nodeId, true);
        // 移除最早节点nodeId，维护checkedKeys数组
        this.checkedKeys.shift();
      }

      this.$emit('checkChange', this.checkedKeys);
    },
    /**
     * 筛选节点
     * @param {Object} value
     * @param {Object} data
     */
    filterNode(value, data) {
      if (!value) return true;

      return data.label.includes(value);
    },
    /**
     * 刷新
     */
    refresh() {
      this.getDeviceTreeData();
    },
    /**
     * 根据当前节点的类型获取上级节点
     * 部门(260,261,262,267,268,269): 直接返回部门
     * 工序(263): 返回工序以及第一个父级部门
     * 功能位置(264): 返回功能位置、第一个父级工序、第一个父级部门
     * 以上为后端code为后端字典,若有改动
     * @param {Object} node
     */
    getParentNode(node) {
      let relation = new Map();
      let level = -1;
      let defaultObj = {};
      // value表示总共向上取几层父级节点
      relation.set([260, 261, 262, 267, 268, 269], 0);
      relation.set([263], 1);
      relation.set([264], 2);

      for (let [key, value] of relation) {
        if (key.includes(node.dataType)) level = value;
      }

      let obj = {
        0: 'deptId',
        1: 'produceId',
        2: 'locationId',
      };

      const dfs = (data, i) => {
        data.forEach((item) => {
          if (
            item.children &&
            item.children.map((item) => item.nodeId).includes(node.nodeId)
          ) {
            defaultObj[obj[i]] = item.id;
            node = item;
          }
          item.children && dfs(item.children, i);
        });
      };

      if (level !== -1) {
        // 根据level进行直接设置
        defaultObj[obj[level]] = node.id;
        level--;
        // 向上查找
        for (let i = level; i > -1; i--) {
          dfs(this.treeData, i);
        }
      }

      this.$emit('review', defaultObj);
    },
  },
};
</script>

<style lang="scss" scoped>
.scrollbar {
  width: 100%;
  height: var(--height);
}
::v-deep .el-tree-node__content > .el-tree-node__expand-icon {
  padding: 3px;
}
// ::v-deep .el-scrollbar__wrap {
//   overflow-x: hidden !important;
// }
::v-deep .el-scrollbar__wrap {
  overflow-x: auto;
  height: calc(100% + 20px); //多出来的20px是横向滚动条默认的样式
}
::v-deep .el-scrollbar .el-scrollbar__wrap .el-scrollbar__view {
  white-space: nowrap;
  display: inline-block;
}

.el-tree {
  border-radius: 6px;
  padding: 5px;
  min-height: var(--height);
}

.left {
  // width: 200px;
  position: relative;
  // margin-right: 20px;
  transition: 0.5s;
}

.bd-rd {
  border-radius: 0 50px 50px 0;
  width: 20px;
  height: 40px;
  background-color: #e6ebf5;
  position: absolute;
  // top: 50%;
  // transform: translateY(-50%);
  top: calc(var(--height) / 2);
}

.clear {
  transform: rotate(180deg); //旋转180°
}

::v-deep
  .el-tree--highlight-current
  .el-tree-node.is-current
  > .el-tree-node__content {
  color: #4d95fd;
  font-weight: bold;
  // width: 234px;
}
::v-deep .el-scrollbar__view {
  width: 100%;
}
::v-deep .el-tree-node__content > .el-checkbox {
  margin-right: 5px;
}
::v-deep .searchBox .el-input {
  width: 90%;
  margin-left: 5%;
  overflow: hidden;
}
::v-deep .searchBox .el-input--medium .el-input__inner {
  height: 30px;
  line-height: 30px;
}
::v-deep .searchBox .el-input__suffix {
  top: -2px;
}
.node-tree-node__expand-icon {
  margin-top: -5px;
  position: absolute;
  right: 10px;
  font-size: 12px;
}
</style>
