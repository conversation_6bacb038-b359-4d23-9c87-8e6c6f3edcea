<template>
  <el-select
    v-model="dataValue"
    :loading="loading"
    reserve-keyword
    :filter-method="filterMethod"
    filterable
    v-bind="$attrs"
    @change="change"
  >
    <el-option
      v-for="item in selectList"
      :key="item[selectLabel] + item[selectValue]"
      :label="item[selectLabel]"
      :value="item[selectValue]"
    >
      <slot :item="item"> {{ item[selectLabel] }} </slot>
    </el-option>
    <el-button-group class="d-flex pd-lr20">
      <el-button
        :disabled="current === 1"
        type="text"
        icon="el-icon-arrow-left"
        @click="switchPages('up')"
      >
        上一页
      </el-button>
      <div class="flex-1 font-13 d-flex a-center j-center">
        <span style="color: #606266;">{{ current }}</span>/<span style="color: var(--primary-color);">{{ totalPages }}</span>
      </div>
      <el-button
        :disabled="totalPages === current"
        type="text"
        @click="switchPages('down')"
      >
        下一页<i class="el-icon-arrow-right el-icon--right" />
      </el-button>
    </el-button-group>
  </el-select>
</template>

<script>
import * as API from '@/api/selectPage/selectPage.js'

export default {
  name: 'LtSelectPage',
  props: {
    value: null,
    api: {
      type: String,
      default: '',
      required: true
    },
    // select的label
    selectLabel: {
      type: String,
      default: 'label'
    },
    // select的value
    selectValue: {
      type: String,
      default: 'value'
    },
    // 筛选项传递给后端的key值
    searchKey: {
      type: String,
      default: 'name'
    },
    // 当前选中的id
    choiceId: {
      type: Number
    },
    // 自动加载
    auto: {
      type: Boolean,
      default: true
    },
    // 模式,分为2种,钟宇星写的取data.pages, 周敏写的取total(后面他会改)
    mode: {
      type: String,
      default: 'new'
    },
    // 下拉分页请求的额外参数
    paramObj: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      dataValue: null,
      loading: false,
      selectList: [], // select数据
      current: 1, // 当前页码
      size: 10, // 每页的数量
      itemName: '', // 筛选关键字
      totalPages: 1 // 总页数
    }
  },
  watch: {
    value: {
      handler(val) {
        this.dataValue = val
      },
      immediate: true
    }
  },
  mounted() {
    if (this.auto) this.getSelectPage()
  },
  methods: {
    change(val) {
      this.$emit('input', val)
      this.$emit('change', this.selectList.filter(item => item[this.selectValue] === val)[0])
    },
    /**
     * 数据的模糊查询
     */
    filterMethod(query) {
      this.loading = true
      this.current = 1
      this.size = 10
      this.itemName = query
      this.getSelectPage()
    },
    /**
     * 查询下拉分页的数据
     * @param {Object} choiceId   当前选中的id
     */
    getSelectPage(choiceId = '') {
      this.loading = true
      let param = {
        pageNum: this.current,
        pageSize: this.size,
        choiceId: this.current === 1 ? (choiceId || this.choiceId) : ''
      }
      param[this.searchKey] = this.itemName
      // 参数拼接
      param = { ...param, ...this.paramObj }

      API[this.api](param).then(res => {
        if (this.mode === 'new') {
          this.selectList = res.data.records
          this.totalPages = res.data.pages
        } else {
          this.selectList = res.rows
          this.totalPages = Math.ceil(res.total / this.size)
        }
        this.loading = false
      }).catch(e => {
        this.loading = false
      })
    },
    /**
     * 切换页面,
     * upper为上一页
     * lower为下一页
     */
    switchPages(direction) {
      direction === 'up' ? this.current-- : this.current++
      this.getSelectPage()
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
