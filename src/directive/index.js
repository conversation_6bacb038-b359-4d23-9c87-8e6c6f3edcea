import hasRole from './permission/hasRole'
import hasPermi from './permission/hasPermi'
import dialogDrag from './dialog/drag'
import dragX from './dragX/dragX'
import dragDialog from '@/directive/el-drag-dialog/drag'
import draggable from './draggable/draggable';

const install = function(Vue) {
  Vue.directive('hasRole', hasRole)
  Vue.directive('hasPermi', hasPermi)
  Vue.directive('dialogDrag', dialogDrag)
  Vue.directive('dragDialog', dragDialog)
  Vue.directive('dragX', dragX)
  Vue.directive('draggable', draggable);
}

if (window.Vue) {
  window['hasRole'] = hasRole
  window['hasPermi'] = hasPermi
  window['dialogDrag'] = dialogDrag
  Vue.use(install); // eslint-disable-line
}

export default install
