export default {
  bind(el, binding) {
    // 确保元素有定位属性
    if (window.getComputedStyle(el).position === 'static') {
      el.style.position = 'fixed';
    }

    // 设置初始位置
    const savedLeft = localStorage.getItem('aiAgentImgLeft');
    const savedTop = localStorage.getItem('aiAgentImgTop');

    if (savedLeft && savedTop) {
      el.style.left = savedLeft;
      el.style.top = savedTop;
    } else {
      el.style.left = '50px';
      el.style.top = '50px';
    }

    let startX = 0;
    let startY = 0;
    let originalLeft = 0;
    let originalTop = 0;
    let isDragging = false;
    let animationId = null;
    let pendingUpdate = false;

    // 使用 requestAnimationFrame 优化性能，添加节流机制
    function updatePosition(targetEl, newLeft, newTop) {
      if (pendingUpdate) {
        return; // 如果已有待执行的更新，则跳过
      }

      pendingUpdate = true;
      if (animationId) {
        cancelAnimationFrame(animationId);
      }

      animationId = requestAnimationFrame(() => {
        targetEl.style.left = `${newLeft}px`;
        targetEl.style.top = `${newTop}px`;
        pendingUpdate = false;
      });
    }

    // 添加拖拽状态类
    function addDraggingClass(targetEl) {
      targetEl.classList.add('dragging');
    }

    // 移除拖拽状态类
    function removeDraggingClass(targetEl) {
      targetEl.classList.remove('dragging');
    }

    // 鼠标按下事件
    el.addEventListener('mousedown', (e) => {
      e.preventDefault();
      e.stopPropagation();

      startX = e.clientX;
      startY = e.clientY;

      // 确定要移动的目标元素
      let targetEl = el;
      if (el.classList.contains('header')) {
        targetEl = el.parentElement;
      }

      // 获取目标元素的当前位置（基于视口）
      const rect = targetEl.getBoundingClientRect();
      originalLeft = rect.left;
      originalTop = rect.top;
      isDragging = false;

      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    });

    // 鼠标移动事件处理
    function handleMouseMove(e) {
      const dx = e.clientX - startX;
      const dy = e.clientY - startY;

      // 如果移动距离超过5px，标记为拖拽
      if (Math.sqrt(dx ** 2 + dy ** 2) > 5) {
        if (!isDragging) {
          isDragging = true;
          // 开始拖拽时添加拖拽状态类
          let targetEl = el;
          if (el.classList.contains('header')) {
            targetEl = el.parentElement;
          }
          addDraggingClass(targetEl);
        }
      }

      if (isDragging) {
        let newLeft = originalLeft + dx;
        let newTop = originalTop + dy;

        // 确定要移动的目标元素和尺寸
        let targetEl = el;
        if (el.classList.contains('header')) {
          targetEl = el.parentElement;
        }

        // 获取屏幕尺寸和元素尺寸
        const screenWidth = window.innerWidth;
        const screenHeight = window.innerHeight;
        const elWidth = targetEl.offsetWidth;
        const elHeight = targetEl.offsetHeight;

        // 限制在屏幕范围内
        newLeft = Math.max(0, Math.min(newLeft, screenWidth - elWidth));
        newTop = Math.max(0, Math.min(newTop, screenHeight - elHeight));

        // 使用优化的位置更新方法
        updatePosition(targetEl, newLeft, newTop);

        // 触发自定义事件，通知Vue组件更新数据
        const event = new CustomEvent('position-update', {
          detail: {
            left: `${newLeft}px`,
            top: `${newTop}px`
          }
        });
        el.dispatchEvent(event);
      }
    }

    // 鼠标抬起事件处理
    function handleMouseUp(e) {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);

      // 取消未执行的动画帧
      if (animationId) {
        cancelAnimationFrame(animationId);
        animationId = null;
      }

      // 如果是拖拽操作，移除拖拽状态类并阻止点击事件
      if (isDragging) {
        let targetEl = el;
        if (el.classList.contains('header')) {
          targetEl = el.parentElement;
        }
        removeDraggingClass(targetEl);

        e.preventDefault();
        e.stopPropagation();
        // 阻止后续的点击事件
        const clickHandler = (e) => {
          e.preventDefault();
          e.stopPropagation();
          document.removeEventListener('click', clickHandler, true);
        };
        document.addEventListener('click', clickHandler, true);
      }

      // 保存位置到本地存储
      let targetEl = el;
      if (el.classList.contains('header')) {
        targetEl = el.parentElement;
      }

      if (!el.classList.contains('header')) {
        localStorage.setItem('aiAgentImgLeft', targetEl.style.left);
        localStorage.setItem('aiAgentImgTop', targetEl.style.top);
      }
    }

  },

  unbind(el) {
    // 清理资源
    el.style.cursor = '';
    el.style.userSelect = '';

    // 确保移除拖拽状态类
    let targetEl = el;
    if (el.classList.contains('header')) {
      targetEl = el.parentElement;
    }
    if (targetEl) {
      targetEl.classList.remove('dragging');
    }
  }
};
