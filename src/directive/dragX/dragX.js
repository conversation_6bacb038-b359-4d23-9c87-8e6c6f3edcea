// drawerDragDirective.js
// 定义指令
const drawerDragDirective = {
    // 指令绑定时的处理函数
    bind(el, ) {
      const minWidth = 300;
      const dragDom = el.querySelector('.el-drawer');
      
      // 创建用于调整大小的元素
      const resizeElL = document.createElement('div');
      const img = new Image(24, 38);
      dragDom.appendChild(resizeElL);
      resizeElL.style.cursor = 'w-resize';
      resizeElL.style.position = 'absolute';
      resizeElL.style.userSelect = 'none';
      resizeElL.style.height = '100%';
      resizeElL.style.width = '10px';
      resizeElL.style.left = '0';
      resizeElL.style.top = '0px';
      img.style.position = 'absolute';
      img.style.left = '-12px';
      img.style.top = '50%';
  
      
      
      // 鼠标按下事件处理函数
      resizeElL.onmousedown = (e) => {
        const elW = dragDom.clientWidth;
        const EloffsetLeft = dragDom.offsetLeft;
        const clientX = e.clientX;
        
        document.onmousemove = function (e) {
          e.preventDefault();
          e.stopPropagation()
          // 左侧鼠标拖拽位置
          if (clientX > EloffsetLeft && clientX < EloffsetLeft + 10) {
            // 往左拖拽
            if (clientX > e.clientX) {
              dragDom.style.width = elW + (clientX - e.clientX) + 'px';
            }
            // 往右拖拽
            if (clientX < e.clientX) {
              if (dragDom.clientWidth >= minWidth) {
                dragDom.style.width = elW - (e.clientX - clientX) + 'px';
              }
            }
          }
        }
        // 拉伸结束
        document.onmouseup = function () {
          document.onmousemove = null;
          document.onmouseup = null;
        }
      }
    }
  };
  
  // 导出指令
  export default drawerDragDirective;