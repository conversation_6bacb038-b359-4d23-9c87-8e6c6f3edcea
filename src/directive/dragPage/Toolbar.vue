<template>
  <div>
    <div class="toolbar">

      <el-button class="mr-l10" @click="undo">后退</el-button>

      <el-button @click="redo">前进</el-button>

      <label for="input" class="insert">插入图片</label>

      <input id="input" type="file" hidden @change="handleFileChange"/>

      <el-button style="margin-left: 10px" @click="preview">预览</el-button>

      <el-button @click="clearCanvas">清空画布</el-button>

      <el-button :disabled="!curComponent || curComponent.isLock" @click="lock">锁定</el-button>
      <el-button
        :disabled="!curComponent || !curComponent.isLock"
        @click="unlock"
      >解锁
      </el-button
      >
      <span style="margin:0 10px">宽度:</span>
      <el-input style="width:120px" @blur="changeStyle(changeWidth,'width')" type="number" v-model="changeWidth">
        <template slot="append">mm</template>
      </el-input>
      <span style="padding: 0 6px;"> - </span>
      <el-input style="width:120px" disabled type="number" v-model="canvasStyleData.width">
        <template slot="append">px</template>
      </el-input>

      <span style="margin:0 10px">高度:</span>
      <el-input style="width:120px" @blur="changeStyle(changeHeight,'height')" type="number" v-model="changeHeight">
        <template slot="append">mm</template>
      </el-input>
      <span style="padding: 0 6px;"> - </span>
      <el-input style="width:120px" disabled type="number" v-model="canvasStyleData.height">
        <template slot="append">px</template>
      </el-input>

      <!--      <el-button style="margin-left:10px" @click="save">保存</el-button>-->
      <!--      <el-button @click="$router.go(-1)">退出</el-button>-->
      <el-button style="margin-left:20px" @click="setTemplate">设为模板</el-button>

      <el-button style="margin-left:20px" class="el-icon-check" type="danger" @click="saveBarCodeInfo" v-if="showSaveBtn"> 保存</el-button>
      <!-- <span style="margin:0 10px">DPI:</span>
      <el-input style="width:100px" type="number" value="300" disabled></el-input> -->

      <!-- 预览 -->
      <Preview ref="PreviewRef" :componentData="componentData" :canvasStyleData="canvasStyleData"
               @change="handlePreviewChange"/>
    </div>
  </div>
</template>

<script>
import toast from "@/directive/dragPage/utils/toast";
import Preview from "@/directive/dragPage/Editor/Preview";
import {
  commonStyle,
  commonAttr,
} from "@/directive/dragPage/custom-component/component-list";
import eventBus from "@/magic-editor/scripts/bus";

export default {
  components: {
    Preview,
  },
  props: ["curComponent", 'canvasStyleData', 'componentData','Dpi', 'showSaveBtn'],
  data() {
    return {
      changeWidth: 400,
      changeHeight: 400
    };
  },
  created() {
    eventBus.$on("preview", this.preview);
    eventBus.$on("clearCanvas", this.clearCanvas);
  },
  watch: {
    "canvasStyleData": {
      handler(newVal, oldVal) {
        this.changeWidth = (parseInt(newVal.width) / 600) * 25
        this.changeHeight = (parseInt(newVal.height) / 600) * 25
      },
      deep: true
    },
  },
  methods: {
    changeStyle(val, type){
      // 1英寸 = 25 毫米
      if(type == 'width'){
        this.canvasStyleData.width = val / 25 * 600
      } else {
        this.canvasStyleData.height = val / 25 * 600
      }

      this.$emit('changeStyle', this.canvasStyleData)
    },
    handleDpiChange(value){
      this.$emit("changeDpi", 600)
    },
    setTemplate(){
      eventBus.$emit("setTemplate");
    },
    saveBarCodeInfo(){
      eventBus.$emit("saveBarCodeInfo");
    },
    lock() {
      eventBus.$emit("lock");
    },

    unlock() {
      eventBus.$emit("unlock");
    },

    undo() {
      eventBus.$emit("undo");
    },

    redo() {
      eventBus.$emit("redo");
    },

    handleFileChange(e) {
      const file = e.target.files[0];
      if (!file.type.includes("image")) {
        toast("只能插入图片");
        return;
      }

      const reader = new FileReader();
      reader.onload = (res) => {
        const fileResult = res.target.result;
        const img = new Image();
        img.onload = () => {
          eventBus.$emit("addComponent", {

            ...commonAttr,
            id: Math.random(),
            component: "Picture",
            label: "图片",
            icon: "",
            propValue: fileResult,
            style: {
              ...commonStyle,
              top: 0,
              left: 0,
              width: img.width,
              height: img.height,
            },

          });

          eventBus.$emit("recordSnapshot");

          // 修复重复上传同一文件，@change 不触发的问题
          document.querySelector("#input").setAttribute("type", "text");
          document.querySelector("#input").setAttribute("type", "file");
        };

        img.src = fileResult;
      };

      reader.readAsDataURL(file);
    },
    /**
     * 查看二维码
     */
    preview() {
      this.$refs.PreviewRef.dialogVisible = true;
      eventBus.$emit("setEditMode", "preview");
    },
    /**
     * 保存
     */

    save() {
      eventBus.$emit("save");
    },

    clearCanvas() {
      eventBus.$emit("setComponentData", []);
      eventBus.$emit("setCurComponent", null);
      eventBus.$emit("recordSnapshot");
    },
    handlePreviewChange() {
      eventBus.$emit("setEditMode", "edit");
    },
  },
};
</script>

<style lang="scss" scoped>
.toolbar {
  height: 64px;
  display: flex;
  align-items: center;
  background: #fff;
  border-bottom: 1px solid #ddd;

  .canvas-config {
    display: inline-block;
    margin-left: 10px;
    font-size: 14px;
    color: #606266;

    input {
      width: 50px;
      margin-left: 10px;
      outline: none;
      padding: 0 5px;
      border: 1px solid #ddd;
      color: #606266;
    }

    span {
      margin-left: 10px;
    }
  }

  .insert {
    display: inline-block;
    line-height: 1;
    white-space: nowrap;
    cursor: pointer;
    background: #fff;
    border: 1px solid #dcdfe6;
    color: #606266;
    -webkit-appearance: none;
    text-align: center;
    box-sizing: border-box;
    outline: 0;
    margin: 0;
    transition: 0.1s;
    font-weight: 500;
    padding: 9px 15px;
    font-size: 12px;
    border-radius: 3px;
    margin-left: 10px;

    &:active {
      color: #3a8ee6;
      border-color: #3a8ee6;
      outline: 0;
    }

    &:hover {
      background-color: #ecf5ff;
      color: #3a8ee6;
    }
  }
}
</style>
