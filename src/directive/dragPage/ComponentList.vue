<template>
  <el-scrollbar style="height: calc(60vh - 158px);" wrap-style="overflow-x:hidden;">
    <div class="component-list" @dragstart="handleDragStart">
      <!--默认组件-->

      <div
        v-for="(item, index) in componentList"
        :key="index"
        class="list"
        draggable
        :data-index="index"
      >
        <span class="iconfont" :class="'icon-' + item.icon" />
        <span>{{ item.label }}</span>
      </div>
      <br>
    </div>
  </el-scrollbar>
</template>

<script>
import componentList from '@/directive/dragPage/custom-component/component-list'
export default {
  data() {
    return {
      componentList
    }
  },
  created() {

  },
  methods: {
    handleDragStart(e) {
      e.dataTransfer.setData('index', e.target.dataset.index)
    }
  }
}
</script>

<style lang="scss" scoped>
.component-list {
      flex-wrap: wrap;
      -webkit-box-pack: justify;
      padding: 10px;
    .list {
        width: 100%;
        border: 1px solid #ddd;
        cursor: grab;
        margin-bottom: 10px !important;
        text-align: center;
        color: #333;
        padding: 5px 5px;
        display: flex;
        align-items: center;

        &:active {
            cursor: grabbing;
        }

        .iconfont {
            margin-right: 4px;
            font-size: 20px;
        }
        .icon-wenben,

        .icon-tupian {
            font-size: 18px;
        }
    }
}
 ::v-deep .list span{
          overflow: hidden !important;
           white-space: nowrap !important;
           text-overflow: ellipsis !important;
        }
</style>
