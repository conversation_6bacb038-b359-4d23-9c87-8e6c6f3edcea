<template>
  <div class="home">
    <Toolbar   :componentData="componentData" :curComponent="curComponent" :canvasStyleData="canvasStyleData"  @changeStyle="changeStyle"/>

    <main>
      <!-- 左侧组件列表 -->
      <section class="left">
        <ComponentList />
      </section>
      <!-- 中间画布 -->
      <section class="center">
        <div
          class="content"
          @drop="handleDrop"
          @dragover="handleDragOver"
          @mousedown="handleMouseDown"
          @mouseup="deselectCurComponent"
        >
          <Editor
            :menuTop="menuTop"
            :menuLeft="menuLeft"
            :menuShow="menuShow"
            :editMode="editMode"
            :componentData="componentData"
            :curComponent="curComponent"
            :canvasStyleData="canvasStyleData"
          />
        </div>
      </section>
      <!-- 右侧属性列表 -->
      <section class="right">
        <el-tabs v-model="activeName">
          <el-tab-pane label="属性" name="attr">
            <AttrList v-if="curComponent" :curComponent="curComponent" />
            <p v-else class="placeholder">请选择组件</p>
          </el-tab-pane>
        </el-tabs>
      </section>
    </main>
  </div>
</template>

<script>
import Editor from "./Editor/index";
import ComponentList from "./ComponentList"; // 左侧列表组件
import AttrList from "./AttrList"; // 右侧属性列表
import componentList from "./custom-component/component-list"; // 左侧列表数据
import Toolbar from "./Toolbar";
import { deepCopy, swap } from "./utils/utils";
import eventBus from "./utils/eventBus";
import toast from "@/directive/dragPage/utils/toast";
import generateID from "./utils/generateID";
import { listenGlobalKeyDown } from "./utils/shortcutKey";
import { updateCodeManage } from "@/api/columnEditApi/columnEditApi.js";
export default {
  components: {
    Editor,
    ComponentList,
    AttrList,
    Toolbar,
  },
  data() {
    return {
      activeName: "attr",
      componentData: [],
      curComponent: null,
      curComponentIndex: 0,
      isClickComponent: false,
      canvasStyleData: {
        // 页面全局数据
        width: 400,
        height: 400,
      },
      editMode: "edit",
      snapshotData: [],
      snapshotIndex: -1,
      obj: {},
      menuTop: 0, // 右击菜单数据
      menuLeft: 0,
      menuShow: false,
      isClickComponent: false,
      copyData: null, // 复制粘贴剪切
      isCut: false,
    };
  },
  created() {
    listenGlobalKeyDown();
    eventBus.$on("setEditMode", this.setEditMode);
    eventBus.$on("setComponentData", this.setComponentData);
    eventBus.$on("addComponent", this.addComponent);
    eventBus.$on("deleteComponent", this.deleteComponent);
    eventBus.$on("lock", this.lock);
    eventBus.$on("unlock", this.unlock);
    eventBus.$on("undo", this.undo);
    eventBus.$on("redo", this.redo);
    eventBus.$on("save", this.save);
    eventBus.$on("recordSnapshot", this.recordSnapshot);
    eventBus.$on("showContextMenu", this.showContextMenu);
    eventBus.$on("hideContextMenu", this.hideContextMenu);
    eventBus.$on("setShapeStyle", this.setShapeStyle);
    eventBus.$on("setClickComponentStatus", this.setClickComponentStatus);
    eventBus.$on("paste", this.paste);
    eventBus.$on("copy", this.copy);
    eventBus.$on("cut", this.cut);
    eventBus.$on("upComponent", this.upComponent);
    eventBus.$on("downComponent", this.downComponent);
    eventBus.$on("topComponent", this.topComponent);
    eventBus.$on("bottomComponent", this.bottomComponent);
    eventBus.$on("setCurComponent", this.setCurComponent);
  },
  beforeDestroy() {
	  eventBus.$off('setEditMode')
	  eventBus.$off('setComponentData')
	  eventBus.$off('addComponent')
	  eventBus.$off('deleteComponent')
	  eventBus.$off('lock')
	  eventBus.$off('unlock')
	  eventBus.$off('undo')
	  eventBus.$off('redo')
	  eventBus.$off('save')
	  eventBus.$off('recordSnapshot')
	  eventBus.$off('showContextMenu')
	  eventBus.$off('setShapeStyle')
	  eventBus.$off('setClickComponentStatus')
	  eventBus.$off('paste')
	  eventBus.$off('copy')
	  eventBus.$off('cut')
	  eventBus.$off('upComponent')
	  eventBus.$off('downComponent')
	  eventBus.$off('topComponent')
	  eventBus.$off('bottomComponent')
	  eventBus.$off('setCurComponent')
  }, 
  mounted() {
    let obj = this.$route.params.obj;
    this.obj = obj;
    let res=obj.codeJson?JSON.parse(obj.codeJson):
    {
      canvasStyleData: {
        width: 400,
        height: 400,
      },
      componentData:[]
    }
    this.componentData = res.componentData
    this.canvasStyleData = res.canvasStyleData
    
  },
  methods: {
    changeStyle(obj){
      console.log(obj)
      this.canvasStyleData=obj
    },
    setCurComponent( component, index ) {
            this.curComponent = component
            this.curComponentIndex = index
        },
    upComponent() {
      let { componentData, curComponentIndex } = this;
      // 上移图层 index，表示元素在数组中越往后
      if (curComponentIndex < componentData.length - 1) {
        swap(componentData, curComponentIndex, curComponentIndex + 1);
      } else {
        toast("已经到顶了");
      }
    },

    downComponent() {
      let { componentData, curComponentIndex } = this;

      // 下移图层 index，表示元素在数组中越往前
      if (curComponentIndex > 0) {
        swap(componentData, curComponentIndex, curComponentIndex - 1);
      } else {
        toast("已经到底了");
      }
    },

    topComponent() {
      let { componentData, curComponentIndex } = this;

      // 置顶
      if (curComponentIndex < componentData.length - 1) {
        swap(componentData, curComponentIndex, componentData.length - 1);
      } else {
        toast("已经到顶了");
      }
    },

    bottomComponent() {
      let { componentData, curComponentIndex } = this;

      // 置底
      if (curComponentIndex > 0) {
        swap(componentData, curComponentIndex, 0);
      } else {
        toast("已经到底了");
      }
    },
    paste(isMouse) {
      if (!this.copyData) {
        toast("请选择组件");
        return;
      }

      const data = this.copyData.data;

      if (isMouse) {
        data.style.top = this.menuTop;
        data.style.left = this.menuLeft;
      } else {
        data.style.top += 10;
        data.style.left += 10;
      }

      data.id = generateID();
      this.addComponent(data);
      if (this.isCut) {
        this.copyData = null;
      }
    },
    copy() {
      if (!this.curComponent) return;
      this.copyData = {
        data: deepCopy(this.curComponent),
        index: this.curComponentIndex,
      };

      this.isCut = false;
    },
    cut() {
      if (!this.curComponent) {
        toast("请选择组件");
        return;
      }

      if (this.copyData) {
        const data = deepCopy(this.copyData.data);
        const index = this.copyData.index;
        data.id = generateID();
        this.addComponent(data, index);
        if (this.curComponentIndex >= index) {
          // 如果当前组件索引大于等于插入索引，需要加一，因为当前组件往后移了一位
          this.curComponentIndex++;
        }
      }

      this.copy();
      this.deleteComponent();
      this.isCut = true;
    },
    setClickComponentStatus(status) {
      this.isClickComponent = status;
    },
    setShapeStyle({ top, left, width, height, rotate }) {
      if (top) this.curComponent.style.top = top;
      if (left) this.curComponent.style.left = left;
      if (width) this.curComponent.style.width = width;
      if (height) this.curComponent.style.height = height;
      if (rotate) this.curComponent.style.rotate = rotate;
    },
    showContextMenu(top, left) {
      this.menuShow = true;
      this.menuTop = top;
      this.menuLeft = left;
    },
    hideContextMenu(state) {
      this.menuShow = false;
    },
    save() {
      updateCodeManage({
        ...this.obj,
        codeJson: JSON.stringify({
          componentData: this.componentData,
          canvasStyleData:this.canvasStyleData
        } ),
      }).then((res) => {
        this.$message.success("保存成功");
        this.$router.go(-1);
      });
    },
    undo() {
      if (this.snapshotIndex >= 0) {
        this.snapshotIndex--;
        this.componentData = deepCopy(this.snapshotData[this.snapshotIndex]);
      }
    },
    redo() {
      if (this.snapshotIndex < this.snapshotData.length - 1) {
        this.snapshotIndex++;
        this.componentData = deepCopy(this.snapshotData[this.snapshotIndex]);
      }
    },
    recordSnapshot() {
      // 添加新的快照
      this.snapshotData[++this.snapshotIndex] = deepCopy(this.componentData);
      // 在 undo 过程中，添加新的快照时，要将它后面的快照清理掉
      if (this.snapshotIndex < this.snapshotData.length - 1) {
        this.snapshotData = this.snapshotData.slice(0, this.snapshotIndex + 1);
      }
    },
    lock() {
      this.curComponent.isLock = true;
    },
    unlock() {
      this.curComponent.isLock = false;
    },
    setEditMode(type) {
      this.setEditMode = type;
    },
    setComponentData(componentData) {
      this.componentData = componentData;
    },
    addComponent(component, index) {
      if (index !== undefined) {
        this.componentData.splice(index, 0, component);
      } else {
        this.componentData.push(component);
      }
    },
    deleteComponent(index) {
      if (index === undefined) {
        index = this.curComponentIndex;
      }
      if (index == this.curComponentIndex) {
        this.curComponentIndex = null;
        this.curComponent = null;
      }

      this.componentData.splice(index, 1);
    },
    handleDrop(e) {
      e.preventDefault();
      e.stopPropagation();
      const index = e.dataTransfer.getData("index");
      if (index) {
        const component = deepCopy(componentList[index]);
        component.style.top = e.offsetY;
        component.style.left = e.offsetX;
        component.id = generateID();
        this.addComponent(component);
        this.recordSnapshot();
      }
    },

    handleDragOver(e) {
      e.preventDefault();
      e.dataTransfer.dropEffect = "copy";
    },

    handleMouseDown() {
      this.setClickComponentStatus(false);
    },

    deselectCurComponent(e) {
      if (!this.isClickComponent) {
        this.setCurComponent,({
          component: null,
          index: null,
        });
      }

      // 0 左击 1 滚轮 2 右击
      if (e.button != 2) {
        this.hideContextMenu();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.home {
  height: calc(100vh - 94px);
  background: #fff;

  main {
    height: calc(100% - 64px);
    position: relative;

    .left {
      position: absolute;
      height: 100%;
      width: 200px;
      left: 0;
      top: 0;
      // padding-top: 10px;
    }

    .right {
      position: absolute;
      height: 100%;
      width: 262px;
      right: 0;
      top: 0;
    }

    .center {
      margin-left: 200px;
      margin-right: 262px;
      background: #f5f5f5;
      height: 100%;
      overflow: auto;
      padding: 20px;

      .content {
        width: 100%;
        height: 100%;
        overflow: auto;
      }
    }
  }

  .placeholder {
    text-align: center;
    color: #333;
  }
}
</style>
