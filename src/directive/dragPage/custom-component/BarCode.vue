<template>
    <div style="overflow: hidden">
        <barcode
      :value="propValue"
      :displayValue="false"
      :width="element.style && element.style.width/74"
      :height="element.style && element.style.height-44"
      style="text-align: center"
    />
    </div>
</template>

<script>
export default {
    props: {
        propValue: {
            type: String,
            require: true,
        },
        element: {
            type: Object,
        },
    },
}
</script>

<style lang="scss" scoped>

</style>
