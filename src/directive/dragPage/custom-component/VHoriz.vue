<template>
  <div class="horizontalline" style="height:100%">
    <v-text  :editMode="editMode" :prop-value="element.propValue" :element="element" />
  </div>
</template>

<script>
export default {
  props: {
    propValue: {
      type: String,
      require: true
    },
    element: {
      type: Object
    },
    editMode:{
      type:String
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
