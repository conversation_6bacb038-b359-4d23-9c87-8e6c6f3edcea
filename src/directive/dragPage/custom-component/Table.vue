<template>
  <div class="bar-code-table" style="overflow: hidden">
    <table>
      <colgroup>
        <col
          v-for="(col, idx) in element.style.tableHeader"
          :key="idx"
          :style="{ width: getColumnWidth(col, idx) }"
        />
      </colgroup>
      <thead>
        <tr>
          <th
            v-for="(col, idx) in element.style.tableHeader"
            :key="idx"
            :style="{
              border: '1px solid ' + (element.style.borderColor || '#ddd'),
              textAlign: col.align,
              fontSize: element.style.headerFontSize + 'px',
              color: element.style.headerFontColor,
              height: element.style.headerRowHeight + 'px',
              lineHeight: element.style.headerRowHeight + 'px',
              padding: '0 8px'
            }"
          >
            {{ col.label }}
          </th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="(row, rowIdx) in propValue.length ? propValue : innerPropValue" :key="rowIdx">
          <td
            v-for="(col, colIdx) in element.style.tableHeader"
            :key="colIdx"
            :style="{
              border: '1px solid ' + (element.style.borderColor || '#ddd'),
              textAlign: col.align,
              fontSize: element.style.bodyFontSize + 'px',
              color: element.style.bodyFontColor,
              height: element.style.bodyRowHeight + 'px',
              lineHeight: element.style.bodyRowHeight + 'px',
              padding: '0 8px'
            }"
          >
            {{ row[col.prop] }}
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>


<script>
export default {
  props: {
    propValue: {
      type: Array,
      default: () => []
    },
    element: {
      type: Object,
    },
  },
  data() {
    return {
      innerPropValue: []
    };
  },
  methods: {
    getColumnWidth(col, idx) {
      if (col.width) {
        return col.width + '%';
      } else {
        // 计算所有有width字段的列的总宽度
        const totalDefinedWidth = this.element.style.tableHeader
          .filter(headerCol => headerCol.width)
          .reduce((sum, headerCol) => sum + headerCol.width, 0);

        // 计算没有width字段的列数量
        const undefinedWidthCount = this.element.style.tableHeader
          .filter(headerCol => !headerCol.width).length;

        // 剩余宽度平均分配
        const remainingWidth = 100 - totalDefinedWidth;
        return remainingWidth / undefinedWidthCount + '%';
      }
    },
  },
  watch: {
    element: {
      handler(newVal) {
        if (
          (!this.propValue || this.propValue.length === 0) &&
          newVal &&
          newVal.style &&
          Array.isArray(newVal.style.tableHeader)
        ) {
          this.innerPropValue = new Array(3).fill('').map((item, i) => {
            let obj = {};
            for (let j = 0; j < newVal.style.tableHeader.length; j++) {
              obj[newVal.style.tableHeader[j].prop] = newVal.style.tableHeader[j].prop + '-' + j;
            }
            return obj;
          });
        }
      },
      immediate: true,
      deep: true
    }
  },
  // // element
};
</script>

<style lang="scss" scoped>
.bar-code-table {
  width: 100%;
  height: 100%;
  overflow: auto;
  table {
    width: 100%;
    border-collapse: collapse;
  }
}
</style>
