// 公共样式
export const commonStyle = {
  rotate: 0,
  opacity: 1,
};

export const commonAttr = {
  animations: [],
  events: {},
  groupStyle: {}, // 当一个组件成为 Group 的子组件时使用
  isLock: false, // 是否锁定组件
};

// 编辑器左侧组件列表
const list = [
  {
    component: 'v-text',
    label: '普通文本',
    propValue: '双击编辑文字',
    icon: 'wenben',
    style: {
      width: 100,
      height: 22,
      fontSize: 14,
      fontWeight: 500,
      lineHeight: '',
      letterSpacing: 0,
      textAlign: '',
      color: '',
    },
  },
  {
    component: 'v-field',
    label: '字段',
    propValue: '双击编辑字段',
    icon: 'wenben',
    column: '',
    table: '',
    style: {
      width: 100,
      height: 22,
      fontSize: 14,
      fontWeight: 500,
      lineHeight: '',
      letterSpacing: 0,
      textAlign: '',
      color: '',
    },
  },
  {
    component: 'v-horiz',
    label: '横线',
    propValue: '&nbsp;',
    icon: 'juxing',
    style: {
      width: 200,
      height: 1,
      fontWeight: 16,
      borderColor: '#000',
      borderWidth: 1,
      borderStyle: 'solid',
    },
  },
  {
    component: 'verticaline',
    label: '竖线',
    propValue: '&nbsp;',
    icon: 'juxing',
    style: {
      width: 1,
      height: 200,
      fontWeight: 16,
      borderColor: '#000',
      borderWidth: 1,
      borderStyle: 'solid',
    },
  },
  {
    component: 'Picture',
    label: '二维码',
    icon: 'tupian',
    field: 'qrCode',
    column: '',
    table: '',
    propValue: require('@/directive/dragPage/assets/title.png'),
    style: {
      width: 100,
      height: 100,
      borderRadius: '',
    },
  },
  {
    component: 'BarCode',
    label: '条码',
    icon: 'tupian',
    field: 'barCode',
    column: '',
    table: '',
    propValue: '123456',
    style: {
      width: 200,
      height: 100,
      borderRadius: '',
    },
  },
  {
    component: 'rect-shape',
    label: '矩形',
    propValue: '&nbsp;',
    icon: 'juxing',
    style: {
      width: 200,
      height: 200,
      fontSize: 14,
      fontWeight: 500,
      lineHeight: '',
      letterSpacing: 0,
      textAlign: 'center',
      color: '',
      borderColor: '#000',
      borderWidth: 1,
      backgroundColor: '',
      borderStyle: 'solid',
      verticalAlign: 'middle',
    },
  },

  {
    component: 'Table',
    label: '表格',
    propValue: [],
    icon: 'juxing',
    style: {
      width: 350,
      height: 200,
      headerFontSize: 16,
      headerFontColor: '#000',
      bodyFontSize: 14,
      bodyFontColor: '#000',
      borderColor: '#000',
      headerRowHeight: 30,
      bodyRowHeight: 30,
      textAlign: 'center',
      tableHeader: [
        { label: '姓名', prop: 'name', width: '', align: 'center' },
        { label: '年龄', prop: 'age', width: '', align: 'center' },
        { label: '城市', prop: 'city', width: '', align: 'center' },
      ],
    },
  },
];

for (let i = 0, len = list.length; i < len; i++) {
  const item = list[i];
  item.style = { ...commonStyle, ...item.style };
  list[i] = { ...commonAttr, ...item };
}

export default list;
