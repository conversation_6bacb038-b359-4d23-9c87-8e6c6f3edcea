<template>
    <div class="contextmenu" v-show="menuShow" :style="{ top: menuTop + 'px', left: menuLeft + 'px' }">
        <ul @mouseup="handleMouseUp">
            <template v-if="curComponent">
                <template v-if="!curComponent.isLock">
                    <li @click="copy">复制</li>
                    <li @click="paste">粘贴</li>
                    <li @click="cut">剪切</li>
                    <li @click="deleteComponent">删除</li>
                    <li @click="lock">锁定</li>
                </template>
                <li v-else @click="unlock">解锁</li>
            </template>
            <li v-else @click="paste">粘贴</li>
        </ul>
    </div>
</template>

<script>
import eventBus from "@/magic-editor/scripts/bus";


export default {
    data() {
        return {
            copyData: null,
        }
    },
    props:[
        'menuTop',
        'menuLeft',
        'menuShow',
        'curComponent',
    ],
    methods: {
        lock() {
            eventBus.$emit('lock')
        },

        unlock() {
            eventBus.$emit('unlock')
        },

        // 点击菜单时不取消当前组件的选中状态
        handleMouseUp() {
            eventBus.$emit('setClickComponentStatus', true)
        },

        cut() {
            eventBus.$emit('cut')
        },

        copy() {
            eventBus.$emit('copy')
        },

        paste() {
            eventBus.$emit('paste', true)
            eventBus.$emit('recordSnapshot')
        },

        deleteComponent() {
            eventBus.$emit('deleteComponent')
            eventBus.$emit('recordSnapshot')
        },

        upComponent() {
            eventBus.$emit('upComponent')
            eventBus.$emit('recordSnapshot')
        },

        downComponent() {
            eventBus.$emit('downComponent')
            eventBus.$emit('recordSnapshot')
        },

        topComponent() {
            eventBus.$emit('topComponent')
            eventBus.$emit('recordSnapshot')
        },

        bottomComponent() {
            eventBus.$emit('bottomComponent')
            eventBus.$emit('recordSnapshot')
        },
    },
}
</script>

<style lang="scss" scoped>
.contextmenu {
    position: absolute;
    z-index: 1000;

    ul {
        border: 1px solid #e4e7ed;
        border-radius: 4px;
        background-color: #fff;
        box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
        box-sizing: border-box;
        margin: 5px 0;
        padding: 6px 0;

        li {
            font-size: 14px;
            padding: 0 20px;
            position: relative;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            color: #606266;
            height: 34px;
            line-height: 34px;
            box-sizing: border-box;
            cursor: pointer;

            &:hover {
                background-color: #f5f7fa;
            }
        }
    }
}
</style>
