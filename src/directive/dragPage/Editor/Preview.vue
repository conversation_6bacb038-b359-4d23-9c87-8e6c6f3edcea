<template>
  <el-dialog :visible.sync="dialogVisible" :width="(+canvasStyleData.width+64)+'px'" center title="预览" @close="close" :close-on-click-modal="false">


      <div
        class="canvas"
        :style="{
          width:canvasStyleData.width+'px' ,
          height: canvasStyleData.height+'px',
        }"
      >
        <ComponentWrapper v-for="(item, index) in componentData" :key="index" :config="item" />
      </div>

  </el-dialog>
<!--  </div> -->
</template>

<script>
import {
  getStyle
} from '@/directive/dragPage/utils/style'
import ComponentWrapper from './ComponentWrapper'


export default {
  components: {
    ComponentWrapper
  },
  model: {
    prop: 'show',
    event: 'change'
  },
  props:[
    'componentData',
    'canvasStyleData'
  ],
  data() {
    return {
      dialogVisible: false // 控制弹窗是否打开
    }
  },

  methods: {
    getStyle,
    close() {
      this.$emit('change', false)
    }
  }
}
</script>

<style lang="scss" scoped>

   .canvas {
        background: #fff;
        position: relative;
        margin: auto;
      }
  .bg {
    width: 88%;
    height: 100%;
    top: 10%;
    left: 12%;
    position: fixed;
    background: rgb(0, 0, 0, .5);
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: auto;
    padding: 20px;






    .close {
      position: absolute;
      right: 20px;
      top: 20px;
    }
  }
</style>
