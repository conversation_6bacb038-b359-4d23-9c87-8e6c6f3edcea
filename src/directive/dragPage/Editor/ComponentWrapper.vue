<template>
        <component
            @click.native="handleClick"
            class="component"
            :is="config.component"
            :style="getStyle(config.style)"
            :propValue="config.propValue"
            :element="config"
        />
    
</template>

<script>
import { getStyle } from '@/directive/dragPage/utils/style'
import { mixins } from '@/directive/dragPage/utils/events'

export default {
    props: {
        config: {
            type: Object,
            require: true,
        },
    },
    mounted() {
    },
    mixins: [mixins],
    methods: {
        getStyle,

        handleClick() {
            const events = this.config.events
            Object.keys(events).forEach(event => {
                this[event](events[event])
            })
        },
    },
}
</script>

<style lang="scss" scoped>
.component {
    position: absolute;
}
</style>