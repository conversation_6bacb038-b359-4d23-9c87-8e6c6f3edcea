<template>
  <div
    id="editor"
    class="editor"
    :class="{ edit: isEdit }"
    :style="{
      width: canvasStyleData.width + 'px',
      height: canvasStyleData.height + 'px',
    }"
    @contextmenu="handleContextMenu"
  >
    <!-- 网格线 -->
    <Grid/>
    <div id="imageDom"
         :style="{
      width: canvasStyleData.width + 'px',
      height: canvasStyleData.height + 'px',
    }">
      <!--页面组件列表展示-->
      <Shape
        v-for="(item, index) in componentData"
        :key="item.id"
        :default-style="item.style"
        :style="item.style&& getShapeStyle(item.style,item.component)"
        :active="item === curComponent"
        :element="item"
        :index="index"
        :curComponent="curComponent"
        :class="{ lock: item.isLock }"
      >
        <component
          :is="item.component"
          v-if="item.component != 'v-text'"
          :id="'component' + item.id"
          class="component"
          :style="getComponentStyle(item.style)"
          :prop-value="item.propValue"
          :editMode="editMode"
          :element="item"
        />

        <component
          :is="item.component"
          v-else
          :id="'component' + item.id"
          class="component"
          :style="getComponentStyle(item.style)"
          :prop-value="item.propValue"
          :element="item"
          :editMode="editMode"
          @input="handleInput"
        />
      </Shape>
    </div>
    <!-- 右击菜单 -->
    <ContextMenu :menuTop="menuTop" :menuLeft="menuLeft" :menuShow="menuShow" :curComponent="curComponent"/>
    <!-- 标线 -->
    <MarkLine :curComponent="curComponent" :componentData="componentData"/>

  </div>
</template>

<script>
import Shape from './Shape'
import {getStyle, getComponentRotatedStyle} from '@/directive/dragPage/utils/style'
import ContextMenu from './ContextMenu'
import MarkLine from './MarkLine'
import eventBus from "@/magic-editor/scripts/bus";
import Grid from './Grid'

export default {
  components: {Shape, ContextMenu, MarkLine, Grid},
  data() {
    return {
      editorX: 0,
      editorY: 0,
      start: { // 选中区域的起点
        x: 0,
        y: 0
      },
      width: 0,
      height: 0,
      isShowArea: false
    }
  },
  props: [
    'menuTop',
    'menuLeft',
    'menuShow',
    'componentData',
    'curComponent',
    'canvasStyleData',
    'isEdit',
    'editMode'
  ],
  mounted() {

  },
  methods: {

    handleContextMenu(e) {
      e.stopPropagation()
      e.preventDefault()

      // 计算菜单相对于编辑器的位移
      let target = e.target
      let top = e.offsetY
      let left = e.offsetX
      while (target instanceof SVGElement) {
        target = target.parentNode
      }

      while (!target.className.includes('editor')) {
        left += target.offsetLeft
        top += target.offsetTop
        target = target.parentNode
      }

      eventBus.$emit('showContextMenu', top, left)
    },

    getShapeStyle(style, type) {
      const result = {};
      ['width', 'height', 'top', 'left', 'rotate'].forEach(attr => {
        if (attr != 'rotate') {
          result[attr] = style[attr] + 'px'
        } else {
          result.transform = 'rotate(' + style[attr] + 'deg)'
        }
      })
      if (['rect-shape', 'verticaline', 'v-horiz'].includes(type)) {
        result.zIndex = 1
      } else {
        result.zIndex = 99
      }
      return result
    },

    getComponentStyle(style) {
      return getStyle(style, ['top', 'left', 'width', 'height', 'rotate'])
    },

    handleInput(element, value) {
      // 根据文本组件高度调整 shape 高度
      eventBus.$emit('setShapeStyle', {height: this.getTextareaHeight(element, value)})
    },

    getTextareaHeight(element, text) {
      let {lineHeight, fontSize, height} = element.style
      if (lineHeight === '') {
        lineHeight = 1.5
      }

      const newHeight = (text.split('<br>').length - 1) * lineHeight * fontSize
      return height > newHeight ? height : newHeight
    }
  }
}
</script>

<style lang="scss" scoped>
.editor {
  position: relative;
  background: #fff;
  margin: auto;
  overflow: visible;
  .lock {
    opacity: .5;

    &:hover {
      cursor: not-allowed;
    }
  }
}

.edit {
  .component {
    outline: none;
    width: 100%;
    height: 100%;
  }
}

</style>
