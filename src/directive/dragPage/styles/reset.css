body,
button,
input,
p,
li,
ol,
ul,
div,
section,
article,
td,
th,
span,
textarea,
form,
footer,
header,
nav,
main,
address,
aside,
pre,
canvas {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}
body {
    overflow: hidden;
}
li {
    list-style: none;
}
#app {
    overflow: hidden;
}
.el-tabs {
    height: 100%;
}
.el-tabs__content {
    height: calc(100% - 55px);
    overflow: auto;
}
.el-tabs__nav-scroll {
    padding-left: 20px;
}