<!-- TODO: 这个页面后续将用 JSX 重构 -->
<template>
  <el-scrollbar
    style="height: calc(100vh - 213px)"
    wrap-style="overflow-x:hidden;"
  >
    <div class="attr-list">
      <el-form>
        <el-form-item v-if="curComponent.component == 'Table'" label="显示表格页码">
          <el-switch v-model="curComponent.style.tablePage"></el-switch>
        </el-form-item>
        <el-form-item
          v-if="
            curComponent.component == 'Picture' ||
            curComponent.component == 'BarCode' ||
            curComponent.component == 'v-field' ||
            curComponent.component == 'Table'
          "
          label="字段"
        >
          <el-select
            v-model="curComponent.column"
            placeholder="请选择字段或者进行输入"
            allow-create
            filterable
          >
            <el-option
              v-for="item in columnsOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item
          v-for="(key, index) in styleKeys.filter((item) => item != 'rotate')"
          :key="index"
          :label="map[key]"
        >
          <el-color-picker
            v-if="['borderColor', 'color', 'backgroundColor', 'headerFontColor', 'bodyFontColor'].includes(key)"
            v-model="curComponent.style[key]"
          />

          <el-select
            v-else-if="selectKey.includes(key)"
            v-model="curComponent.style[key]"
          >
            <template v-if="key == 'textAlign'">
              <el-option
                v-for="item in textAlignOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </template>
            <template v-else-if="key == 'borderStyle'">
              <el-option
                v-for="item in borderStyleOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </template>
            <template v-else>
              <el-option
                v-for="item in verticalAlignOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </template>
          </el-select>

          <el-button v-else-if="key === 'tableHeader'" type="primary"" @click="dialogShow = true" >
            表格列配置
          </el-button>

          <el-input
            v-else
            v-model.number="curComponent.style[key]"
            type="number"
          />
        </el-form-item>

        <el-form-item
          v-if="curComponent && !excludes.includes(curComponent.component)"
          label="内容"
        >
          <el-input v-model="curComponent.propValue" type="textarea" />
        </el-form-item>
      </el-form>
    </div>

    <el-dialog
      v-dialog-drag
      title="表格列配置"
      width="95%"
      :visible="dialogShow"
      :show-close="false"
    >
      <lt-sort-table
        v-model="curComponent.style.tableHeader"
        max-height="500"
        row-key="prop"
        default-expand-all
      >
        <lt-table-column-edit prop="label" label="列名" show-overflow-tooltip />
        <lt-table-column-edit prop="prop" label="字段" show-overflow-tooltip />
        <lt-table-column-edit prop="width" label="宽度" />
        <lt-table-column-edit prop="align" state="select" :select-opt="{ options: $enum.alignFix }" >
          <template slot="header">
            <div style="display: flex;align-items: center;">
              <span style="margin-right: 10px;">对齐方式</span>
              <svg-icon
                icon-class="左对齐"
                style="cursor: pointer; height: 32px; width: 16px; margin-right: 6px;"
                @click="handleAllAlign('left')"
              ></svg-icon>
              <svg-icon
                icon-class="居中对齐"
                style="cursor: pointer; height: 32px; width: 16px; margin-right: 6px;"
                @click="handleAllAlign('center')"
              ></svg-icon>
              <svg-icon
                icon-class="右对齐"
                style="cursor: pointer; height: 32px; width: 16px;"
                @click="handleAllAlign('right')"
              ></svg-icon>
            </div>
          </template>
        </lt-table-column-edit>

        <lt-table-column-operation
          width="340"
          v-model="curComponent.style.tableHeader"
          :row-data="rowObj"
          :unshift="false"
          fixed="right"
          ref="operationRef"
           @save="save"
          @delete="del"
          :unlimitedAdd="true"
          :validateProp="false"
          primary-key="prop"
          :layout="['delete', 'edit', 'save']"
        >
        </lt-table-column-operation>
      </lt-sort-table>
      <div slot="footer" class="dialog-footer">
        <!-- <el-button @click="dialogShow = false"> 取消</el-button> -->
        <el-button type="primary" @click="saveCheck"> 确认</el-button>
      </div>
    </el-dialog>
  </el-scrollbar>
</template>

<script>
export default {
  props: ['curComponent', 'models'],
  data() {
    return {
      value: '',
      excludes: ['Table'], // 这些组件不显示内容
      textAlignOptions: [
        {
          label: '左对齐',
          value: 'left',
        },
        {
          label: '居中',
          value: 'center',
        },
        {
          label: '右对齐',
          value: 'right',
        },
      ],
      borderStyleOptions: [
        {
          label: '实线',
          value: 'solid',
        },
        {
          label: '虚线',
          value: 'dashed',
        },
      ],
      verticalAlignOptions: [
        {
          label: '上对齐',
          value: 'top',
        },
        {
          label: '居中对齐',
          value: 'middle',
        },
        {
          label: '下对齐',
          value: 'bottom',
        },
      ],
      selectKey: ['textAlign', 'borderStyle', 'verticalAlign'],
      map: {
        left: 'x 坐标',
        top: 'y 坐标',
        height: '高',
        width: '宽',
        color: '颜色',
        backgroundColor: '背景色',
        borderStyle: '边框风格',
        borderWidth: '边框宽度',
        borderColor: '边框颜色',
        borderRadius: '边框半径',
        fontSize: '字体大小',
        fontWeight: '字体粗细',
        lineHeight: '行高',
        letterSpacing: '字间距',
        opacity: '透明度',
        textAlign: '左右对齐',
        verticalAlign: '上下对齐',
        model: '数据模型',
        headerFontSize: '表头字体大小',
        headerFontColor: '表头字体颜色',
        bodyFontSize: '表体字体大小',
        bodyFontColor: '表体字体颜色',
        headerRowHeight: '表头高度',
        bodyRowHeight: '表体高度',
        tableHeader: '表格列配置'
      },
      dialogShow: false,
      rowObj: {
        label: '',
        prop: '',
        width: '',
        align: 'center'
      }
    };
  },
  computed: {
    styleKeys() {
      return this.curComponent.style
        ? Object.keys(this.curComponent.style)
        : [];
    },
    columnsOptions() {
      if (this.models) {
        this.curComponent.table = this.models.tableName;
        if (this.models.columns && this.models.columns.length > 0) {
          return this.models.columns.map((e) => {
            return {
              label: e.columnComment,
              value: e.columnName,
            };
          });
        }
      }
      return [];
    },
  },
  methods: {
    save({ data }) {
      this.$notify.success({ message: "保存成功" });
      this.$refs.operationRef.saveComplete(data);
    },
    del({ data, index }) {
      console.log(data, index)
      // function getItem(arr, id) {
      //   arr.forEach((item, index2) => {
      //     if (item.id && item.id === id) {
      //       arr.splice(index2, 1);
      //     } else {
      //       item.children && getItem(item.children, id);
      //     }
      //   });
      // }
      // getItem(this.optionModel.cols, data.id);
      this.curComponent.style.tableHeader.splice(index, 1)
      this.$forceUpdate();
    },
    handleAllAlign(val) {
      this.curComponent.style.tableHeader.forEach(item => {
        item.align = val
      })
    },
    saveCheck() {
      this.dialogShow = false
    }
  },
};
</script>

<style lang="scss" scoped>
.attr-list {
  overflow: auto;
  padding: 20px;
  padding-top: 0;
  height: 100%;
}
</style>
