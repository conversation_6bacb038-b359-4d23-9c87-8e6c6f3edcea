import router from './router';
import store from './store';
import { Message } from 'element-ui';
import NProgress from 'nprogress';
import 'nprogress/nprogress.css';
import { getToken, setToken, getEnv, getDevelop } from '@/utils/auth';
import { getConfigKey, getStationInfo, getConfigKeyAll } from '@/api/system/config';
import socket from '@/utils/websocket.js';
import { getSysConfig, getPublicKey } from '@/api/login';
import { changeFavicon } from '@/utils';
import db from '@/utils/localStorage-utils';
import { initRouter } from '@/views/workstation/js/initRouter.js';
NProgress.configure({ showSpinner: false });

const whiteList = [
  '/eqPowerByDays',
  '/commonPage',
  '/login',
  '/auth-redirect',
  '/bind',
  '/register',
  '/materData',
  '/multiple',
  '/single',
  '/meterStructure',
  '/meterCollection',
  '/factorMatch',
  '/peakSet',
  '/codeView'
];

router.beforeEach((to, from, next) => {
  NProgress.start();

  if (!store.state.app.uploadUrl) {
    getConfigKey('sys.net.upload_url').then((res) => {
      store.state.app.uploadUrl = res.msg;
    });
  }
  store.dispatch('app/setEnv', getEnv());
  store.dispatch('app/setTenantData', db.get("tenantData"));
  store.dispatch('app/setDevelop', getDevelop());
  if (to.query.hasOwnProperty('token')) {
    setToken(to.query.token);
    if (to.query.hasOwnProperty('env')) {
      store.dispatch('app/setEnv', to.query.env);
    }
  }

  if (getToken()) {
    to.meta.title && store.dispatch('settings/setTitle', to.meta.title);
    /* has token*/
    if (to.path === '/login') {
      next({ path: '/' });
      NProgress.done();
    } else {
      // to.query.hasOwnProperty('toPath')
      if (to.query.hasOwnProperty('toPath')) {
        next(to.query.toPath);
        NProgress.done();
      }

      if (store.getters.roles.length === 0) {
        // 判断当前用户是否已拉取完user_info信息
        store
          .dispatch('GetInfo')
          .then((res) => {
            getConfigKeyAll().then((res)=>{
              let data = res.data
              store.state.app.taskbord = data["sys_index_taskbord"]?.configValue;

              store.state.app.systemName = data["sys_index_systemName"]?.configValue;
              document.title = data["sys_index_systemName"]?.configValue;

              store.state.app.indexTopTitle = data["sys_index_topTitle"]?.configValue;
              store.state.app.corporateCulture1 = data["sys_index_corporateCulture1"]?.configValue;
              store.state.app.corporateCulture2 = data["sys_index_corporateCulture2"]?.configValue;
              store.state.settings.showBigScreen = data["showBigScreen"]?.configValue;
              store.state.settings.bigScreenUrl = data["bigScreenUrl"]?.configValue;


              store.state.app.uploadUrl = data["sys_net_upload_url"]?.configValue;
              if (data["top_icon_href"]?.configValueOptions) {
                store.state.settings.topIconHref= JSON.parse(data["top_icon_href"]?.configValueOptions)
              }

              if (data["ldf_flow_top_info"]?.configValueOptions) {
                const result = {};
                JSON.parse(data["ldf_flow_top_info"]?.configValueOptions).forEach(item => {
                  // 将 `value` 转换为布尔类型
                  result[item.label] = item.value === "true";
                });

                store.state.app.ldfFlowTopInfo = result
              } else {
                store.state.app.ldfFlowTopInfo = { "app": true, "pc": true, "ipad": true }
              }
              getSysConfig().then((res) => {
                store.state.app.sysConfig = res.data;

                // 更改项目图标
                changeFavicon(res.data.headerLogoId);
              });
              // 获取当前页面的协议
              const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
              let ipStr = window.location.href.split("//")[1].split("/")[0] + process.env.VUE_APP_BASE_API
              if(!ipStr.includes('localhost')){
                /*store.state.app.webSocket = new socket({
                  //网址（端口是我下面的服务器的端口）
                  url: `${protocol}//${ipStr}/socket/ws?channel=1&token=${getToken()}`,
                  //心跳时间（单位:ms）
                  //'heartBeat':5000,
                  //发送心跳信息（支持json传入）(这个一般内容不重要，除非后端变态)
                  //'heartMsg':'hello',
                  //开起重连
                  //'reconnect':true,
                  //重连间隔时间（单位:ms）
                  //'reconnectTime':5000,
                  //重连次数
                  //'reconnectTimes':10
                });*/
              }
            })

            store.dispatch('GenerateRoutes').then((accessRoutes) => {
              // 根据roles权限生成可访问的路由表
              router.addRoutes(accessRoutes); // 动态添加可访问路由表
              let fun = () => {
                if (to.path == '/index') {
                  getConfigKey('sys.indexType').then((res) => {
                    if (res.msg == 'noIndex') {
                      let path = accessRoutes[0].path;
                      if (
                        accessRoutes[0].children &&
                        accessRoutes[0].children.length > 0
                      ) {
                        path += '/' + accessRoutes[0].children[0].path;
                      }
                      next({ path, replace: true });
                    } else {
                      next({ ...to, replace: true }); // hack方法 确保addRoutes已完成
                    }
                  });
                }
                else if (
                  (to.path.includes('/workstationDesign') ||
                    to.path.includes('/workstationRender')) &&
                  !store.state.permission.workstationRouters.length
                ) {
                  initRouter().then(() => {
                    next({ ...to, replace: true });
                  });
                } else {
                  next({ ...to, replace: true }); // hack方法 确保addRoutes已完成
                }
              };
              getConfigKey('sys.hasWorkStation')
                .then((res) => {
                  let bol = res &&
                    res.msg !== null &&
                    (res.msg === true || res.msg == 'true')
                  store.state.app.hasWorkStation = bol;
                  if (bol) {
                    // 根据ip自动跳转
                    getStationInfo()
                      .then((res) => {
                        if (res.data) {
                          router.push({
                            name: 'workStation',
                            params: {
                              info: res.data,
                            },
                          });
                        } else {
                          fun();
                        }
                      })
                      .catch((err) => {
                        fun();
                      });
                  } else {
                    fun();
                  }
                })
                .catch((e) => {
                  fun();
                });
            });
          })
          .catch((err) => {
            store.dispatch('LogOut').then(() => {
              Message.error(err);
              next({ path: '/' });
            });
          });
      } else {
        if (
          (to.path.includes('/workstationDesign') ||
          to.path.includes('/workstationRender')) &&
          !store.state.permission.workstationRouters.length
        ) {
          initRouter().then(() => {
            next();
          });
        } else {
          next();
        }
      }
    }
  } else {
    if (
      to.query.hasOwnProperty('userName') &&
      to.query.hasOwnProperty('password')
    ) {
      let username = to.query.userName;
      let password = to.query.password;
      let fun = (publicKey) => {
        store
          .dispatch('Login', {
            username: username,
            password: password,
            publicKey,
          })
          .then((res) => {
            next();
          })
          .catch((err) => {
            next(`/login?redirect=${to.fullPath}`);
          });
      };
      getPublicKey(1, username)
        .then((res) => {
          let isLoging = res.data.isLogin;
          let publicKey = res.data.publicKey;
          if (isLoging) {
            this.$confirm('此账号已在线，是否继续登陆?', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning',
            })
              .then(() => {
                fun(publicKey);
              })
              .catch(() => {
                next(`/login?redirect=${to.path}`);
              });
          } else {
            fun(publicKey);
          }
        })
        .catch(() => {
          next(`/login?redirect=${to.path}`);
        });
    }
    //     // 没有token
    else if (whiteList.some((item) => to.path.includes(item))) {
      // 在免登录白名单，直接进入
      next();
    } else {
      next(`/login?redirect=${to.fullPath}`); // 否则全部重定向到登录页
      NProgress.done();
    }
  }
});

router.afterEach(() => {
  NProgress.done();
});
