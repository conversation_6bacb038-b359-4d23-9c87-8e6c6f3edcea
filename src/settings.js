module.exports = {
  /**
   * 侧边栏主题 深色主题theme-dark，浅色主题theme-light
   */
  sideTheme: 'theme-dark',

  /**
   * 是否系统布局配置
   */
  showSettings: false,

  /**
   * 是否显示顶部导航
   */
  topNav: true,

  /**
   * 是否显示 tagsView
   */
  tagsView: true,

  /**
   * 是否显示 面包屑
   */

  breadcrumbView:true,
  /**
   * 是否显示 面包屑
   */

  topNavBgColor:'#FFFFFF',
  /**
   * 是否显示 面包屑
   */

  topNavColor:'#333333',
  

  /**
   * 是否固定头部
   */
  fixedHeader: true,
  /**
   * 是否固定侧边栏
   */
  fixedSidbar: true,
  /**
   * 是否显示logo
   */
  sidebarLogo: true,

  /**
   * 是否显示折叠菜单
   */
  foldMenu: true,
  
   /**
   * 手风琴菜单
   */
   accordionMenu: true,
   
   /**
   * 菜单栏宽度
   */
   menuWidth: 220,

   /**
   * 灰色模式
   */
   greyModel: false,

   /**
   * 是否显示大屏
   */
   showBigScreen: false,

   /**
   * 大屏地址
   */
   bigScreenUrl: '',

  /**
   * @type {string | array} 'production' | ['production', 'development']
   * @description Need show err logs component.说明需要显示错误日志组件
   * The default is only used in the production env 默认值仅在生产环境中使用
   * If you want to also use it in dev, you can pass ['production', 'development']
   * 如果您还想在dev中使用它，可以通过['production', 'development']
   */
  errorLog: 'development'
}
