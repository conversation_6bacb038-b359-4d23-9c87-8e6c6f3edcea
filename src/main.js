import Vue from 'vue';

import Cookies from 'js-cookie';

import Element from 'element-ui';
import '@/magic-editor/assets/magic-editor.css';
import '@/assets/styles/index.scss'; // global css
import './assets/iconfont/iconfont.css';
import './assets/iconfont/iconfont-weapp-icon.css';
// import '../public/luckysheet/assets/iconfont/iconfont.css';
import 'vue-json-pretty/lib/styles.css';
import App from './App';
import store from './store';
import router from './router';
import directive from './directive'; // directive
import './assets/icons'; // icon
import './permission'; // permission control

// 引入配置拖拽式生成页面样式
import '@/directive/dragPage/custom-component'; // 注册自定义组件
import '@/directive/dragPage/assets/iconfont/iconfont.css';
import '@/directive/dragPage/styles/animate.css';

import './components/install';

import './utils/global-mount'; // 挂载到全局的组件和方法

import CodeView from 'vue-code-view';
Vue.use(CodeView);

// 在线markdown
import 'mavon-editor/dist/css/index.css';
import mavonEditor from 'mavon-editor';
Vue.use(mavonEditor);

// 引入接口编辑器
import MagicContextMenu from '@/magic-editor/components/common/magic-contextmenu';
import Modal from '@/magic-editor/components/common/modal';

import contentmenu from 'v-contextmenu';
import 'v-contextmenu/dist/index.css';
Vue.use(contentmenu);

Vue.use(directive);
Vue.use(MagicContextMenu);
Vue.use(Modal);

import request from '@/utils/request';

Vue.prototype.$api = request;
import formModal from '@/views/tool/variantform/formModal/formModal';
Vue.use(formModal);

import printDialog from '@/views/tool/excelreport/viewer/printDialog';
Vue.use(printDialog);


import vformRender from '@/views/tool/variantform/formModal/vFormRender';
Vue.use(vformRender);

// 挂载流程操作全局内容
import './utils/flows.js';
import {
  reMessage
} from '@/utils/resetMessage'
import { encrypt, decrypt } from './utils/jsencrypt.js';
Vue.prototype.$encrypt = encrypt;
Vue.prototype.$decrypt = decrypt;
Vue.prototype.$bus = new Vue();

/**
 * If you don't want to use mock-server
 * you want to use MockJs for mock api
 * you can execute: mockXHR()
 *
 * Currently MockJs will be used in the production environment,
 * please remove it before going online! ! !
 */

// set element-ui default size
Vue.use(Element, { size: Cookies.get('size') || 'medium' });
Vue.prototype.$message = reMessage;
Vue.config.productionTip = false;

// setInterval(() => {
//   if (window.performance && window.performance.memory) {
//     const memory = window.performance.memory;
//     console.log(`Total JS heap: ${memory.totalJSHeapSize / 1024 / 1024} MB`);
//     // console.log(`Used JS heap: ${memory.usedJSHeapSize / 1024 / 1024} MB`);
//     // console.log(`Heap limit: ${memory.jsHeapSizeLimit / 1024 / 1024} MB`);
//   }
// }, 1000);

new Vue({
  el: '#app',
  router,
  store,
  render: (h) => h(App),
  created() {
    Vue.prototype.$emitUp = (event, params) => {
      this.$bus.$emit('onReceive', {
        event,
        params,
      })
    }
  }
});
