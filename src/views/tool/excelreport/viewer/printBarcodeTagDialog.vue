<template>
  <el-dialog :visible="dialogVisible" :close-on-click-modal="true" width="75vw" class="print-dialog" :show-close="false" center destroy-on-close append-to-body v-loading="printLoading" element-loading-text="文件生成中...">

    <div class="d-flex" style="width: 100%; height: 100%">
      <div class="left" v-if="!printConfig.isDialogPreview">
        <div class="print-tips" :style="{ color: tipsComp.color, 'margin-top': '8px' }" v-html="tipsComp.msg" />
        <div>
          <el-form class="flex-1">
            <el-form-item label="打印机">
              <el-select v-model="currPrinter" @change="printerChange" style="width: 200px">
                <el-option v-for="(item, index) in printerList" :key="index" :label="item" :value="item" />
              </el-select>
            </el-form-item>
            <el-form-item label="份数">
              <el-input-number v-model="printConfig.copies" controls-position="right" :min="1" :max="10" />
            </el-form-item>
            <div :class="{ 'break': !isDesign }"></div>
            <el-radio-group
              v-model="printConfig.orientation"
              @change="handleChange"
              :class="{ 'normal-layout': !isDesign }"
            >
              <el-radio :label="1">纵向</el-radio>
              <el-radio :label="0">横向</el-radio>
            </el-radio-group>
          </el-form>
          <div class="d-flex j-center">
            <el-button type="primary" @click="printMain" :disabled="isPrintReady()">打印
            </el-button>
            <el-button @click="cancel" style="margin-left: 10%">取消</el-button>
          </div>
        </div>
      </div>
      <div class="right card_now" :style="{ width:'70%',padding:'0px'}">
        <div style="margin-top: 8%;overflow:auto;height: 90%">
          <el-row :gutter="20" v-loading="privewLoading">
            <el-col :span="24">
              <el-card v-for="(item, index) in barCodeUids" :key="index" style="margin-top:10px;float:left">
                <el-image :src="item"></el-image>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { builderBarCodeTag } from "@/api/ldf/ldfInfo"
import axios from "axios";
import db from "@/utils/localStorage-utils";
import store from "@/store";

export default {
  props: {
    isByTable: {
      type: Boolean,
      required: false
    },
    isReportCodeS: {
      type: Boolean,
      required: false
    },
    isOpenUrl: {
      type: Object,
      required: false,
      default: () => ({
        flag: false,
        url: ''
      }) //是否通过URL打开 则不经过表格
    },
    isDesign: {
      type: Boolean,
      required: false,
      default: false
    },
    field: {
      type: Object,
      required: false,
    }
  },
  data() {
    return {
      //模板
      template: null,
      //放大缩小比例

      printLoading: false,
      dialogVisible: false,
      //预览列表加载开关
      privewLoading: false,
      //打印机列表
      printerList: [],
      //当前打印机
      currPrinter: db.get('default_print_device', null),
      paramsDataList: [],
      printConfig: {
        copies: 1, // 份数
        paperSizeType: 0, // 纸张大小
        printLayoutType: 1, //布局类型 0 默认 1居中 2垂直
        printFooterPageNum: false, // 是否显示页码
        zoom: 100, // 缩放比列`
        scalingFactor: 1,
        printMode: 0, // 单双面打印
        orientation: 1, // 打印方向
        printGridLines: false, // 是否显示网格线
        hasHomePageCover: false, // 首页是否封面
        isDialogPreview: false, // 弹窗预览
        isJumpPreview: false, // 跳转预览
        Dpi: 100,

      },
      barCodeUids: [],
      tips: 'NORMAL',
      margin: [1, 0.75, 1, 0.75],
    };
  },
  computed: {
    tipsComp() {
      const download_addr = `${store.state.app.uploadUrl}/file/resources/logictrue/resources/LTPrintToolSetup.exe`;
      const tipsEnum = {
        NORMAL: { msg: '', color: '#fff', code: 0 }, // 正常
        PRINT_RUN: { msg: '正在打印中....', color: store.state.settings.theme, code: 1 },
        PRINT_OK: { msg: '打印完毕！', color: '#67C23A', code: 2 },
        PRINT_ERR: { msg: '打印错误！', color: 'red', code: 3 },
        NO_LODOP: {
          msg: `打印服务未安装启动！<a href="${download_addr}" target="_self">点我</a>下载安装！`,
          color: 'red',
          code: 4,
        },
        NO_PRINTER: { msg: '未检测到打印机！', color: 'red', code: 5 },
      };
      return tipsEnum[this.tips];
    }
  },
  methods: {
    handleInput(value) {
      this.scalingFactor = value.replace(/[^\d.]/g, '')
        // 限制只有一个小数点
        .replace(/^\./g, '')
        .replace(/\.{2,}/g, '.')
        // 限制小数点后只能有两位
        .replace('.', '$#$').replace(/\./g, '').replace('$#$', '.')
        .replace(/^(\-)*(\d+)\.(\d{2}).*$/, '$1$2.$3');
    },
    open(barcodeTagId, paramsDataList) {
      if (!paramsDataList || paramsDataList.length === 0) {
        this.$message.error('打印数据源不能为空!');
        return
      }

      if (!barcodeTagId) {
        this.$message.error('打印模板标识不能为空!');
        return
      }

      this.dialogVisible = true
      this.barcodeTagId = barcodeTagId
      this.paramsDataList = paramsDataList
      this.checkLodop();

      builderBarCodeTag(barcodeTagId, this.paramsDataList).then((res) => {
        this.barCodeUids = []
        res.data.forEach(uid => {
          // .replace("stage-api", "dev-api") 本地图片不显示
          this.barCodeUids.push(`${store.state.app.uploadUrl}/interfaces/barcodeTag/${uid}/image`)
        });
      })
    },
    isPrintReady() {
      if (this.printerList.length > 0) {
        return this.loading || this.printing;
      }
      return true;
    },
    /**
     * lodop信息检查
     */
    checkLodop() {
      this.tips = 'NORMAL';
      axios('http://127.0.0.1:18950/prints').then((res) => {
        this.printerList = res.data.data;
        this.printing = false;
      }).catch(() => {
        this.tips = 'NO_LODOP';
      });
    },
    /**
     * 打印
     */
    printMain: async function () {
      this.printLoading = true

      // 打印中、加载中 不可执行打印请求
      if (this.printing || this.loading) {
        return
      }

      this.barCodeUids.forEach((fileUrl) => {

        const paramsString = Object.keys(this.printConfig).map(key => {
          return encodeURIComponent(key) + '=' + encodeURIComponent(this.printConfig[key]);
        }).join('&');

        this.tips = 'PRINT_RUN';
        axios.get('http://127.0.0.1:18950/printImg?print=' + this.currPrinter + "&" + paramsString +"&url="+fileUrl).then((res) => {
          if (res.data.code === 500) {
            this.$message({
              message: res.data.msg,
              type: 'error',
            });
          } else {
            this.$message({
              message: ' 打印成功',
              type: 'success',
            });
          }


        }).catch((e) => {
          this.$message({
            message: ' 打印失败！',
            type: 'error',
          });
        });
        this.cancel();
      })
    },
    /**
     * 取消
     */
    cancel() {
      this.paramsDataList = []
      this.printConfig = {
        copies: 1, // 份数
        paperSizeType: 0, // 纸张大小
        printLayoutType: 1, //布局类型 0 默认 1居中 2垂直
        printFooterPageNum: false, // 是否显示页码
        zoom: 100, // 缩放比列`
        printMode: 0, // 单双面打印
        orientation: 1, // 打印方向
        printGridLines: false, // 是否显示网格线
        hasHomePageCover: false, // 首页是否封面
      };
      // 值重置
      this.isByTable = false;
      this.isReportCodeS = false;
      this.pdfUrl = '';
      this.printing = false;
      this.pdfPages = 0;
      this.scale = 100;
      this.margin = [1, 0.75, 1, 0.75];
      this.dialogVisible = false;
      this.printLoading = false;
    },
    printerChange(e) {
      db.save('default_print_device', e);
    },
    handleChange() {
      if (this.isDesign) {
        return
      }
      if (this.isByTable || this.isReportCodeS) {
        // this.otherPdf()
      } else {
        // this.getPdfPreviewFile()
      }
    },
  },
};
</script>

<style lang="scss">
.print-tips {
  font-size: 13px;
  height: 32px;
  line-height: 16px;
  margin: 10px 0;

  a,
  a:focus,
  a:hover {
    text-decoration: underline !important;
    color: var(--primary-color) !important;
  }
}
.card_now .el-card__body {
  padding: 0;
}
</style>
<style lang="scss" scoped>
::v-deep .vue-barcode-element {
  width: 100% !important;
}
::v-deep .el-dialog {
  background: #fff;
  border-radius: 5px;
  height: 45.5vw;

  .el-dialog__header {
    padding-top: 0;
  }

  .el-dialog__body {
    padding: 0 !important;
    overflow: hidden;
  }
}

::v-deep .el-dialog--center {
  overflow-y: hidden;
  min-width: 800px;
}

.print-dialog {
  .left {
    overflow: hidden;
    padding: 15px 30px;
    width: 30%;
    min-width: 300px;
    box-sizing: border-box;

    ::v-deep .el-form-item {
      width: 100%;
      margin-bottom: 15px;
      display: flex;

      .el-form-item__label {
        flex: 1;
        text-align: left;
      }
    }

    .el-radio-group {
      width: 100%;
      height: 36px;
      margin-bottom: 15px;
      display: flex;
      align-items: center;

      label {
        &:first-of-type {
          width: 40%;
        }
      }
    }
  }

  .right {
    width: 70%;
    position: relative;

    .scrollContainer {
      user-select: none;
      height: 100%;
      background: #ccc;
      box-sizing: border-box;
      width: 100%;
      padding: 15px 100px;
      overflow: auto;

      .scaleContainer {
        overflow: hidden;
        background-color: #fff;
      }

      .pdfBox {
        transition: 0.1s;
        position: relative;
        cursor: move;
      }
    }

    &:hover {
      .tool {
        transition-delay: 0.2s;
        top: 10px;
      }
    }

    .tool {
      position: absolute;
      top: -50px;
      right: 10px;
      transition: top 0.2s;
      display: flex;

      .icon {
        color: #fff;
        width: 35px;
        height: 35px;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        user-select: none;
        background: rgba(0, 0, 0, 0.3);
        margin-right: 10px;

        &:hover {
          background-color: rgba(80, 77, 77, 0.6);
          box-shadow: 0px 0px 3px rgba(80, 77, 77, 0.6);
        }

        &:active {
          box-shadow: 0px 0px 1px rgba(80, 77, 77, 0.6);
          background-color: rgba(80, 77, 77, 0.75);
          transform: scale(0.95) !important;
        }
      }
    }

    .el-icon-close {
      position: absolute;
      top: 20px;
      right: 40px;
      color: #fff;
      font-size: 30px;
      cursor: pointer;
    }
  }
}
</style>
