<template>
  <div>
    <el-dialog
      :visible="dialogVisible"
      :close-on-click-modal="false"
      width="75vw"
      class="print-dialog"
      :show-close="false"
      center
      destroy-on-close
      append-to-body
    >
      <div class="d-flex" style="width: 100%; height: 100%">
        <div class="left" v-if="!printConfig.isDialogPreview">
          <div
            class="print-tips"
            :style="{ color: tipsComp.color, 'margin-top': '8px' }"
            v-html="tipsComp.msg"
          />
          <div>
            <printForm
              :currPrinter="currPrinter"
              :printerList="printerList"
              :printConfig="printConfig"
              :margin="margin"
              :isByTable="isByTable"
              :isReportCodeS="isReportCodeS"
              :isOpenUrl="isOpenUrl"
              @printerChange="printerChange"
              @otherPdf="otherPdf"
              @getPdfPreviewFile="getPdfPreviewFile"
            />
            <div class="d-flex j-center">
              <el-button
                type="primary"
                @click="printMain"
                :disabled="isPrintReady()"
                >打印</el-button
              >
              <el-button @click="cancel">取消</el-button>
            </div>
          </div>
        </div>
        <div
          class="right"
          :style="{ width: printConfig.isDialogPreview ? '100%' : '70%' }"
        >
          <template v-if="!loading && !isOpenUrl.flag">
            <iframe
              :src="iframeUrlByLuckysheet"
              style="width: 100%; height: 100%; border: 0px; overflow: hidden"
            />
          </template>
          <template v-if="!loading && isOpenUrl.flag">
            <iframe
              :src="`${isOpenUrl.url}#scrollbars=0&toolbar=0&statusbar=0`"
              style="width: 100%; height: 100%; border: 0px; overflow: hidden"
            />
          </template>
          <i class="el-icon-close" @click="dialogVisible = false" />
        </div>
      </div>
    </el-dialog>

    <iframe id="printFrame" style="display: none"></iframe>

    <el-dialog
      :visible.sync="wordPreviewVisible"
      @close="wordPreviewVisible = false"
      custom-class="preview-dialog"
      width="68%"
      :close-on-click-modal="false"
      :show-close="true"
      destroy-on-close>
      <word-report-preview ref="wordPreviewRef" :is-show-query="false"/>
    </el-dialog>
  </div>
</template>

<script>
import store from '@/store';
import pdf from 'vue-pdf';
import db from '@/utils/localStorage-utils';
import {
  exportPdf,
  excel2PdfByTable,
  exportListPdf, excel2PdfByReport
} from '@/api/interfaces/interfaces'
import { getObjectURL, getUniCode } from '@/utils/print-util.js';
import { debounce } from '@/utils/decorator.js';
import request from '@/utils/request';
import axios from 'axios';
import { paperSizeTypeList } from '@/utils/enum';
import printForm from './printForm.vue';
import {builderBarCodeTag, getBarCodeStyle} from "@/api/ldf/ldfInfo";
import wordReportPreview from '@/views/admin/layout/components/WordReportPreview.vue'
export default {
  components: {
    wordReportPreview,
    pdf,
    printForm
  },
  props: {
    reportName: {
      type: String,
      default: 'lt-print',
    },
  },
  computed: {
    // 提示信息
    tipsComp() {
      const download_addr = `${store.state.app.uploadUrl}/file/resources/logictrue/resources/LTPrintToolSetup.exe`;
      const tipsEnum = {
        NORMAL: { msg: '', color: '#fff', code: 0 }, // 正常
        PRINT_RUN: { msg: '正在打印中....', color: store.state.settings.theme, code: 1 },
        PRINT_OK: { msg: '打印完毕！', color: '#67C23A', code: 2 },
        PRINT_ERR: { msg: '打印错误！', color: 'red', code: 3 },
        NO_LODOP: {
          msg: `打印服务未安装启动！<a href="${download_addr}" target="_self">点我</a>下载安装！`,
          color: 'red',
          code: 4,
        },
        NO_PRINTER: { msg: '未检测到打印机！', color: 'red', code: 5 },
      };
      return tipsEnum[this.tips];
    },
  },
  data() {
    return {
      reportExcelMoreDto: { param: [], unicode: this.uniCode },
      excel2PdfParam: {},
      isByTable: false,
      isByReport: false,
      isReportCodeS: false,
      iframeUrlByLuckysheet: '',
      isOpenUrl: {
        flag: false,
        url: '',
      }, //是否通过URL打开 则不经过表格
      dialogVisible: false, // 弹窗显示
      wordPreviewVisible: false, //word预览
      uniCode: '', // pdf文件的唯一标识
      loading: false, // loading
      tips: 'NORMAL',
      printerList: [], // 打印机列表
      currPrinter: db.get('default_print_device', null), // 当前打印机名字
      printConfig: {
        copies: 1, // 份数
        paperSizeType: 9, // 纸张大小
        printLayoutType: 1, //布局类型 0 默认 1居中 2垂直
        printFooterPageNum: false, // 是否显示页码
        zoom: 100, // 缩放比列`
        printMode: 0, // 单双面打印
        orientation: 1, // 打印方向
        printGridLines: false, // 是否显示网格线
        hasHomePageCover: false, // 首页是否封面
        isDialogPreview: false, // 弹窗预览
        isJumpPreview: false, // 跳转预览
      },

      margin: [1, 0.75, 1, 0.75],
      excelJson: '', // pdf json
      range: [], // 区域打印的范围
      pdfUrl: '', // pdf预览的临时路径
      printing: false, // 是否在打印中
      pdfPages: 0, // pdf文件有多少页
      scale: 100, // 放大系数 默认百分百
      mouseDownFlag: false,

      // 总页数
      pageTotalNum: 1,
      // 当前页数
      pageNum: 1,
      // 加载进度
      loadedRatio: 0,
      // 页面加载完成
      curPageNum: 0,

      // 旋转角度 '90'的倍数才有效
      pageRotate: 0,
      // 单击内部链接时触发 (目前我没有遇到使用场景)
      page: 0,
    };
  },
  methods: {
    /**
     * 打开打印弹窗
     */
    open(excelJson, range = []) {
      this.excelJson = excelJson;
      this.range = range;
      // this.dialogVisible = true;
      if (this.$parent) {
        let defaultConfig = this.$parent.printConfig;
        // 默认值设置
        if (defaultConfig) {
          for (let key in defaultConfig) {
            if (key in this.printConfig) {
              this.printConfig[key] = defaultConfig[key];
            }
          }
          if ('margin' in defaultConfig) {
            this.margin = defaultConfig.margin;
          }
        }
      }
      this.getPdfPreviewFile();
      this.checkLodop();
    },
    openByUrl(url) {
      this.loading = true;
      fetch(url)
        .then((response) => {
          if (response.ok) {
            const contentType = response.headers.get('content-type');
            if (contentType.includes('application/pdf')) {
              this.loading = false;
              this.isOpenUrl.flag = true;
              this.isOpenUrl.url = url;
              this.dialogVisible = true;
              this.getPdfPreviewFile();
              this.tips = 'NO_LODOP';
              this.checkLodop();
            } else {
              this.$message({
                message: '请保证文件为PDF格式',
                type: 'error',
              });
            }
          } else {
            this.$message({
              message: '请保证文件为PDF格式',
              type: 'error',
            });
          }
        })
        .catch((error) => {
          this.$message({
            message: '请保证文件为PDF格式',
            type: 'error',
          });
        });
    },
    printerChange(e) {
      db.save('default_print_device', e);
    },
    /**
     * 打印
     */
    printMain() {
      // 打印中、加载中 不可执行打印请求
      if (this.printing || this.loading) return;

      this.printing = true;
      var strURL = this.isOpenUrl.flag
        ? this.isOpenUrl.url
        : `${store.state.app.uploadUrl}/interfaces/reportExcel/exportPdfUrl?unicode=${this.uniCode}`;
      console.log(strURL);
      this.tips = 'PRINT_RUN';

      axios
        .post('http://127.0.0.1:18950/print', {
          print: this.currPrinter,
          url: strURL,
          orientation: this.printConfig.orientation.toString(),
          paperSizeType: this.printConfig.paperSizeType.toString(),
          copies: this.printConfig.copies.toString(),
          printMode: this.printConfig.printMode.toString(),
          margin: this.margin, // 添加边距参数
        })
        .then(() => {
          this.$message({
            message: '打印成功',
            type: 'success',
          });
          this.cancel();
        })
        .catch((e) => {
          console.log(e);
        });

      try {
        // 调用axios
        if (this.$parent.axios) request(this.$parent.axios);
      } catch (error) {}
    },

    otherPdf() {
      if (this.isByTable && this.isByReport) {
        this.reportPrint()
      } else if (this.isByTable) {
        this.table2Pdf()
      } else if (this.isReportCodeS) {
        this.reportGaeaList()
      }
    },

    reportGaeaList(param) {
      this.isReportCodeS = true;
      this.loading = true;
      if (this.$parent) {
        let defaultConfig = this.$parent.printConfig;
        // 默认值设置
        if (defaultConfig) {
          for (let key in defaultConfig) {
            if (key in this.printConfig) {
              this.printConfig[key] = defaultConfig[key];
            }
          }
          if ('margin' in defaultConfig) {
            this.margin = defaultConfig.margin;
          }
        }
      }
      this.uniCode = getUniCode();
      this.reportExcelMoreDto = !param ? this.reportExcelMoreDto : param;
      this.reportExcelMoreDto['uniCode'] = this.uniCode;
      this.reportExcelMoreDto['reportExcelDto'] = {
        ...this.printConfig,
        margin: this.margin, // 添加边距参数
      };
      exportListPdf(this.reportExcelMoreDto)
        .then((res) => {
          this.pdfUrl = getObjectURL(res);
          this.pdfPages = pdf.numPages;
          this.$nextTick(() => {
            this.loading = false;
            this.iframeUrlByLuckysheet = `${store.state.app.uploadUrl}/interfaces/reportExcel/exportPdfUrl?unicode=${this.uniCode}#scrollbars=0&toolbar=0&statusbar=0`;
            if (this.printConfig.isJumpPreview) {
              this.$parent.fullscreenLoading.close();
              window.open(this.iframeUrlByLuckysheet);
            }
            this.tips = 'NO_LODOP';
            this.checkLodop();
          });
        })
        .catch((e) => {
          this.$parent.fullscreenLoading.close();
        });
    },

    table2Pdf(param) {
      this.isByTable = true;
      this.loading = true;
      this.dialogVisible = true;
      this.uniCode = getUniCode();
      this.excel2PdfParam = !param ? this.excel2PdfParam : param;
      this.excel2PdfParam['reportExcelDto'] = {
        ...this.printConfig,
        unicode: this.uniCode,
        margin: this.margin, // 添加边距参数
      };

      excel2PdfByTable(this.excel2PdfParam).then((res) => {
        this.pdfUrl = getObjectURL(res);
        this.pdfPages = pdf.numPages;
        this.$nextTick(() => {
          this.loading = false;
          this.iframeUrlByLuckysheet = `${store.state.app.uploadUrl}/interfaces/reportExcel/exportPdfUrl?unicode=${this.uniCode}#scrollbars=0&toolbar=0&statusbar=0`;
          if (this.printConfig.isJumpPreview) {
            this.$parent.fullscreenLoading.close();
            window.open(this.iframeUrlByLuckysheet);
          }
          this.tips = 'NO_LODOP';
          this.checkLodop();
        })
      }).catch((e) => {
        this.$parent.fullscreenLoading.close();
      })
    },
    reportPrint(param){

      this.uniCode = getUniCode()
      this.excel2PdfParam = !param ? this.excel2PdfParam : param;
      this.excel2PdfParam['reportExcelDto'] = {
        ...this.printConfig,
        unicode: this.uniCode,
        reportUid: this.excel2PdfParam.reportUid,
        margin: this.margin // 添加边距参数
      }

      if (param.reportType === 'word') {
        function convertBodyToArray(param) {
          if (!param || !param.body) {
            return [];
          }

          return Object.entries(param.body).map(([key, value]) => ({
            label: key,
            name: key,
            value: value,
            type: ['input']
          }));
        }
        this.wordPreviewVisible = true;
        //将param.body里的所有key:value转换为{name: key,value: value}对象组成的数组
        const wordParam = {
          common: convertBodyToArray(param)
        }
        this.$nextTick(() => {
          this.$refs.wordPreviewRef.preview(param.reportUid, wordParam)
        })
        return
      }

      this.report2Pdf(this.excel2PdfParam);
    },

    report2Pdf(param){
      this.isByTable = true;
      this.isByReport = true;
      this.loading = true;
      this.dialogVisible = true;
      this.excel2PdfParam = param;
      this.printConfig = param.reportExcelDto;

      excel2PdfByReport(param).then((res) => {
        // this.pdfUrl = getObjectURL(res);
        // this.pdfPages = pdf.numPages;
        this.$nextTick(() => {
          this.loading = false;
          this.iframeUrlByLuckysheet = `${store.state.app.uploadUrl}/interfaces/reportExcel/exportPdfUrl?unicode=${param.reportExcelDto.unicode}#scrollbars=0&toolbar=0&statusbar=0`;
          if (this.printConfig.isJumpPreview) {
            this.$parent.fullscreenLoading.close();
            window.open(this.iframeUrlByLuckysheet);
          }
          this.tips = 'NO_LODOP';
          this.checkLodop();
        }).catch((e) => {
          this.$parent.fullscreenLoading.close();
        });
      });
    },


    /**
     * lodop信息检查
     */
    checkLodop() {
      this.tips = 'NORMAL';
      axios('http://127.0.0.1:18950/prints')
        .then((res) => {
          console.log(res);
          this.printerList = res.data.data;
          this.printing = false;
        })
        .catch(() => {
          this.tips = 'NO_LODOP';
        });
    },
    /**
     * 打印按钮是否只读
     */
    isPrintReady() {
      if (this.printerList.length > 0) {
        return this.loading || this.printing;
      }
      return true;
    },
    /**
     * 取消
     */
    cancel() {
      this.printConfig = {
        copies: 1, // 份数
        paperSizeType: 9, // 纸张大小
        printLayoutType: 1, //布局类型 0 默认 1居中 2垂直
        printFooterPageNum: false, // 是否显示页码
        zoom: 100, // 缩放比列`
        printMode: 0, // 单双面打印
        orientation: 1, // 打印方向
        printGridLines: false, // 是否显示网格线
        hasHomePageCover: false, // 首页是否封面
      };
      // 值重置
      this.isByTable = false;
      this.isReportCodeS = false;
      this.pdfUrl = '';
      this.printing = false;
      this.pdfPages = 0;
      this.scale = 100;
      this.margin = [1, 0.75, 1, 0.75];
      this.dialogVisible = false;
    },
    /**
     * 获取pdf预览文件
     */
    @debounce(200) // 防抖
    getPdfPreviewFile() {
      this.loading = true;

      this.uniCode = getUniCode();
      // 自定义宽高
      let customPaperSize = null;
      let custom = paperSizeTypeList.find(
        (e) => e.value == this.printConfig.paperSizeType,
      );
      if (custom.feetWidth != null) {
        customPaperSize = [custom.feetWidth, custom.feetHigh];
      }

      if (this.isOpenUrl.flag) {
        pdf
          .createLoadingTask({ url: this.isOpenUrl.url })
          .promise.then((pdf) => {
            this.pdfPages = pdf.numPages;
          });
        this.loading = false;
      } else {
        exportPdf({
          customPaperSize: customPaperSize, // 自定义宽高
          customExcel: this.excelJson,
          range: this.range,
          unicode: this.uniCode,
          margin: this.margin, // 添加边距参数
          ...this.printConfig,
        })
          .then((res) => {
            this.pdfUrl = getObjectURL(res);
            console.table(pdf);
            // https://blog.csdn.net/yyxy_gis/article/details/121200971
            pdf.createLoadingTask({ url: this.pdfUrl }).promise.then((pdf) => {
              this.pdfPages = pdf.numPages;
            });
            this.iframeUrlByLuckysheet = `${store.state.app.uploadUrl}/interfaces/reportExcel/exportPdfUrl?unicode=${this.uniCode}#scrollbars=0&toolbar=0&statusbar=0`;
            if (this.printConfig.isJumpPreview) {
              this.$parent.fullscreenLoading.close();
              window.open(this.iframeUrlByLuckysheet);
            }
            this.loading = false;
          })
          .catch((e) => {
            console.log('===报错了', e);
            if (this.printConfig.isJumpPreview) {
              this.$parent.fullscreenLoading.close();
            }
            this.loading = false;
          });
      }
    },
    barCodePrint(formId, multipleSelection) {
      let barCodeUids = []
      getBarCodeStyle(formId).then(style => {
        let width = (parseInt(style.data.canvasStyleData.width) / 600) * 25
        let height = (parseInt(style.data.canvasStyleData.height) / 600) * 25
        builderBarCodeTag(formId, multipleSelection).then(res => {
          res.data.forEach(uid => {
            // .replace("stage-api", "dev-api") 本地图片不显示
            barCodeUids.push(`${store.state.app.uploadUrl}/interfaces/barcodeTag/${uid}/image`)
          });
          // 获取iframe元素
          const iframe = document.getElementById('printFrame');
          const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
          // 设置iframe的内容
          iframeDoc.open();
          iframeDoc.write(`
            <!DOCTYPE html>
            <html>
              <head>
                <title>Print Preview</title>
                <style>
                  /* 在这里添加打印样式 */
                  @media print {
                    body {
                      margin: 0;
                      padding: 0;
                      transform: none;
                      -webkit-print-color-adjust: exact;
                      print-color-adjust: exact;
                    }
                    img {
                      width: ${width}mm !important;
                      height: ${height}mm !important;
                      image-rendering: pixelated;
                      -webkit-transform: none;
                      transform: none;
                      margin-left: 4mm;
                      margin-top: 2mm;
                    }
                  }
                  @page {
                    margin: 0;
                  }
                  body {
                    margin: 0;
                  }
                </style>
              </head>
              <body>
                <!-- 在这里插入要打印的内容 -->
                ${barCodeUids.map(url => `<img src="${url}" alt="Print Image" width="${width}mm" height="${height}mm">`).join('')}
              </body>
            </html>
          `);
          iframeDoc.close();

          // 等待iframe内容加载完成
          iframe.onload = function () {
            // 调用iframe的打印功能
            iframe.contentWindow.focus(); // 确保iframe获得焦点
            iframe.contentWindow.print();
          };

        });
      })
    }
  },
};
</script>

<style lang="scss">
.print-tips {
  font-size: 13px;
  height: 32px;
  line-height: 16px;
  margin: 10px 0;

  a,
  a:focus,
  a:hover {
    text-decoration: underline !important;
    color: var(--primary-color) !important;
  }
}
</style>
<style lang="scss" scoped>
::v-deep .el-dialog {
  background: #fff;
  border-radius: 5px;
  height: 45.5vw;

  .el-dialog__header {
    padding-top: 0;
  }

  .el-dialog__body {
    padding: 0 !important;
    overflow: hidden;
  }
}

::v-deep .el-dialog--center {
  overflow-y: hidden;
  min-width: 800px;
}

.print-dialog {
  .left {
    overflow: hidden;
    padding: 15px 30px;
    width: 30%;
    min-width: 300px;
    box-sizing: border-box;

    ::v-deep .el-form-item {
      width: 100%;
      margin-bottom: 15px;
      display: flex;

      .el-form-item__label {
        flex: 1;
        text-align: left;
      }
    }

    .el-radio-group {
      width: 100%;
      height: 36px;
      margin-bottom: 15px;
      display: flex;
      align-items: center;

      label {
        &:first-of-type {
          width: 40%;
        }
      }
    }
  }

  .right {
    width: 70%;
    position: relative;

    .scrollContainer {
      user-select: none;
      height: 100%;
      background: #ccc;
      box-sizing: border-box;
      width: 100%;
      padding: 15px 100px;
      overflow: auto;

      .scaleContainer {
        overflow: hidden;
        background-color: #fff;
      }

      .pdfBox {
        transition: 0.1s;
        position: relative;
        cursor: move;
      }
    }

    &:hover {
      .tool {
        transition-delay: 0.2s;
        top: 10px;
      }
    }

    .tool {
      position: absolute;
      top: -50px;
      right: 10px;
      transition: top 0.2s;
      display: flex;

      .icon {
        color: #fff;
        width: 35px;
        height: 35px;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        user-select: none;
        background: rgba(0, 0, 0, 0.3);
        margin-right: 10px;

        &:hover {
          background-color: rgba(80, 77, 77, 0.6);
          box-shadow: 0px 0px 3px rgba(80, 77, 77, 0.6);
        }

        &:active {
          box-shadow: 0px 0px 1px rgba(80, 77, 77, 0.6);
          background-color: rgba(80, 77, 77, 0.75);
          transform: scale(0.95) !important;
        }
      }
    }

    .el-icon-close {
      position: absolute;
      top: 20px;
      right: 40px;
      color: #fff;
      font-size: 30px;
      cursor: pointer;
    }
  }
}

/* 设置对话框高度为100% */
::v-deep .preview-dialog {
  display: flex;
  flex-direction: column;
  margin-top: 0 !important;
  margin-bottom: 0 !important;
  height: 100%;

  .el-dialog__headerbtn {
    z-index: 10;
    position: absolute;
    top: 20px;
    right: 5px;
  }

  .el-dialog__close {
    font-size: 20px;
    color: #333;

    &:hover {
      color: var(--primary-color);
    }
  }
}

::v-deep .preview-dialog .el-dialog {
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
}

::v-deep .preview-dialog .el-dialog__header {
  border-bottom: 0 !important;
  position: relative;
  z-index: 9;
}

::v-deep .preview-dialog .el-dialog__body {
  flex: 1;
  overflow: auto;
  z-index: 8;
}

</style>
