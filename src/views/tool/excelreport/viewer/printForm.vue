<template>
  <el-form class="flex-1" :class="{ 'design-layout': isDesign }">
    <el-form-item label="打印机">
      <el-select
        v-model="currPrinter"
        @change="printerChange"
        style="width: 200px"
      >
        <el-option
          v-for="(item, index) in printerList"
          :key="index"
          :label="item"
          :value="item"
        />
      </el-select>
    </el-form-item>
    <el-form-item label="份数">
      <el-input-number
        v-model="printConfig.copies"
        controls-position="right"
        :min="1"
        :max="10"
      />
    </el-form-item>
    <el-form-item label="纸张大小">
      <div class="d-flex">
        <div class="flex-1">
          <el-select
            v-model="printConfig.paperSizeType"
            @change="handleChange"
            style="width: 130px"
          >
            <el-option
              v-for="(item, index) in this.$enum.paperSizeTypeList"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
        <el-popover
          placement="right"
          width="400"
          @hide="handleChange"
          trigger="click"
          :disabled="!printConfig.enableMarginAdjust"
        >
          <div class="box">
            上
            <el-input v-model="margin[0]" :disabled="!printConfig.enableMarginAdjust"></el-input>
            左
            <el-input
              v-model="margin[1]"
              :disabled="!printConfig.enableMarginAdjust"
            ></el-input>
            下
            <el-input v-model="margin[2]" :disabled="!printConfig.enableMarginAdjust"></el-input>
            右
            <el-input
              v-model="margin[3]"
              :disabled="!printConfig.enableMarginAdjust"
            ></el-input>
          </div>
          <el-button slot="reference" :disabled="!printConfig.enableMarginAdjust">边距</el-button>
        </el-popover>
      </div>
    </el-form-item>
    <el-form-item label="调整边距" v-if="!isOpenUrl.flag">
      <el-switch
        v-model="printConfig.enableMarginAdjust"
        @change="handleChange"
      />
    </el-form-item>
    <el-form-item label="布局类型" v-if="!isOpenUrl.flag">
      <el-select
        v-model="printConfig.printLayoutType"
        @change="handleChange"
        style="width: 200px"
      >
        <el-option label="默认" :value="0"/>
        <el-option label="水平居中" :value="1"/>
        <el-option label="垂直居中" :value="2"/>
        <el-option label="水平垂直居中" :value="3"/>
      </el-select>
    </el-form-item>
    <el-form-item label="缩放比(%)" v-if="!isOpenUrl.flag">
      <el-input-number
        v-model="printConfig.zoom"
        @change="handleChange"
        controls-position="right"
        :min="10"
        :max="400"
      />
    </el-form-item>
    <el-form-item label="显示网格线" v-if="!isOpenUrl.flag">
      <el-switch
        v-model="printConfig.printGridLines"
        @change="handleChange"
      />
    </el-form-item>
    <el-form-item label="显示页码" v-if="!isOpenUrl.flag">
      <el-switch
        v-model="printConfig.printFooterPageNum"
        @change="handleChange"
      />
    </el-form-item>

    <el-form-item label="首页是否封面" v-if="!isOpenUrl.flag">
      <el-switch
        v-model="printConfig.hasHomePageCover"
        @change="handleChange"
      />
    </el-form-item>
    <el-radio-group v-model="printConfig.printMode" :class="{ 'normal-layout': !isDesign }">
      <el-radio :label="0">单面打印</el-radio>
      <el-radio :label="1">双面打印</el-radio>
    </el-radio-group>
    <div :class="{ 'break': !isDesign }"></div>
    <el-radio-group
      v-model="printConfig.orientation"
      @change="handleChange"
      :class="{ 'normal-layout': !isDesign }"
    >
      <el-radio :label="1">纵向</el-radio>
      <el-radio :label="0">横向</el-radio>
    </el-radio-group>
  </el-form>
</template>

<script>
export default {
  props: {
    currPrinter: {
      type: String,
      required: false,
      default: null
    },
    printerList: {
      type: Array,
      required: false,
      default: () => []
    },
    printConfig: {
      type: Object,
      required: true
    },
    margin: {
      type: Array,
      required: true
    },
    isByTable: {
      type: Boolean,
      required: false
    },
    isReportCodeS: {
      type: Boolean,
      required: false
    },
    isOpenUrl: {
      type: Object,
      required: false,
      default: () => ({
        flag: false,
        url: ''
      }) //是否通过URL打开 则不经过表格
    },
    isDesign: {
      type: Boolean,
      required: false,
      default: false
    },
    field: {
      type: Object,
      required: false,
    }
  },
  methods: {
    handleChange() {
      if (this.isDesign) {
        return
      }
      if (this.isByTable || this.isReportCodeS) {
        this.otherPdf()
      } else {
        this.getPdfPreviewFile()
      }
    },
    printerChange(e) {
      this.$emit('printerChange', e)
    },
    otherPdf() {
      this.$emit('otherPdf')
    },
    getPdfPreviewFile() {
      this.$emit('getPdfPreviewFile')
    }
  }
}
</script>

<style lang="scss" scoped>
.el-form {
  display: flex;
  flex-wrap: wrap;

  .el-form-item {
    width: 100%;
  }

  &.design-layout {
    border: 1px solid #ccc;
    padding: 10px;
    margin: 5px;
    box-sizing: border-box;

    .el-form-item {
      width: 33.33%;
    }

    .el-radio-group {
      margin-right: 30px;
    }
  }
  .break {
    width: 100%;
    height: 0;
  }

  .normal-layout {
    width: 100%;
    height: 36px;
    margin-bottom: 15px;
    display: flex;
    align-items: center;

    label {
      &:first-of-type {
        width: 40%;
      }
    }
  }

}
</style>
