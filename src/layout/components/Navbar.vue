<template>
  <div
    class="navbar"
    :style="{ backgroundColor: topNavBgColor, color: topNavColor }"
  >
    <!-- <hamburger
      id="hamburger-container"
      :is-active="sidebar.opened"
      class="hamburger-container"
      @toggleClick="toggleSideBar"
    /> -->

    <!-- <breadcrumb
      v-if="!topNav || true"
      id="breadcrumb-container"
      class="breadcrumb-container"
    /> -->
    <div v-if="sidebarLogo" class="left-menu d-flex a-center pd-lr20">
      <img v-if="logoBase64" :src="logoBase64" style="height: 46px"/>
      <div class="divider mr-lr20"/>
      <h1 class="font-20">{{ title }}</h1>
    </div>

    <div class="zyrm-right d-flex" v-if="$store.state.app.env === 'zyrm'">
      <div class="avatar-wrapper d-flex a-center mr-r20" >
        <lt-user-avatar :info="$store.state.user" :width="40" style="border-radius: 50%;" />
        <span class="pd-l10 font-14" style="color: #fff;">{{
            $store.state.user.nickName
          }}</span>
      </div>
      <div class="closeBox" @click="handleClose">
        <svg t="1745744462993" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3127" width="200" height="200"><path d="M512 975.4624c-112.384 0-218.0096-43.776-297.472-123.2384-163.9936-163.9936-163.9936-430.8992 0-594.944 36.4544-36.4544 78.592-65.536 125.2352-86.4768 15.4624-6.9632 33.6384 0 40.6016 15.4624 6.9632 15.4624 0 33.6384-15.4624 40.6016-39.7824 17.8688-75.776 42.7008-106.9568 73.8304-140.0832 140.0832-140.0832 367.9744 0 508.0576 67.84 67.84 158.0544 105.216 254.0032 105.216s186.1632-37.376 254.0032-105.216c140.0832-140.0832 140.0832-367.9744 0-508.0576-33.0752-33.0752-71.5264-58.9824-114.2272-77.0048a30.78656 30.78656 0 0 1-16.384-40.2432 30.78656 30.78656 0 0 1 40.2432-16.384c50.0736 21.0944 95.0784 51.456 133.7856 90.2144 164.0448 164.0448 164.0448 430.8992 0 594.944-79.36 79.4624-184.9856 123.2384-297.3696 123.2384z" fill="#ffffff" p-id="3128" data-spm-anchor-id="a313x.search_index.0.i0.10e63a81qsZvpR" class="selected"></path><path d="M512 432.7936c-16.9472 0-30.72-13.7728-30.72-30.72V77.8752c0-16.9472 13.7728-30.72 30.72-30.72s30.72 13.7728 30.72 30.72v324.1984c0 16.9984-13.7728 30.72-30.72 30.72z" fill="#ffffff" p-id="3129" data-spm-anchor-id="a313x.search_index.0.i1.10e63a81qsZvpR" class="selected"></path></svg>
      </div>
    </div>

    <div class="right-menu d-flex" v-else >
      <template v-if="$store.state.settings.topIconHref.length">
        <div
          v-for="item in $store.state.settings.topIconHref"
          :key="item.label"
          class="pd-l25 pointer"
          @click="topHref(item.value)"
        >
          <svg-icon :icon-class="item.label" class="font-22"/>
        </div>
      </template>
      <div v-if="showBigScreen == 'true'" class="pd-l25">
        <el-tooltip
          class="item"
          effect="dark"
          content="大屏设计器"
          placement="bottom"
        >
          <span
            class="iconfont font-23 pd-r5"
            style="cursor: pointer"
            @click="toBigScreen"
          ><i class="el-icon-s-platform"
          /></span>
        </el-tooltip>
      </div>
      <div v-if="showWorkStationTab" class="pd-l25">
        <el-tooltip
          class="item"
          effect="dark"
          content="工位机"
          placement="bottom"
        >
          <span
            class="iconfont font-23 pd-r5"
            style="cursor: pointer"
            @click="toWorkStation"
          ><i class="el-icon-monitor"
          /></span>
        </el-tooltip>
      </div>

      <div class="pd-l25" v-if="obj.versionApkFile || obj.versionFile">
        <el-popover placement="top-start" width="250" trigger="hover">
          <div slot="reference">
            <span class="iconfont font-23" style="cursor: pointer"
            ><i class="el-icon-download"
            /></span>
          </div>
          <el-row>
            <el-col :span="12">
              <div>
                <div style="text-align: center; margin-top: 10px">
                  <a
                    class="appDownload"
                    :href="appUrlIn"
                    style="cursor: pointer"
                  >
                    <span style="color: #e6a23c">内网</span>
                  </a>
                </div>
                <div style="text-align: center">
                  <!--二维码内容-->
                  <div ref="qrCodeUrlIn" class="qrcode"></div>
                </div>
              </div>
            </el-col>
            <el-col :span="12">
              <div>
                <div style="text-align: center; margin-top: 10px">
                  <a
                    class="appDownload"
                    :href="appUrlOut"
                    style="cursor: pointer"
                  >
                    <span style="color: #f56c6c">外网</span>
                  </a>
                </div>
                <div style="text-align: center">
                  <!--二维码内容-->
                  <div ref="qrCodeUrlOut" class="qrcode"></div>
                </div>
              </div>
            </el-col>
          </el-row>

          <div style="text-align: center">
            <a
              class="appDownload"
              :href="appUrlIn"
              style="cursor: pointer"
            >
              APP本地下载
            </a>
          </div>
        </el-popover>
      </div>

      <div class="pd-l25">
        <el-popover placement="top-start" trigger="hover">
          <div slot="reference">
            <span class="iconfont font-23" style="cursor: pointer"
            ><i class="el-icon-coin"
            /></span>
          </div>
          <el-row>
            <el-col :span="12">
              <div>
                <div style="text-align: center; margin-top: 10px">
                  <a
                    class="appDownload"
                    @click="openDbDocUrl('html')"
                    style="cursor: pointer"
                  >
                    <span style="color: #e6a23c">查看</span>
                  </a>
                </div>
              </div>
            </el-col>
            <el-col :span="12">
              <div>
                <div style="text-align: center; margin-top: 10px">
                  <a
                    class="appDownload"
                    @click="generateDbDoc"
                    style="cursor: pointer"
                  >
                    <span style="color: #f56c6c">生成</span>
                  </a>
                </div>
              </div>
            </el-col>
          </el-row>

          <div style="text-align: center; margin-top: 20px">
            <a
              class="appDownload"
              @click="openDbDocUrl('doc')"
              style="cursor: pointer"
            >
              <span style="color: #e6a23c">Word下载</span>
            </a>
          </div>
        </el-popover>
      </div>

      <news class="pd-l25"/>

      <el-dropdown
        class="avatar-container right-menu-item hover-effect pd-l25"
        trigger="click"
      >
        <div class="avatar-wrapper d-flex a-center">
          <!-- <img :src="avatar" class="user-avatar" /> -->
          <lt-user-avatar :info="$store.state.user" :width="40"/>
          <span class="pd-l10 font-14" :style="{ color: topNavColor }">{{
              $store.state.user.nickName
            }}</span>
        </div>
        <el-dropdown-menu slot="dropdown">
          <router-link to="/user/profile">
            <el-dropdown-item>个人中心</el-dropdown-item>
          </router-link>
          <el-dropdown-item
            @click.native="$parent.$refs.updatePasswordRef.open()"
          >
            <span>修改密码</span>
          </el-dropdown-item>
          <el-dropdown-item @click.native="logout">
            <span>退出登录</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <div
        class="d-flex a-center font-14 pointer pd-l25"
        @click="showSetting('systemSwitch')"
      >
        <div class="pd-r10">{{ activeSystem }}</div>
        <i class="el-icon-caret-bottom"/>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Hamburger from '@/components/Hamburger'
import errorLog from './errorLog/errorLog.vue'
import { nowVersion } from '@/api/system/appVersion'
import news from './news'
import QRCode from 'qrcodejs2'
import { getStationInfo } from '@/api/system/config'
import { generateDbDoc, getDbDocUrl } from '@/api/file/file'
import { Loading } from 'element-ui'
import updateLog from './updateLog/updateLog.vue'
import { getToken, getEnv } from '@/utils/auth'
import { getIconImage } from '@/utils'

export default {
  components: {
    // Breadcrumb,
    Hamburger,
    errorLog,
    news,
    updateLog
  },
  data() {
    return {
      drawerFlag: false,
      appUrlIn: '',
      appUrlOut: '',
      obj: {}
    }
  },
  computed: {
    ...mapGetters(['sidebar', 'avatar', 'device', 'user']),
    topNavBgColor() {
      return this.$store.state.settings.topNavBgColor
    },
    topNavColor() {
      return this.$store.state.settings.topNavColor
    },
    sidebarLogo() {
      return this.$store.state.settings.sidebarLogo
    },
    showBigScreen() {
      return this.$store.state.settings.showBigScreen
    },
    bigScreenUrl() {
      return this.$store.state.settings.bigScreenUrl
    },
    activeSystem() {
      // 这一行代码有问题,当刷新界面时会报错,暂时找不到解决办法
      let key = '/' + this.$route.path.split('/')[1]
      if (key == '/formDesign' || key == '/flow' || key == '/flowFormStart') {
        return '系统工具'
      }
      return this.$route.matched.filter(
        (item) => item.meta && item.meta.title
      )[0].meta.title
    },
    logoBase64() {
      let { logo2Id } = this.$store.state.app.sysConfig
      if (logo2Id) return getIconImage(logo2Id)
      return ''

    },
    title() {
      return this.$store.state.app.systemName
    },
    showWorkStationTab() {
      return (
        this.user.roles.filter((item) =>
          ['admin', 'workStation'].includes(item)
        ).length && this.$store.state.app.hasWorkStation
      )
    }
  },
  mounted() {
    this.creatQrCode()
  },
  methods: {
    topHref(url) {
      window.open(url, '_blank')
    },
    showSetting(type) {
      this.$store.dispatch('settings/changeSetting', {
        key: 'settingType',
        value: type
      })
      this.$store.dispatch('settings/changeSetting', {
        key: 'showSettings',
        value: true
      })
    },
    generateDbDoc() {
      let loading = Loading.service({
        target: 'body'
      })
      generateDbDoc().then(() => {
        loading.close()
        this.$message({
          showClose: true,
          message: '生成成功',
          type: 'success'
        })
      })
    },
    openDbDocUrl(suffix) {
      let loading = Loading.service({
        target: 'body'
      })
      getDbDocUrl(suffix).then((res) => {
        loading.close()
        //let dom = "http://127.0.0.1:8080"
        let dom = this.$store.state.app.uploadUrl
        window.open(`${dom}${res['data']}`)
      })
    },
    toWorkStation() {
      getStationInfo({
        ipAddress: 'admin'
      }).then((res) => {
        if (res.data) {
          this.$router.push({
            name: 'workStation',
            params: {
              info: res.data
            }
          })
        } else {
          this.$message.error('该用户暂未配置工位机')
        }
      })
    },
    toBigScreen() {
      if (this.showBigScreen == 'true' && this.bigScreenUrl != '') {
        // let url = this.bigScreenUrl + '/big-screen-list?token=' + getToken()
        let url = `${
          this.bigScreenUrl
        }/big-screen-list?token=${getToken()}&env=${getEnv()}`
        window.open(url)
      }
    },
    creatQrCode() {
      nowVersion().then((e) => {
        this.obj = e.data
        if (e.data.versionApkFile) {
          this.appUrlIn = this.$store.state.app.uploadUrl + e.data.versionApkFile
          if (this.$refs.qrCodeUrlIn) {
            new QRCode(this.$refs.qrCodeUrlIn, {
              text: this.appUrlIn, // 需要转换为二维码的内容
              width: 100,
              height: 100,
              colorDark: '#000000',
              colorLight: '#ffffff',
              correctLevel: QRCode.CorrectLevel.H
            })
          }

          this.getConfigKey('sys.net.public').then((res) => {
            this.appUrlOut = res.msg + e.data.versionApkFile
            if (this.appUrlOut) {
              new QRCode(this.$refs.qrCodeUrlOut, {
                text: this.appUrlOut, // 需要转换为二维码的内容
                width: 100,
                height: 100,
                colorDark: '#000000',
                colorLight: '#ffffff',
                correctLevel: QRCode.CorrectLevel.H
              })
            }
          })
        }
      })
    },
    toAdminEditor() {
      window.open('http://' + window.location.host + '/adminEditor')
    },
    toggleSideBar() {
      this.$store.dispatch('app/toggleSideBar')
    },
    async logout() {
      this.$confirm('确定注销并退出系统吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$store.dispatch('LogOut').then(() => {
            location.href = '/index'
          })
        })
        .catch(() => {
        })
    },
    handleClose() {
      this.$confirm('是否关闭当前页面?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        window.close()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.appDownload:hover {
  opacity: 0.8;
}

.navbar {
  height: 50px;
  overflow: hidden;
  position: relative;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);

  .left-menu {
    float: left;
    height: 100%;

    .divider {
      width: 1px;
      height: 22px;
      background: #dcdfe6;
    }
  }

  .hamburger-container {
    line-height: 46px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background 0.3s;
    -webkit-tap-highlight-color: transparent;

    &:hover {
      background: rgba(0, 0, 0, 0.025);
    }
  }

  .breadcrumb-container {
    float: left;
  }

  .topmenu-container {
    position: absolute;
    left: 50px;
    background-color: var(--primary-color);
  }

  .errLog-container {
    display: inline-block;
    vertical-align: top;
  }

  .zyrm-right {
    float: right;
    height: 100%;
    line-height: 50px;
    .avatar-wrapper {
      position: relative;

      .user-avatar {
        cursor: pointer;
        width: 40px;
        height: 40px;
        border-radius: 10px;
      }

      .el-icon-caret-bottom {
        cursor: pointer;
        position: absolute;
        right: -20px;
        top: 25px;
        font-size: 12px;
      }
    }
    .closeBox {
      padding: 0 20px;
      border-left: 1px solid #fff;
      cursor: pointer;
      font-size: 14px;
      display: flex;
      align-items: center;
      svg {
        width: 25px;
        height: 25px;
      }
    }
  }

  .right-menu {
    float: right;
    height: 100%;
    line-height: 50px;
    padding-right: 20px;

    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      // padding: 0 8px;
      height: 100%;
      font-size: 18px;
      vertical-align: text-bottom;
      color: #fff;

      &.hover-effect {
        cursor: pointer;
        transition: background 0.3s;

        &:hover {
          background: rgba(0, 0, 0, 0.025);
        }
      }
    }

    .avatar-container {
      // margin-right: 30px;

      .avatar-wrapper {
        position: relative;

        .user-avatar {
          cursor: pointer;
          width: 40px;
          height: 40px;
          border-radius: 10px;
        }

        .el-icon-caret-bottom {
          cursor: pointer;
          position: absolute;
          right: -20px;
          top: 25px;
          font-size: 12px;
        }
      }
    }
  }
}

::v-depp .el-breadcrumb__item {
  line-height: 50px;
}

.qrcode {
  display: inline-block;

  img {
    width: 132px;
    height: 132px;
    background-color: #fff; //设置白色背景色
    padding: 6px; // 利用padding的特性，挤出白边
    box-sizing: border-box;
  }
}
</style>
