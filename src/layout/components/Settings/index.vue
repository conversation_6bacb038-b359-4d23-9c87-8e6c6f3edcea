<template>
  <div class="drawer-container">
    <div>
      <div class="setting-drawer-content">
        <div class="setting-drawer-title">
          <h3 class="drawer-title">导航栏设置</h3>
        </div>
        <div class="setting-drawer-block-checbox">
          <div
            class="setting-drawer-block-checbox-item"
            @click="handleTheme('theme-dark')"
          >
            <img src="@/assets/images/dark.svg" alt="dark" />
            <div
              v-if="sideTheme === 'theme-dark'"
              class="setting-drawer-block-checbox-selectIcon"
              style="display: block"
            >
              <i aria-label="图标: check" class="anticon anticon-check">
                <svg
                  viewBox="64 64 896 896"
                  data-icon="check"
                  width="1em"
                  height="1em"
                  :fill="theme"
                  aria-hidden="true"
                  focusable="false"
                  class=""
                >
                  <path
                    d="M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 0 0-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z"
                  />
                </svg>
              </i>
            </div>
          </div>
          <div
            class="setting-drawer-block-checbox-item"
            @click="handleTheme('theme-light')"
          >
            <img src="@/assets/images/light.svg" alt="light" />
            <div
              v-if="sideTheme === 'theme-light'"
              class="setting-drawer-block-checbox-selectIcon"
              style="display: block"
            >
              <i aria-label="图标: check" class="anticon anticon-check">
                <svg
                  viewBox="64 64 896 896"
                  data-icon="check"
                  width="1em"
                  height="1em"
                  :fill="theme"
                  aria-hidden="true"
                  focusable="false"
                  class=""
                >
                  <path
                    d="M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 0 0-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z"
                  />
                </svg>
              </i>
            </div>
          </div>
        </div>

        <div class="drawer-item">
          <span>主题颜色</span>
          <theme-picker
            style="float: right; height: 26px; margin: -3px 8px 0 0"
            @change="themeChange"
          />
        </div>
      </div>

      <el-divider />

      <h3 class="drawer-title">系统布局配置</h3>

      <!-- <div class="drawer-item">
        <span>开启顶部栏</span>
        <el-switch v-model="topNav" class="drawer-switch" />
      </div> -->
      <div class="drawer-item">
        <span>顶栏背景色</span>
        <el-color-picker
          style="float: right; height: 26px; margin: -3px 8px 0 0"
          popper-class="theme-picker-dropdown"
          v-model="topNavBgColor"
          :predefine="predefineColors"
        />
      </div>

      <div class="drawer-item">
        <span>顶栏字体色</span>
        <el-color-picker
          style="float: right; height: 26px; margin: -3px 8px 0 0"
          v-model="topNavColor"
          popper-class="theme-picker-dropdown"
          :predefine="['#333333', '#FFFFFF']"
        />
      </div>

      <div class="drawer-item">
        <span>开启面包屑</span>
        <el-switch v-model="breadcrumbView" class="drawer-switch" />
      </div>

      <div class="drawer-item">
        <span>开启标签页</span>
        <el-switch v-model="tagsView" class="drawer-switch" />
      </div>

      <!-- <div class="drawer-item">
        <span>固定头部</span>
        <el-switch v-model="fixedHeader" class="drawer-switch" />
      </div>

      <div class="drawer-item">
        <span>固定侧边栏</span>
        <el-switch v-model="fixedSidbar" class="drawer-switch" />
      </div> -->

      <div class="drawer-item">
        <span>显示Logo</span>
        <el-switch v-model="sidebarLogo" class="drawer-switch" />
      </div>

      <div class="drawer-item">
        <span>显示菜单折叠按钮</span>
        <el-switch v-model="foldMenu" class="drawer-switch" />
      </div>

      <div class="drawer-item">
        <span>菜单手风琴模式</span>
        <el-switch v-model="accordionMenu" class="drawer-switch" />
      </div>

      <div class="drawer-item">
        <span>菜单栏宽度</span>
        <el-input-number
          size="mini"
          v-model="menuWidth"
          :min="150"
          class="drawer-switch"
        />
      </div>

      <div class="drawer-item">
        <span>灰色模式</span>
        <el-switch v-model="greyModel" class="drawer-switch" />
      </div>

      <el-divider />

      <el-button
        size="small"
        type="primary"
        plain
        icon="el-icon-document-add"
        @click="saveSetting"
        >保存配置</el-button
      >
      <el-button size="small" plain icon="el-icon-refresh" @click="resetSetting"
        >重置配置</el-button
      >
    </div>
  </div>
</template>

<script>
import ThemePicker from '@/components/ThemePicker';

export default {
  components: { ThemePicker },
  data() {
    return {
      theme: this.$store.state.settings.theme,
      sideTheme: this.$store.state.settings.sideTheme,
    };
  },
  computed: {
    topNavBgColor: {
      get() {
        return this.$store.state.settings.topNavBgColor;
      },
      set(val) {
        this.$store.dispatch('settings/changeSetting', {
          key: 'topNavBgColor',
          value: val,
        });
      },
    },
    topNavColor: {
      get() {
        return this.$store.state.settings.topNavColor;
      },
      set(val) {
        this.$store.dispatch('settings/changeSetting', {
          key: 'topNavColor',
          value: val,
        });
      },
    },
    topNav: {
      get() {
        return this.$store.state.settings.topNav;
      },
      set(val) {
        this.$store.dispatch('settings/changeSetting', {
          key: 'topNav',
          value: val,
        });
        if (!val) {
          this.$store.commit(
            'SET_SIDEBAR_ROUTERS',
            this.$store.state.permission.defaultRoutes,
          );
        }
      },
    },
    tagsView: {
      get() {
        return this.$store.state.settings.tagsView;
      },
      set(val) {
        this.$store.dispatch('settings/changeSetting', {
          key: 'tagsView',
          value: val,
        });
      },
    },
    breadcrumbView: {
      get() {
        return this.$store.state.settings.breadcrumbView;
      },
      set(val) {
        this.$store.dispatch('settings/changeSetting', {
          key: 'breadcrumbView',
          value: val,
        });
      },
    },
    sidebarLogo: {
      get() {
        return this.$store.state.settings.sidebarLogo;
      },
      set(val) {
        this.$store.dispatch('settings/changeSetting', {
          key: 'sidebarLogo',
          value: val,
        });
      },
    },
    foldMenu: {
      get() {
        return this.$store.state.settings.foldMenu;
      },
      set(val) {
        this.$store.dispatch('settings/changeSetting', {
          key: 'foldMenu',
          value: val,
        });
      },
    },
    accordionMenu: {
      get() {
        return this.$store.state.settings.accordionMenu;
      },
      set(val) {
        this.$store.dispatch('settings/changeSetting', {
          key: 'accordionMenu',
          value: val,
        });
      },
    },
    menuWidth: {
      get() {
        return this.$store.state.settings.menuWidth;
      },
      set(val) {
        this.$store.dispatch('settings/changeSetting', {
          key: 'menuWidth',
          value: val,
        });
      },
    },
    greyModel: {
      get() {
        return this.$store.state.settings.greyModel;
      },
      set(val) {
        this.$store.dispatch('settings/changeSetting', {
          key: 'greyModel',
          value: val,
        });
      },
    },
    predefineColors() {
      return [
            '#FFFFFF',
            this.$store.state.settings.theme,
            '#ff9800',
            '#24292e',
            '#c0e4ff',
          ]
    }

  },
  methods: {
    themeChange(val) {
      this.$store.dispatch('settings/changeSetting', {
        key: 'theme',
        value: val,
      });
      this.theme = val;
    },
    handleTheme(val) {
      this.$store.dispatch('settings/changeSetting', {
        key: 'sideTheme',
        value: val,
      });
      this.sideTheme = val;
    },
    saveSetting() {
      const loading = this.$loading({
        lock: true,
        fullscreen: false,
        text: '正在保存到本地，请稍后...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)',
      });
      localStorage.setItem(
        'layout-setting',
        JSON.stringify({
          topNavBgColor: this.topNavBgColor,
          topNavColor: this.topNavColor,
          topNav: this.topNav,
          tagsView: this.tagsView,
          fixedHeader: this.fixedHeader,
          sidebarLogo: this.sidebarLogo,
          foldMenu: this.foldMenu,
          sideTheme: this.sideTheme,
          theme: this.theme,
        }),
      );
      setTimeout(() => {
        loading.close()
        this.$store.dispatch('settings/changeSetting', {
          key: 'showSettings',
          value: false,
        });
      }, 1000);

    },
    resetSetting() {
      this.$loading({
        lock: true,
        fullscreen: false,
        text: '正在清除设置缓存并刷新，请稍后...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)',
      });
      localStorage.removeItem('layout-setting');
      setTimeout('window.location.reload()', 1000);
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-color-picker__trigger {
  height: 26px !important;
  width: 26px !important;
  padding: 2px;
}

::v-deep .el-color-dropdown__link-btn {
  display: none;
}

.setting-drawer-content {
  .setting-drawer-title {
    margin-bottom: 10px;
    color: rgba(0, 0, 0, 0.85);
    font-size: 14px;
    line-height: 22px;
    font-weight: bold;
  }

  .setting-drawer-block-checbox {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-top: 10px;
    margin-bottom: 15px;

    .setting-drawer-block-checbox-item {
      position: relative;
      margin-right: 16px;
      border-radius: 2px;
      cursor: pointer;

      img {
        width: 48px;
        height: 48px;
      }

      .setting-drawer-block-checbox-selectIcon {
        position: absolute;
        top: 0;
        right: 0;
        width: 100%;
        height: 100%;
        padding-top: 15px;
        padding-left: 24px;
        color: var(--primary-color);
        font-weight: 700;
        font-size: 14px;
      }
    }
  }
}

.drawer-container {
  padding: 15px;
  font-size: 14px;
  line-height: 1.5;
  word-wrap: break-word;
  overflow: auto;
  height: 100%;
  .drawer-title {
    margin-bottom: 12px;
    color: rgba(0, 0, 0, 0.85);
    font-size: 14px;
    line-height: 22px;
  }

  .drawer-item {
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
    padding: 10px 0;
  }

  .drawer-switch {
    float: right;
  }
}
</style>
