<template>
  <div id="app">
    <router-view v-if="isRouterAlive" />
    <AIAgent v-if="isUserLoggedIn"/>
  </div>
</template>

<script>
import AIAgent from "@/views/admin/aiAgent/index"
export default {
  components: {
    AIAgent
  },
  name: 'App',
  provide() {
    return {
      reload: this.reload,
    };
  },
  data() {
    return {
      isRouterAlive: true,
    };
  },
  computed: {
    isUserLoggedIn() {
      // 通过Vuex store判断用户是否已登录
      return this.$store.state.user.token ? true : false;
    }
  },
  mounted() {
    // 禁用手动缩放
    // 方法一
    // 判断pc浏览器是否缩放，若返回100则为默认无缩放，如果大于100则是放大，否则缩小
    function detectZoom() {
      var ratio = 0;
      var screen = window.screen;

      const ua = navigator.userAgent.toLowerCase();

      if (window.devicePixelRatio !== undefined) {
        ratio = window.devicePixelRatio;
      } else if (~ua.indexOf('msie')) {
        if (screen.deviceXDPI && screen.logicalXDPI) {
          ratio = screen.deviceXDPI / screen.logicalXDPI;
        }
      } else if (
        window.outerWidth !== undefined &&
        window.innerWidth !== undefined
      ) {
        ratio = window.outerWidth / window.innerWidth;
      }
      if (ratio) {
        ratio = Math.round(ratio * 100);
      }
      return ratio;
    }
    // window.onresize 事件可用于检测页面是否触发了放大或缩小。
    window.addEventListener('resize', function () {
      isScale();
    });
    isScale();
    // 判断PC端浏览器缩放比例不是100%时的情况
    function isScale() {
      var rate = detectZoom();
      var isMac = /macintosh|mac os x/i.test(navigator.userAgent); // Mac默认缩放值为200，windows默认为100，需要分开判断
      if ((isMac && rate !== 200) || (!isMac && rate !== 100)) {
        // 如何让页面的缩放比例自动为100,'transform':'scale(1,1)'没有用，又无法自动条用键盘事件，目前只能提示让用户如果想使用100%的比例手动去触发按ctrl+0
        // alert('当前页面不是100%显示，请按键盘ctrl/command + 0恢复100%显示标准，以防页面显示错乱！')
      }
    }

    // 阻止pc端浏览器缩放js代码
    // 由于浏览器菜单栏属于系统软件权限，没发控制，我们着手解决ctrl/cammond + +/- 或 Windows下ctrl + 滚轮 缩放页面的情况，只能通过js来控制了
    document.addEventListener(
      'DOMContentLoaded',
      function (event) {
        // chrome 浏览器直接加上下面这个样式就行了，但是ff不识别
        document.body.style.zoom = 'reset';
        document.addEventListener(
          'keydown',
          function (event) {
            if (
              (event.ctrlKey === true || event.metaKey === true) &&
              (event.which === 61 ||
                event.which === 107 ||
                event.which === 173 ||
                event.which === 109 ||
                event.which === 187 ||
                event.which === 189)
            ) {
              event.preventDefault();
            }
          },
          false,
        );
        var scrollFunc = function (event) {
          event = event || window.event;
          if (event.wheelDelta) {
            // 判断浏览器IE，谷歌滑轮事件
            if (event.ctrlKey === true || event.metaKey) {
              event.preventDefault();
            }
          }
        };
        // 给页面绑定滑轮滚动事件
        if (document.addEventListener) {
          document.addEventListener('DOMMouseScroll', scrollFunc, false);
        }
        // 滚动滑轮触发scrollFunc方法
        window.onmousewheel = document.onmousewheel = scrollFunc;
      },
      false,
    );

    // 方法二
    document.addEventListener(
      'keydown',
      function (event) {
        if (
          (event.ctrlKey === true || event.metaKey === true) &&
          (event.which === 61 ||
            event.which === 107 ||
            event.which === 173 ||
            event.which === 109 ||
            event.which === 187 ||
            event.which === 189)
        ) {
          event.preventDefault();
        }
      },
      false,
    );
    // Chrome IE 360
    window.addEventListener(
      'mousewheel',
      function (event) {
        if (event.ctrlKey === true || event.metaKey) {
          event.preventDefault();
        }
      },
      {
        passive: false,
      },
    );

    // firefox
    window.addEventListener(
      'DOMMouseScroll',
      function (event) {
        if (event.ctrlKey === true || event.metaKey) {
          event.preventDefault();
        }
      },
      {
        passive: false,
      },
    );
  },
  // updated() {
  //   const loading = document.getElementById('loader-wrapper');
  //   if (loading) {
  //     document.body.removeChild(loading);
  //   }
  // },
  methods: {
    /**
     * 刷新当前路由
     */
    reload() {
      this.isRouterAlive = false;
      this.$nextTick(() => {
        this.isRouterAlive = true;
      });
    },
  },
};
</script>

<style lang="scss">
</style>
