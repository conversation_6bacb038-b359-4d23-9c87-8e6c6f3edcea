export const HighLightOptions = {
  // 是否区分大小写，true区分
  ignoreCase: false,
  // 关键字
  keywords: [
    'new',
    'var',
    'if',
    'else',
    'for',
    'in',
    'return',
    'import',
    'break',
    'continue',
    'as',
    'null',
    'true',
    'false',
    'try',
    'catch',
    'finally',
    'async',
    'while',
    'exit',
    'asc',
    'desc',
    'ASC',
    'DESC',
    'assert',
    'let',
    'const',
    'throw',
  ],
  // 类型关键字
  typeKeywords: [],
  // 代码解析配置格式为[正则表达式,对应的类型如（string，number，comment）]
  tokenizer: {
    // 解析的主入口，用于关联正则表达式和解析类型
    root: [
      [/real/, 'namespace'],
      // 关键字的匹配
      [
        /[a-z_$][\w$]*/,
        {
          cases: {
            '@typeKeywords': 'keyword',
            '@keywords': 'keyword',
            '@default': 'identifier',
          },
        },
      ],
      [
        /"""/,
        {
          token: 'string',
          next: '@string_multi_embedded',
          nextEmbedded: 'mybatis',
        },
      ],
      // [/[A-Z][\w\$]*/, 'type.identifier' ],  // 高亮首字母大写的class
      // 注释
      // 可以理解为[正则表达式，匹配类型如（string，comment，namespace等），命名空间（可以理解为class）]
      // 以注释为例以下配置表示为检索到"后开始标识为注释，按照comment去配置
      [/"/, 'comment', '@comment'],
      // 可以同时对comment设置多组配置，用‘@类名’区分
      [/\/\*/, 'comment', '@commentT'],
      // 单行注释
      [/\/\//, 'comment', '@commentL'],
      // include表示将@numbers的解析应用
      { include: '@numbers' },
    ],
    string_multi_embedded: [
      [/[^"]+/, ''],
      ['"""', { token: 'string', next: '@pop', nextEmbedded: '@pop' }],
    ],
    // 用于对应上方
    comment: [
      // 表示匹配到"后停止验证
      [/"/, 'comment', '@pop'],
      // escape表示在当前验证模式下忽略/\\"/匹配项
      [/\\"/, 'comment.escape'],
      // content表示在这个验证模式下内容应该是那些/./表示所有包含换行，空格等字符
      [/./, 'comment.content'],
      // 还有诸如@push等方法还没试
    ],
    commentT: [
      [/\*\//, 'comment', '@pop'],
      // 如需配置行注释则添加换行符等验证
      [/./, 'comment.content'],
    ],
    // 对应{include:'@numbers'}
    numbers: [
      [/-?0x([abcdef]|[ABCDEF]|\d)+[lL]?/, 'number.hex'],
      [/-?(\d*\.)?\d+([eE][+\-]?\d+)?[jJ]?[lL]?/, 'number'],
    ],
    commentL: [[/.+/, 'comment', '@pop']],
  },
};
