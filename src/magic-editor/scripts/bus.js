import Vue from 'vue'
import contants from './contants.js'
import {formatDate} from "@/magic-editor/scripts/utils.js";
const statusLog = [];
const bus = new Vue()

bus.$on('report', (eventId) => {
    try {
        window._czc.push(["_trackEvent",eventId,eventId])
    } catch (ignored) {

    }
})
bus.$on('status', (content) => {
    statusLog.push({
        timestamp: formatDate(new Date()),
        content
    })
})
bus.$getStatusLog = () => statusLog;
bus.$clearStatusLog = () => statusLog.length = 0
export default bus
