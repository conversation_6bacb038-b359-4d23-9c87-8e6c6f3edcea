import bus from "@/magic-editor/scripts/bus";
import ReconnectingWebSocket from './reconnecting-websocket'
import { randomString } from '@/magic-editor/scripts/utils'
function MagicWebSocket(url) {
    /*this.listeners = {};
    this.socket = new ReconnectingWebSocket(url);
    this.socket.onmessage = this.messageReceived;
    bus.$on('message', (msgType, content) => {
        if (content) {
            this.socket.send(`${msgType},${content}`)
        } else {
            this.socket.send(msgType)
        }
    })
    this.socket.onopen = ()=> {
        localStorage.setItem("clientId", randomString(16))
        bus.$emit('ws_open')
    }
    this.socket.onerror=(err)=>{
        this.socket.close()
        // throw err
    }
    this.socket.onclose=()=>{

    }*/
}

MagicWebSocket.prototype.on = function (msgType, callback) {
    /*this.listeners[msgType] = this.listeners[msgType] || []
    this.listeners[msgType].push(callback)*/
}

MagicWebSocket.prototype.messageReceived = function (e) {
  /*let payload = e.data
  let index = payload.indexOf(",")
  let msgType = index === -1 ? payload : payload.substring(0, index)
  let args = []
  while (index > -1) {
    payload = payload.substring(index + 1)
    if (payload.startsWith('[') || payload.startsWith('{')) {
      args.push(JSON.parse(payload))
      break;
    }
    index = payload.indexOf(",")
    args.push(index === -1 ? payload : payload.substring(0, index))
  }
    bus.$emit('ws_' + msgType, args)*/
}
MagicWebSocket.prototype.close = function () {
    // this.socket.close()
}

export default MagicWebSocket
