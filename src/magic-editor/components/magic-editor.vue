<template>
  <div ref="container" :style="themeStyle" class="ma-container" tabindex="0">
    <magic-loading
      v-if="loading"
      :title.sync="config.title"
      :version.sync="config.version"
    />
    <magic-login v-if="showLogin" :onLogin="onLogin" />
    <!-- 顶部Header -->
    <magic-header
      :config="config"
      :themeStyle.sync="themeStyle"
      class="not-select"
    />
    <ul class="ma-toolbar-container not-select">
      <li
        v-for="(item, index) in toolbars"
        :key="'toolbar_' + index"
        :class="{ selected: toolbarIndex === index }"
        @click="toolbarIndex = toolbarIndex === index ? -1 : index"
      >
        {{ item }}
        <i class="ma-icon ma-icon-list"></i>
      </li>
    </ul>

    <!-- 中间主要内容 -->
    <div class="ma-main-container">
      <div class="ma-middle-container">
        <magic-api-list
          v-show="toolbarIndex === 0"
          ref="apiList"
          :style="{ width: toolboxWidth }"
          class="not-select"
        />
        <magic-function-list
          v-show="toolbarIndex === 1"
          ref="functionList"
          :style="{ width: toolboxWidth }"
          class="not-select"
        />
        <magic-menu-list
          v-show="toolbarIndex === 2"
          ref="menuList"
          :style="{ width: toolboxWidth }"
          class="not-select"
        />
        <magic-view-list
          v-show="toolbarIndex === 3"
          ref="viewList"
          :style="{ width: toolboxWidth }"
          class="not-select"
        />

        <magic-report-list
          v-show="toolbarIndex === 4"
          ref="reportList"
          :style="{ width: toolboxWidth }"
          class="not-select"
        />
        <magic-screen-list
          v-show="toolbarIndex === 5"
          ref="screenList"
          :style="{ width: toolboxWidth }"
          class="not-select"
        />
        <magic-flow-list
          v-show="toolbarIndex === 6"
          ref="flowList"
          :style="{ width: toolboxWidth }"
          class="not-select"
        />
        <magic-assembly-list
          v-show="toolbarIndex === 7"
          ref="assemblyList"
          :style="{ width: toolboxWidth }"
          class="not-select"
        />
        <div ref="resizer" class="ma-resizer-x" @mousedown="doResizeX"></div>
        <magic-script-editor class="ma-editor-container" />
        <div
          ref="resizerRight"
          class="ma-resizer-right-x"
          @mousedown="doResizeRightX"
        ></div>
        <magic-datasource-list
          v-show="toolbarsRightIndex === 0"
          ref="datasourceList"
          :style="{ width: toolbarsRightWidth }"
          class="not-select"
        />
        <magic-timer-list
          v-show="toolbarsRightIndex === 1"
          ref="timerList"
          :style="{ width: toolbarsRightWidth }"
          class="not-select"
        />
        <magic-data-sync-list
          v-show="toolbarsRightIndex === 2"
          ref="dataSyncList"
          :style="{ width: toolbarsRightWidth }"
          class="not-select"
        />
        <magic-listen-list
          v-show="toolbarsRightIndex === 3"
          ref="listenList"
          :style="{ width: toolbarsRightWidth }"
          class="not-select"
        />
        <magic-initialization-list
          v-show="toolbarsRightIndex === 4"
          ref="initializationList"
          :style="{ width: toolbarsRightWidth }"
          class="not-select"
        />
      </div>
      <!-- 底部区域 -->
      <magic-options
        v-show="
          currentInfo === null ||
          ['api', 'timer',  'function' , 'initTask'].includes(
            currentInfo && currentInfo.temps && currentInfo.temps.type,
          )
        "
      />
    </div>

    <ul class="ma-toolbar-container-right not-select">
      <li
        v-for="(item, index) in toolbarsRight"
        :key="'toolbar_right_' + index"
        :class="{ selected: toolbarsRightIndex === index }"
        @click="toolbarsRightIndex = toolbarsRightIndex === index ? -1 : index"
      >
        {{ item }}
        <i class="ma-icon ma-icon-list"></i>
      </li>
    </ul>

    <!-- 最近打开 -->
    <magic-recent-opened />
    <!-- 状态条 -->
    <magic-status-bar :config="config" />
  </div>
</template>

<script>
import '@/magic-editor/assets/index.css';
import '@/magic-editor/assets/iconfont/iconfont.css';
import '@/assets/index.css';
import '@/assets/iconfont/iconfont.css';
import bus from '@/magic-editor/scripts/bus.js';
import MagicLoading from './common/magic-loading.vue';
import MagicHeader from './layout/magic-header.vue';
import MagicLogin from './layout/magic-login.vue';
import MagicStatusBar from './layout/magic-status-bar.vue';
import MagicOptions from './layout/magic-options.vue';
import MagicApiList from './resources/magic-api-list.vue';
import MagicMenuList from './resources/magic-menu-list.vue';
import MagicViewList from './resources/magic-view-list.vue';
import MagicFlowList from './resources/magic-flow-list.vue';
import MagicAssemblyList from './resources/magic-assembly-list.vue';
import MagicScreenList from './resources/magic-bigscreen-list.vue';
import MagicReportList from './resources/magic-report-list.vue';

import MagicFunctionList from './resources/magic-function-list.vue';
import MagicDatasourceList from './resources/magic-datasource-list.vue';
import MagicTimerList from './resources/magic-timer-list.vue';
import MagicInitializationList from './resources/magic-initialization-list.vue';
import MagicListenList from './resources/magic-listen-list.vue';
import MagicScriptEditor from './editor/magic-script-editor.vue';
import MagicRecentOpened from './resources/magic-recent-opened.vue';
import MagicDataSyncList from './resources/magic-data-sync-list.vue';
import request from '@/magic-editor/api/request.js';
import contants from '@/magic-editor/scripts/contants.js';
import MagicWebSocket from '@/magic-editor/scripts/websocket.js';
import store from '@/magic-editor/scripts/store.js';
import Key from '@/magic-editor/scripts/hotkey.js';
import { getQueryVariable, replaceURL } from '@/magic-editor/scripts/utils.js';
import { defineTheme } from '@/magic-editor/scripts/editor/theme.js';
import defaultTheme from '@/magic-editor/scripts/editor/default-theme.js';
import darkTheme from '@/magic-editor/scripts/editor/dark-theme.js';
import JavaClass from '@/magic-editor/scripts/editor/java-class.js';
import JsonWorker from '@/magic-editor/scripts/workers/json.worker.js';
import EditorWorker from '@/magic-editor/scripts/workers/editor.worker.js';

self.MonacoEnvironment = {
  getWorker: function (moduleId, label) {
    if (label === 'json') {
      return new JsonWorker();
    }
    return new EditorWorker();
  },
};
export default {
  name: 'MagicEditor',
  props: {
    config: {
      type: Object,
      required: true,
    },
  },
  components: {
    MagicHeader,
    MagicStatusBar,
    MagicApiList,
    MagicMenuList,
    MagicViewList,
    MagicFlowList,
    MagicScreenList,
    MagicReportList,
    MagicAssemblyList,
    MagicFunctionList,
    MagicScriptEditor,
    MagicOptions,
    MagicLoading,
    MagicLogin,
    MagicDatasourceList,
    MagicListenList,
    MagicTimerList,
    MagicInitializationList,
    MagicRecentOpened,
    MagicDataSyncList,
  },
  data() {
    return {
      loading: true,
      toolbars: [
        '接口',
        '函数',
        '路由',
        '视图',
        '报表',
        '大屏',
        '流程设计',
        '在线组件',
      ],
      toolbarIndex: 0,
      toolboxWidth: 'auto', //工具条宽度

      toolbarsRight: ['数据源', '定时任务', '数据同步', '监听任务','初始化任务'],
      toolbarsRightIndex: '',
      toolbarsRightWidth: 'auto', //工具条宽度

      themeStyle: {},
      showLogin: false,
      websocket: null,
      currentInfo: null,
      onLogin: () => {
        this.showLogin = false;
        Promise.all([
          // 初始化列表组件数据
          this.$refs.apiList.initData(),
          this.$refs.functionList.initData(),
          this.$refs.datasourceList.initData(),
          this.$refs.timerList.initData(),
          this.$refs.listenList.initData(),
          this.$refs.initializationList.initData(),
          this.$refs.menuList.initData(),
          this.$refs.viewList.initData(),
          this.$refs.flowList.initData(),
          this.$refs.screenList.initData(),
          this.$refs.reportList.initData(),
          this.$refs.dataSyncList.initData(),
          this.$refs.assemblyList.initData(),
        ]).then(() => {
          bus.$emit('login');
        });
      },
    };
  },
  beforeMount() {
    contants.BASE_URL = this.config.baseURL || '';
    contants.SERVER_URL = this.config.serverURL || '';
    let link =
      `${location.protocol}//${location.host}${location.pathname}`.replace(
        '/index.html',
        '',
      );
    if (contants.BASE_URL.startsWith('http')) {
      // http开头
      link = contants.BASE_URL;
    } else if (contants.BASE_URL.startsWith('/')) {
      // / 开头的
      link = `${location.protocol}/${location.host}${contants.BASE_URL}`;
    } else {
      link = link + '/' + contants.BASE_URL;
    }
    //"ws://localhost:9210/magic/web/console"
    console.log(link.replace(/^http/, 'ws') + '/console')
    /* this.websocket = new MagicWebSocket(
      replaceURL(link.replace(/^http/, 'ws') + '/console'),
    ); */
    bus.$on('ws_open', () =>

      bus.$emit('message', 'login', [contants.HEADER_MAGIC_TOKEN_VALUE, localStorage.getItem("clientId")].join(','))
    );
    contants.DEFAULT_EXPAND = this.config.defaultExpand !== false;
    contants.JDBC_DRIVERS = this.config.jdbcDrivers || [];
    contants.DATASOURCE_TYPES = this.config.datasourceTypes || [];
    contants.OPTIONS = this.config.options || [];
    if (this.config.editorFontFamily !== undefined) {
      contants.EDITOR_FONT_FAMILY = this.config.editorFontFamily;
    }
    if (this.config.editorFontSize !== undefined) {
      contants.EDITOR_FONT_SIZE = this.config.editorFontSize;
    }
    if (this.config.logMaxRows !== undefined) {
      contants.LOG_MAX_ROWS = Math.max(this.config.logMaxRows, 10);
    }
    this.config.version = contants.MAGIC_API_VERSION_TEXT;
    this.config.title = this.config.title || 'magic-api';
    this.config.themes = this.config.themes || {};
    this.config.defaultTheme = this.config.defaultTheme || 'default';
    this.config.header = this.config.header || {
      skin: true,
      document: true,
      repo: true,
      qqGroup: true,
    };
    contants.AUTO_SAVE = this.config.autoSave !== false;
    if (this.config.decorationTimeout !== undefined) {
      contants.DECORATION_TIMEOUT = this.config.decorationTimeout;
    }
    this.config.request = this.config.request || {
      beforeSend: (config) => config,
      onError: (err) => Promise.reject(err),
    };
    this.config.response = this.config.response || {
      onSuccess: (resp) => resp,
      onError: (err) => Promise.reject(err),
    };
    request.setBaseURL(contants.BASE_URL);
    request.getAxios().interceptors.request.use(
      (config) => {
        if (this.config.request.beforeSend) {
          return this.config.request.beforeSend(config);
        }
        return config;
      },
      (err) => {
        if (this.config.request.onError) {
          return this.config.request.onError(err);
        }
        return Promise.reject(err);
      },
    );
    request.getAxios().interceptors.response.use(
      (resp) => {
        if (this.config.response.onSuccess) {
          return this.config.response.onSuccess(resp);
        }
        return resp;
      },
      (err) => {
        if (this.config.response.onError) {
          return this.config.response.onError(err);
        }
        return Promise.reject(err);
      },
    );
    defineTheme('default', defaultTheme);
    defineTheme('dark', darkTheme);
    Object.keys(this.config.themes || {}).forEach((themeKey) => {
      defineTheme(themeKey, this.config.themes[themeKey]);
    });
  },
  mounted() {
    if (this.config.blockClose !== false) {
      window.onbeforeunload = () => '系统可能不会保存您所做的更改。';
    }
    this.bindKey();
    JavaClass.initClasses();
    JavaClass.initImportClass();
    Promise.all([this.loadConfig()])
      .then((e) => {
        this.hideLoading();
        this.login();
      })
      .catch((e) => {
        this.hideLoading();
        this.$magicAlert({
          title: '加载失败',
          content: '请检查配置项baseURL是否正确！',
        });
      });
    bus.$on('search-open', (item) => {
      if (item.type === 1) {
        this.toolbarIndex = 0;
      } else if (item.type === 2) {
        this.toolbarIndex = 1;
      }
    });
    bus.$on('logout', () => {
      this.showLogin = true;
      //this.websocket.close();
    });
    bus.$on('showLogin', () => (this.showLogin = true));
    bus.$on('position-api', (id) => {
      this.toolbarIndex = 0;
      this.$refs.apiList.position(id);
    });
    bus.$on('position-function', (id) => {
      this.toolbarIndex = 1;
      this.$refs.functionList.position(id);
    });
    bus.$on('opened', (info) => {
      this.currentInfo = info;
    });
    this.open();
  },
  destroyed() {
    bus.$off();
    Key.unbind();
    //this.websocket.close();
  },
  methods: {
    // 隐藏loading
    hideLoading() {
      this.loading = false;
      if (typeof hideMaLoading === 'function') {
        hideMaLoading();
      }
    },
    bindKey() {
      let element = this.$refs.container;
      // 绑定保存快捷键
      Key.bind(element, Key.Ctrl | Key.S, () => bus.$emit('doSave'));
      // 绑定测试快捷键
      Key.bind(element, Key.Ctrl | Key.Q, () => bus.$emit('doTest'));
      // 绑定F8快捷键，恢复断点
      Key.bind(element, Key.F8, () => bus.$emit('doContinue'));
      // 绑定F6快捷键，进入下一步
      Key.bind(element, Key.F6, () => bus.$emit('doStepInto'));
    },
    async loadConfig() {
      request
        .execute({ url: '/config.json' })
        .then((res) => {
          contants.config = res.data;
          // 如果在jar中引用，需要处理一下SERVER_URL
          if (this.config.inJar && location.href.indexOf(res.data.web) > -1) {
            let host = location.href.substring(
              0,
              location.href.indexOf(res.data.web),
            );
            contants.SERVER_URL = replaceURL(
              host + '/' + (res.data.prefix || ''),
            );
          }
          if (
            contants.config.version &&
            contants.config.version !== contants.MAGIC_API_VERSION_TEXT
          ) {
            // this.$magicAlert({
            //   title: '版本检测',
            //   content: `检测到前后端版本不一致（前端：${contants.MAGIC_API_VERSION_TEXT} 后端：${contants.config.version}），请检查`
            // })
          }
        })
        .catch((e) => {
          this.$magicAlert({
            title: '加载配置失败',
            content:
              (e.response.status || 'unknow') +
              ':' +
              (JSON.stringify(e.response.data) || 'unknow'),
          });
        });
    },
    doResizeRightX() {
      let rect = this.$refs.resizerRight.getBoundingClientRect();
      // 工具条宽度
      let container;
      if (this.toolbarsRightIndex == 0) {
        container = this.$refs.datasourceList;
      } else if (this.toolbarsRightIndex == 1) {
        container = this.$refs.timerList;
      } else if (this.toolbarsRightIndex == 2) {
        container = this.$refs.dataSyncList;
      }
      else if (this.toolbarsRightIndex == 3) {
        container = this.$refs.listenList;
      }else if (this.toolbarsRightIndex == 4) {
        container = this.$refs.initializationList;
      }

      let width = container.$el.clientWidth;
      document.onmousemove = (e) => {
        let move = rect.x - e.clientX + width;
        if (move >= 274 && move < 700) {
          this.toolbarsRightWidth = move + 'px';
        }
      };
      document.onmouseup = () => {
        document.onmousemove = document.onmouseup = null;
        this.$refs.resizerRight.releaseCapture &&
          this.$refs.resizerRight.releaseCapture();
      };
      bus.$emit('update-window-size');
    },
    doResizeX() {
      let rect = this.$refs.resizer.getBoundingClientRect();
      // 工具条宽度
      let container = this.$refs.apiList;
      if (this.toolbarIndex === 1) {
        container = this.$refs.functionList;
      } else if (this.toolbarIndex === 2) {
        container = this.$refs.menuList;
      } else if (this.toolbarIndex === 3) {
        container = this.$refs.viewList;
      } else if (this.toolbarIndex === 4) {
        container = this.$refs.reportList;
      } else if (this.toolbarIndex === 5) {
        container = this.$refs.screenList;
      }else if (this.toolbarIndex === 6) {
        container = this.$refs.flowList;
      }else if (this.toolbarIndex === 7) {
        container = this.$refs.assemblyList;
      }

      let width = container.$el.clientWidth;
      document.onmousemove = (e) => {
        let move = e.clientX - rect.x + +width;
        if (move >= 274 && move < 700) {
          this.toolboxWidth = move + 'px';
        }
      };
      document.onmouseup = () => {
        document.onmousemove = document.onmouseup = null;
        this.$refs.resizer.releaseCapture &&
          this.$refs.resizer.releaseCapture();
      };
      bus.$emit('update-window-size');
    },
    async login() {
      contants.HEADER_MAGIC_TOKEN_VALUE =
        store.get(contants.HEADER_MAGIC_TOKEN) ||
        contants.HEADER_MAGIC_TOKEN_VALUE;
      bus.$emit('status', '尝试自动登录');
      request.send('/login').success((isLogin) => {
        if (isLogin) {
          bus.$emit('status', '自动登录成功');
          this.onLogin();
        } else {
          this.showLogin = true;
        }
      });
    },
    /**
     * 传入id来打开对应api或者function
     */
    open(openIds) {
      try {
        JSON.parse(store.get(contants.RECENT_OPENED_TAB)).forEach((id) => {
          this.$refs.apiList.openItemById(id);
          this.$refs.functionList.openItemById(id);
          this.$refs.datasourceList.openItemById(id);
          this.$refs.timerList.openItemById(id);
          this.$refs.listenList.openItemById(id);
          this.$refs.initializationList.openItemById(id);
          this.$refs.menuList.openItemById(id);
          this.$refs.viewList.openItemById(id);
          this.$refs.flowList.openItemById(id);
          this.$refs.screenList.openItemById(id);
          this.$refs.reportList.openItemById(id);
          this.$refs.dataSyncList.openItemById(id);
          this.$refs.assemblyList.openItemById(id);
        });
      } catch (e) {
        // ignore
      }
      openIds = openIds || getQueryVariable('openIds');
      if (openIds) {
        if (typeof openIds === 'string') {
          openIds = openIds.split(',');
        }
        openIds.forEach((id) => {
          this.$refs.apiList.openItemById(id);
          this.$refs.functionList.openItemById(id);
          this.$refs.datasourceList.openItemById(id);
          this.$refs.timerList.openItemById(id);
          this.$refs.listenList.openItemById(id);
          this.$refs.initializationList.openItemById(id);
          this.$refs.menuList.openItemById(id);
          this.$refs.viewList.openItemById(id);
          this.$refs.flowList.openItemById(id);
          this.$refs.screenList.openItemById(id);
          this.$refs.reportList.openItemById(id);
          this.$refs.dataSyncList.openItemById(id);
          this.$refs.assemblyList.openItemById(id);
        });
      }
    },
  },
  watch: {
    toolbarIndex() {
      bus.$emit('update-window-size');
    },
  },
};
</script>
<style scoped>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.ma-main-container {
  position: absolute;
  top: 30px;
  left: 0px;
  bottom: 24px;
  right: 22px;
  display: flex;
  flex-direction: column;
}

.ma-middle-container {
  flex: 1;
  display: flex;
  overflow: auto;
  background: var(--middle-background);
  border-bottom: 1px solid var(--border-color);
}

.ma-toolbar-container {
  background: var(--background);
  border-right: 1px solid var(--toolbox-border-color);
  width: 22px;
  position: absolute;
  left: 0;
  bottom: 24px;
  top: 30px;
}

.ma-toolbar-container-right {
  background: var(--background);
  border-right: 1px solid var(--toolbox-border-color);
  width: 22px;
  position: absolute;
  right: 0;
  bottom: 24px;
  top: 30px;
}

.ma-toolbar-container > li {
  padding: 6px 3px;
  cursor: pointer;
  letter-spacing: 2px;
  text-align: center;
  color: var(--color);
  border-bottom: 1px solid var(--toolbox-border-color);
}

.ma-toolbar-container > li > i {
  color: var(--icon-color);
  font-size: 14px;
  padding-top: 3px;
  display: inline-block;
}

.ma-toolbar-container > li:hover {
  background: var(--hover-background);
}

.ma-toolbar-container > li.selected {
  background: var(--selected-background);
  color: var(--selected-color);
}

.ma-toolbar-container-right > li {
  padding: 6px 3px;
  cursor: pointer;
  letter-spacing: 2px;
  text-align: center;
  color: var(--color);
  border-bottom: 1px solid var(--toolbox-border-color);
}

.ma-toolbar-container-right > li > i {
  color: var(--icon-color);
  font-size: 14px;
  padding-top: 3px;
  display: inline-block;
}

.ma-toolbar-container-right > li:hover {
  background: var(--hover-background);
}

.ma-toolbar-container-right > li.selected {
  background: var(--selected-background);
  color: var(--selected-color);
}
.ma-resizer-right-x {
  float: right;
  width: 10px;
  height: 100%;
  margin-right: -10px;
  background: none;
  cursor: e-resize;
  z-index: 1000;
}
.ma-resizer-x {
  float: left;
  width: 10px;
  height: 100%;
  margin-left: -10px;
  background: none;
  cursor: e-resize;
  z-index: 1000;
}

.ma-bottom-container {
  background: var(--background);
}

.ma-bottom-container .ma-bottom-content-container {
  border-bottom: 1px solid var(--tab-bar-border-color);
  height: 300px;
}

.ma-bottom-container .ma-bottom-content-container > div {
  display: none;
}

.ma-bottom-container .ma-bottom-content-container > .visible {
  display: block;
}

.ma-bottom-tab li {
  float: left;
  cursor: pointer;
  padding: 0 8px;
  height: 24px;
  line-height: 24px;
  color: var(--color);
}
.ma-bottom-tab li.float-right {
  float: right;
}

.ma-bottom-tab li i {
  color: var(--icon-color);
  padding: 0 2px;
  display: inline-block;
  vertical-align: top;
}

.ma-bottom-tab li:hover {
  background: var(--hover-background);
}

.ma-bottom-tab li.selected {
  background: var(--selected-background);
}

.ma-resizer-y {
  position: absolute;
  width: 100%;
  height: 10px;
  margin-top: -5px;
  background: none;
  cursor: n-resize;
}

.ma-nav {
  border-bottom: 1px solid var(--tab-bar-border-color);
  height: 24px;
}

.ma-nav li {
  display: inline-block;
  height: 24px;
  line-height: 24px;
  padding: 0 10px;
  cursor: pointer;
}

.ma-nav li.selected {
  background: var(--selected-background);
}

.ma-nav li:hover:not(.selected) {
  background: var(--hover-background);
}

.ma-layout {
  display: flex;
  flex: auto;
  flex-direction: row;
  height: 100%;
}

.ma-layout .ma-layout-container {
  flex: auto;
  height: 100%;
  width: 100%;
}

.ma-layout .ma-header > * {
  padding: 0 2px;
  border-right: none !important;
}

.ma-layout .ma-table-row > * {
  display: inline-block;
  width: 60%;
  height: 23px;
  line-height: 23px;
  border-bottom: 1px solid var(--input-border-color);
  border-right: 1px solid var(--input-border-color);
  background: var(--background);
}

.ma-layout .ma-table-row input:focus {
  border-color: var(--input-border-foucs-color);
}

.ma-layout .ma-table-row input {
  border-color: transparent;
  border-top-color: transparent;
  border-right-color: transparent;
  border-bottom-color: transparent;
  border-left-color: transparent;
}

.ma-layout .ma-table-row > *:first-child,
.ma-layout .ma-table-row > *:last-child {
  width: 20%;
}

.ma-layout .ma-content {
  flex: auto;
  overflow-x: hidden;
  height: calc(100% - 50px);
}

.ma-layout .ma-sider {
  border-right: 1px solid var(--tab-bar-border-color);
}

.ma-layout .ma-sider > * {
  width: 18px;
  height: 18px;
  line-height: 18px;
  margin: 3px;
  text-align: center;
  padding: 0;
  color: var(--icon-color);
  border-radius: 2px;
}

.ma-layout .ma-sider > *:hover {
  background: var(--hover-background);
}

.ma-nav-tab li {
  display: inline-block;
  height: 24px;
  line-height: 24px;
  padding: 0 10px;
  cursor: pointer;
}

.ma-nav-tab li.selected {
  background: var(--selected-background);
}

.ma-nav-tab li:hover {
  background: var(--hover-background);
}

</style>
