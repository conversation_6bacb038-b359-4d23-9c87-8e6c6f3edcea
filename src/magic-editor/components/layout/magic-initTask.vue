<template>
  <div class="ma-settings">
    <div class="ma-layout">
      <div class="ma-layout-container">
        <div class="ma-header ma-table-row">
          <div>名称</div>
          <div>启用</div>
        </div>
        <div class="ma-content">
          <div class="ma-table-row" style="display:flex">
            <div>
              <magic-input :value.sync="parameter.initName" style="width: 100%"/>
            </div>
            <div>
              <magic-checkbox :value.sync="parameter.isEnable"/>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import store from '@/magic-editor/scripts/store.js'
import MagicInput from '@/magic-editor/components/common/magic-input.vue'
import MagicCheckbox from '@/magic-editor/components/common/magic-checkbox.vue'
import bus from '@/magic-editor/scripts/bus.js'

export default {
  name: 'MagicInitTask',
  props: {
    message: String
  },
  components: {
    MagicInput,
    MagicCheckbox
  },
  data() {
    let parameter = JSON.parse(store.get('initTask-parameter'))
    return {
      parameter,
      show: false,
      isOptionChange: false,
      oldEnable: null
    }
  },
  mounted() {
    bus.$on('initTaskOptionChange', (info) => {
      this.parameter = info
      this.isOptionChange = true
      console.log(this.parameter)
      this.currentInfo = info;
    })
    this.oldEnable = this.parameter.isEnable
  },
  methods: {
    save() {
      bus.$emit('initTaskChange', this.parameter.initId, this.parameter.isEnable !== this.oldEnable)
      store.set('initTask-parameter', this.parameter)
    }
  },
  watch: {
    parameter: {
      deep: true,
      handler() {
        if (!this.isOptionChange) {
          this.save()
        } else {
          this.isOptionChange = false
        }

      }
    }
  }
}
</script>

<style scoped>
.ma-settings {
  background: var(--background);
  height: 100%;
  width: 100%;
  position: relative;
  outline: 0;
}

.ma-settings .ma-layout {
  height: calc(100% - 25px);
}
</style>
