<template>
  <div class="ma-todo d-flex flex-column pd-10">
    <el-table v-loading="showLoading" class="flex-1 lt-table" :data="tableData" style="width: 100%">
      <el-table-column label="执行日志">
        <template slot-scope="scope">
          <el-link type="primary" @click="
              dialogVisible = true;
              dialogMsg = scope.row.logMsg;
            ">查看</el-link>
        </template>
      </el-table-column>
      <el-table-column
        prop="lastExecState"
        label="执行结果"
        width="150"
      >
        <template slot-scope="scope">
          <el-tag v-if="scope.row.lastExecState == null" type="info">无状态</el-tag>
          <el-tag v-else-if="scope.row.lastExecState" type="success">执行成功</el-tag>
          <el-tag v-else type="danger">执行失败</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="execTime" label="执行时间" width="280" />
    </el-table>
    <lt-pagination :total="pageData.total" :page.sync="pageData.pageNumber" :limit.sync="pageData.pageSize" :pageSizes="[10, 20, 30, 50]" @pagination="getData()" />
    <el-dialog title="执行日志" :visible.sync="dialogVisible" width="50%">
      <pre>{{ dialogMsg }}</pre>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="dialogVisible = false">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import MagicTextIcon from '@/magic-editor/components/common/magic-text-icon';
import { executeTaskLog } from '@/api/interfaces/interfaces';
import bus from "@/magic-editor/scripts/bus";
export default {
  name: 'MagicTaskLog',
  components: { MagicTextIcon },
  props: {
    info: Object,
  },
  data() {
    return {
      todoList: [],
      // 是否展示loading
      showLoading: false,
      tableData: [],
      dialogVisible: false,
      dialogMsg: '',
      pageData: {
        total: 0,
        pageSize: 20,
        pageNumber: 1,
      },
    };
  },
  mounted() {

    bus.$on('opened', (info) => {
      // 解决修改了接口不更新的问题
      this.info = info
      this.getData()
    })
    this.getData();
  },
  methods: {
    getData() {
      this.showLoading = true
      if (this.info.taskId) {
        executeTaskLog({
          id: this.info.taskId,
          pageSize: this.pageData.pageSize,
          pageNum: this.pageData.pageNumber,
        }).then((data) => {
          this.pageData.total = data.data.total;
          this.pageData.pageSize = data.data.size;
          this.pageData.pageNumber = data.data.current;
          this.tableData = data.data.records;

          this.showLoading = false
        });
      }
    },
  },
};
</script>

<style scoped>
.ma-todo {
  background: var(--background);
  height: 100%;
  width: 100%;
  position: relative;
  outline: 0;
}

.ma-todo .ma-layout {
  height: 100%;
}

.ma-todo .ma-layout .ma-content .content-bg {
  cursor: pointer;
}

.ma-todo .ma-layout .ma-content .content-bg span {
  color: var(--toolbox-list-span-color);
}

.ma-todo .ma-layout .ma-content .content-bg:nth-child(even) {
  background: var(--table-even-background);
}
.ma-todo .ma-layout .ma-content .content-bg .todo-item {
  font-style: italic;
  color: var(--todo-color);
}
.ma-todo .ma-layout .ma-content .content-bg:hover {
  background: var(--toolbox-list-hover-background);
}

.ma-layout .ma-table-row > * {
  width: 30% !important;
  background: none;
}

.ma-layout .ma-table-row > *:last-child {
  width: 70% !important;
}

.ma-todo .loading i {
  color: var(--color);
  font-size: 20px;
}
.ma-todo .loading .icon {
  width: 20px;
  margin: 0 auto;
  animation: rotate 1s linear infinite;
}
.ma-todo .loading {
  color: var(--color);
  position: absolute;
  text-align: center;
  width: 100%;
  top: 50%;
  margin-top: -20px;
}
.ma-todo .no-data {
  color: var(--color);
  position: absolute;
  top: 50%;
  left: 50%;
  margin-left: -20px;
}
</style>
