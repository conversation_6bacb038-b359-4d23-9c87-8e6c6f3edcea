<template>
  <div class="ma-settings">
    <div class="ma-layout">
      <div class="ma-layout-container">
        <div class="ma-header ma-table-row">
          <div>运行周期</div>
          <div>名称</div>
          <div>启用</div>
        </div>
        <div class="ma-content">
          <div class="ma-table-row" style="display:flex">
            <div class="focus">
              <magic-input :value.sync="parameter.cron" @click.native="show=true" style="width: 100%" />
            </div>
            <div>
              <magic-input :value.sync="parameter.taskName" style="width: 100%" />
            </div>
            <div>
              <magic-checkbox :value.sync="parameter.isEnable" />
            </div>
          </div>
        </div>
        <el-dialog :visible="show" :show-close="false">
          <div style="height:650px;overflow:auto">
            <Corntab @hide="show=false" @fill="fill" :expression="parameter.cron">
            </Corntab>
          </div>

        </el-dialog>
      </div>
    </div>
  </div>
</template>

<script>
import store from '@/magic-editor/scripts/store.js'
import MagicInput from '@/magic-editor/components/common/magic-input.vue'
import MagicCheckbox from '@/magic-editor/components/common/magic-checkbox.vue'
import bus from "@/magic-editor/scripts/bus";
import Corntab from '@/components/Crontab'
export default {
  name: 'MagicTimer',
  props: {
    message: String
  },
  components: {
    MagicInput,
    MagicCheckbox,
    Corntab
  },
  data() {
    //let parameter = JSON.parse(store.get('timer-parameter'))
    return {
      parameter: {},
      show: false
    }
  },
  mounted() {
    bus.$on('opened', (info) => {
      // 解决修改了接口不更新的问题
      this.parameter = info
    })
  },
  methods: {
    save() {
      store.set('timer-parameter', this.parameter)
      bus.$emit('timerChange', this.parameter.taskId, true)

    },
    fill(val) {
      this.parameter.cron = val
    }
  },
  watch: {
    parameter: {
      deep: true,
      handler() {
        this.save()
      }
    }
  }
}
</script>

<style scoped>
.ma-settings {
  background: var(--background);
  height: 100%;
  width: 100%;
  position: relative;
  outline: 0;
}

.ma-settings .ma-layout {
  height: calc(100% - 25px);
}

::v-deep .popup-result .title {
  top: -15px !important;
}
</style>
