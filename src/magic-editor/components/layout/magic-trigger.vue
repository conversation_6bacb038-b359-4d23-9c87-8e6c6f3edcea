<template>
  <div class="ma-settings">
    <el-form ref="elForm" :model="parameter" :rules="triggerRules" size="medium" label-width="100px">
      <el-row>
        <el-col :span="8">
          <el-form-item label="触发器名称" prop="triggerName">
            <el-input v-model="parameter.triggerName" placeholder="请输入触发器名称" clearable :style="{ width: '100%' }">
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="触发器类型" prop="type">
            <el-select v-model="parameter.type" placeholder="请选择触发器类型" style="width: 100%;" @change="typeChange" >
              <el-option v-for="(item) in $enum.triggerTypeList" :label="item.label" :value="item.value" :key="item.value" ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="数据模型" prop="tableName" v-if="['SYS_TABLE_INSERT','SYS_TABLE_UPDATE'].includes(parameter.type)" >
            <el-select v-model="parameter.tableName" placeholder="请选择数据模型" style="width: 100%;">
              <el-option v-for="(item) in sortForm" :label="item.templateName" :value="item.tableName" :key="item.tableName" ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="业务模型" prop="tableName" v-if="['FLOWS_SUBMIT', 'FLOWS_END'].includes(parameter.type)" >
            <el-select v-model="parameter.tableName" placeholder="请选择业务模型" style="width: 100%;">
              <el-option v-for="(item) in sortFlows" :label="item.processElement.name" :value="item.tableName" :key="item.tableName" ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="自定义名称" prop="tableName" v-if="['CUSTOM_TYPE'].includes(parameter.type)" >
            <el-input v-model="parameter.tableName" placeholder="请输入内容"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
import store from '@/magic-editor/scripts/store.js'
import bus from "@/magic-editor/scripts/bus";

export default {
  name: 'MagicTrigger',
  data() {
    return {
      parameter: {},
      triggerRules: {
        triggerName: [
          { required: true, message: "请输入触发器名称", trigger: "blur" },
        ],
        type: [
          { required: true, message: "请输入触发器类型", trigger: "change" },
        ]
      },
    }
  },
  computed: {
    setup() {
      return this.$store.state.design
    },
    sortForm() {
      let forms = this.setup.forms || []
      return forms.filter(item => item.templateType === 4)
    },
    sortFlows(){
      let flows = this.setup.flows || []
      return flows
    }
  },
  mounted() {
    bus.$on('opened', (info) => {
      // 解决修改了接口不更新的问题
      this.parameter = info
    })
  },
  watch: {
    parameter: {
      deep: true,
      handler() {
        this.save()
      }
    }
  },
  methods: {
    save() {
      // store.set('timer-parameter', this.parameter)
      // bus.$emit('timerChange', this.parameter.taskId, true)
    },
    typeChange(e) {
      if(!['SYS_TABLE_INSERT','SYS_TABLE_UPDATE'].includes(e)) {
        this.formData.tableName = undefined
      }
    },
  },
}
</script>

<style scoped>
.ma-settings {
  background: var(--background);
  height: 100%;
  width: 100%;
  position: relative;
  outline: 0;
}

.ma-settings .ma-layout {
  height: calc(100% - 25px);
}

::v-deep .popup-result .title {
  top: -15px !important;
}
</style>
