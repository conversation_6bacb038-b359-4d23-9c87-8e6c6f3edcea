<template>
  <magic-dialog v-show="true" :moveable="false" :shade="true" :showClose="false" title="登录">
    <template #content>
      <label>用户名：</label>
      <magic-input :onEnter="doLogin" :value.sync="username"/>
      <div style="height: 2px"/>
      <label>密码：</label>
      <magic-input :onEnter="doLogin" :value.sync="password" type="password"/>
    </template>
    <template #buttons>
      <button class="ma-button active" @click="doLogin">登录</button>
    </template>
  </magic-dialog>
</template>

<script>
import MagicInput from '@/magic-editor/components/common/magic-input'
import MagicDialog from '@/magic-editor/components/common/modal/magic-dialog'
import request from '@/magic-editor/api/request.js'
import contants from '@/magic-editor/scripts/contants.js'
import store from '@/magic-editor/scripts/store.js'
import bus from "@/magic-editor/scripts/bus.js";

export default {
  name: 'MagicLogin',
  props: {
    onLogin: Function,
  },
  components: {
    MagicInput,
    MagicDialog,
  },
  data() {
    return {
      username: '',
      password: '',
    }
  },
  methods: {
    doLogin() {
      request.send('/login', {
        username: this.username,
        password: this.password
      }).success((res, response) => {
        if (res) {
          bus.$emit('status', '登录成功')
          contants.HEADER_MAGIC_TOKEN_VALUE = response.headers[contants.HEADER_MAGIC_TOKEN];
          store.set(contants.HEADER_MAGIC_TOKEN, contants.HEADER_MAGIC_TOKEN_VALUE);
          this.onLogin();
        } else {
          bus.$emit('status', '登录失败')
          this.$magicAlert({
            title: '登录',
            content: '登录失败,用户名或密码不正确'
          })
        }
      })
    },
  },
}
</script>
<style scoped>
label {
  width: 80px;
  text-align: right;
  display: inline-block;
}
</style>