<template>
  <svg xmlns="http://www.w3.org/2000/svg" version="1.1" width="40" height="12" font-size="10" font-weight="bold">
    <text x="18" y="8" :fill="color" style="dominant-baseline: middle;text-anchor:middle;" >{{ text }}</text>
  </svg>
</template>
<script>
export default {
  name: 'MagicTextIcon',
  props: {
    value: String
  },
  data(){
    return {
      colors: {
        GET: '#249C47',
        POST: '#FFB400',
        DELETE: ['DEL', '#EB2013'],
        PUT: '#097BED',
        F: ['BUT','#097BED'],
        C: ['MENU', '#FFB400'],
        S: ['SYSTEM', '#097BED'],
        M: ['DIRE', '#9012FE'],
        D: ['MODEL', '#9012FE'],
        E: ['REPORT', '#9012FE'],
        FV: ['VIEW', '#9012FE'],
        T: ['CODE', '#9012FE'],
        FORM: ['FORM', '#097BED'],
        SCREEN: ['Screen', '#097BED'],
        REPORT: ['Report', '#097BED'],
        'function': ['Fn', '#9012FE'],
        'timer': ['Timer', '#9012FE'],
        'assembly': ['Ass', '#c1651a'],
        'listen': ['Listen', '#40D6FF'],
        'flow': ['Flow', '#40D6FF'],
      }
    }
  },
  computed: {
    text: function() {
      let color = this.colors[this.value]
      let text = this.value
      if(color !== undefined && Array.isArray(color)) {
        text = color[0]
      }
      return text
    },
    color: function() {
      let color = this.colors[this.value]
      if(color !== undefined && Array.isArray(color)) {
        return color[1]
      }
      return color || 'var(--icon-color)'
    }
  }
}
</script>
<style scoped>

</style>
