<template>
  <div class="ma-tree-wrapper">
    <div class="ma-tree-toolbar">
      <div class="ma-tree-toolbar-search">
        <i class="ma-icon ma-icon-search"></i
        ><input
          placeholder="输入关键字搜索"
          @input="(e) => doSearch(e.target.value)"
        />
      </div>
      <div>
        <div class="ma-tree-toolbar-btn" title="刷新视图" @click="initData()">
          <i class="ma-icon ma-icon-refresh"></i>
        </div>
      </div>
    </div>
    <magic-tree :data="tree" v-if="tree.length" :forceUpdate="forceUpdate" :loading="showLoading">
      <template #folder="{ item }">
        <div
          v-if="item._searchShow !== false"
          :id="'magic-flow-list-' + item.objId"
          :class="{ 'ma-tree-select': item.selectRightItem }"
          :title="item.objName"
          class="ma-tree-item-header ma-tree-hover"
          @dblclick="$set(item, 'opened', !item.opened)"
          @contextmenu.prevent="(e) => folderRightClickHandle(e, item)"
        >
          <i
            :class="
              item.opened ? 'ma-icon-arrow-bottom' : 'ma-icon-arrow-right'
            "
            class="ma-icon"
            @click="$set(item, 'opened', !item.opened)"
          />
          <i class="ma-icon ma-icon-list"></i>
          <label>{{ item.objName }}</label>
        </div>
      </template>
      <template #file="{ item }">
        <div
          v-if="item._searchShow !== false"
          :class="{
            'ma-tree-select':
              item.selectRightItem || item.objId === currentFileItem.objId,
          }"
          :id="'magic-flow-list-' + item.objId"
          :style="{ 'padding-left':  '17px' }"
          class="ma-tree-hover"
          :title="item.objName"
          @click="open(item)"
          @contextmenu.prevent="(e) => fileRightClickHandle(e, item)"
        >
          <magic-text-icon
            :value="'flow'"
          />
          <label>{{ item.objName }}</label>
        </div>
      </template>
    </magic-tree>

    <magic-dialog
      v-model="visible"
      :title="
        createflowObj.objId
          ? '修改流程:' + createflowObj.objName
          : '创建流程'
      "
      align="right"
    >
      <template #content>
        <label>流程名称：</label>
        <magic-input
          placeholder="请输入流程名称"
          v-model="createflowObj.objName"
        />
        <div style="height: 5px"></div>
        <label>是否可申请：</label>
        <div style="display:inline-block">
          <magic-checkbox
          :value.sync="createflowObj.hasApply"
        />
        </div>

         <div style="height: 5px"></div>
         <div style="display:flex;align-items:center">
              <label>业务类型：</label>
        <magic-select
          style="width:150px"
          placeholder="请选择业务类型"
          :options="options"
          :value.sync="createflowObj.akey"
        />
         </div>

        <div style="height: 5px"></div>
        <label>流程描述：</label>
        <magic-textarea
          placeholder="请输入流程描述"
          :value.sync="createflowObj.objDesc"
          style="width: 100%; height: 100%; margin: 2px"
        />

      </template>
      <template #buttons>
        <button class="ma-button active" @click="createflow">
          {{ createflowObj.objId ? "修改" : "创建" }}
        </button>
        <button class="ma-button" @click="visible = false">
          取消
        </button>
      </template>
    </magic-dialog>
  </div>
</template>

<script>
import bus from "../../scripts/bus.js";
import MagicTree from "../common/magic-tree.vue";
import MagicDialog from "@/magic-editor/components/common/modal/magic-dialog.vue";
import MagicSelect from "@/magic-editor/components/common/magic-select.vue";
import MagicTextarea from "@/magic-editor/components/common/magic-textarea.vue";
import MagicInput from "@/magic-editor/components/common/magic-input.vue";
import MagicGroupChoose from "@/magic-editor/components/resources/magic-group-choose.vue";
import {
  replaceURL,
  download as downloadFile,
  requestGroup,
  goToAnchor,
  deepClone,
} from "@/magic-editor/scripts/utils.js";
import MagicTextIcon from "@/magic-editor/components/common/magic-text-icon";
import { saveProcess, delProcessModel } from "@/api/columnEditApi/columnEditApi";
import {
  getProcessList,
  getBusList,
  mobilePush,
  busByFunctional,
} from "@/api/activity/activity.js";
export default {
  name: "MagicFlowList",
  props: {
    apis: Array,
  },
  components: {
    MagicTextIcon,
    MagicTree,
    MagicDialog,
    MagicTextarea,
    MagicInput,
    MagicSelect,
    MagicGroupChoose
  },
  data() {
    return {
      options: [
      ],
      bus: bus,
      // 分组list数据
      nodeItem: {
        id: null,
        nodeName: "",
        parentId: null,
      },
      show: false,
      // 接口list数据
      // 分组+接口tree数据
      tree: [],
      forceUpdate: true,
      // 是否展示tree-loading
      showLoading: true,
      dragging: false,
      draggableItem: {},
      draggableTargetItem: {},
      // 缓存一个openId
      tmpOpenId: [],
      currentFileItem:{},
      createflowObj: {
        objId: null,
        objDecs: "",
        objName: "",
        hasApply:false,
        akey:null,
      },
      visible: false,
    };
  },
  methods: {
    // 新增一个流程
    createflow() {
      this.visible = false;
      saveProcess(this.createflowObj).then((e) => {
        this.initData();
      });
    },
    addProcess(key){
      busByFunctional({busId:key}).then(res=>{
        this.options=res.data.map(item=>({
          text:item.functionalName,
          value:item.functionalId
        }))
      })
      this.createflowObj= {
        objId: null,
        objDecs: "",
        objName: "",
        hasApply:false,
        akey:null,
      }
      this.visible= true
    },
    editProcess(obj){
      busByFunctional({busId:obj.akey}).then(res=>{
        this.options=res.data.map(item=>({
          text:item.functionalName,
          value:item.functionalId
        }))
      })
      this.createflowObj=obj
      this.visible= true
    },


    doSearch(keyword) {
      keyword = keyword.toLowerCase();
      let loopSearch = (row, parentName, parentPath) => {
        if (row.folder && row.children) {
          row.children.forEach((it) => loopSearch(it, row.objName));
          row._searchShow =
            (row.label || "").toLowerCase().indexOf(keyword) > -1 ||
            row.children.some((it) => it._searchShow);
        } else {
          row._searchShow =
            replaceURL(parentName).toLowerCase().indexOf(keyword) > -1 || replaceURL(row.label).toLowerCase().indexOf(keyword) > -1;
        }
      };
      this.tree.forEach((it) => loopSearch(it, "", ""));
    },
    open(item) {
      bus.$emit("status", `查看流程「${item.objName}」详情`);
      bus.$emit("open", item);
    },
    // 初始化数据
    initData() {
      bus.$emit("status", "正在初始化流程列表...");
      this.showLoading = true;
      this.tree = [];
      return new Promise((resolve) => {
        getBusList().then((res) => {
          let arr = res.data.map(item=>({
              objId:item.busId,
              objName:item.busName,
              folder :true,
              opened:false,
              temps:{
                id:item.busId,
              },
              children:[]
          }));

          arr.forEach(async(item)=>{
             let e=await getProcessList({busId:item.objId})
             e.data.forEach(item2=>{
                 item2.folder = false;
                 item2.opened = false;
                 item2.temps = {
                    id: item2.objId,
                    name: item2.objName,
                    type: "flow",
                    editFlag: false,
                  };
             })
             item.children=e.data
          })
          console.log(arr)
          this.tree = arr;
          bus.$emit("status", "流程加载完毕");
          this.showLoading = false;
          resolve();
        });
      });
    },
    folderRightClickHandle(event, item) {
      console.log(item)
      this.$set(item, "selectRightItem", true);
      this.$magicContextmenu({
        menus: [
          {
            label: "新增流程",
            icon: "ma-icon-plus",
            divided: true,
            onClick: () => {
              this.addProcess(item.objId);
            },
          },
        ],
        event,
        zIndex: 9999,
        destroy: () => {
          this.$set(item, "selectRightItem", false);
        },
      });
      return false;
    },
    // 文件右键菜单
    fileRightClickHandle(event, item) {
      this.$set(item, "selectRightItem", true);
      this.$magicContextmenu({
        menus: [
          {
            label: "编辑流程",
            icon: "ma-icon-refresh",
            divided: true,
            onClick: () => {
              this.editProcess(item)
            },
          },
          {
            label: "发布视图",
            icon: "ma-icon-push",
            divided: true,
            onClick: () => {
              mobilePush(item.objId).then((e) => {
                this.$message.success(e.data ? `发布成功` : `发布失败`);
              });
            },
          },
          {
            label: "删除视图",
            icon: "ma-icon-delete",
            divided: true,
            onClick: () => {
              this.deleteForm(item);
            },
          },
        ],
        event,
        zIndex: 9999,
        destroy: () => {
          this.$set(item, "selectRightItem", false);
        },
      });
      return false;
    },
    deleteForm(item) {
      bus.$emit("status", `准备删除流程「${item.objName}」`);
      this.$magicConfirm({
        title: "删除流程",
        content: `是否要删除流程「${item.objName}」`,
        onOk: () => {
          delProcessModel(item.objId).then((res) => {
            bus.$emit("status", `流程「${item.objName}」已删除`);
            this.initData();
          });
        },
      });
    },

  },
  mounted() {
    this.bus.$on("logout", () => (this.tree = []));
    this.bus.$on("opened", (item) => {
      this.currentFileItem = item;
    });
    this.bus.$on("refresh-resource", () => {
      this.initData();
    });
  },
};
</script>

<style>
@import "./magic-resource.css";

.loading {
  color: var(--color);
  position: absolute;
  text-align: center;
  width: 100%;
  top: 50%;
  margin-top: -20px;
}
.loading i {
  color: var(--color);
  font-size: 20px;
}
.loading .icon .ma-icon {
  padding: 0;
}
.loading .icon {
  width: 20px;
  margin: 0 auto;
  line-height: normal;
  animation: rotate 1s linear infinite;
}
.no-data {
  color: var(--color);
  position: absolute;
  top: 50%;
  left: 50%;
  margin-left: -20px;
}
</style>
<style scoped>
.redDots {
  /* width: 6px; */
  /* height: 6px; */
  border-radius: 50%;
  /* background-color: red; */
  color: red;
  position: absolute;
  right: -10px;
  top: 0;
}
</style>
