<template>
  <magic-dialog
    width="450px"
    height="350px"
    v-model="showPlcDialog"
    :title="
      plcSourceObj.id ? '修改 PLC连接 :' + plcSourceObj.dbName : '创建 PLC连接'
    "
    align="right"
    @onClose="showPlcDialog = false"
  >
    <template #content>
      <div class="ds-form">
        <label>名称</label>
        <magic-input
          :value.sync="plcSourceObj.dbName"
          placeholder="数据源名称，仅做展示使用"
        />
      </div>
      <div class="ds-form">
        <label>Key</label>
        <magic-input
          :value.sync="plcSourceObj.dbKey"
          placeholder="数据库key，后续代码中使用"
        />
      </div>
      <div class="ds-form">
        <label>协议</label>
        <magic-select
          :border="true"
          :value.sync="plcSourceObj.protocolType"
          :options="$parent.setParam"
          :placeholder="'协议类型'"
        />
      </div>
      <div class="ds-form">
        <label>地址</label>
        <magic-input :value.sync="plcSourceObj.address" />
      </div>
      <div class="ds-form">
        <label>其它配置</label>
        <div
          ref="editor"
          class="ma-editor"
          style="width: 100%; height: 150px"
        />
      </div>
    </template>
    <template #buttons>
      <button class="ma-button active" @click="doSave">
        {{ plcSourceObj.id ? '修改' : '创建' }}
      </button>
      <button class="ma-button" @click="doTest">测试连接</button>
      <button class="ma-button" @click="showPlcDialog = false">取消</button>
    </template>
  </magic-dialog>
</template>

<script>
import MagicDialog from '@/magic-editor/components/common/modal/magic-dialog.vue';
import MagicInput from '@/magic-editor/components/common/magic-input.vue';
import MagicSelect from '@/magic-editor/components/common/magic-select';
import * as monaco from 'monaco-editor/esm/vs/editor/editor.api';
import contants from '@/magic-editor/scripts/contants.js';
import store from '@/magic-editor/scripts/store';
import {
  updateMagicPlc,
  checkConnection,
} from '@/api/interfaces/interfaces.js';
export default {
  components: {
    MagicDialog,
    MagicInput,
    MagicSelect,
  },
  data() {
    return {
      showPlcDialog: false,
      plcSourceObj: {
        address: '',
        config: '',
        dbKey: '',
        dbName: '',
        id: 0,
        protocolType: '',
      },
      editor: null,
    };
  },
  methods: {
    open(data = '') {
      if (data) {
        this.plcSourceObj = JSON.parse(JSON.stringify(data));
      } else {
        this.plcSourceObj = {
          address: '',
          config: '',
          dbKey: '',
          id: 0,
          protocolType: '',
        };
      }
      this.showPlcDialog = true;
      this.$nextTick(() => {
        this.editor = monaco.editor.create(this.$refs.editor, {
          minimap: {
            enabled: false,
          },
          language: 'json',
          fixedOverflowWidgets: true,
          folding: true,
          wordWrap: 'on',
          fontFamily: contants.EDITOR_FONT_FAMILY,
          fontSize: contants.EDITOR_FONT_SIZE,
          fontLigatures: true,
          renderWhitespace: 'none',
          theme: store.get('skin') || 'default',
          value: '{\r\n\t\r\n}',
        });
      });
    },
    doSave() {
      if (!this.plcSourceObj.dbName) {
        this.$magicAlert({ content: '数据源名称不能为空' });
        return;
      }
      if (!this.plcSourceObj.dbKey) {
        this.$magicAlert({ content: '数据库key不能为空' });
        return;
      }
      if (!this.plcSourceObj.protocolType) {
        this.$magicAlert({ content: '协议不能为空' });
        return;
      }
      if (!this.plcSourceObj.address) {
        this.$magicAlert({ content: '地址不能为空' });
        return;
      }
      this.plcSourceObj.config = this.editor.getValue();

      updateMagicPlc(this.plcSourceObj).then(() => {
        this.showPlcDialog = false;
        this.$emit('save');
      });
    },
    doTest() {
      checkConnection(this.plcSourceObj).then(() => {
        this.$magicAlert({
          content: '连接成功',
        });
      });
    },
  },
};
</script>
<style scoped lang="scss">
.ds-form {
  margin-bottom: 5px;
  display: flex;
}
.ds-form label {
  margin-right: 5px;
  display: inline-block;
  width: 60px;
  text-align: right;
  height: 22px;
  line-height: 22px;
}
.ds-form > div {
  flex: 1;
}
</style>
