<template>
  <div class="ma-tree-wrapper">
    <div class="ma-tree-toolbar">
      <div class="ma-tree-toolbar-search"><i class="ma-icon ma-icon-search"></i><input placeholder="输入关键字搜索"
                                                                                       @input="e => doSearch(e.target.value)"/>
      </div>
      <div>
        <div class="ma-tree-toolbar-btn" title="新建路由" @click="addMenu">
          <i class="ma-icon ma-icon-group-add"></i>
        </div>
        <div class="ma-tree-toolbar-btn" title="刷新" @click="initData()">
          <i class="ma-icon ma-icon-refresh"></i>
        </div>
        <div class="ma-tree-toolbar-btn" title="折叠" @click="rebuildTree(true)">
          <i class="ma-icon ma-icon-folding"></i>
        </div>

      </div>
    </div>
    <magic-tree :data="tree" v-if="tree.length" :forceUpdate="forceUpdate" :loading="showLoading">
      <template #folder="{ item }">
        <div
            :id="'magic-menu-list-' + item.menuId"
            :class="{ 'ma-tree-select': item.selectRightItem }"
            :draggable="true"
            :style="{ 'padding-left': 17 * item.lavel + 'px' }"
            :title="(item.label || '') + '(' + (item.path || '') + ')'"
            class="ma-tree-item-header ma-tree-hover"
            :dragtarget="dragging && draggableTargetItem === item"
            @dblclick="$set(item, 'opened', !item.opened)"
            @dragenter="e => draggable(item, e, 'dragenter')"
            @contextmenu.prevent="e => folderRightClickHandle(e, item)"
            @dragstart.stop="e => draggable(item, e, 'dragstart')"
            @dragend.stop="e => draggable(item, e, 'dragend')"
            @dragover.prevent
        >
          <i :class="item.opened ? 'ma-icon-arrow-bottom' : 'ma-icon-arrow-right'" class="ma-icon" @click="$set(item, 'opened', !item.opened)"/>
          <magic-text-icon :value="item.menuType"/>
          <label>{{ item.menuName }}</label>
          <span>({{ item.path }})</span>
        </div>
      </template>
      <template #file="{ item }">
        <div
            :class="{ 'ma-tree-select': item.selectRightItem || item.menuId === currentFileItem.menuId }"
            :draggable="true"
            :id="'magic-menu-list-' + item.menuId"
            :style="{ 'padding-left': 17 * item.lavel + 'px' }"
            class="ma-tree-hover"
            :title="item.menuName + ':' + (item.menuName || '') + '(' + (item.path || '') + ')'"
            @click="open(item)"
            @dragenter="e => draggable(item, e, 'dragenter')"
            @contextmenu.prevent="(e) => fileRightClickHandle(e, item)"
            @dragstart.stop="e => draggable(item, e, 'dragstart')"
            @dragend.stop="e => draggable(item, e, 'dragend')"
            @dragover.prevent
        >
          <magic-text-icon :value="item.menuType"/>
          <label>{{ item.menuName }}</label>
          <span>({{ item.path }})</span>
          <i class="ma-icon ma-icon-lock" v-if="item.lock === '1'"></i>
        </div>
      </template>
    </magic-tree>
    <magic-dialog v-model="visible" :title="form.id ? '修改路由' : '添加路由'"
                  align="right">
      <template #content>
          <div style="padding: 5px">
    <div class="row">
      <label>上级菜单</label>
      <treeselect
        style="flex: 1"
        v-model="form.parentId"
        :options="menuOptions"
        :normalizer="normalizer"
        :show-count="true"
        placeholder="选择上级菜单"
      />
      <label>菜单类型</label>
      <magic-select
        :options="[
          { value: 'M', text: '目录' },
          { value: 'C', text: '菜单' },
          { value: 'F', text: '按钮' },
        ]"
        :value.sync="form.menuType"
        style="flex: 1"
      />
    </div>
    <div class="row" v-if="form.menuType == 'C'">
      <label>视图类型</label>
      <magic-select
        :options="[
          { value: 'A', text: '组件' },
          { value: 'F', text: '表单' },
          { value: 'D', text: '数据模型' },
          { value: 'E', text: '报表' },
          { value: 'T', text: '在线组件' },
        ]"
        :value.sync="form.viewType"
        style="flex: 1"
      />
    </div>
    <div class="row" v-if="form.viewType == 'D'">
      <label>数据模型</label>
      <lt-select-page
        style="width: 100%"
        v-model="form.formId"
        api="listTable"
        select-label="tableComment"
        select-value="tableId"
        placeholder="请选择数据模型"
        search-key="tableId"
        mode="old"
        clearable
      />
    </div>
    <div class="row" v-if="form.viewType == 'E'">
      <label>报表</label>
      <magic-select
        :options="
          execlList.map((item) => ({
            value: item.reportUid,
            text: item.reportName,
          }))
        "
        :value.sync="form.formId"
        style="flex: 1"
      />
    </div>
    <div class="row" v-if="form.viewType == 'T'">
      <label>在线组件</label>
      <magic-select
        :options="
          templateList.map((item) => ({ value: item.id, text: item.name }))
        "
        :value.sync="form.formId"
        style="flex: 1"
      />
    </div>
    <div class="row" v-if="form.viewType == 'F'">
      <label>视图表单</label>
      <magic-select
        :options="
          availableFormList.map((item) => ({
            value: item.formId + '',
            text: item.templateName,
          }))
        "
        :value.sync="form.formId"
        style="flex: 1"
      />
      <div
        v-if="form.formId"
        class="d-flex a-center j-end pointer"
        style="width: 40px; color: var(--primary-color)"
        @click="
          $refs.formPreviewDialogRef.open(
            availableFormList.filter(
              (item) => item.formId + '' === form.formId + ''
            )[0].templateJson
          )
        "
      >
        预览
      </div>
    </div>
    <template v-if="form.menuType != 'F'">
      <div class="row">
        <label>名称待定</label>
        <magic-select
          :options="[
            { value: '1', text: '图标' },
            { value: '2', text: '图片' },
          ]"
          :value.sync="form.iconType"
          style="flex: 1"
        />
        <template v-if="form.iconType == '2'">
          <label>菜单图标</label>
          <el-upload
            action=""
            :show-file-list="false"
            :multiple="false"
            :auto-upload="false"
            list-type="picture-card"
            :on-change="changeFile"
            style="flex: 1"
          >
            <img
              v-if="imageFile"
              :src="imageFile.url"
              style="width: 100%; height: 100%"
            />
            <i v-else class="el-icon-plus"></i>
          </el-upload>
        </template>
        <template v-else>
          <label>菜单图标</label>
          <el-popover
            placement="bottom-start"
            width="460"
            trigger="click"
            @show="$refs['iconSelect'].reset()"
            style="flex: 1"
          >
            <IconSelect ref="iconSelect" @selected="selected" />
            <magic-input
              slot="reference"
              :value.sync="form.icon"
              placeholder="点击选择图标"
              readonly
              style="width:100%"
            >
            </magic-input>
          </el-popover>
        </template>
      </div>
    </template>
    <div class="row">
      <label>菜单名称</label>
      <magic-input :value.sync="form.menuName" style="flex: 1" />
      <label>显示排序</label>
      <magic-input type="number" :value.sync="form.orderNum" style="flex: 1" />
    </div>
    <div class="row" v-if="form.menuType != 'F' && form.viewType != 'F'">
      <label>是否外链</label>
      <magic-select
        :options="[
          { value: '0', text: '是' },
          { value: '1', text: '否' },
        ]"
        :value.sync="form.isFrame"
        style="flex: 1"
      />
    </div>

    <div class="row" v-if="form.menuType != 'F'">
      <label>路由地址</label>
      <magic-input :value.sync="form.path" style="flex: 1" />
      <label>组件路径</label>
      <magic-input :value.sync="form.component" style="flex: 1" />
    </div>
    <div class="row" v-if="form.menuType != 'M'">
      <label>权限字符</label>
      <magic-input :value.sync="form.perms" style="flex: 1" />
    </div>
    <div class="row" v-if="form.menuType != 'F'">
      <label>显示状态</label>
      <magic-select
        :options="
          visibleOptions.map((item) => ({
            value: item.dictValue,
            text: item.dictLabel,
          }))
        "
        :value.sync="form.visible"
        style="flex: 1"
      />
      <label>菜单状态</label>
      <magic-select
        :options="
          statusOptions.map((item) => ({
            value: item.dictValue,
            text: item.dictLabel,
          }))
        "
        :value.sync="form.visible"
        style="flex: 1"
      />
    </div>
    <div class="row" style="margin-bottom: 20px" v-if="form.menuType == 'C'">
      <label>是否缓存</label>
      <magic-select
        :options="[
          { value: '0', text: '缓存' },
          { value: '1', text: '不缓存' },
        ]"
        :value.sync="form.isCache"
        style="flex: 1"
      />
    </div>

    <formPreviewDialog ref="formPreviewDialogRef" />
  </div>
      </template>
      <template #buttons>
        <button class="ma-button active" @click="submit">{{ form.menuId ? '修改' : '创建' }}</button>
        <button class="ma-button" @click="visible=false">取消</button>
      </template>
    </magic-dialog>

  </div>
</template>

<script>
import bus from '../../scripts/bus.js'
import MagicTree from '../common/magic-tree.vue'
import { listMenu } from "@/api/system/menu";
import { reportList } from "@/api/bigscreen";
import { getIconImage } from "@/utils/index.js";
import { uploadCloudImage } from '@/api/file/file.js'
import Treeselect from "@riophae/vue-treeselect";
import MagicFile from "@/magic-editor/components/common/magic-file.vue";
import IconSelect from "@/components/IconSelect";
import formPreviewDialog from "@/views/tool/variantform/formPreviewDialog.vue";
import { getFormTemplateList } from "@/api/activity/activity";
import { getAssemblyList } from "@/api/interfaces/interfaces";
import MagicDialog from '@/magic-editor/components/common/modal/magic-dialog.vue'
import MagicInput from '@/magic-editor/components/common/magic-input.vue'
import MagicSelect from "@/magic-editor/components/common/magic-select.vue";
import { replaceURL, download as downloadFile,  } from '@/magic-editor/scripts/utils.js'
import MagicTextIcon from "@/magic-editor/components/common/magic-text-icon";
import { updateMenu, addMenu } from '@/api/system/menu'
import { menuTree,delMenu } from "@/api/system/menu.js"
export default {
  name: 'MagicMenuList',
  props: {
    apis: Array
  },
  components: {
    MagicTextIcon,
    MagicTree,
    MagicDialog,
    MagicInput,
    MagicFile,
    MagicSelect,
    Treeselect,
    IconSelect,
    formPreviewDialog,
  },
  data() {
    return {
      bus: bus,
      // 分组+接口tree数据
      tree: [],
      visible:false,
      // 数据排序规则,true:升序,false:降序
      treeSort: true,
      apiCopyGroupChooseVisible: false,
      groupChooseVisible: false,
      srcItem: {},
      srcId: '',
      // 当前打开的文件
      form: {},
      // 绑定给magic-tree组件，用来触发子组件强制更新
      forceUpdate: true,
      // 拖拽的item
      draggableItem: {},
      draggableTargetItem: {},
      // 是否展示tree-loading
      showLoading: true,
      dragging: false,
      execlList: [],
      templateList: [],
      getIconImage,
      imageFile: "",
      availableFormList: [],
      menuOptions: [],
      visibleOptions: [],
      statusOptions: [],
      currentFileItem:{}
    }
  },
  methods: {
    addMenu(id){
      this.form={
        menuId: undefined,
        parentId: id || 0,
        menuName: undefined,
        icon: undefined,
        menuType: "M",
        viewType: "A",
        orderNum: undefined,
        isFrame: "1",
        isCache: "0",
        visible: "0",
        status: "0",
        temps:{
          type:'menu'
        }
      }
      this.visible=true
    },
    editMenu(item){
      this.form=item
      this.visible=true
    },
    doSearch(keyword) {
      keyword = keyword.toLowerCase();
      let loopSearch = (row, parentName, parentPath) => {
        if (row.folder) {
          row.children.forEach(it => loopSearch(it, parentName + '/' + (row.name || ''), parentPath + '/' + (row.path || '')))
          row._searchShow = (row.name || '').toLowerCase().indexOf(keyword) > -1 || row.children.some(it => it._searchShow)
        } else {
          row._searchShow = replaceURL(parentName + '/' + (row.name || '')).toLowerCase().indexOf(keyword) > -1 || replaceURL(parentPath + '/' + (row.path || '')).toLowerCase().indexOf(keyword) > -1
        }
      }
      this.tree.forEach(it => loopSearch(it, '', ''))
      this.changeForceUpdate()
    },
    submit(){
        let data=this.form
        if (data.viewType === 'F') {
          data.component = 'tool/variantform/commonIndex'
        }
        if (data.viewType === 'T') {
          data.component = 'tool/variantform/templateIndex'
        }
        if (data.viewType === 'D') {
          data.component = 'tool/gen/commonIndex'
        }
        if (data.viewType === 'E') {
          data.component = 'tool/excelreport/viewer'
        }
        if (data.menuId) {
          updateMenu(data).then((response) => {
            bus.$emit('status', `路由「${data.menuName}」已保存`)
            this.initData()
          })
        } else {
          addMenu(data).then((response) => {
            bus.$emit('status', `添加路由成功`)
            this.initData()
          })
        }
        this.visible=false
    },
    selected(name) {
      this.$set(this.form,'icon',name)
    },
    open(item) {
        item.temps = {
          type: 'menu',
          id: item.menuId,
          name: item.menuName
        }
        bus.$emit('status', `查看菜单「${item.menuName}(${item.path})」详情`)
        bus.$emit('open', item)
      this.currentFileItem = item
    },
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.menuId,
        label: node.menuName,
        children: node.children,
      };
    },
    // 初始化数据
    initData() {
      bus.$emit('status', '正在初始化接口列表')
      this.showLoading = true
      this.tree = []
      return new Promise((resolve) => {
        menuTree().then(res =>{
          function func(items, parentPath , parentName){
            items.forEach(item => {
              // item.textIcon = this.getMenuType(item.menuType)
              if (item.children){
                item.folder = true
                item.opened = false
                item.type = 'menu'
                item.paths =  parentPath + "/" +  item.path
                item.names =  (parentName + "/" +  item.menuName).slice(1)

                func(item.children, item.paths,item.names)
              } else {
                item.folder = false
                item.opened = false
                item.type = 'menu'
                item.paths =  parentPath + "/" +  item.path
                item.names =  parentName + "/" +  item.menuName

                // item.path = "/" +  item.path
              }
            })
          }
          let data = res.data
          func(data, "" , "")
          this.tree = data
          bus.$emit('status', '接口分组加载完毕')
          this.showLoading = false
          resolve()
        })
      })
    },

    // 重新构建tree的path和name,第一个参数表示是否全部折叠
    rebuildTree(folding) {
      let buildHandle = (arr, parentItem, level) => {
        arr.forEach(element => {
          element.level = level
          // 处理分组
          if (element.folder === true) {
            element.tmpName = (parentItem.tmpName + '/' + element.name).replace(new RegExp('(/)+', 'gm'), '/')
            element.tmpPath = (parentItem.tmpPath + '/' + element.path).replace(new RegExp('(/)+', 'gm'), '/')
            this.$set(element, 'opened', folding !== true)
            if (element.children && element.children.length > 0) {
              buildHandle(element.children, element, level + 1)
            }
          } else {
            // 处理接口
            element.groupName = parentItem.tmpName
            element.groupPath = parentItem.tmpPath
            element.groupId = parentItem.id
          }
        })
      }
      buildHandle(this.tree, {tmpName: '', tmpPath: ''}, 0)
      if (this.currentFileItem.tmp_id) {
        this.open(this.currentFileItem)
      }
      this.sortTree()
    },

    // 排序tree,分组在前,接口在后
    sortTree() {
      if (this.treeSort === null) {
        return
      }
      let sortItem = function (item1, item2) {
        return item1.name.localeCompare(item2.name, 'zh-CN')
      }
      let sortHandle = arr => {
        // 分组
        let folderArr = []
        // 接口
        let fileArr = []
        arr.forEach(element => {
          if (element.folder === true) {
            if (element.children && element.children.length > 0) {
              element.children = sortHandle(element.children)
            }
            folderArr.push(element)
          } else {
            fileArr.push(element)
          }
        })
        folderArr.sort(sortItem)
        fileArr.sort(sortItem)
        if (this.treeSort === false) {
          folderArr.reverse()
          fileArr.reverse()
        }
        return folderArr.concat(fileArr)
      }
      this.tree = sortHandle(this.tree)
      this.changeForceUpdate()
    },
    // 文件夹右键菜单
    folderRightClickHandle(event, item) {
      this.$set(item, 'selectRightItem', true)
      this.$magicContextmenu({
        menus: [
          {
            label: '新增',
            icon: 'ma-icon-update',
            onClick: () => {
              this.addMenu(item.menuId)
            }
          },
          {
            label: '修改',
            icon: 'ma-icon-update',
            onClick: () => {
              this.editMenu(item)
            }
          },
          {
            label: '删除',
            icon: 'ma-icon-delete',
            onClick: () => {
              this.del(item)
            }
          },
        ],
        event,
        zIndex: 9999,
        destroy: () => {
          this.$set(item, 'selectRightItem', false)
        }
      })
      return false
    },
    // 文件右键菜单
    fileRightClickHandle(event, item) {
      this.$set(item, 'selectRightItem', true)
      this.$magicContextmenu({
        menus: [
          {
            label: '新增',
            icon: 'ma-icon-update',
            onClick: () => {
              this.addMenu(item.menuId)
            }
          },
          {
            label: '修改',
            icon: 'ma-icon-update',
            onClick: () => {
              this.editMenu(item)
            }
          },
          {
            label: '删除',
            icon: 'ma-icon-delete',
            onClick: () => {
              this.del(item)
            }
          },
        ],
        event,
        zIndex: 9999,
        destroy: () => {
          this.$set(item, 'selectRightItem', false)
        }
      })
      return false
    },
    del(item){
      bus.$emit("status", `准备删除菜单「${item.menuName}」`);
      this.$magicConfirm({
        title: "删除删除菜单",
        content: `是否要删除菜单「${item.menuName}」`,
        onOk: () => {
          delMenu(item.menuId).then((res) => {
            bus.$emit("status", `菜单${item.menuName}」已删除`);
            this.initData();
          });
        },
      });
    },
    // 强制触发子组件更新
    changeForceUpdate() {
      this.forceUpdate = !this.forceUpdate
    },

  },
  mounted() {
    bus.$on('getMenuList',()=>{this.initData()} )
    this.bus.$on('logout', () => this.tree = [])
    this.bus.$on('opened', item => {
      this.currentFileItem = item
    })
    this.bus.$on('refresh-resource', () => {
      this.initData()
    })
    this.getDicts("sys_show_hide").then((response) => {
      this.visibleOptions = response.data;
    });
    this.getDicts("sys_normal_disable").then((response) => {
      this.statusOptions = response.data;
    });
    listMenu().then((response) => {
      this.menuOptions = [];
      const menu = { menuId: 0, menuName: "主类目", children: [] };
      menu.children = this.$array.handleTree(response.data, "menuId");
      this.menuOptions.push(menu);
    });
    getFormTemplateList({ formType: 2 }).then((res) => {
      this.availableFormList = res.data;
    });
    reportList({
      reportType: "report_report_excel",
    }).then((response) => {
      this.execlList = response.data.records;
    });

    getAssemblyList().then((res) => {
      this.templateList = res.data.records;
    });
    // 新建分组快捷键
  }
}
</script>

<style scoped lang="scss">
@import './magic-resource.css';
::v-deep .vue-treeselect__control {
  height: 22px;
  line-height: 22px;
  border-radius: 0;
  border: 1px solid #bdbdbd;
}

::v-deep .vue-treeselect__menu{
  max-height: 200px !important;
  z-index: 10000;
}

::v-deep .vue-treeselect__single-value {
  line-height: 22px;
}
::v-deep .el-upload--picture-card {
  width: 80px;
  height: 80px;
  line-height: 90px;
  user-select: none;
}
.ma-settings {
  background: var(--background);
  height: 100%;
  width: 100%;
  position: relative;
  outline: 0;
}

.ma-settings .ma-layout {
  height: calc(100% - 25px);
}
.row {
  display: flex;
  padding: 5px;
  align-items: center;

  label {
    margin: 0 5px;
  }
}
</style>
