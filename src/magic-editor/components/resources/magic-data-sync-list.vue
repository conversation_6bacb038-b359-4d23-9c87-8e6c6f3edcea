<template>
  <div style="width: 100%;height: 100%;background: var(--toolbox-background)">
    <div class="ma-tree-wrapper">
      <div class="ma-tree-toolbar">
        <div class="ma-tree-toolbar-search">
          <i class="ma-icon ma-icon-search"></i>
          <input placeholder="输入关键字搜索" @input="e => doSearch(e.target.value)"/>
        </div>
        <div>
          <div class="ma-tree-toolbar-btn" title="新建数据同步" @click="toogleDialog(true,true)">
            <i class="ma-icon ma-icon-group-add"></i>
          </div>
          <div class="ma-tree-toolbar-btn" title="刷新数据同步" @click="initData()">
            <i class="ma-icon ma-icon-refresh"></i>
          </div>
        </div>
      </div>
      <ul v-show="!showLoading">
        <template v-for="(item,index) in datasources" >
        <li
          v-if="item._searchShow === true || item._searchShow === undefined"
          :key="index"
          @click="open(item)"
          @contextmenu.prevent="e => datasourceContextMenu(e, item)">
            <!-- <i class="ma-icon ma-icon-datasource"/> -->
            <magic-text-icon value="sync"/>
            <label>{{item.taskName}}</label>
        </li>
        </template>
      </ul>
      <div class="loading" v-show="showLoading">
        <div class="icon">
          <i class="ma-icon ma-icon-refresh "></i>
        </div>
        加载中...
      </div>
      <div class="no-data" v-show="!showLoading && (!datasources || datasources.length === 0)">无数据</div>
    </div>
    <magic-dialog width="auto" height="auto" v-model="showDialog" :title="datasourceObj.taskId ? '修改数据同步:' + datasourceObj.taskName : '创建数据同步'" align="center" @onClose="toogleDialog(false)">
      <template #content>
        <label>名称: </label>
        <magic-input :value.sync="datasourceObj.taskName" placeholder="数据同步名称，仅做展示使用"/>
      </template>
      <template #buttons>
        <button class="ma-button active" @click="doSave">{{ datasourceObj.taskId ? '修改' : '创建' }}</button>
        <button class="ma-button" @click="toogleDialog(false)">取消</button>
      </template>
    </magic-dialog>
  </div>
</template>

<script>
import bus from '@/magic-editor/scripts/bus.js'
import request from '@/magic-editor/api/request.js'
import contants from "@/magic-editor/scripts/contants.js"
import MagicDialog from '@/magic-editor/components/common/modal/magic-dialog.vue'
import MagicInput from '@/magic-editor/components/common/magic-input.vue'
import {formatJson, isVisible, replaceURL} from '@/magic-editor/scripts/utils.js'
import JavaClass from '@/magic-editor/scripts/editor/java-class.js'
import * as monaco from 'monaco-editor/esm/vs/editor/editor.api'
import store from "@/magic-editor/scripts/store";
import MagicSelect from "@/magic-editor/components/common/magic-select";
import { getMagicSyncData, saveMagicSyncData, deleteMagicSyncData } from '@/api/interfaces/interfaces.js'
import MagicTextIcon from "@/magic-editor/components/common/magic-text-icon";

export default {
  name: 'MagicDataSyncList',
  components: {
    MagicSelect,
    MagicDialog,
    MagicInput,
    MagicTextIcon
  },
  data() {
    return {
      bus: bus,
      datasources: [],
      showDialog:false,
      datasourceObj: {
        config: null,
        taskId: null,
        taskName: ""
      },
      editor: null,
      // 是否展示loading
      showLoading: true
    }
  },
  methods: {

    doSearch(keyword) {
      keyword = keyword.toLowerCase()
      this.datasources.forEach(it => {
        it._searchShow = keyword ? (it.name&&it.name.toLowerCase().indexOf(keyword) > -1) || (it.key && it.key.toLowerCase().indexOf(keyword) > -1) : true;
      })
      this.$forceUpdate();
    },
    datasourceContextMenu(event,item){
      this.$magicContextmenu({
        menus: [{
          label : '编辑数据同步',
          onClick: ()=> {
            this.datasourceObj.config = item.config
            this.datasourceObj.taskId = item.taskId
            this.datasourceObj.taskName = item.taskName
            this.toogleDialog(true, false)
          }
        },{
          label : '删除数据同步',
          onClick: ()=>this.deleteDataSource(item)
        }],
        event,
        zIndex: 9999
      });
      return false;
    },
    // 初始化数据
    initData() {
      this.showLoading = true
      this.datasources = []
      bus.$emit('status', '正在初始化数据同步列表')
      return new Promise((resolve) => {
        getMagicSyncData().then(res => {
          this.datasources = res.data || []
          setTimeout(() => {
            this.showLoading = false
          }, 500)
          bus.$emit('status', '数据同步初始化完毕')
          resolve()
        })
      })
    },

    doSave(){
      if(!this.datasourceObj.taskName){
        this.$magicAlert({
          content : '名称不能为空'
        })
      }else{
        bus.$emit('status', `保存数据同步「${this.datasourceObj.taskName}」...`)

        saveMagicSyncData(this.datasourceObj).then(res => {
          bus.$emit('status', `数据「${this.datasourceObj.taskName}」保存成功`)
          this.showDialog = false;
          this.initDataSourceObj()
          this.initData();
        })
      }
    },
    initDataSourceObj(){
      this.datasourceObj = {
        config: null,
        taskId: null,
        taskName: ""
      }
    },
    toogleDialog(show, clear){
      this.showDialog = show;
      if(show){
        if(clear){
          this.initDataSourceObj()
        }
        bus.$emit('status', `准备编辑数据同步`)
      }
    },
    // 删除接口
    deleteDataSource(item) {
      bus.$emit('status', `准备删除数据同步「${item.taskName}」`)
      this.$magicConfirm({
        title: '删除数据同步',
        content: `是否要删除数据同步「${item.taskName}」`,
        onOk: () => {
          deleteMagicSyncData({ taskId: item.taskId }).then(res => {
            if (res) {
              bus.$emit('status', `数据同步「${item.taskName}」已删除`)
              this.initData();
            } else {
              this.$magicAlert({content: '删除失败'})
            }
          })
        }
      })
    },
    open(item) {
      item['temps'] = {
        type: 'sync-data',
        id: item.taskId,
        name: item.taskName,
        config: item.config,
      }
      bus.$emit('open', item)
    }
  },
  mounted() {
    this.bus.$on('logout', () => this.datasources = []);
    this.bus.$on('getSyncList',() => {
      this.initData()
    })
  }
}
</script>

<style>
@import './magic-resource.css';
</style>
<style scoped>

ul li {
  line-height: 20px;
  padding-left: 10px;
}
ul li:hover{
  background: var(--toolbox-list-hover-background);
}
.ds-form{
  margin-bottom: 5px;
  display: flex;
}
.ds-form label{
  margin-right: 5px;
  display: inline-block;
  width: 60px;
  text-align: right;
  height: 22px;
  line-height: 22px;
}
.ds-form > div{
  flex: 1;
}
.ds-form label:nth-of-type(2){
  margin: 0 5px;
}
.ma-editor span{
  color: unset;
}
.ma-tree-wrapper{
  width: 100% !important;
  height: 100%;
}
.ma-tree-wrapper .loading i {
  color: var(--color);
  font-size: 20px;
}
.ma-tree-wrapper .loading .icon {
  width: 20px;
  margin: 0 auto;
  animation: rotate 1s linear infinite;
}
.ma-tree-wrapper .loading {
  color: var(--color);
  position: absolute;
  text-align: center;
  width: 100%;
  top: 50%;
  margin-top: -20px;
}
.ma-tree-wrapper .no-data {
  color: var(--color);
  position: absolute;
  top: 50%;
  left: 50%;
  margin-left: -20px;
}
</style>
