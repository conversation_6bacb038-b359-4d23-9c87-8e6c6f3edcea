<template>
  <div class="ma-tree-wrapper">
    <div class="ma-tree-toolbar">
      <div class="ma-tree-toolbar-search">
        <i class="ma-icon ma-icon-search"></i
        ><input
          placeholder="输入关键字搜索"
          @input="(e) => doSearch(e.target.value)"
        />
      </div>
      <div>
        <div class="ma-tree-toolbar-btn" title="刷新" @click="initData()">
          <i class="ma-icon ma-icon-refresh"></i>
        </div>
        <div
          class="ma-tree-toolbar-btn"
          title="新建分组"
          @click="addTask('', 'group')"
        >
          <i class="ma-icon ma-icon-group-add"></i>
        </div>
        <div
          class="ma-tree-toolbar-btn"
          title="折叠"
          @click="rebuildTree(true)"
        >
          <i class="ma-icon ma-icon-folding"></i>
        </div>
      </div>
    </div>
    <magic-tree :data="tree" :forceUpdate="forceUpdate" :loading="showLoading">
      <template #folder="{ item }">
        <div
          v-if="item._searchShow !== false"
          :id="'magic-function-list-' + item.taskId"
          :class="{ 'ma-tree-select': item.selectRightItem }"
          :style="{ 'padding-left': 17 * item.level + 'px' }"
          :title="item.taskName"
          class="ma-tree-item-header ma-tree-hover"
          :dragtarget="dragging && draggableTargetItem === item"
          @click="$set(item, 'opened', !item.opened)"
          @contextmenu.prevent="(e) => folderRightClickHandle(e, item)"
        >
          <i
            :class="
              item.opened ? 'ma-icon-arrow-bottom' : 'ma-icon-arrow-right'
            "
            class="ma-icon"
          ></i>
          <i class="ma-icon ma-icon-list"></i>
          <label>{{ item.taskName }}</label>
        </div>
      </template>
      <template #file="{ item }">
        <div
          v-if="item._searchShow !== false"
          :class="{
            'ma-tree-select':
              item.selectRightItem || item.taskId === currentFileItem.taskId,
          }"
          :id="'magic-function-list-' + item.taskId"
          :style="{ 'padding-left': 17 * item.level + 'px' }"
          class="ma-tree-hover"
          :title="item.taskName"
          @contextmenu.prevent="(e) => folderRightClickHandle(e, item)"
          @click="open(item)"
        >
          <magic-text-icon value="timer" />
          <label>{{ item.taskName }}</label>
          <i class="ma-icon ma-icon-lock" v-if="item.lock === '1'"></i>
        </div>
      </template>
    </magic-tree>
    <magic-dialog
      v-model="visible"
      :title="
        current.taskId
          ? '修改分组:'
          : current.taskType == 'group'
          ? '新增分组'
          : '新增任务'
      "
      align="right"
    >
      <template #content>
        <label>{{
          current.taskType == 'group' ? '分组名称' : '任务名称'
        }}</label>
        <magic-input placeholder="请输入名称" v-model="current.taskName" />
        <div style="height: 5px"></div>
        <label>{{
          current.taskType == 'group' ? '分组描述' : '任务描述'
        }}</label>
        <magic-textarea
          placeholder="请输入模板描述"
          :value.sync="current.taskDesc"
          style="width: 100%; height: 100%; margin: 2px"
        />
      </template>
      <template #buttons>
        <button class="ma-button active" @click="save">
          {{ current.taskId ? '修改' : '创建' }}
        </button>
        <button class="ma-button" @click="visible = false">取消</button>
      </template>
    </magic-dialog>
  </div>
</template>

<script>
import bus from '@/magic-editor/scripts/bus.js';
import MagicTree from '@/magic-editor/components/common/magic-tree.vue';
import request from '@/magic-editor/api/request.js';
import { deleteMagicTask, getMagicTask, saveMagicTask } from '@/api/interfaces/interfaces'
import MagicDialog from '@/magic-editor/components/common/modal/magic-dialog.vue';
import MagicInput from '@/magic-editor/components/common/magic-input.vue';
import MagicTextarea from '@/magic-editor/components/common/magic-textarea.vue';
import MagicGroupChoose from '@/magic-editor/components/resources/magic-group-choose.vue';
import {
  replaceURL,
  requestGroup,
  goToAnchor,
  deepClone,
} from '@/magic-editor/scripts/utils.js';
import JavaClass from '@/magic-editor/scripts/editor/java-class.js';
import Key from '@/magic-editor/scripts/hotkey.js';
import contants from '@/magic-editor/scripts/contants.js';
import MagicTextIcon from '@/magic-editor/components/common/magic-text-icon';
import store from '@/magic-editor/scripts/store.js';
export default {
  name: 'MagicTimeList',
  props: {
    groups: Array,
  },
  components: {
    MagicTextIcon,
    MagicTree,
    MagicDialog,
    MagicInput,
    MagicGroupChoose,
    MagicTextarea,
  },
  data() {
    return {
      bus: bus,
      // 分组list数据
      listGroupData: [],
      // 接口list数据
      listChildrenData: [],
      // 分组+接口tree数据
      tree: [],
      // 数据排序规则,true:升序,false:降序
      treeSort: true,
      groupChooseVisible: false,
      srcId: '',
      // 换成一个临时对象，修改使用
      tempGroupObj: {},
      // 当前打开的文件
      currentFileItem: {},
      // 绑定给magic-tree组件，用来触发子组件强制更新
      forceUpdate: true,
      // 拖拽的item
      draggableItem: {},
      draggableTargetItem: {},
      // 是否展示tree-loading
      showLoading: false,
      dragging: false,
      // 缓存一个openId
      tmpOpenId: [],
      current: {},
      visible: false,
    };
  },
  methods: {
    doSearch(keyword) {
      keyword = keyword.toLowerCase();
      let loopSearch = (row) => {
        if (row.folder) {
          row.children.forEach((it) => loopSearch(it));
          row._searchShow =
            (row.taskName || '').toLowerCase().indexOf(keyword) > -1 ||
            row.children.some((it) => it._searchShow);
        } else {
          row._searchShow =
            replaceURL(row.taskName || '')
              .toLowerCase()
              .indexOf(keyword) > -1;
        }
      };
      this.tree.forEach((it) => loopSearch(it));
      this.changeForceUpdate();
    },
    doFindFunction(path) {
      let finds = this.listChildrenData.filter(
        (it) => !it.folder && replaceURL(it.groupPath + '/' + it.path) === path,
      );
      return finds.length > 0 ? finds[0] : null;
    },
    open(item) {
      bus.$emit('open', item);
      bus.$emit('status', `查看定时任务「${item.taskName}」详情`);
      this.currentFileItem = item;
    },
    // 初始化数据
    initData() {
      this.showLoading = true;
      this.tree = [];
      bus.$emit('status', '正在初始化定时任务列表');
      this.showLoading = true;
      return new Promise((resolve) => {
        getMagicTask().then((res) => {
          function func(items) {
            items.forEach((item) => {
              item.temps = {
                id: item.taskId,
                name: item.taskName,
                type: 'timer',
              };
              // item.textIcon = this.getMenuType(item.menuType)
              if (item.taskType == 'group') {
                item.folder = true;
                item.opened = false;
                if (item.children.length > 0) {
                  func(item.children);
                }
              } else {
                item.folder = false;
                item.opened = false;
              }
            });
          }
          let data = res.data;
          func(data);
          this.tree = data;
          bus.$emit('status', '定时任务加载完毕');
          this.showLoading = false;
          resolve();
        });
      });
    },
    // 初始化tree结构数据
    initTreeData() {
      // 1.把所有的分组id存map,方便接口列表放入,root为没有分组的接口
      let groupItem = { root: [] };
      this.listGroupData.forEach((element) => {
        groupItem[element.id] = [];
        element.folder = true;
        this.$set(element, 'opened', contants.DEFAULT_EXPAND);
        // 缓存一个name和path给后面使用
        element.tmpName =
          element.name.indexOf('/') === 0 ? element.name : '/' + element.name;
        element.tmpPath =
          element.path.indexOf('/') === 0 ? element.path : '/' + element.path;
      });
      // 2.把所有的接口列表放入分组的children
      this.listChildrenData.forEach((element, index) => {
        element.tmp_id = element.id;
        element._type = 'function';
        if (groupItem[element.groupId]) {
          groupItem[element.groupId].push(element);
        } else {
          element.groupName = '';
          element.groupPath = '';
          groupItem['root'].push(element);
        }
      });
      // 3.将分组列表变成tree,并放入接口列表,分组在前,接口在后
      let arrayToTree = (arr, parentItem, groupName, groupPath, level) => {
        //  arr 是返回的数据  parendId 父id
        let temp = [];
        let treeArr = arr;
        treeArr.forEach((item, index) => {
          if (item.parentId == parentItem.id) {
            item.level = level;
            item.tmpName = groupName + item.tmpName;
            item.tmpPath = groupPath + item.tmpPath;
            // 递归调用此函数
            item.children = arrayToTree(
              treeArr,
              item,
              item.tmpName,
              item.tmpPath,
              level + 1,
            );
            if (groupItem[item.id]) {
              groupItem[item.id].forEach((element) => {
                element.level = item.level + 1;
                element.groupName = item.tmpName;
                element.groupPath = item.tmpPath;
                element.groupId = item.id;
                this.$set(item.children, item.children.length, element);
              });
            }
            this.$set(temp, temp.length, treeArr[index]);
          }
        });
        return temp;
      };
      this.tree = [
        ...arrayToTree(this.listGroupData, { id: 0 }, '', '', 0),
        ...groupItem['root'],
      ];
      this.sortTree();
    },
    // 重新构建tree的path和name,第一个参数表示是否全部折叠
    rebuildTree(folding) {
      let buildHandle = (arr, parentItem, level) => {
        arr.forEach((element) => {
          element.level = level;
          // 处理分组
          if (element.folder === true) {
            element.tmpName = (parentItem.tmpName + '/' + element.name).replace(
              new RegExp('(/)+', 'gm'),
              '/',
            );
            element.tmpPath = (parentItem.tmpPath + '/' + element.path).replace(
              new RegExp('(/)+', 'gm'),
              '/',
            );
            this.$set(element, 'opened', folding !== true);
            if (element.children && element.children.length > 0) {
              buildHandle(element.children, element, level + 1);
            }
          } else {
            // 处理接口
            element.groupName = parentItem.tmpName;
            element.groupPath = parentItem.tmpPath;
            element.groupId = parentItem.id;
          }
        });
      };
      buildHandle(this.tree, { tmpName: '', tmpPath: '' }, 0);
      if (this.currentFileItem.tmp_id) {
        this.open(this.currentFileItem);
      }
      // this.sortTree()
    },
    addTask(id, type) {
      if (type == 'group') {
        this.current = {
          taskName: '',
          taskType: 'group',
          parentId: id,
          isEnable: true,
          taskDesc: '',
        };
      } else {
        let item = {
          cron: '',
          taskName: '',
          magicScript: '',
          taskType: 'task',
          taskDesc: '',
          isEnable: true,
          parentId: id,
          temps: {
            id: '',
            type: 'timer',
          },
          folder: false,
          opened: false,
        };
        this.current = item;
      }
      this.visible = true;
    },
    updateTask(item) {
      this.current = item;
      this.visible = true;
    },
    delete(item) {
      deleteMagicTask(item).then((res) => {
        this.visible = false;
        // this.open(this.current);
        this.initData();
      });
    },
    save() {
      if (this.current.temps) {
        // 修改
        saveMagicTask(this.current).then((res) => {
          this.visible = false;
          // this.open(this.current);
          this.initData();
        });
      } else {
        saveMagicTask(this.current).then((res) => {
          this.visible = false;
          this.initData();
        });
      }
    },

    // 文件夹右键菜单
    folderRightClickHandle(event, item) {
      this.$set(item, 'selectRightItem', true);
      if (item.taskType === 'task') {
        this.$magicContextmenu({
          menus: [
            {
              label: '删除任务',
              onClick: () => {
                this.delete(item)
              },
            }
          ],
          event,
          zIndex: 9999,
          destroy: () => {
            this.$set(item, 'selectRightItem', false);
          },
        });
      } else {
        this.$magicContextmenu({
          menus: [
            {
              label: '新建分组',
              icon: 'ma-icon-group-add',
              onClick: () => {
                this.addTask(item.taskId, 'group');
              },
            },
            {
              label: '修改分组',
              icon: 'ma-icon-update',
              onClick: () => {
                this.updateTask(item);
              },
            },
            {
              label: '删除分组',
              icon: 'ma-icon-delete',
              onClick: () => {
                this.delete(item)
              },
            },
            {
              label: '新建任务',
              icon: 'ma-icon-plus',
              onClick: () => {
                this.addTask(item.taskId, 'task');
              },
            },
          ],
          event,
          zIndex: 9999,
          destroy: () => {
            this.$set(item, 'selectRightItem', false);
          },
        });
      }
      return false;
    },
    // 文件右键菜单
    fileRightClickHandle(event, item) {
      this.$set(item, 'selectRightItem', true);
      this.$magicContextmenu({
        menus: [],
        event,
        zIndex: 9999,
        destroy: () => {
          this.$set(item, 'selectRightItem', false);
        },
      });
      return false;
    },
  },
  mounted() {
    bus.$on('getTimerList', () => {
      this.initData();
    });
    this.bus.$on('logout', () => (this.tree = []));
    this.bus.$on('opened', (item) => {
      this.currentFileItem = item;
    });
    let element = document.getElementsByClassName('ma-container')[0];
  },
};
</script>

<style>
@import './magic-resource.css';
</style>
