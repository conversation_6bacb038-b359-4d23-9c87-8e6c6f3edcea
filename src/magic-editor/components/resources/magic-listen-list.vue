<template>
  <div class="ma-tree-wrapper">
    <div class="ma-tree-toolbar">
      <div class="ma-tree-toolbar-search">
        <i class="ma-icon ma-icon-search"></i
        ><input
          placeholder="输入关键字搜索"
          @input="(e) => doSearch(e.target.value)"
        />
      </div>
      <div>
        <div class="ma-tree-toolbar-btn" title="刷新" @click="initData()">
          <i class="ma-icon ma-icon-refresh"></i>
        </div>
        <div
          class="ma-tree-toolbar-btn"
          title="新建任务"
          @click="addTask"
        >
          <i class="ma-icon ma-icon-group-add"></i>
        </div>
      </div>
    </div>
    <template v-for="(item, index) in tree">
      <div
        :key="index"
        v-show="!showLoading"
        :class="{
          'ma-tree-select':
            item.selectRightItem ||
            (item.temps &&
              currentFileItem.temps &&
              item.temps.id === currentFileItem.temps.id),
        }"
        @click="open(item)"
        @contextmenu.prevent="(e) => fileRightClickHandle(e, item)"
        class="ma-tree-hover"
      >
        <magic-text-icon
          style="margin-left: 10px"
          :value="'listen'"
        />
        <label>{{ item.temps.name }}</label>
      </div>
    </template>
    <div class="loading" v-show="showLoading">
      <div class="icon">
        <i class="ma-icon ma-icon-refresh"></i>
      </div>
      加载中...
    </div>
    <div class="no-data" v-show="!showLoading && (!tree || tree.length === 0)">
      无数据
    </div>
    <magic-dialog
      v-model="visible"
      :title="
        current.taskId
          ? '修改任务:'
          :  '新增任务'
      "
      align="right"
    >
      <template #content>
        <label>
          任务名称
        </label>
        <magic-input style="margin-left:10px" placeholder="请输入名称" v-model="current.taskName" />
        <div style="height:10px"></div>
        <div style="display:flex;align-items:center">
          <label>
          绑定点位
        </label>
        <button style="margin-left:10px" class="ma-button" @click="select">
          选择点位
        </button>
        <!-- <magic-select
        style="width:174px;margin-left:10px"
          :placeholder="'请选择绑定点位'"
          :options="keys"
          :value.sync="current.taskBind"
          :multiple="true"
        /> -->
        </div>
        
      </template>
      <template #buttons>
        <button class="ma-button active" @click="save">
          {{ current.taskId ? '修改' : '创建' }}
        </button>
        <button class="ma-button" @click="visible = false">取消</button>
      </template>
    </magic-dialog>
    <el-dialog
    :visible.sync="dialogVisible"
    title="key"
    center
    width="80%"
    :close-on-click-modal="false"
  >
    <el-table
      :data="keys"
      class="lt-table"
      max-height="400"
      ref="keyTable"
      @selection-change="handleSelectionChange"
    >
      <template slot="empty">
        <lt-empty></lt-empty>
      </template>
      <el-table-column type="selection"></el-table-column>
      <lt-table-column-edit prop="vkey" label="vkey" />
      <lt-table-column-edit
        prop="monitorSleep"
        label="监听步长"
        type="number"
      />
      <lt-table-column-edit prop="vaddress" label="地址" />
      <lt-table-column-edit prop="hasBuiltIn" label="是否内置" state="switch" />
      <lt-table-column-edit prop="hasMonitor" label="是否监听" state="switch" />
      <lt-table-column-edit
        prop="keyType"
        label="keyType"
        state="select"
        :select-opt="{
          options: [
            { text: 'float', value: 'float' },
            { text: 'bool', value: 'bool' },
            { text: 'int', value: 'int' },
            { text: 'double', value: 'double' },
            { text: 'string', value: 'string' },
          ],
          label: 'text',
          value: 'value',
        }"
      />
      <lt-table-column-edit
        prop="aggregateScript"
        label="聚合脚本"
        width="400"
      />
      
    </el-table>
    <!-- <div class="pop_btn" >
        <el-button type="primary" @click="submit">确定</el-button>
        <el-button @click="dialogVisible=false">取消</el-button>
    </div> -->
  </el-dialog>
  </div>
</template>

<script>
import bus from '@/magic-editor/scripts/bus.js';
import MagicTree from '@/magic-editor/components/common/magic-tree.vue';
import request from '@/magic-editor/api/request.js';
import { deletePlcTask, getMagicPlcTasks,getMagicPlcAllKey ,updatePlcTasks} from '@/api/interfaces/interfaces'
import MagicDialog from '@/magic-editor/components/common/modal/magic-dialog.vue';
import MagicInput from '@/magic-editor/components/common/magic-input.vue';
import MagicSelect from '@/magic-editor/components/common/magic-select.vue';
import MagicTextarea from '@/magic-editor/components/common/magic-textarea.vue';
import MagicGroupChoose from '@/magic-editor/components/resources/magic-group-choose.vue';
import {
  replaceURL,
  requestGroup,
  goToAnchor,
  deepClone,
} from '@/magic-editor/scripts/utils.js';
import JavaClass from '@/magic-editor/scripts/editor/java-class.js';
import Key from '@/magic-editor/scripts/hotkey.js';
import contants from '@/magic-editor/scripts/contants.js';
import MagicTextIcon from '@/magic-editor/components/common/magic-text-icon';
import store from '@/magic-editor/scripts/store.js';
export default {
  name: 'MagicTimeList',
  props: {
    groups: Array,
  },
  components: {
    MagicTextIcon,
    MagicTree,
    MagicDialog,
    MagicInput,
    MagicSelect,
    MagicGroupChoose,
    MagicTextarea,
  },
  data() {
    return {
      bus: bus,
      tree: [],
      flag:false,
      // 数据排序规则,true:升序,false:降序
      groupChooseVisible: false,
      // 换成一个临时对象，修改使用
      tempGroupObj: {},
      // 当前打开的文件
      currentFileItem: {},
      // 绑定给magic-tree组件，用来触发子组件强制更新
      forceUpdate: true,
      keys:[],
      // 拖拽的item
      draggableItem: {},
      draggableTargetItem: {},
      // 是否展示tree-loading
      showLoading: false,
      // 缓存一个openId
      current: {},
      visible: false,
      dialogVisible:false
    };
  },
  methods: {
    doSearch(keyword) {
      keyword = keyword.toLowerCase();
      let loopSearch = (row) => {
        if (row.folder) {
          row.children.forEach((it) => loopSearch(it));
          row._searchShow =
            (row.taskName || '').toLowerCase().indexOf(keyword) > -1 ||
            row.children.some((it) => it._searchShow);
        } else {
          row._searchShow =
            replaceURL(row.taskName || '')
              .toLowerCase()
              .indexOf(keyword) > -1;
        }
      };
      this.tree.forEach((it) => loopSearch(it));
      this.changeForceUpdate();
    },
    select(){
      this.dialogVisible=true
      this.$nextTick(()=>{
        let that=this
        this.flag=false
        this.keys.forEach(item=>{
          if(this.current.taskBind.split(',').includes(item.vkey)){
            that.$refs.keyTable&&that.$refs.keyTable.toggleRowSelection(item,true);
          }else{
            that.$refs.keyTable&&that.$refs.keyTable.toggleRowSelection(item,false);
          }
        })
        this.flag=true
      })
    },
    handleSelectionChange(val){
      if(this.flag){
        this.current.taskBind=val.map(item=>item.vkey).join(',')
      }
    },
    open(item) {
      bus.$emit('open', item);
      bus.$emit('status', `查看监听任务「${item.taskName}」详情`);
      this.currentFileItem = item;
    },
    // 初始化数据
    initData() {
      this.tree = [];
      getMagicPlcAllKey().then(res=>{
        this.keys=res.data
      })
      bus.$emit('status', '正在初始化监听任务列表');
      this.showLoading = true;
      return new Promise((resolve) => {
        getMagicPlcTasks().then((res) => {
          function func(items) {
            items.forEach((item) => {
              item.temps = {
                id: item.taskId,
                name: item.taskName,
                type: 'listen',
              };
              item.opened = false
            });
          }
          let data = res.data;
          func(data);
          this.tree = data;
          bus.$emit('status', '监听任务加载完毕');
          this.showLoading = false;
          resolve();
        });
      });
    },

    addTask() {
        this.current = {
          taskName:'',
          taskScript:"",
          taskBind:[]
        };
      this.visible = true;
    },
    updateTask(item) {
      this.current = item;
      this.visible = true;
    },
    delete(item) {
      bus.$emit("status", `准备删除任务「${item.taskName}」`);
      this.$magicConfirm({
        title: "删除任务",
        content: `是否要删除任务「${item.taskName}」`,
        onOk: () => {
          deletePlcTask(item.taskId).then((res) => {
            bus.$emit("status", `任务「${item.taskName}」已删除`);
            this.initData();
      });
        },
      });
      
    },
    save() {
        updatePlcTasks(this.current).then((res) => {
          this.visible = false;
          this.initData();
        });
    },

    // 文件夹右键菜单
    fileRightClickHandle(event, item) {
      this.$set(item, 'selectRightItem', true);
        this.$magicContextmenu({
          menus: [
            {
              label: '修改任务',
              onClick: () => {
                this.updateTask(item)
              },
            },
            {
              label: '删除任务',
              onClick: () => {
                this.delete(item)
              },
            }
          ],
          event,
          zIndex: 9999,
          destroy: () => {
            this.$set(item, 'selectRightItem', false);
          },
        });
      return false;
    },

  },
  mounted() {
    
    bus.$on('getListenList', () => {
      this.initData();
    });
    this.bus.$on('logout', () => (this.tree = []));
    this.bus.$on('opened', (item) => {
      this.currentFileItem = item;
    });
  },
};
</script>

<style>
@import './magic-resource.css';
</style>
