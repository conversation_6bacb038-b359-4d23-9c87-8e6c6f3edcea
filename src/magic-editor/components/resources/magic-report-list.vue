<template>
  <div class="ma-tree-wrapper">
    <div class="ma-tree-toolbar">
      <div class="ma-tree-toolbar-search">
        <i class="ma-icon ma-icon-search"></i
        ><input
          placeholder="输入关键字搜索"
          @input="(e) => doSearch(e.target.value)"
        />
      </div>
      <div>
        <div class="ma-tree-toolbar-btn" title="刷新报表" @click="initData()">
          <i class="ma-icon ma-icon-refresh"></i>
        </div>
        <div
          class="ma-tree-toolbar-btn"
          title="新建报表"
          @click="openCreateViewModal()"
        >
          <i class="ma-icon ma-icon-group-add"></i>
        </div>
      </div>
    </div>
    <template v-for="(item, index) in tree">
      <div
        :key="index"
        v-show="!showLoading"
        :class="{
          'ma-tree-select':
            item.selectRightItem ||
            (item.temps &&
              currentFileItem.temps &&
              item.temps.id === currentFileItem.temps.id),
        }"
        @click="open(item)"
        @contextmenu.prevent="(e) => fileRightClickHandle(e, item)"
        class="ma-tree-hover"
      >
        <magic-text-icon
          style="margin-left: 10px"
          v-model="item.temps.name && 'REPORT'"
        />
        <label>{{ item.temps.name }}</label>
      </div>
    </template>
    <div class="loading" v-show="showLoading">
      <div class="icon">
        <i class="ma-icon ma-icon-refresh"></i>
      </div>
      加载中...
    </div>
    <div class="no-data" v-show="!showLoading && (!tree || tree.length === 0)">
      无数据
    </div>

    <magic-dialog
      v-model="createViewObj.visible"
      :title="
        createViewObj.id ? '修改报表:' + createViewObj.reportName : '创建报表'
      "
      align="right"
    >
      <template #content>
        <label>报表名称：</label>
        <magic-input
          placeholder="请输入报表名称"
          v-model="createViewObj.reportName"
        />
        <div style="height: 5px"></div>
        <label>报表code：</label>
        <magic-input
          placeholder="请输入报表code"
          v-model="createViewObj.reportCode"
        />
        <div style="height: 5px"></div>
        <label>报表描述：</label>
        <magic-textarea
          placeholder="请输入报表描述"
          :value.sync="createViewObj.reportDesc"
          style="width: 100%; height: 100%; margin: 2px"
        />
      </template>
      <template #buttons>
        <button class="ma-button active" @click="createView">
          {{ createViewObj.id ? '修改' : '创建' }}
        </button>
        <button class="ma-button" @click="createViewObj.visible = false">
          取消
        </button>
      </template>
    </magic-dialog>
  </div>
</template>

<script>
import bus from '../../scripts/bus.js';
import MagicTree from '../common/magic-tree.vue';
import request from '@/magic-editor/api/request.js';
import MagicDialog from '@/magic-editor/components/common/modal/magic-dialog.vue';
import MagicSelect from '@/magic-editor/components/common/magic-select.vue';
import MagicTextarea from '@/magic-editor/components/common/magic-textarea.vue';
import MagicInput from '@/magic-editor/components/common/magic-input.vue';
import MagicGroupChoose from '@/magic-editor/components/resources/magic-group-choose.vue';
import {
  replaceURL,
  download as downloadFile,
  requestGroup,
  goToAnchor,
  deepClone,
} from '@/magic-editor/scripts/utils.js';
import MagicTextIcon from '@/magic-editor/components/common/magic-text-icon';
import { formReleaseo } from '@/api/tool/form.js';
import { reportList } from '@/api/bigscreen';
import { createDesigner } from '@/components/form-designer/designer';
import {
  reportAdd,
  reportUpdate,
  reportDeleteBatch,
} from '@/api/columnEditApi/columnEditApi';
export default {
  name: 'MagicReportList',
  props: {
    apis: Array,
  },
  components: {
    MagicTextIcon,
    MagicTree,
    MagicDialog,
    MagicTextarea,
    MagicInput,
    MagicSelect,
    MagicGroupChoose,
  },
  data() {
    return {
      bus: bus,
      // 分组list数据
      listGroupData: [],
      // 接口list数据
      listChildrenData: [],
      // 分组+接口tree数据
      tree: [],
      apiCopyGroupChooseVisible: false,

      srcItem: {},

      // 换成一个临时对象，修改使用
      tempGroupObj: {},
      // 当前打开的文件
      currentFileItem: {},
      // 绑定给magic-tree组件，用来触发子组件强制更新
      forceUpdate: true,
      // 是否展示tree-loading
      showLoading: true,

      // 缓存一个openId
      tmpOpenId: [],
      createViewObj: {
        id: null,
        reportName: '',
        reportCode: '',
        reportDesc: '',
        reportType: 'report_report_excel',
        visible: false,
      },
    };
  },
  methods: {
    // 新增一个报表
    createView() {
      this.createViewObj.visible = false;

      if (this.createViewObj.id) {
        reportUpdate(this.createViewObj).then((e) => {
          this.initData();
        });
      } else {
        reportAdd(this.createViewObj).then((e) => {
          this.initData();
        });
      }
    },
    // 打开新建分组弹窗
    openCreateViewModal(item, parentItem) {
      if (item) {
        for (const key in this.createViewObj) {
          this.createViewObj[key] = item[key];
        }
      } else {
        this.createViewObj = {
          id: null,
          reportName: '',
          reportCode: '',
          reportDesc: '',
          reportType: 'report_report_excel',
          visible: false,
        };
      }
      this.createViewObj.visible = true;
    },
    doSearch(keyword) {
      keyword = keyword.toLowerCase();
      let loopSearch = (row, parentName, parentPath) => {
        if (row.folder) {
          row.children.forEach((it) =>
            loopSearch(
              it,
              parentName + '/' + (row.name || ''),
              parentPath + '/' + (row.path || ''),
            ),
          );
          row._searchShow =
            (row.name || '').toLowerCase().indexOf(keyword) > -1 ||
            row.children.some((it) => it._searchShow);
        } else {
          row._searchShow =
            replaceURL(parentName + '/' + (row.name || ''))
              .toLowerCase()
              .indexOf(keyword) > -1 ||
            replaceURL(parentPath + '/' + (row.path || ''))
              .toLowerCase()
              .indexOf(keyword) > -1;
        }
      };
      this.tree.forEach((it) => loopSearch(it, '', ''));
      this.changeForceUpdate();
    },
    open(item) {
      bus.$emit('status', `查看报表「${item.temps.name}」详情`);
      bus.$emit('open', item);
      this.currentFileItem = item;
    },
    // 初始化数据
    initData() {
      bus.$emit('status', '正在初始化报表列表...');
      this.showLoading = true;
      this.tree = [];
      return new Promise((resolve) => {
        reportList({
          pageNum: 1,
          pageSize: 99999,
          reportType: 'report_report_excel',
        }).then((res) => {
          function func(items) {
            items.forEach((item) => {
              item.temps = {
                id: item.id,
                name: item.reportName,
                reportDesc: item.reportDesc,
                reportCode: item.reportCode,
                editFlag: false,
                type: 'report',
              };
            });
          }
          let data = res.data.records;
          func(data);
          this.tree = data;
          bus.$emit('status', '报表加载完毕!');
          this.showLoading = false;
          resolve();
        });
      });
    },
    // 文件右键菜单
    fileRightClickHandle(event, item) {
      this.$set(item, 'selectRightItem', true);
      this.$magicContextmenu({
        menus: [
          {
            label: '设计报表',
            icon: 'ma-icon-refresh',
            divided: true,
            onClick: () => {
              bus.$emit('editView', item);
            },
          },
          {
            label: '编辑报表',
            icon: 'ma-icon-refresh',
            divided: true,
            onClick: () => {
              this.createViewObj = item;
              this.$set(this.createViewObj, 'visible', true);
            },
          },
          {
            label: '删除报表',
            icon: 'ma-icon-delete',
            divided: true,
            onClick: () => {
              this.$confirm(`确定删除报表${item.reportName}吗?`, '警告', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
              })
                .then((res) => {
                  reportDeleteBatch(item.id).then((res) => {
                    this.$message.success('删除成功');
                    this.initData();
                  });
                })
                .catch((err) => {});
            },
          },
        ],
        event,
        zIndex: 9999,
        destroy: () => {
          this.$set(item, 'selectRightItem', false);
        },
      });
      return false;
    },
    // 强制触发子组件更新
    changeForceUpdate() {
      this.forceUpdate = !this.forceUpdate;
    },
  },
  mounted() {
    this.bus.$on('logout', () => (this.tree = []));
    this.bus.$on('opened', (item) => {
      this.currentFileItem = item;
    });
    this.bus.$on('refresh-resource', () => {
      this.initData();
    });
  },
};
</script>

<style>
@import './magic-resource.css';

.loading {
  color: var(--color);
  position: absolute;
  text-align: center;
  width: 100%;
  top: 50%;
  margin-top: -20px;
}
.loading i {
  color: var(--color);
  font-size: 20px;
}
.loading .icon .ma-icon {
  padding: 0;
}
.loading .icon {
  width: 20px;
  margin: 0 auto;
  line-height: normal;
  animation: rotate 1s linear infinite;
}
.no-data {
  color: var(--color);
  position: absolute;
  top: 50%;
  left: 50%;
  margin-left: -20px;
}
</style>
