<template>
  <div class="ma-tree-wrapper">
    <div class="ma-tree-toolbar">
      <div class="ma-tree-toolbar-search"><i class="ma-icon ma-icon-search"></i><input placeholder="输入关键字搜索"
                                                                                       @input="e => doSearch(e.target.value)"/>
      </div>
      <div>
        <div class="ma-tree-toolbar-btn" title="刷新组件" @click="initData()">
          <i class="ma-icon ma-icon-refresh"></i>
        </div>
        <div class="ma-tree-toolbar-btn" title="新建组件" @click="openCreateViewModal()">
          <i class="ma-icon ma-icon-group-add"></i>
        </div>
      </div>
    </div>
    <template v-for="(item, index) in tree" v-show="!showLoading">
      <div
        :class="{ 'ma-tree-select': item.selectRightItem || ((item.temps && currentFileItem.temps) &&  (item.temps.id === currentFileItem.temps.id)) }"
        @click="open(item)"
        @contextmenu.prevent="e => fileRightClickHandle(e, item)"
        class="ma-tree-hover">
        <magic-text-icon value="ASBLY"/>
        <label>{{ item.temps.name }}</label>
      </div>
    </template>
    <div class="loading" v-show="showLoading">
      <div class="icon">
        <i class="ma-icon ma-icon-refresh "></i>
      </div>
      加载中...
    </div>
    <div class="no-data" v-show="!showLoading && (!tree || tree.length === 0)">无数据</div>

    <magic-dialog v-model="createViewObj.visible" :title="createViewObj.formId ? '修改视图模板:' + createViewObj.templateName : '创建视图模板'"
                  align="right"
                  @onClose="">
      <template #content>
        <label>模板名称：</label>
        <magic-input placeholder="请输入模板名称" v-model="createViewObj.templateName"/>
        <div style="height: 5px"></div>
        <label>模板描述：</label>
        <magic-textarea placeholder="请输入模板描述" :value.sync="createViewObj.templateDecs" style="width: 100%; height: 100%; margin: 2px"/>
        <div style="height: 5px"></div>
        <label>模板类型：</label>
        <magic-select placeholder="请选择模板类型"  :options="validates" :value.sync="createViewObj.templateType" default-value="pass"/>
      </template>
      <template #buttons>
        <button class="ma-button active" @click="createView">{{ createViewObj.formId ? '修改' : '创建' }}</button>
        <button class="ma-button" @click="createViewObj.visible = false">取消</button>
      </template>
    </magic-dialog>
  </div>
</template>

<script>
import bus from '../../scripts/bus.js'
import MagicTree from '../common/magic-tree.vue'
import request from '@/magic-editor/api/request.js'
import MagicDialog from '@/magic-editor/components/common/modal/magic-dialog.vue'
import MagicSelect from '@/magic-editor/components/common/magic-select.vue'
import MagicTextarea from '@/magic-editor/components/common/magic-textarea.vue'
import MagicInput from '@/magic-editor/components/common/magic-input.vue'
import MagicGroupChoose from '@/magic-editor/components/resources/magic-group-choose.vue'
import { replaceURL, download as downloadFile, requestGroup, goToAnchor, deepClone } from '@/magic-editor/scripts/utils.js'
import MagicTextIcon from "@/magic-editor/components/common/magic-text-icon";
import { formRelease, listInfo } from '@/api/tool/form.js'
import { createDesigner } from '@/components/form-designer/designer'
import { addInfo } from '@/api/columnEditApi/columnEditApi'
import { getAssemblyList } from '@/api/interfaces/interfaces'
export default {
  name: 'MagicAssemblyList',
  props: {
    apis: Array
  },
  components: {
    MagicTextIcon,
    MagicTree,
    MagicDialog,
    MagicTextarea,
    MagicInput,
    MagicSelect,
    MagicGroupChoose
  },
  data() {
    return {
      validates: [
        {value: 1, text: '表单'},
        {value: 2, text: '视图'},
      ],
      bus: bus,
      // 分组list数据
      listGroupData: [],
      // 接口list数据
      listChildrenData: [],
      // 分组+接口tree数据
      tree: [],
      apiCopyGroupChooseVisible: false,

      srcItem: {},

      // 换成一个临时对象，修改使用
      tempGroupObj: {},
      // 当前打开的文件
      currentFileItem: {

      },
      // 绑定给magic-tree组件，用来触发子组件强制更新
      forceUpdate: true,
      // 是否展示tree-loading
      showLoading: true,

      // 缓存一个openId
      tmpOpenId: [],
      createViewObj:{
        formId: null,
        templateDecs:"",
        templateName:"",
        templateType:1,
        visible: false
      }

    }
  },
  methods: {
    // 新增一个模板
    createView() {
      this.createViewObj.visible = false
      addInfo(this.createViewObj).then(e => {
        this.initData()
      })
    },
    // 打开新建分组弹窗
    openCreateViewModal(item, parentItem) {
      if (item) {
        for (const key in this.createViewObj) {
          this.createViewObj[key] = item[key]
        }
      }
      this.createViewObj.visible = true
    },
    doSearch(keyword) {
      keyword = keyword.toLowerCase();
      let loopSearch = (row, parentName, parentPath) => {
        if (row.folder) {
          row.children.forEach(it => loopSearch(it, parentName + '/' + (row.name || ''), parentPath + '/' + (row.path || '')))
          row._searchShow = (row.name || '').toLowerCase().indexOf(keyword) > -1 || row.children.some(it => it._searchShow)
        } else {
          row._searchShow = replaceURL(parentName + '/' + (row.name || '')).toLowerCase().indexOf(keyword) > -1 || replaceURL(parentPath + '/' + (row.path || '')).toLowerCase().indexOf(keyword) > -1
        }
      }
      this.tree.forEach(it => loopSearch(it, '', ''))
      this.changeForceUpdate()
    },
    open(item) {
      bus.$emit('status', `查看在线组件「${item.temps.name}」详情`)
      bus.$emit('open', item)
      this.currentFileItem = item
    },
    // 初始化数据
    initData() {
      bus.$emit('status', '正在初始化组件列表...')
      this.showLoading = true
      this.tree = []
      return new Promise((resolve) => {
        getAssemblyList({pageNum: 1, pageSize: 99999 }).then(res =>{
          function func(items){
            items.forEach(item => {
               item.temps = {
                 id: item.templateCode,
                 name: item.name,
                 type: 'assembly',
                 editFlag: false
               }
            })
          }
          let data = res.data.records
          func(data)
          this.tree = data
          bus.$emit('status', '组件加载完毕!')
          this.showLoading = false
          resolve()
        }).catch(msg => {
          this.showLoading = false
        })
      })
    },
    // 文件右键菜单
    fileRightClickHandle(event, item) {
      this.$set(item, 'selectRightItem', true)
      this.$magicContextmenu({
        menus: [
          {
            label: '设计模板',
            icon: 'ma-icon-refresh',
            divided: true,
            onClick: () => {
              if (item.templateJson) {
                item.temps.temJson = JSON.parse(item.templateJson)
              } else {
                let designer = createDesigner(this)
                let widgetList = deepClone(designer.widgetList)
                let formConfig = deepClone(designer.formConfig)
                item.temps.temJson = JSON.parse(JSON.stringify({widgetList, formConfig}, null, '  '))
              }
              bus.$emit('editView', item)
            }
          },
          {
            label: '编辑模板',
            icon: 'ma-icon-refresh',
            divided: true,
            onClick: () => {
              this.createViewObj = item
              this.$set(this.createViewObj, 'visible', true)
            }
          },
          {
            label: '发布视图',
            icon: 'ma-icon-push',
            divided: true,
            onClick: () => {
              formRelease(item.formId).then(e => {
                this.$message.success(e.data ? `发布成功` : `发布失败`)
                item.isNew = true
              })
            }
          },
          {
            label: '删除视图',
            icon: 'ma-icon-delete',
            divided: true,
            onClick: () => {
            }
          }
        ],
        event,
        zIndex: 9999,
        destroy: () => {
          this.$set(item, 'selectRightItem', false)
        }
      })
      return false
    },
    // 强制触发子组件更新
    changeForceUpdate() {
      this.forceUpdate = !this.forceUpdate
    },
  },
  mounted() {
    this.bus.$on('logout', () => this.tree = [])
    this.bus.$on('opened', item => {
      this.currentFileItem = item
    })
    this.bus.$on('refresh-resource', () => {
      this.initData()
    })
  }
}
</script>

<style>
@import './magic-resource.css';

.loading {
  color: var(--color);
  position: absolute;
  text-align: center;
  width: 100%;
  top: 50%;
  margin-top: -20px;
}
 .loading i {
  color: var(--color);
  font-size: 20px;
}
 .loading .icon .ma-icon {
  padding: 0;
}
 .loading .icon {
  width: 20px;
  margin: 0 auto;
  line-height: normal;
  animation: rotate 1s linear infinite;
}
.no-data {
  color: var(--color);
  position: absolute;
  top: 50%;
  left: 50%;
  margin-left: -20px;
}
</style>
