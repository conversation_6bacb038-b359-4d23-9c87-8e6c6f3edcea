.ma-tree-wrapper:not(.ma-dialog-wrapper) {
  text-align: left;
  float: left;
  border-right: 1px solid var(--toolbox-border-right-color);
  overflow: auto;
  /* width: 250px;     */
  min-width: 250px;
  background: var(--toolbox-background);
  position: relative;
}

.ma-tree-wrapper .ma-tree-item .ma-tree-item-header {
  font-weight: bold;
  height: 20px;
  line-height: 20px;
}

.ma-tree-wrapper .ma-tree-item .ma-tree-sub-items>* {
  line-height: 20px;
  /* padding-left: 17px; */
  white-space: nowrap;
}

.ma-tree-wrapper .ma-tree-item .ma-tree-item-header[dragtarget] label {
  border-color: red;
}

.ma-tree-wrapper .ma-tree-hover:hover,
.ma-tree-wrapper .ma-tree-select {
  background: var(--toolbox-list-hover-background);
}

.ma-tree-wrapper .ma-tree-item .ma-tree-sub-items>*.selected {
  background: var(--toolbox-list-selected-background);
}

.ma-tree-wrapper .ma-icon {
  color: var(--toolbox-list-icon-color);
  padding-right: 2px;
  font-size: 14px;
}

.ma-tree-wrapper .ma-icon-arrow-bottom {
  color: var(--toolbox-list-arrow-color);
}

.ma-tree-wrapper span {
  color: var(--toolbox-list-span-color);
  display: inline-block;
  /* height: 22px;
    line-height: 22px; */
}

.ma-tree-wrapper .ma-icon-lock {
  color: var(--toolbox-list-label-color);
  margin-left: 5px;
}

.ma-tree-wrapper label {
  color: var(--toolbox-list-label-color);
  display: inline-block;
  border: 1px solid transparent;
}

.ma-tree-wrapper .ma-tree-toolbar-search {
  flex: 1;
}

.ma-tree-wrapper .ma-tree-toolbar-search input {
  border: none;
  background: none;
  height: 100%;
  color: var(--input-color);
}

.ma-tree-wrapper .ma-tree-toolbar-search input:focus {
  outline: none;
}

.ma-tree-wrapper .ma-tree-toolbar {
  background: var(--background);
  color: var(--toolbox-list-label-color);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  /* flex-direction: row-reverse; */
  padding: 1px;
}

.ma-tree-wrapper .ma-tree-toolbar-btn {
  padding: 2px;
  align-self: flex-end;
  display: inline-block;
}

.ma-tree-wrapper .ma-tree-toolbar-btn:hover,
.ma-tree-wrapper .ma-tree-toolbar-btn.hover {
  background: var(--toolbox-list-hover-background);
}

.ma-tree-wrapper .ma-tree-toolbar i {
  color: var(--toolbox-list-header-icon-color);
}

.ma-tree-wrapper .ma-tree-container {
  height: calc(100% - 25px);
  overflow: auto;
}

.ma-tree-wrapper .ma-icon-datasource {
  color: #089910;
}