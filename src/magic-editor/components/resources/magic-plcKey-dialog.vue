<template>
  <el-dialog
    :visible.sync="dialogVisible"
    title="key"
    center
    width="80%"
    :close-on-click-modal="false"
  >
    <el-table
      v-loading="loading"
      :data="tableData"
      class="lt-table"
      max-height="400"
    >
      <template slot="empty">
        <lt-empty></lt-empty>
      </template>
      <lt-table-column-edit prop="vkey" label="vkey" />
      <lt-table-column-edit
        prop="monitorSleep"
        label="监听步长"
        type="number"
      />
      <lt-table-column-edit prop="vaddress" label="地址" />
      <lt-table-column-edit prop="hasBuiltIn" label="是否内置" state="switch" />
      <lt-table-column-edit prop="hasMonitor" label="是否监听" state="switch" />
      <lt-table-column-edit
        prop="keyType"
        label="keyType"
        state="select"
        :select-opt="{
          options: [
            { text: 'float', value: 'float' },
            { text: 'bool', value: 'bool' },
            { text: 'int', value: 'int' },
            { text: 'double', value: 'double' },
            { text: 'string', value: 'string' },
          ],
          label: 'text',
          value: 'value',
        }"
      />
      <lt-table-column-edit
        prop="aggregateScript"
        label="聚合脚本"
        width="400"
      />
      <lt-table-column-operation
        v-model="tableData"
        width="140"
        :row-data="rowData"
        primary-key="keyId"
        deleteApi="delMagicPlcKey"
        saveApi="updateMagicPlcKey"
        validateProp="vkey,keyType"
      >
      </lt-table-column-operation>
    </el-table>
    <lt-pagination
      :total="total"
      :page.sync="currentPage"
      :limit.sync="size"
      @pagination="getTableData"
    />
  </el-dialog>
</template>

<script>
import { getMagicPlcKeys } from '@/api/interfaces/interfaces.js';
export default {
  data() {
    return {
      dialogVisible: false, // 关闭开启标识
      loading: true,
      tableData: [],
      rowData: {
        aggregateScript: '',
        hasBuiltIn: true,
        hasMonitor: true,
        keyId: '',
        keyType: undefined,
        monitorSleep: '',
        vaddress: '',
        vkey: '',
        dbKey: '',
      },
      currentPage: 1,
      size: 10,
      total: 0,
    };
  },
  methods: {
    /**
     * 打开
     */
    open(key) {
      this.dialogVisible = true;
      this.currentPage = 1;
      this.size = 10;
      this.rowData.dbKey = key;
      this.getTableData();
    },
    getTableData() {
      this.loading = true;
      getMagicPlcKeys({
        dbKey: this.rowData.dbKey,
        pageNum: this.currentPage,
        pageSize: this.size,
      }).then((res) => {
        this.loading = false;
        this.tableData = res.data.records;
        this.total = res.data.total;
      });
    },
  },
};
</script>

<style lang="scss" scoped></style>
