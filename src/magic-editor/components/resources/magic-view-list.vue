<template>
  <div class="ma-tree-wrapper">
    <div class="ma-tree-toolbar">
      <div class="ma-tree-toolbar-search">
        <i class="ma-icon ma-icon-search"></i
        ><input
          placeholder="输入关键字搜索"
          @input="(e) => doSearch(e.target.value)"
        />
      </div>
      <div>
        <div class="ma-tree-toolbar-btn" title="刷新视图" @click="initData()">
          <i class="ma-icon ma-icon-refresh"></i>
        </div>
        <div class="ma-tree-toolbar-btn" title="新建节点" @click="addNode(0)">
          <i class="ma-icon ma-icon-group-add"></i>
        </div>
      </div>
    </div>
    <magic-tree :data="tree" v-if="tree.length" :forceUpdate="forceUpdate" :loading="showLoading">
      <template #folder="{ item }">
        <div
          v-if="item._searchShow !== false"
          :id="'magic-view-list-' + item.nodeId"
          :class="{ 'ma-tree-select': item.selectRightItem }"
          :draggable="true"
          :style="{ 'padding-left': 17 * item.lavel + 'px' }"
          :title="item.label"
          class="ma-tree-item-header ma-tree-hover"
          :dragtarget="dragging && draggableTargetItem === item"
          @dblclick="$set(item, 'opened', !item.opened)"
          @dragenter="(e) => draggable(item, e, 'dragenter')"
          @contextmenu.prevent="(e) => folderRightClickHandle(e, item)"
          @dragstart.stop="(e) => draggable(item, e, 'dragstart')"
          @dragend.stop="(e) => draggable(item, e, 'dragend')"
          @dragover.prevent
        >
          <i
            :class="
              item.opened ? 'ma-icon-arrow-bottom' : 'ma-icon-arrow-right'
            "
            class="ma-icon"
            @click="$set(item, 'opened', !item.opened)"
          />
          <i class="ma-icon ma-icon-list"></i>
          <label>{{ item.label }}</label>
        </div>
      </template>
      <template #file="{ item }">
        <div
          v-if="item._searchShow !== false"
          :class="{
            'ma-tree-select':
              item.selectRightItem || item.nodeId === currentFileItem.nodeId,
          }"
          :draggable="true"
          :id="'magic-view-list-' + item.nodeId"
          :style="{ 'padding-left': 17 * item.lavel + 'px' }"
          class="ma-tree-hover"
          :title="item.label"
          @click="open(item.data)"
          @dragenter="(e) => draggable(item, e, 'dragenter')"
          @contextmenu.prevent="(e) => fileRightClickHandle(e, item.data)"
          @dragstart.stop="(e) => draggable(item, e, 'dragstart')"
          @dragend.stop="(e) => draggable(item, e, 'dragend')"
          @dragover.prevent
        >
          <magic-text-icon
            :value="item.data && item.data.templateType == '1' ? 'FORM' : 'FV'"
          />
          <label>{{ item.label }}</label>
        </div>
      </template>
    </magic-tree>

    <magic-dialog
      v-model="show"
      :title="dialogType == 'add' ? '添加节点' : '修改节点'"
      align="right"
    >
      <template #content>
        <label>节点名称：</label>
        <magic-input placeholder="请输入节点名称" v-model="nodeItem.nodeName" />
      </template>
      <template #buttons>
        <button class="ma-button active" @click="createNode">
          {{ nodeItem.id ? "修改" : "创建" }}
        </button>
        <button class="ma-button" @click="show = false">取消</button>
      </template>
    </magic-dialog>

    <magic-dialog
      v-model="createViewObj.visible"
      :title="
        createViewObj.formId
          ? '修改视图模板:' + createViewObj.templateName
          : '创建视图模板'
      "
      align="right"
    >
      <template #content>
        <label>模板名称：</label>
        <magic-input
          placeholder="请输入模板名称"
          v-model="createViewObj.templateName"
        />
        <div style="height: 5px"></div>
        <label>模板描述：</label>
        <magic-textarea
          placeholder="请输入模板描述"
          :value.sync="createViewObj.templateDecs"
          style="width: 100%; height: 100%; margin: 2px"
        />
        <div style="height: 5px"></div>
        <label>模板类型：</label>
        <magic-select
          placeholder="请选择模板类型"
          :options="validates"
          :value.sync="createViewObj.templateType"
          default-value="pass"
        />
      </template>
      <template #buttons>
        <button class="ma-button active" @click="createView">
          {{ createViewObj.formId ? "修改" : "创建" }}
        </button>
        <button class="ma-button" @click="createViewObj.visible = false">
          取消
        </button>
      </template>
    </magic-dialog>
  </div>
</template>

<script>
import bus from "../../scripts/bus.js";
import MagicTree from "../common/magic-tree.vue";
import request from "@/magic-editor/api/request.js";
import MagicDialog from "@/magic-editor/components/common/modal/magic-dialog.vue";
import MagicSelect from "@/magic-editor/components/common/magic-select.vue";
import MagicTextarea from "@/magic-editor/components/common/magic-textarea.vue";
import MagicInput from "@/magic-editor/components/common/magic-input.vue";
import MagicGroupChoose from "@/magic-editor/components/resources/magic-group-choose.vue";
import {
  replaceURL,
  download as downloadFile,
  requestGroup,
  goToAnchor,
  deepClone,
} from "@/magic-editor/scripts/utils.js";
import MagicTextIcon from "@/magic-editor/components/common/magic-text-icon";
import {
  formRelease,
  listInfo,
  moveTemplate,
  moveNode,
  deleteStructure,
  addOrUpdateStructure,
  getFormTree,
} from "@/api/tool/form.js";
import { createDesigner } from "@/components/form-designer/designer";
import { addInfo, delInfo } from "@/api/columnEditApi/columnEditApi";
export default {
  name: "MagicMenuList",
  props: {
    apis: Array,
  },
  components: {
    MagicTextIcon,
    MagicTree,
    MagicDialog,
    MagicTextarea,
    MagicInput,
    MagicSelect,
    MagicGroupChoose,
  },
  data() {
    return {
      validates: [
        { value: 1, text: "表单" },
        { value: 2, text: "视图" },
      ],
      bus: bus,
      // 分组list数据
      listGroupData: [],
      nodeItem: {
        id: null,
        nodeName: "",
        parentId: null,
      },
      show: false,
      dialogType: "add",
      // 接口list数据
      listChildrenData: [],
      // 分组+接口tree数据
      tree: [],
      apiCopyGroupChooseVisible: false,

      srcItem: {},

      // 换成一个临时对象，修改使用
      tempGroupObj: {},
      // 当前打开的文件
      currentFileItem: {},
      // 绑定给magic-tree组件，用来触发子组件强制更新
      forceUpdate: true,
      // 是否展示tree-loading
      showLoading: true,
      dragging: false,
      draggableItem: {},
      draggableTargetItem: {},
      // 缓存一个openId
      tmpOpenId: [],
      createViewObj: {
        formId: null,
        templateDecs: "",
        templateName: "",
        structureId: null,
        templateType: 1,
        visible: false,
      },
    };
  },
  methods: {
    addNode(id) {
      this.nodeItem = {
        id: null,
        nodeName: "",
        parentId:id,
      };
      this.show = true;
    },
    editNode(item) {
      this.nodeItem = {...item,nodeName:item.label};
      this.show = true;
    },
    // 新增一个模板
    createView() {
      this.createViewObj.visible = false;
      addInfo(this.createViewObj).then((e) => {
        this.initData();
      });
    },
    createNode() {
      addOrUpdateStructure(this.nodeItem).then((res) => {
        bus.$emit("status", "操作成功");
        this.show = false;
        this.initData();
      });
    },
    // 打开新建分组弹窗
    openCreateViewModal(id) {
      this.createViewObj = {
        formId: null,
        templateDecs: "",
        templateName: "",
        structureId: id,
        templateType: 1,
        visible: true,
      };
    },
    draggable(item, event, type) {
      switch (type) {
        // 开始拖拽
        case "dragstart":
          this.draggableItem = item;
          break;
        // 拖拽到某个元素上
        case "dragenter":
          this.draggableTargetItem = item;
          this.dragging = true;
          break;
        // 结束拖拽
        case "dragend":
          this.dragging = false;
          // 目标对象必须是分组
          if (this.draggableTargetItem.folder === true) {
            // 移动分组
            if (
              this.draggableItem.folder === true &&
              this.draggableItem.parentNodeId !==
                this.draggableTargetItem.nodeId &&
              this.draggableItem.nodeId !== this.draggableTargetItem.nodeId
            ) {
              // 检测移动的目录是否是自己的子目录
              let checkChildrenFolder = (arr) => {
                let flag = arr.some(
                  (item) => item.nodeId === this.draggableTargetItem.nodeId
                );
                if (flag) {
                  return flag;
                }
                for (let i = 0; i < arr.length; i++) {
                  if (arr[i].children && arr[i].children.length > 0) {
                    if (checkChildrenFolder(arr[i].children || [])) {
                      return true;
                    }
                  }
                }
                return false;
              };
              if (!this.draggableItem.children || checkChildrenFolder(this.draggableItem.children) === false) {
                let params = JSON.parse(JSON.stringify(this.draggableItem));
                params.parentNodeId = this.draggableTargetItem.nodeId;
                bus.$emit("status", `准备移动分组「${params.label}」`);
                moveNode({
                  nodeId: params.nodeId,
                  parentNodeId: params.parentNodeId,
                })
                  .then((res) => {
                    this.initData();
                    bus.$emit("status", `分组「${params.label}」移动成功`);
                  })
                  .catch((err) => {
                    bus.$emit("status", `分组「${params.label}」移动失败`);
                  });
              } else {
                this.$magicAlert({
                  content: `不能移到${this.draggableTargetItem.label}`,
                });
              }
            } else if (
              this.draggableItem.parentNodeId !==
              this.draggableTargetItem.nodeId
            ) {
              // 移动接口
              // 接口不能在目标分组的第一级children里
              if (!this.draggableTargetItem.children ||
                this.draggableTargetItem.children.some(
                  (item) => item.nodeId === this.draggableItem.nodeId
                ) === false
              ) {
                bus.$emit(
                  "status",
                  `准备移动模板「${this.draggableItem.label}」`
                );

                moveTemplate({
                  nodeId: this.draggableTargetItem.nodeId,
                  formId: this.draggableItem.data.formId,
                })
                  .then((res) => {
                    this.initData();
                    bus.$emit(
                      "status",
                      `模板「${this.draggableItem.label}」移动成功`
                    );
                  })
                  .catch((err) => {
                    bus.$emit(
                      "status",
                      `模板「${this.draggableItem.label}」移动失败`
                    );
                  });
              }
            }
          }
          break;
      }
    },
    doSearch(keyword) {
      keyword = keyword.toLowerCase();
      let loopSearch = (row, parentName, parentPath) => {
        if (row.folder && row.children) {
          row.children.forEach((it) => loopSearch(it, row.label));
          row._searchShow =
            (row.label || "").toLowerCase().indexOf(keyword) > -1 ||
            row.children.some((it) => it._searchShow);
        } else {
          row._searchShow =
            replaceURL(parentName).toLowerCase().indexOf(keyword) > -1 || replaceURL(row.label).toLowerCase().indexOf(keyword) > -1;
        }
      };
      this.tree.forEach((it) => loopSearch(it, "", ""));
      this.changeForceUpdate();
    },
    open(item) {
      if (item.templateJson) {
        item.temps.temJson = JSON.parse(item.templateJson);
      } else {
        let designer = createDesigner(this);
        let widgetList = deepClone(designer.widgetList);
        let formConfig = deepClone(designer.formConfig);
        item.temps.temJson = JSON.parse(
          JSON.stringify({ widgetList, formConfig }, null, "  ")
        );
      }
      bus.$emit("status", `查看视图模板「${item.templateName}」详情`);
      bus.$emit("open", item);
      this.currentFileItem = item;
    },
    // 初始化数据
    initData() {
      bus.$emit("status", "正在初始化视图列表...");
      this.showLoading = true;
      this.tree = [];
      return new Promise((resolve) => {
        getFormTree().then((res) => {
          function func(items, lavel) {
            items.forEach((item) => {
              // item.textIcon = this.getMenuType(item.menuType)
              if (item.type=='node') {
                item.folder = true;
                item.opened = false;
                item.lavel = lavel;
                item.id=item.nodeId
                if(item.children&&item.children.length>0){
                  func(item.children, item.lavel + 1);
                }
              } else {
                item.folder = false;
                item.opened = false;
                item.lavel = lavel;
                item.id=item.nodeId
                if (item.data) {
                  item.data.temps = {
                    id: item.data.formId,
                    name: item.data.templateName,
                    type: "view",
                    editFlag: false,
                  };
                }
              }
            });
          }
          let data = res.data;
          func(data, 0);
          this.tree = data;
          bus.$emit("status", "接口分组加载完毕");
          this.showLoading = false;
          resolve();
        });
      });
    },
    folderRightClickHandle(event, item) {
      console.log(item)
      this.$set(item, "selectRightItem", true);
      this.$magicContextmenu({
        menus: [
          {
            label: "新增节点",
            icon: "ma-icon-plus",
            divided: true,
            onClick: () => {
              this.addNode(item.nodeId);
            },
          },
          {
            label: "新增模板",
            icon: "ma-icon-plus",
            divided: true,
            onClick: () => {
              this.openCreateViewModal(item.nodeId);
            },
          },
          {
            label: "编辑节点",
            icon: "ma-icon-refresh",
            divided: true,
            onClick: () => {
              this.editNode(item);
            },
          },
          {
            label: "删除节点",
            icon: "ma-icon-delete",
            divided: true,
            onClick: () => {
              bus.$emit("status", `准备删除节点「${item.label}」`);
              this.$magicConfirm({
                title: "删除节点",
                content: `是否要删除节点「${item.label}」`,
                onOk: () => {
                  deleteStructure(item.nodeId).then((res) => {
                    bus.$emit("status", `节点「${item.label}」已删除`);
                    this.initData();
                  });
                },
              });
            },
          },
        ],
        event,
        zIndex: 9999,
        destroy: () => {
          this.$set(item, "selectRightItem", false);
        },
      });
      return false;
    },
    // 文件右键菜单
    fileRightClickHandle(event, item) {
      this.$set(item, "selectRightItem", true);
      this.$magicContextmenu({
        menus: [
          {
            label: "设计模板",
            icon: "ma-icon-refresh",
            divided: true,
            onClick: () => {
              if (item.templateJson) {
                item.temps.temJson = JSON.parse(item.templateJson);
              } else {
                let designer = createDesigner(this);
                let widgetList = deepClone(designer.widgetList);
                let formConfig = deepClone(designer.formConfig);
                item.temps.temJson = JSON.parse(
                  JSON.stringify({ widgetList, formConfig }, null, "  ")
                );
              }
              bus.$emit("editView", item);
            },
          },
          {
            label: "编辑模板",
            icon: "ma-icon-refresh",
            divided: true,
            onClick: () => {
              this.createViewObj = item;
              this.$set(this.createViewObj, "visible", true);
            },
          },
          {
            label: "发布视图",
            icon: "ma-icon-push",
            divided: true,
            onClick: () => {
              formRelease(item.formId).then((e) => {
                this.$message.success(e.data ? `发布成功` : `发布失败`);
                item.isNew = true;
              });
            },
          },
          {
            label: "删除视图",
            icon: "ma-icon-delete",
            divided: true,
            onClick: () => {
              this.deleteForm(item);
            },
          },
        ],
        event,
        zIndex: 9999,
        destroy: () => {
          this.$set(item, "selectRightItem", false);
        },
      });
      return false;
    },
    deleteForm(item) {
      bus.$emit("status", `准备删除视图「${item.templateName}」`);
      this.$magicConfirm({
        title: "删除视图",
        content: `是否要删除视图「${item.templateName}」`,
        onOk: () => {
          delInfo(item.formId).then((res) => {
            bus.$emit("status", `视图「${item.templateName}」已删除`);
            this.initData();
          });
        },
      });
    },
    // 强制触发子组件更新
    changeForceUpdate() {
      this.forceUpdate = !this.forceUpdate;
    },
  },
  mounted() {
    this.bus.$on("logout", () => (this.tree = []));
    this.bus.$on("opened", (item) => {
      this.currentFileItem = item;
    });
    this.bus.$on("refresh-resource", () => {
      this.initData();
    });
  },
};
</script>

<style>
@import "./magic-resource.css";

.loading {
  color: var(--color);
  position: absolute;
  text-align: center;
  width: 100%;
  top: 50%;
  margin-top: -20px;
}
.loading i {
  color: var(--color);
  font-size: 20px;
}
.loading .icon .ma-icon {
  padding: 0;
}
.loading .icon {
  width: 20px;
  margin: 0 auto;
  line-height: normal;
  animation: rotate 1s linear infinite;
}
.no-data {
  color: var(--color);
  position: absolute;
  top: 50%;
  left: 50%;
  margin-left: -20px;
}
</style>
<style scoped>
.redDots {
  /* width: 6px; */
  /* height: 6px; */
  border-radius: 50%;
  /* background-color: red; */
  color: red;
  position: absolute;
  right: -10px;
  top: 0;
}
</style>
