<template>
  <div style="height: calc(100vh - 50px);">
    <VFormDesigner :in-designer="designer" ref="vForm" />
    <vfDialog ref="linkDialog"/>
  </div>

</template>

<script>
import VFormDesigner from '@/components/form-designer/index';
import { getInfo } from '@/api/tool/form';
import { createDesigner } from '@/components/form-designer/designer';
import { deepClone } from '@/utils/util';
import vfDialog from '@/views/tool/variantform/vfDialog.vue'
export default {
  name: 'index',
  components: {
    VFormDesigner, vfDialog
  },
  props: {
    json: null,
    formId: null,
  },
  provide() {
    return {
      // formId: this.formId,
      formObj: {
        formId: this.formId,
      },
    };
  },
  data() {
    return {};
  },
  beforeRouteLeave(to, from, next) {
    this.$confirm('即将离开页面, 是否保存?', '警告', {
      distinguishCancelAndClose: true,
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
      .then((res) => {
        this.$refs.vForm.$refs.toolbal.generateSave();
        next();
      })
      .catch((action) => {
        if (action == 'cancel') {
          next();
        }
      });
  },
  computed: {
    designer() {
      if (this.json === null || this.json === '') {
        let designer = createDesigner(this);
        let widgetList = deepClone(designer.widgetList);
        let formConfig = deepClone(designer.formConfig);
        return JSON.parse(
          JSON.stringify({ widgetList, formConfig }, null, '  '),
        );
      }
      return JSON.parse(this.json);
    },
  },
  methods:{
    openVfDialog(hasOpen, formId, data) {
      let dataKey = 'openVfDialog-'+formId
      localStorage.setItem(dataKey, JSON.stringify(data))
      getInfo(formId, {})
        .then((res) => {
          let formJson = ""
          if (res.data.templateJson) {
            formJson = JSON.parse(res.data.templateJson);
          } else {
            let designer = createDesigner(this);
            let widgetList = deepClone(designer.widgetList);
            let formConfig = deepClone(designer.formConfig);
            formJson = JSON.parse(
              JSON.stringify({ widgetList, formConfig }, null, '  '),
            );
          }
          this.$refs.linkDialog.open(hasOpen, formJson, dataKey)
        })
        .catch((err) => {
        });
    },
  }
};
</script>

<style scoped>
#app {
  height: 100%;
}
</style>
