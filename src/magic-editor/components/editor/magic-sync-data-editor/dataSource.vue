<template>
  <el-form>
    <div class="a-center pd-b10">
      <el-form ref="form" label-width="80px">
        <el-col :span="12">
          <el-form-item label="数据源">
            <el-select v-model="value.targetDatasource.datasourceId"
                       placeholder="请选择数据源"
                       @change="targetDatasourcesChange">
              <el-option
                v-for="item in dataSources"
                :key="item.value"
                :label="item.text"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="同步表">
            <el-select v-model="value.targetDatasource.table"
                       placeholder="请选择同步表"
                       class="flex-1"
                       clearable
                       filterable
                       @change="tableChange">
              <el-option
                v-for="item in tableList"
                :key="item.value"
                :label="item.text"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="DDL" v-if="!value.targetDatasource.table">
            <el-input
              type="textarea"
              :rows="5"
              placeholder="请输入创建表DLL语句"
              v-model="value.targetDatasource.tableDDL"/>
            <el-button v-if="value.targetDatasource.tableDDL" type="success" @click="parseField">解析字段</el-button>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="同步字段">
            <el-select v-model="value.targetDatasource.columns"
                       placeholder="请选择同步字段"
                       class="flex-1"
                       multiple
                       collapse-tags
                       filterable
                       clearable
                       @change="tableChange">
              <el-option
                v-for="item in fieldList"
                :key="item.value"
                :label="item.text"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="主键字段">
            <el-select v-model="value.targetDatasource.splitPk"
                       placeholder="请选择主键字段"
                       class="flex-1"
                       collapse-tags
                       clearable
                       @change="tableChange">
              <el-option
                v-for="item in fieldList"
                :key="item.value"
                :label="item.text"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="写入模式">
            <el-select v-model="value.targetDatasource.writeMode"
                       placeholder="请选择写入模式"
                       class="flex-1"
                       clearable
                       @change="tableChange">
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.text"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-form>
    </div>
  </el-form>
</template>

<script>
import { analysis_create_sql } from './utils.js';
import MagicSelect from '@/magic-editor/components/common/magic-select.vue';
import MagicTextarea from '@/magic-editor/components/common/magic-textarea.vue';
import { getDatasouceTableInfo } from '@/api/interfaces/interfaces';
export default {
  components: {
    MagicSelect,
    MagicTextarea,
  },
  props: {
    value: null,
    dataSources: Array,
  },
  data() {
    return {
      tableList: [],
      fieldList: [],
      options: [
        {
          value: 'insert',
          text: '全量更新',
        },
        {
          value: 'update',
          text: '增量更新(mysql)',
        },
        {
          value: 'replace',
          text: '增量更新(其他)',
        },
      ],
    };
  },
  mounted() {
    if (this.value.targetDatasource.datasourceId) {
      getDatasouceTableInfo({
        datasourceKey: this.value.targetDatasource.datasourceId,
      }).then((res) => {
        res.data.forEach((item) => {
          item.value = item.tableName;
          item.text = item.tableName;
        });
        this.tableList = res.data;
        if (this.value.targetDatasource.table) {
          this.fieldList = this.tableList
            .find((item) => item.value === this.value.targetDatasource.table)
            .columns.map((item) => {
              return {
                text: item,
                value: item,
              };
            });
        }
      });
    }
  },
  methods: {
    targetDatasourcesChange(e) {
      getDatasouceTableInfo({ datasourceKey: e }).then((res) => {
        res.data.forEach((item) => {
          item.value = item.tableName;
          item.text = item.tableName;
        });
        this.value.targetDatasource.table = '';
        this.tableList = res.data;
      });
    },
    tableChange(e) {
      this.fieldList = this.tableList
        .find((item) => item.value === e)
        .columns.map((item) => {
          return {
            text: item,
            value: item,
          };
        });
      this.value.targetDatasource.columns = [];
      this.value.targetDatasource.splitPk = '';
    },
    parseField() {
      try {
        let list = analysis_create_sql(
          this.value.targetDatasource.tableDDL,
        ).field_array;
        list.forEach((item) => {
          item.text = item.annotation;
          item.value = item.name;
        });
        this.fieldList = list;
        this.value.targetDatasource.columns = [];
        this.value.targetDatasource.splitPk = '';
        this.$message.success("解析成功")
      }catch (e) {
        this.$message.error("解析失败");
      }
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-input__inner {
  background: var(--select-hover-background) !important;
  border: 1px solid var(--input-border-color) !important;
  position: relative !important;
  display: inline-block !important;
  background: var(--select-background) !important;
  height: 22px !important;
  line-height: 22px !important;
  width: 100% !important;
  font-size: 12px !important;
}
::v-deep .el-select {
  width: 100%;
}
::v-deep .el-input__icon {
  line-height: 24px;
}
::v-deep .el-tag--small {
  height: 18px;
}
</style>
