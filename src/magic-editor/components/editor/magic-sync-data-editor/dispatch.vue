<template>
  <div>
    <el-form ref="form" label-width="80px">
      <el-form-item label="执行周期">
        <el-input @click.native="show = true" v-model="value.cron" placeholder="请配置执行周期"></el-input>
      </el-form-item>
    </el-form>
    <el-dialog :visible="show"  width="50%" :show-close="false">
      <div style="height: 800px; overflow: auto">
        <crontab :expression="expression" @hide="show = false" @fill="fill">
        </crontab>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import crontab from '@/components/Crontab';
import MagicInput from '@/magic-editor/components/common/magic-input.vue';
export default {
  components: {
    crontab,
    MagicInput,
  },
  props: {
    value: null,
  },
  data() {
    return {
      show: false,
      expression: null,
    };
  },
  mounted() {
    this.expression = this.value.cron;
  },
  methods: {
    fill(e) {
      this.value.cron = e;
      console.log(e);
      console.log(this.expression);
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-input__inner {
  background: var(--select-hover-background) !important;
  border: 1px solid var(--input-border-color) !important;
  position: relative !important;
  display: inline-block !important;
  background: var(--select-background) !important;
  height: 22px !important;
  line-height: 22px !important;
  width: 100% !important;
  font-size: 12px !important;
}
::v-deep .el-select {
  width: 100%;
}
::v-deep .el-input__icon {
  line-height: 24px;
}
::v-deep .el-tag--small {
  height: 18px;
}


</style>
