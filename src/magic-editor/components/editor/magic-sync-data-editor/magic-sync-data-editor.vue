<template>
  <div class="pd-20" style="height: calc(100vh - 210px)">
    <el-steps :active="curSteps" finish-status="success" simple>
      <el-step :title="item" v-for="item in componentTitle" :key="item" />
    </el-steps>

    <div style="height: 530px; padding: 20px; overflow: auto">
      <transition mode="out-in" :name="transitionName">
        <keep-alive>
          <component
            :is="componentArr[curSteps]"
            v-model="value"
            :dataSources="dataSources"
          />
        </keep-alive>
      </transition>
    </div>

    <div style="display: flex; justify-content: center; padding: 20px 30px">
      <el-button v-show="curSteps > 0" @click="prev">上一步</el-button>
      <el-button v-show="curSteps < componentArr.length - 1" @click="next"
        >下一步</el-button>
    </div>
  </div>
</template>

<script>
import dataSource from './dataSource';
import targetSource from './targetSource';
import dispatch from './dispatch';
import bus from '@/magic-editor/scripts/bus.js';
import request from '@/magic-editor/api/request.js';
export default {
  props: {
    value: {
      type: null,
      default: () => {
        return {
          type: 'off-line',
          cron: '',
          enable: true,
          targetDatasource: {
            datasourceId: '',
            table: '',
            columns: [],
            preSql: '',
            writeMode: '',
          },
          datasource: {
            datasourceId: '',
            table: '',
            tableDDL: '',
            columns: [],
            splitPk: '',
            where: '',
          },
        };
      },
    },
  },
  components: {
    dataSource,
    targetSource,
    dispatch,
  },
  data() {
    return {
      curSteps: 0,
      componentTitle: ['写入数据源', '读取数据源', '调度参数'],
      componentArr: ['dataSource', 'targetSource', 'dispatch'],
      transitionName: '',
      dataSources: [],
    };
  },
  mounted() {
    request.send('resource').success((data) => {
      this.dataSources =
        data.datasource.children[0].children
          .map((item) => {
            return {
              value: item.node.key,
              text: item.node.name,
            };
          })
          .filter((item) => item.value) || [];
    });
  },
  methods: {
    input(value) {
      this.value = JSON.parse(value)
    },
    prev() {
      if (this.curSteps > 0) {
        this.curSteps--;
        this.transitionName = 'pre';
      }
    },
    next() {
      if (this.curSteps < this.componentArr.length - 1) {
        this.curSteps++;
        this.transitionName = 'next';
      }
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-scrollbar__bar.is-horizontal {
  display: none;
}
.next-enter {
  opacity: 0;
  webkit-transform: translateX(100%);
  -moz-transform: translateX(100%);
  -ms-transform: translateX(100%);
  -o-transform: translateX(100%);
  transform: translateX(100%);
}
.next-leave-to {
  opacity: 0;
  webkit-transform: translateX(-100%);
  -moz-transform: translateX(-100%);
  -ms-transform: translateX(-100%);
  -o-transform: translateX(-100%);
  transform: translateX(-100%);
  position: absolute;
}
.pre-enter {
  opacity: 0;
  webkit-transform: translateX(-100%);
  -moz-transform: translateX(-100%);
  -ms-transform: translateX(-100%);
  -o-transform: translateX(-100%);
  transform: translateX(-100%);
}
.pre-leave-to {
  opacity: 0;
  webkit-transform: translateX(100%);
  -moz-transform: translateX(100%);
  -ms-transform: translateX(100%);
  -o-transform: translateX(100%);
  transform: translateX(100%);
  position: absolute;
}
.next-enter-active,
.next-leave-active,
.pre-enter-active,
.pre-leave-active {
  // transform: translateX(0px);
  transition: all 0.3s ease;
}
</style>
