<template>
  <el-form>
    <div class="a-center pd-b10">
      <el-form ref="form" label-width="80px">
        <el-col :span="12">
          <el-form-item label="数据源">
            <el-select v-model="value.datasource.datasourceId"
                       placeholder="请选择数据源"
                       @change="dataSourcesChange">
              <el-option
                v-for="item in dataSources"
                :key="item.value"
                :label="item.text"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="同步表">
            <el-select v-model="value.datasource.table"
                       placeholder="请选择同步表"
                       class="flex-1"
                       clearable
                       filterable
                       @change="tableChange">
              <el-option
                v-for="item in tableList"
                :key="item.value"
                :label="item.text"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="同步字段">
            <el-select v-model="value.datasource.columns"
                       placeholder="请选择同步字段"
                       class="flex-1"
                       multiple
                       filterable
                       collapse-tags
                       clearable
                       @change="tableChange">
              <el-option
                v-for="item in fieldList"
                :key="item.value"
                :label="item.text"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="where">
            <el-input
              type="textarea"
              :rows="5"
              class="flex-1"
              placeholder="请输入查询where条件"
              v-model="value.targetDatasource.where"/>
          </el-form-item>
        </el-col>
      </el-form>
    </div>
  </el-form>
</template>

<script>
import MagicSelect from '@/magic-editor/components/common/magic-select.vue';
import MagicTextarea from '@/magic-editor/components/common/magic-textarea.vue';
import { getDatasouceTableInfo } from '@/api/interfaces/interfaces';
export default {
  components: {
    MagicSelect,
    MagicTextarea,
  },
  props: {
    value: null,
    dataSources: Array,
  },
  data() {
    return {

      tableList: [],
      fieldList: [],
    };
  },
  mounted() {
    if (this.value.datasource.datasourceId) {
      getDatasouceTableInfo({
        datasourceKey: this.value.datasource.datasourceId,
      }).then((res) => {
        res.data.forEach((item) => {
          item.value = item.tableName;
          item.text = item.tableName;
        });
        this.tableList = res.data;
        if (this.value.datasource.table) {
          this.fieldList = this.tableList
            .find((item) => item.value === this.value.datasource.table)
            .columns.map((item) => {
              return {
                text: item,
                value: item,
              };
            });
        }
      });
    }
  },
  methods: {
    dataSourcesChange(e) {
      getDatasouceTableInfo({ datasourceKey: e }).then((res) => {
        res.data.forEach((item) => {
          item.value = item.tableName;
          item.text = item.tableName;
        });
        this.value.datasource.table = '';
        this.tableList = res.data;
      });
    },
    tableChange(e) {
      this.fieldList = this.tableList
        .find((item) => item.value === e)
        .columns.map((item) => {
          return {
            text: item,
            value: item,
          };
        });
      this.value.datasource.columns = [];
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-input__inner {
  background: var(--select-hover-background) !important;
  border: 1px solid var(--input-border-color) !important;
  position: relative !important;
  display: inline-block !important;
  background: var(--select-background) !important;
  height: 22px !important;
  line-height: 22px !important;
  width: 100% !important;
  font-size: 12px !important;
}
::v-deep .el-select {
  width: 100%;
}
::v-deep .el-input__icon {
  line-height: 24px;
}
::v-deep .el-tag--small {
  height: 18px;
}
</style>
