<script src="../../../utils/localStorage-utils.js"></script>
<template>
  <div class="ma-editor-container">
    <div class="ma-wrapper d-flex">
      <ul
        ref="scrollbar"
        class="ma-tab not-select flex-1"
        style="overflow-y: hidden"
      >
        <li
          v-for="(item, index) in scripts"
          :key="'opened_script_' + index"
          :class="{
            selected: currentInfo === item,
            draggableTargetItem: item.ext.tabDraggable,
          }"
          :title="
            item.displayName || item.name || item.menuName || item.temps.name
          "
          :id="'ma-tab-item-' + item.temps.id"
          @click="open(item)"
          @contextmenu.prevent="(e) => tabsContextmenuHandle(e, item, index)"
          @mousedown.middle.stop="close(item.id || item.tmp_id)"
          :draggable="true"
          @dragenter="(e) => tabDraggable(item, e, 'dragenter')"
          @dragstart.stop="(e) => tabDraggable(item, e, 'dragstart')"
          @dragend.stop="(e) => tabDraggable(item, e, 'dragend')"
          @dragover.prevent
        >
          <magic-text-icon
            v-if="item._type === 'api'"
            v-model="item.method"
            style="margin-top: -4px"
          />
          <magic-text-icon
            v-else-if="item.viewType != null"
            :value="item.viewType ? item.viewType : item.menuType"
            style="margin-top: -4px"
          />
          <magic-text-icon
            v-else-if="item.temps != null && item.temps.type === 'view'"
            :value="item.templateType == '1' ? 'FORM' : 'FV'"
            style="margin-top: -4px"
          />
          <magic-text-icon
            v-else-if="item.temps != null && item.temps.type === 'timer'"
            value="timer"
          />
          <magic-text-icon
            v-else-if="item.temps != null && item.temps.type === 'initTask'"
            value="initTask"
          />
          <magic-text-icon
            v-else-if="item.temps != null && item.temps.type === 'listen'"
            value="listen"
          />
          <magic-text-icon
            v-else-if="item.temps != null && item.temps.type === 'sync-data'"
            value="sync"
          />
          <magic-text-icon
            v-else-if="item.temps != null && item.temps.type === 'menu'"
            :value="item.viewType ? item.viewType : item.menuType"
          />
          <magic-text-icon
            v-else-if="item.temps != null && item.temps.type === 'report'"
            value="REPORT"
          />
          <magic-text-icon
            v-else-if="item.temps != null && item.temps.type === 'screen'"
            value="SCREEN"
            style="margin-top: -4px"
          />
          <magic-text-icon
            v-else-if="item.temps != null && item.temps.type === 'assembly'"
            value="assembly"
            style="margin-top: -4px"
          />
          <magic-text-icon
            v-else-if="item.temps != null && item.temps.type === 'flow'"
            value="flow"
            style="margin-top: -4px"
          />

          <magic-text-icon
            v-else-if="item._type !== 'api'"
            value="function"
            style="margin-top: -4px"
          />
          {{
            item.displayName ||
            item.name ||
            item.menuName ||
            item.temps.name ||
            item.taskName ||
            item.objName
          }}

          <i class="ma-icon ma-icon-lock" v-if="item.lock === '1'" />
          <!--   菜单类型是没有临时状态的        -->
          <div v-if="item.temps.type === 'menu' || item.temps.type === 'flow'">
            <span v-show="item.isChange">*</span>
          </div>
          <div
            v-else-if="
              item.temps.type === 'view' || item.temps.type === 'assembly'
            "
          >
            <span
              v-show="
                !item.temps.id || item.templateJson !== item.ext.tmpScript
              "
              >*</span
            >
          </div>
          <div v-else-if="item.temps.type === 'sync-data'">
            <span v-show="!item.temps.id || item.config !== item.ext.tmpScript"
              >*</span
            >
          </div>
          <div v-else-if="item.temps.type === 'timer'">
            <span v-show="item.magicScript !== item.ext.tmpScript">*</span>
          </div>
          <div v-else-if="item.temps.type === 'initTask'">
            <span
              v-show="item.isChange || item.magicScript !== item.ext.tmpScript"
              >*</span
            >
          </div>
          <div v-else-if="item.temps.type === 'listen'">
            <span
              v-show="!item.temps.id || item.taskScript !== item.ext.tmpScript"
              >*</span
            >
          </div>

          <div v-else>
            <span v-show="!item.temps.id || item.script != item.ext.tmpScript"
              >*</span
            >
          </div>
          <i class="ma-icon ma-icon-close" @click.stop="close(item.temps.id)" />
        </li>
      </ul>
      <div v-if="currentInfo != null">
        <div v-if="currentInfo.temps.type === 'view'" class="operation">
          <span
            title="保存（Ctrl+S）"
            class="mr-lr5"
            v-if="!currentInfo.temps.editFlag"
            @click="onSaveView(currentInfo)"
          >
            <i class="ma-icon ma-icon-save"></i>
          </span>
          <template v-if="currentInfo.temps.editFlag">
            <span
              title="清空"
              class="mr-lr5"
              @click="bus.$emit('view-all-clear')"
            >
              <i class="ma-icon ma-icon-delete"></i>
            </span>
            <span title="导出" class="mr-lr5" @click="bus.$emit('view-export')">
              <i class="ma-icon ma-icon-download"></i>
            </span>
            <span title="导入" class="mr-lr5" @click="bus.$emit('view-import')">
              <i class="ma-icon ma-icon-upload"></i>
            </span>
            <span title="预览" class="mr-lr5" @click="onSwitchViewStatus">
              <i class="el-icon-view font-16"></i>
            </span>
          </template>
          <span title="编辑" class="mr-lr5" v-else @click="viewEditor">
            <i class="el-icon-edit" style="font-size: 16px"></i>
          </span>
        </div>
        <div
          v-else-if="currentInfo.temps.type === 'assembly'"
          class="operation"
        >
          <span
            title="保存（Ctrl+S）"
            class="mr-lr5"
            @click="onSaveAssembly(currentInfo)"
          >
            <i class="ma-icon ma-icon-save"></i>
          </span>
          <span
            v-if="currentInfo.temps.editFlag"
            title="预览"
            class="mr-lr5"
            @click="currentInfo.temps.editFlag = false"
          >
            <i class="el-icon-view" style="font-size: 16px"></i>
          </span>
          <span
            title="编辑"
            class="mr-lr5"
            v-else
            @click="currentInfo.temps.editFlag = true"
          >
            <i class="el-icon-edit" style="font-size: 16px"></i>
          </span>
        </div>
        <div v-else-if="currentInfo.temps.type === 'flow'" class="operation">
          <span title="保存（Ctrl+S）" class="mr-lr5" @click="onSaveFlow">
            <i class="ma-icon ma-icon-save"></i>
          </span>
        </div>
      </div>
      <div v-if="currentInfo != null && currentInfo.temps.type === 'screen'">
        <span
          title="保存（Ctrl+S）"
          class="mr-lr5"
          @click="onSaveScreen(currentInfo)"
        >
          <i class="ma-icon ma-icon-save"></i>
        </span>
        <template v-if="currentInfo.temps.editFlag">
          <span title="清空" class="mr-lr5" @click="clear">
            <i class="ma-icon ma-icon-delete"></i>
          </span>
          <span title="预览" class="mr-lr5" @click="onSwitchScreenStatus">
            <i class="el-icon-view" style="font-size: 16px"></i>
          </span>
        </template>
        <span
          title="编辑"
          class="mr-lr5"
          v-else
          @click="currentInfo.temps.editFlag = true"
        >
          <i class="el-icon-edit" style="font-size: 16px"></i>
        </span>
      </div>
      <div v-if="currentInfo != null && currentInfo.temps.type === 'report'">
        <span
          title="保存（Ctrl+S）"
          class="mr-lr5"
          @click="onSaveReport(currentInfo)"
        >
          <i class="ma-icon ma-icon-save"></i>
        </span>
        <template v-if="currentInfo.temps.editFlag">
          <span title="预览" class="mr-lr5" @click="onSwitchReportStatus">
            <i class="el-icon-view" style="font-size: 16px"></i>
          </span>
        </template>
        <span title="编辑" class="mr-lr5" v-else @click="viewEditor">
          <i class="el-icon-edit" style="font-size: 16px"></i>
        </span>
      </div>
    </div>
    <template v-for="item in scripts">
      <template
        v-if="
          ![
            'api',
            'assembly',
            'timer',
            'function',
            'listen',
            'initTask',
          ].includes(item.temps.type)
        "
      >
        <div
          :style="{ position: item == currentInfo ? 'static' : 'absolute' }"
          :key="item.temps.id"
          style="background-color: #ffffff"
        >
          <div
            v-show="item == currentInfo"
            style="height: calc(100vh - 60px); overflow: auto"
          >
            <template v-if="item.temps.type == 'view'">
              <template v-if="item.temps.editFlag == true">
                <!--   视图模板编辑组件   -->
                <VForm
                  :key="'vfrom-' + item.temps.id"
                  :form-id="item.formId"
                  :json="item.templateJson"
                />
              </template>
              <template v-if="item.temps.editFlag == false">
                <!--   视图模板预览组件   -->
                <VFormRender
                  v-if="item.templateJson"
                  style="padding: 10px"
                  :key="'vformRender' + item.temps.id"
                  :form-json="parseTemplateJson(item.templateJson)"
                  :form-data="{}"
                />
                <template v-else> 空模板，请编辑该模板在预览 </template>
              </template>
            </template>
            <div v-if="item.temps.type == 'menu'">
              <keep-alive>
                <!--   路由编辑组件   -->
                <menuView :key="item.menuId" :formId="item.formId"></menuView>
              </keep-alive>
            </div>
            <div v-if="item.temps.type == 'screen'">

            </div>
            <template v-if="item.temps.type == 'report'">
              <!--   视图模板编辑组件   -->

            </template>
            <template v-if="item.temps.type == 'flow'">
              <!-- <Flow
                v-if="item.objId == info.objId"
                ref="flow"
                :id="item.objId"
              ></Flow> -->
            </template>
            <!-- 数据同步 组件 -->
            <MagicSyncDataEditor
              v-if="item.temps.type == 'sync-data'"
              v-model="syncInfo.config"
            />
          </div>
        </div>
      </template>
    </template>
    <!--  代码编写组件   -->
    <div
      v-show="
        currentInfo !== null &&
        (currentInfo.temps.type === 'api' ||
          currentInfo.temps.type === 'timer' ||
          currentInfo.temps.type === 'initTask' ||
          currentInfo.temps.type === 'function' ||
          currentInfo.temps.type === 'listen')
      "
      :style="{
        position:
          currentInfo != null &&
          (currentInfo.temps.type === 'api' ||
            currentInfo.temps.type === 'timer' ||
            currentInfo.temps.type === 'initTask' ||
            currentInfo.temps.type === 'function' ||
            currentInfo.temps.type === 'listen')
            ? 'static'
            : 'absolute',
      }"
    >
      <div ref="editor" style="height: 100vh"></div>
    </div>
    <!--  在线组件代码编写组件   -->
    <div
      v-show="currentInfo !== null && currentInfo.temps.type === 'assembly'"
      :style="{
        position:
          currentInfo !== null && currentInfo.temps.type === 'assembly'
            ? 'static'
            : 'absolute',
      }"
    >
      <div
        ref="assemblyEditor"
        style="height: 100vh"
        v-show="
          currentInfo !== null &&
          currentInfo.temps.type === 'assembly' &&
          currentInfo.temps.editFlag == true
        "
      />
      <div
        class="preview"
        v-if="currentInfo && currentInfo.temps.editFlag == false"
      >
        <code-viewer
          v-if="assemblyFlag"
          :source="currentInfo.templateJson"
        ></code-viewer>
      </div>
    </div>

    <!--  默认界面  -->
    <div v-show="!scripts.length" class="ma-empty-container">
      <div class="ma-hot-key">
        <p>
          保存<em>Ctrl + S</em><br />
          测试<em>Ctrl + Q</em><br />
          代码提示<em>Alt + /</em><br />
          恢复断点<em>F8</em><br />
          步进<em>F6</em><br />
          代码格式化<em>Ctrl + Alt + L</em><br />
          最近打开<em>Ctrl + E</em>
        </p>
      </div>
    </div>

    <magic-dialog
      :title="'历史记录：' + (info && info.name)"
      :value="showHsitoryDialog"
      align="right"
      height="550px"
      maxWidth="inherit"
      padding="none"
      width="1100px"
      :moveable="false"
      @onClose="showHsitoryDialog = false"
    >
      <template #content>
        <magic-history ref="history" />
      </template>
      <template #buttons>
        <button
          class="ma-button active"
          @click="
            () => {
              $refs.history.reset();
              showHsitoryDialog = false;
            }
          "
        >
          恢复
        </button>
        <button class="ma-button" @click="showHsitoryDialog = false">
          关闭
        </button>
      </template>
    </magic-dialog>
  </div>
</template>

<script>
import {
  saveMagicTask,
  saveMagicSyncData,
  executeTask,
  updatePlcTasks,
  saveInitialization,
  doTestInitialization,
} from '@/api/interfaces/interfaces.js';
import * as monaco from 'monaco-editor';
import { initializeMagicScript } from '@/magic-editor/scripts/editor/magic-script.js';
import bus from '@/magic-editor/scripts/bus.js';
import MagicDialog from '@/magic-editor/components/common/modal/magic-dialog.vue';
import MagicHistory from './magic-history.vue';
import request from '@/magic-editor/api/request.js';
import contants from '@/magic-editor/scripts/contants.js';
import * as utils from '@/magic-editor/scripts/utils.js';
import store from '@/magic-editor/scripts/store.js';
import { Parser } from '@/magic-editor/scripts/parsing/parser.js';
import tokenizer from '@/magic-editor/scripts/parsing/tokenizer.js';
import { TokenStream } from '@/magic-editor/scripts/parsing/index.js';
import RequestParameter from '@/magic-editor/scripts/editor/request-parameter.js';
import { CommandsRegistry } from 'monaco-editor/esm/vs/platform/commands/common/commands';
import { KeybindingsRegistry } from 'monaco-editor/esm/vs/platform/keybinding/common/keybindingsRegistry.js';
import { ContextKeyExpr } from 'monaco-editor/esm/vs/platform/contextkey/common/contextkey.js';
import MagicTextIcon from '@/magic-editor/components/common/magic-text-icon';
import VFormRender from '@/components/form-render/index';
import VFormDesigner from '@/components/form-designer/index';
import VForm from '@/magic-editor/components/editor/magic-view-editor';
import { getToken } from '@/utils/auth';
import { delJob } from '@/api/monitor/job';
import { updateTemplateInfoJson } from '@/api/tool/form';
// import ScreenDesigner from '@/views/tool/bigscreenDesigner/designer/index.vue';
// import BigscreenViewer from '@/views/tool/bigscreenDesigner/viewer/index';
// import ReportDesigner from '@/views/tool/excelreport/designer/index.vue';
import ReportViewer from '@/views/tool/excelreport/viewer/index';
import MagicSyncDataEditor from '@/magic-editor/components/editor/magic-sync-data-editor/magic-sync-data-editor';
import { updateAssembly } from '@/api/columnEditApi/columnEditApi';
import { Face3 } from 'three';

export default {
  name: 'MagicScriptEditor',
  components: {
    MagicTextIcon,
    MagicDialog,
    MagicHistory,
    VFormRender,
    VFormDesigner,
    VForm,
    // ScreenDesigner,
    // BigscreenViewer,
    // ReportDesigner,
    ReportViewer,
    MagicSyncDataEditor,
    // Flow,
  },
  data() {
    return {
      bus,
      scripts: [],
      selected: null,
      menuInfo: null,
      viewInfo: null,
      timer: null,
      initTask: null,
      syncInfo: null,
      menuMap: {},
      flowFlag: true,
      // 当前对象
      currentInfo: null,
      info: null,
      editor: null,
      assemblyEditor: null,
      showHsitoryDialog: false,
      assemblyFlag: true,
      // tab拖拽的item
      draggableItem: {},
      draggableTargetItem: {},
      viewLoading: false,
    };
  },
  mounted() {
    this.initEditor();
    this.initAssemblyEditor();
    window.onresize = () => bus.$emit('update-window-size');
    bus.$on('update-window-size', this.layout);
    bus.$on('open', this.open);
    bus.$on('changed', this.changed);
    bus.$on('menuChange', this.menuChange);
    bus.$on('timerChange', this.timerChange);
    bus.$on('initTaskChange', this.initTaskChange);
    bus.$on('setCurrent', this.setCurrent);
    bus.$on('doSave', this.doSave);
    bus.$on('viewHistory', this.viewHistory);
    bus.$on('doTest', this.doTest);
    bus.$on('doContinue', this.doContinue);
    bus.$on('doStepInto', this.doStepInto);
    bus.$on('logout', this.closeAll);
    bus.$on('editView', this.editView);
    bus.$on('view-change', this.viewChange);
    bus.$on('flowChange', this.flowChange);
    bus.$on('flowDataOnLoad', this.flowDataOnLoad);
    bus.$on('ready-delete', () => {
      if (this.info) {
        bus.$emit('delete-api', this.info);
      }
    });
    bus.$on('ws_breakpoint', (rows) => this.onBreakpoint(rows));
    bus.$on('ws_exception', (args) => this.onException(args[0]));

    let javaTypes = {
      String: 'java.lang.String',
      Integer: 'java.lang.Integer',
      Double: 'java.lang.Double',
      Long: 'java.lang.Long',
      Byte: 'java.lang.Byte',
      Short: 'java.lang.Short',
      Float: 'java.lang.Float',
      MultipartFile: 'org.springframework.web.multipart.MultipartFile',
      MultipartFiles: 'java.util.List',
    };
    RequestParameter.setEnvironment(() => {
      let env = {};
      if (this.info && this.info._type === 'api') {
        this.info.parameters.forEach((it) => {
          env[it.name] =
            javaTypes[it.dataType || 'String'] || 'java.lang.Object';
        });
        this.info.paths.forEach((it) => {
          env[it.name] =
            javaTypes[it.dataType || 'String'] || 'java.lang.Object';
        });
      }
      return env;
    });
  },
  methods: {
    // 视图模板点击编辑事件
    viewEditor() {
      //TODO ZhongYuXing  v-loading 没有效果
      // this.viewLoading = true;
      this.currentInfo.temps.editFlag = true;
      // setTimeout(() => this.viewLoading = true, 900)
    },
    parseTemplateJson(json) {
      return json ? JSON.parse(json) : {};
    },
    setCurrent(item) {
      this.currentInfo = item;
    },
    clear() {
      this.$refs['screenDesigner' + this.currentInfo.temps.id][0].clear();
    },
    initEditor() {
      this.addScrollEventListener();
      initializeMagicScript();

      this.editor = monaco.editor.create(this.$refs.editor, {
        minimap: {
          enabled: true,
        },
        value: '',
        language: 'magicscript',
        folding: true,
        lineDecorationsWidth: 35,
        wordWrap: 'on',
        theme: store.get('skin') || 'default',
        fontFamily: contants.EDITOR_FONT_FAMILY,
        fontSize: contants.EDITOR_FONT_SIZE,
        fontLigatures: true,
        renderWhitespace: 'none',
        // 自动调整大小
        automaticLayout: true,
      });
      console.log(monaco.languages.getLanguages())



      this.editor.addAction({
        id: 'editor.action.triggerSuggest.extension',
        label: '触发代码提示',
        precondition:
          '!suggestWidgetVisible && !markersNavigationVisible && !parameterHintsVisible && !findWidgetVisible',
        run: () => {
          this.editor.trigger(null, 'editor.action.triggerSuggest', {});
        },
      });
      CommandsRegistry.registerCommand('editor.action.scrollUp1Line', () => {
        this.editor.setScrollTop(this.editor.getScrollTop() - 22);
      });
      this.editor.addCommand(
        monaco.KeyMod.Alt | monaco.KeyCode.US_SLASH,
        () => {
          let triggerParameterHints = this.editor.getAction(
            'editor.action.triggerParameterHints',
          );
          let triggerSuggest = this.editor.getAction(
            'editor.action.triggerSuggest.extension',
          );
          triggerParameterHints.run().then(() => {
            setTimeout(() => {
              if (triggerSuggest.isSupported()) {
                triggerSuggest.run();
              }
            }, 0);
          });
        },
        '!findWidgetVisible && !inreferenceSearchEditor && !editorHasSelection',
      );
      const updateKeys = [
        [
          'editor.action.triggerParameterHints',
          monaco.KeyMod.Alt | monaco.KeyCode.US_SLASH,
        ],
        [
          'editor.action.triggerSuggest',
          monaco.KeyMod.Alt | monaco.KeyCode.US_SLASH,
        ],
        [
          'toggleSuggestionDetails',
          monaco.KeyMod.Alt | monaco.KeyCode.US_SLASH,
          ContextKeyExpr.deserialize('suggestWidgetVisible && textInputFocus'),
        ],
        [
          'editor.action.formatDocument',
          monaco.KeyMod.CtrlCmd | monaco.KeyMod.Alt | monaco.KeyCode.KEY_L,
        ],
        [
          'editor.action.marker.nextInFiles',
          monaco.KeyMod.CtrlCmd | monaco.KeyCode.F8,
        ],
      ];
      updateKeys.forEach((item) => {
        let action = item[0];
        const { handler, when } = CommandsRegistry.getCommand(action) ?? {};
        if (handler) {
          let index = KeybindingsRegistry._coreKeybindings.findIndex(
            (it) => it.command === action,
          );
          if (index > 0) {
            KeybindingsRegistry._coreKeybindings.splice(index, 1);
          }
          this.editor._standaloneKeybindingService.addDynamicKeybinding(
            action,
            item[1],
            handler,
            when || item[2],
          );
        }
      });
      KeybindingsRegistry._cachedMergedKeybindings = null;
      this.editor.onMouseDown((e) => {
        if (e.target.element.classList.contains('codicon')) {
          return;
        }
        if (
          e.target.detail &&
          e.target.detail.offsetX &&
          e.target.detail.offsetX >= 0 &&
          e.target.detail.offsetX <= 90
        ) {
          var line = e.target.position.lineNumber;
          if (this.editor.getModel().getLineContent(line).trim() === '') {
            return;
          }
          let decorations = this.editor.getLineDecorations(line);
          let decoration = decorations.filter(
            (it) => it.options.linesDecorationsClassName === 'breakpoints',
          );
          if (decoration && decoration.length > 0) {
            this.editor.getModel().deltaDecorations([decoration[0].id], []);
          } else {
            this.editor.getModel().deltaDecorations(
              [],
              [
                {
                  range: new monaco.Range(line, 1, line, 1),
                  options: {
                    isWholeLine: true,
                    linesDecorationsClassName: 'breakpoints',
                    className: 'breakpoint-line',
                  },
                },
              ],
            );
          }
          this.info.ext.decorations = this.editor
            .getModel()
            .getAllDecorations();
        }
      });
      this.editor.onDidChangeModelContent(() => {
        if (this.info) {
          if (
            this.info.temps.type == 'timer' ||
            this.info.temps.type === 'initTask'
          ) {
            this.info.magicScript = this.editor.getValue();
          } else if (this.info.temps.type == 'listen') {
            this.info.taskScript = this.editor.getValue();
          } else {
            this.info.script = this.editor.getValue();
          }
          if (this.timeout) {
            clearTimeout(this.timeout);
          }
          this.timeout = setTimeout(() => this.doValidate(), 500);
        }
      });
    },
    initAssemblyEditor() {
      this.assemblyEditor = monaco.editor.create(this.$refs.assemblyEditor, {
        minimap: {
          enabled: true,
        },
        value: '',
        language: 'html',
        folding: true,
        lineDecorationsWidth: 35,
        wordWrap: 'on',
        theme: store.get('skin') || 'default',
        fontFamily: contants.EDITOR_FONT_FAMILY,
        fontSize: contants.EDITOR_FONT_SIZE,
        fontLigatures: true,
        renderWhitespace: 'none',
        // 自动调整大小
        automaticLayout: true,
      });
      this.assemblyEditor.addAction({
        id: 'editor.action.triggerSuggest.extension',
        label: '触发代码提示',
        precondition:
          '!suggestWidgetVisible && !markersNavigationVisible && !parameterHintsVisible && !findWidgetVisible',
        run: () => {
          this.assemblyEditor.trigger(null, 'editor.action.triggerSuggest', {});
        },
      });
      CommandsRegistry.registerCommand('editor.action.scrollUp1Line', () => {
        this.assemblyEditor.setScrollTop(
          this.assemblyEditor.getScrollTop() - 22,
        );
      });
      this.assemblyEditor.addCommand(
        monaco.KeyMod.Alt | monaco.KeyCode.US_SLASH,
        () => {
          let triggerParameterHints = this.assemblyEditor.getAction(
            'editor.action.triggerParameterHints',
          );
          let triggerSuggest = this.assemblyEditor.getAction(
            'editor.action.triggerSuggest.extension',
          );
          triggerParameterHints.run().then(() => {
            setTimeout(() => {
              if (triggerSuggest.isSupported()) {
                triggerSuggest.run();
              }
            }, 0);
          });
        },
        '!findWidgetVisible && !inreferenceSearchEditor && !editorHasSelection',
      );
      const updateKeys = [
        [
          'editor.action.triggerParameterHints',
          monaco.KeyMod.Alt | monaco.KeyCode.US_SLASH,
        ],
        [
          'editor.action.triggerSuggest',
          monaco.KeyMod.Alt | monaco.KeyCode.US_SLASH,
        ],
        [
          'toggleSuggestionDetails',
          monaco.KeyMod.Alt | monaco.KeyCode.US_SLASH,
          ContextKeyExpr.deserialize('suggestWidgetVisible && textInputFocus'),
        ],
        [
          'editor.action.formatDocument',
          monaco.KeyMod.CtrlCmd | monaco.KeyMod.Alt | monaco.KeyCode.KEY_L,
        ],
        [
          'editor.action.marker.nextInFiles',
          monaco.KeyMod.CtrlCmd | monaco.KeyCode.F8,
        ],
      ];
      updateKeys.forEach((item) => {
        let action = item[0];
        const { handler, when } = CommandsRegistry.getCommand(action) ?? {};
        if (handler) {
          let index = KeybindingsRegistry._coreKeybindings.findIndex(
            (it) => it.command === action,
          );
          if (index > 0) {
            KeybindingsRegistry._coreKeybindings.splice(index, 1);
          }
          this.assemblyEditor._standaloneKeybindingService.addDynamicKeybinding(
            action,
            item[1],
            handler,
            when || item[2],
          );
        }
      });
      KeybindingsRegistry._cachedMergedKeybindings = null;
      this.assemblyEditor.onMouseDown((e) => {
        if (e.target.element.classList.contains('codicon')) {
          return;
        }
        if (
          e.target.detail &&
          e.target.detail.offsetX &&
          e.target.detail.offsetX >= 0 &&
          e.target.detail.offsetX <= 90
        ) {
          var line = e.target.position.lineNumber;
          if (
            this.assemblyEditor.getModel().getLineContent(line).trim() === ''
          ) {
            return;
          }
          let decorations = this.assemblyEditor.getLineDecorations(line);
          let decoration = decorations.filter(
            (it) => it.options.linesDecorationsClassName === 'breakpoints',
          );
          if (decoration && decoration.length > 0) {
            this.assemblyEditor
              .getModel()
              .deltaDecorations([decoration[0].id], []);
          } else {
            this.assemblyEditor.getModel().deltaDecorations(
              [],
              [
                {
                  range: new monaco.Range(line, 1, line, 1),
                  options: {
                    isWholeLine: true,
                    linesDecorationsClassName: 'breakpoints',
                    className: 'breakpoint-line',
                  },
                },
              ],
            );
          }
          this.info.ext.decorations = this.assemblyEditor
            .getModel()
            .getAllDecorations();
        }
      });
      this.assemblyEditor.onDidChangeModelContent(() => {
        if (this.currentInfo) {
          this.currentInfo.templateJson = this.assemblyEditor.getValue();
          if (this.timeout) {
            clearTimeout(this.timeout);
          }
          this.timeout = setTimeout(() => this.doValidate(), 500);
        }
      });
    },
    // 点击预览按钮  从编辑界面到预览界面需要刷新预览界面内容
    onSwitchViewStatus() {
      bus.$emit('switch-view-status');
    },
    onSwitchScreenStatus() {
      this.currentInfo.reportData =
        this.$refs[
          'screenDesigner' + this.currentInfo.temps.id
        ][0].getCurrentData();
      this.currentInfo.temps.editFlag = !this.currentInfo.temps.editFlag;
    },
    onSwitchReportStatus() {
      this.currentInfo.reportData =
        this.$refs[
          'reportDesigner' + this.currentInfo.temps.id
        ][0].getCurrentData();

      this.currentInfo.temps.editFlag = !this.currentInfo.temps.editFlag;
    },
    // 接收修改的视图模板的JSON串
    viewChange(templateJson, editFlag) {
      console.log('修改模板', templateJson);
      this.currentInfo.templateJson = templateJson;
      this.currentInfo.temps.editFlag = editFlag;
      this.$forceUpdate();
    },
    flowChange(str) {
      if (str !== this.info.temps.temp) {
        this.$set(this.info, 'isChange', true);
      } else {
        this.$set(this.info, 'isChange', false);
      }
    },
    flowDataOnLoad(data) {
      if (this.info.temps.type == 'flow') {
        this.info.temps.temp = JSON.stringify(data);
        console.log(data);
      }
    },
    async onSaveScreen(item) {
      let result = false;

      await this.$refs['screenDesigner' + item.temps.id][0].saveData();
      result = true;
      this.currentInfo.reportData = null;
      bus.$emit('status', '大屏「' + item.temps.name + '}」已保存');

      return result;
    },

    async onSaveReport(item) {
      let result = false;

      await this.$refs['reportDesigner' + item.temps.id][0].save();
      result = true;
      this.currentInfo.reportData = null;
      bus.$emit('status', '报表「' + item.temps.name + '}」已保存');

      return result;
    },
    async onSaveAssembly(item) {
      let result = false;
      await updateAssembly(item).then((res) => {
        result = true;
        bus.$emit('status', '在线组件「' + item.name + '}」已保存');
        item.ext.tmpScript = item.templateJson;
      });
      return result;
    },
    async onSaveFlow() {
      if (this.$refs.flow) {
        let flag = await this.$refs.flow[0].save();
        flag && this.$set(this.info, 'isChange', false);
        return flag;
      }
    },
    async onSaveView(item) {
      let result = false;
      await updateTemplateInfoJson({
        formId: item.formId,
        templateJson: item.templateJson,
      }).then((res) => {
        result = true;
        bus.$emit('status', '视图模板「' + item.temps.name + '}」已保存');
        item.ext.tmpScript = item.templateJson;
      });
      return result;
    },
    menuChange(id, flag) {
      let obj = this.scripts.find((item) => item.menuId == id);
      if (obj) {
        this.$set(obj, 'isChange', flag);
      }
    },
    timerChange(id, flag) {
      let obj = this.scripts.find((item) => item.taskId == id);
      this.$set(obj, 'isChange', flag);
    },
    initTaskChange(id, flag) {
      let obj = this.scripts.find((item) => item.initId == id);
      this.$set(obj, 'isChange', flag);
    },
    onException(args) {
      // if (this.info?.ext?.sessionId === args[0]) {
      let line = args[2];
      let range = new monaco.Range(line[0], line[2], line[1], line[3] + 1);
      let decorations = this.editor.deltaDecorations(
        [],
        [
          {
            range,
            options: {
              hoverMessage: {
                value: args[1],
              },
              inlineClassName: 'squiggly-error',
            },
          },
        ],
      );
      this.editor.revealRangeInCenter(range);
      this.editor.focus();
      if (contants.DECORATION_TIMEOUT >= 0) {
        setTimeout(
          () => this.editor.deltaDecorations(decorations, []),
          contants.DECORATION_TIMEOUT,
        );
      }
      // }
    },
    onBreakpoint(data) {
      let variables = data[1];
      bus.$emit('status', '进入断点...');
      // 进入断点
      this.info.ext.debuging = true;

      this.info.ext.variables = variables.variables;
      let range = variables.range;
      let decoration = {
        range: new monaco.Range(range[0], 1, range[0], 1),
        options: {
          isWholeLine: true,
          inlineClassName: 'debug-line',
          className: 'debug-line',
        },
      };
      this.info.ext.debugDecoration = decoration;
      this.info.ext.debugDecorations = [
        this.editor.deltaDecorations([], [decoration]),
      ];
      bus.$emit('switch-tab', 'debug');
    },
    doValidate() {
      try {
        let parser = new Parser(
          new TokenStream(tokenizer(this.editor.getValue())),
        );
        parser.parse();
        monaco.editor.setModelMarkers(this.editor.getModel(), 'validate', [{}]);
      } catch (e) {
        if (e.span) {
          let line = e.span.getLine();
          monaco.editor.setModelMarkers(this.editor.getModel(), 'validate', [
            {
              startLineNumber: line.lineNumber,
              endLineNumber: line.endLineNumber,
              startColumn: line.startCol,
              endColumn: line.endCol,
              message: e.message,
              severity: monaco.MarkerSeverity.Error,
            },
          ]);
        }
      }
    },
    layout() {
      this.$nextTick(() => {
        if (utils.isVisible(this.$refs.editor)) {
          this.$nextTick(() => this.editor.layout());
        }
      });
    },
    editView(item) {
      let data = this.scripts.filter((it) => it.temps.id === item.temps.id)[0];
      if (data) {
        data.temps.editFlag = !data.temps.editFlag;
      } else {
        item.temps.editFlag = true;
        this.open(item);
      }
    },
    open(item) {
      this.currentInfo = item;
      if (item.temps.type === 'menu') {
        this.menuMap[item.menuId] = false;
        store.set('menu-parameter', item);

        if (item.viewType) {
          item.tmp_id = item.menuId;
          this.menuInfo = item;
          localStorage.setItem(
            'url',
            JSON.stringify({
              date: new Date().getTime(),
              url: '/' + item.path + '/' + item.formId,
            }),
          );
          this.$options.components['menuView'] = require('@/views/' +
            item.component).default;
          this.$forceUpdate();
        }

        if (!item.ext) {
          this.$set(item, 'ext', {
            logs: [],
            debuging: false,
            sessionId: '',
            variables: [],
            decorations: [],
            debugDecorations: [],
            debugDecoration: null,
            save: true,
            loading: false,
            scrollTop: 0,
            tmpScript: null, // 缓存一个未修改前的脚本
            tabDraggable: false, // tab拖拽
          });
        }
        if (!this.scripts.filter((it) => it.id && it.id === item.id)[0]) {
          this.scripts.push(item);
        }
        bus.$emit('opened', item);
        bus.$emit('setselectedTab', '');
        this.$nextTick(() => {
          bus.$emit('setselectedTab', 'menu');
        });
      } else if (item.temps.type === 'view') {
        // 视图模板预览
        this.viewInfo = item;
        this.$forceUpdate();
        if (!item.ext) {
          this.$set(item, 'ext', {
            logs: [],
            debuging: false,
            sessionId: '',
            variables: [],
            decorations: [],
            debugDecorations: [],
            debugDecoration: null,
            save: true,
            loading: false,
            scrollTop: 0,
            tmpScript: item.templateJson, // 缓存一个未修改前的脚本
            tabDraggable: false, // tab拖拽
          });
        }
        if (
          !this.scripts.filter(
            (it) => it.temps && it.temps.id && it.temps.id === item.temps.id,
          )[0]
        ) {
          this.scripts.push(item);
        }
        bus.$emit('opened', item);
      } else if (item.temps.type === 'flow') {
        // 视图模板预览
        this.$forceUpdate();
        if (!item.ext) {
          this.$set(item, 'ext', {
            logs: [],
            debuging: false,
            sessionId: '',
            variables: [],
            decorations: [],
            debugDecorations: [],
            debugDecoration: null,
            save: true,
            loading: false,
            scrollTop: 0,
            tabDraggable: false, // tab拖拽
          });
        }
        if (
          !this.scripts.filter(
            (it) => it.temps && it.temps.id && it.temps.id === item.temps.id,
          )[0]
        ) {
          this.scripts.push(item);
        }
        this.flowFlag = false;
        this.$nextTick(() => {
          this.flowFlag = true;
        });
        this.info = item;
        bus.$emit('opened', item);
      } else if (item.temps.type === 'screen') {
        // 视图模板预览
        this.currentInfo.reportData = null;
        this.$forceUpdate();
        if (!item.ext) {
          this.$set(item, 'ext', {
            logs: [],
            debuging: false,
            sessionId: '',
            variables: [],
            decorations: [],
            debugDecorations: [],
            debugDecoration: null,
            save: true,
            loading: false,
            scrollTop: 0,
            tmpScript: item.templateJson, // 缓存一个未修改前的脚本
            tabDraggable: false, // tab拖拽
          });
        }
        if (
          !this.scripts.filter(
            (it) => it.temps && it.temps.id && it.temps.id === item.temps.id,
          )[0]
        ) {
          this.scripts.push(item);
        }
        bus.$emit('opened', item);
      } else if (item.temps.type === 'report') {
        // 视图模板预览
        this.currentInfo.reportData = null;
        this.$forceUpdate();
        if (!item.ext) {
          this.$set(item, 'ext', {
            logs: [],
            debuging: false,
            sessionId: '',
            variables: [],
            decorations: [],
            debugDecorations: [],
            debugDecoration: null,
            save: true,
            loading: false,
            scrollTop: 0,
            tmpScript: item.templateJson, // 缓存一个未修改前的脚本
            tabDraggable: false, // tab拖拽
          });
        }
        if (
          !this.scripts.filter(
            (it) => it.temps && it.temps.id && it.temps.id === item.temps.id,
          )[0]
        ) {
          this.scripts.push(item);
        }
        bus.$emit('opened', item);
      } else if (item.temps.type === 'initTask') {
        // 视图模板预览
        this.initTask = item;
        store.set('initTask-parameter', item);
        if (this.info) {
          this.info.ext.scrollTop = this.editor.getScrollTop();
        }
        if (!item.ext) {
          this.$set(item, 'ext', {
            type: 'initTask',
            logs: [],
            debuging: false,
            sessionId: '',
            variables: [],
            tmpScript: '',
            decorations: [],
            debugDecorations: [],
            debugDecoration: null,
            save: true,
            loading: false,
            scrollTop: 0,
            tabDraggable: false, // tab拖拽
          });
        }
        if (
          !this.scripts.filter(
            (it) => it.temps && it.temps.id && it.temps.id === item.temps.id,
          )[0]
        ) {
          this.scripts.push(item);
        }
        this.info = item;
        item.ext.tmpScript = item.magicScript;
        this.editor.setValue(item.magicScript || '');
        this.editor.setScrollTop(item.ext.scrollTop);
        bus.$emit('opened', item);
        bus.$emit('setselectedTab', '');
        bus.$emit('initTaskOptionChange', item);
        this.$nextTick(() => {
          bus.$emit('setselectedTab', 'initTask');
        });
      } else if (item.temps.type === 'timer') {
        // 视图模板预览
        this.timer = item;
        store.set('timer-parameter', item);
        if (this.info) {
          this.info.ext.scrollTop = this.editor.getScrollTop();
        }
        if (!item.ext) {
          this.$set(item, 'ext', {
            type: 'timer',
            logs: [],
            debuging: false,
            sessionId: '',
            variables: [],
            tmpScript: '',
            decorations: [],
            debugDecorations: [],
            debugDecoration: null,
            save: true,
            loading: false,
            scrollTop: 0,
            tabDraggable: false, // tab拖拽
          });
        }
        if (
          !this.scripts.filter(
            (it) => it.temps && it.temps.id && it.temps.id === item.temps.id,
          )[0]
        ) {
          this.scripts.push(item);
        }
        this.info = item;
        item.ext.tmpScript = item.magicScript;
        this.editor.setValue(item.magicScript || '');
        this.editor.setScrollTop(item.ext.scrollTop);
        bus.$emit('opened', item);
        bus.$emit('setselectedTab', '');
        this.$nextTick(() => {
          bus.$emit('setselectedTab', 'timer');
        });
      } else if (item.temps.type === 'listen') {
        // 视图模板预览
        if (this.info) {
          this.info.ext.scrollTop = this.editor.getScrollTop();
        }
        if (!item.ext) {
          this.$set(item, 'ext', {
            type: 'listen',
            logs: [],
            debuging: false,
            sessionId: '',
            variables: [],
            tmpScript: item.taskScript,
            decorations: [],
            debugDecorations: [],
            debugDecoration: null,
            save: true,
            loading: false,
            scrollTop: 0,
            tabDraggable: false, // tab拖拽
          });
        }
        if (
          !this.scripts.filter(
            (it) => it.temps && it.temps.id && it.temps.id === item.temps.id,
          )[0]
        ) {
          this.scripts.push(item);
        }
        this.info = item;
        // console.log(item)
        this.editor.setValue(item.taskScript || '');
        this.editor.setScrollTop(item.ext.scrollTop);
        bus.$emit('opened', item);
      } else if (item.temps.type === 'function') {
        // 视图模板预览
        if (this.info) {
          this.info.ext.scrollTop = this.editor.getScrollTop();
        }
        let id = item.id;
        if (!item.ext) {
          this.$set(item, 'ext', {
            type: 'function',
            logs: [],
            debuging: false,
            sessionId: '',
            variables: [],
            tmpScript: '',
            decorations: [],
            debugDecorations: [],
            debugDecoration: null,
            save: true,
            loading: false,
            scrollTop: 0,
            tabDraggable: false, // tab拖拽
          });
        }
        request
          .send(`/resource/file/${id}`)
          .success((data) => {
            this.$set(item, 'option', JSON.parse(data.option || '[]'));
            this.$set(item, 'parameters', data.parameters);
            this.$set(item, 'paths', data.paths);
            this.$set(item, 'script', data.script);
            item.ext.tmpScript = data.script;
            this.$set(item, 'description', data.description);
            if (item.copy === true) {
              item.id = '';
              item.copy = false;
            }
          })

          .end(() => {
            item.ext.loading = false;
          });
        if (
          !this.scripts.filter(
            (it) => it.temps && it.temps.id && it.temps.id === item.temps.id,
          )[0]
        ) {
          this.scripts.push(item);
        }
        this.info = item;
        item.ext.tmpScript = item.script;
        this.editor.setValue(item.script || '');
        this.editor.setScrollTop(item.ext.scrollTop);
        bus.$emit('opened', item);
      } else if (item.temps.type === 'api') {
        if (item.delete) {
          this.close(item.id || item.tmp_id);
          return;
        }
        let id = item.id;
        this.selected = item;
        let isNew =
          id === '' && !this.scripts.some((it) => it.tmp_id === item.tmp_id);
        let info = this.scripts.filter((it) => it.id === id)[0];
        if (this.info) {
          this.info.ext.scrollTop = this.editor.getScrollTop();
        }
        if (!item.ext) {
          this.$set(item, 'ext', {
            logs: [],
            debuging: false,
            sessionId: '',
            variables: [],
            decorations: [],
            debugDecorations: [],
            debugDecoration: null,
            save: true,
            loading: false,
            scrollTop: 0,
            tmpScript: null, // 缓存一个未修改前的脚本
            tabDraggable: false, // tab拖拽
          });
        }
        if (item.ext.loading) {
          return;
        }
        if (item.copy !== true && (info || isNew)) {
          if (isNew) {
            this.$set(item, 'headers', item.headers || []);
            this.$set(item, 'option', item.option || []);
            this.$set(item, 'paths', item.paths || []);
            this.$set(item, 'requestBody', item.requestBody || '');
            this.$set(item, 'method', contants.API_DEFAULT_METHOD);
            this.$set(item, 'parameters', item.parameters || []);
            item.ext.save = false;
            this.scripts.push(item);
          } else if (info) {
            item = info;
            this.selected = item;
          }
          this.info = item;
          this.editor.setValue(item.script || '');
          this.editor.getModel().deltaDecorations([], item.ext.decorations);
          this.editor.setScrollTop(item.ext.scrollTop);
          if (item.ext.debugDecoration) {
            item.ext.debugDecorations = this.editor
              .getModel()
              .deltaDecorations([], [item.ext.debugDecoration]);
          }
          bus.$emit('opened', item);
        } else {
          let process = (data) => {
            if (!data) {
              return data;
            }
            if (Array.isArray(data)) {
              return data;
            }
            let array = [];
            for (let key in data) {
              array.push({
                name: key,
                value: data[key] === null ? '' : data[key].toString(),
              });
            }
            return array;
          };
          item.ext.loading = true;
          request
            .send(`/resource/file/${id}`)
            .success((data) => {
              this.$set(item, 'option', data.options);
              this.$set(item, 'parameters', data.parameters);
              this.$set(item, 'headers', data.headers);
              this.$set(item, 'paths', data.paths);
              this.$set(
                item,
                'responseHeader',
                JSON.parse(data.responseHeader || '[]'),
              );
              this.$set(item, 'responseBody', data.responseBody);
              this.$set(
                item,
                'responseBodyDefinition',
                data.responseBodyDefinition,
              );
              this.$set(
                item,
                'requestBodyDefinition',
                data.requestBodyDefinition,
              );
              this.$set(item, 'requestBody', data.requestBody);
              this.$set(item, 'method', data.method);

              this.$set(item, 'script', data.script);
              item.ext.tmpScript = data.script;
              this.$set(item, 'description', data.description);
              if (item.copy === true) {
                item.id = '';
                item.copy = false;
              }
              this.scripts.forEach((it) => {
                if (it.name == item.name) {
                  it.displayName = it.groupName + '/' + it.name;
                  item.displayName = item.groupName + '/' + item.name;
                }
              });
              this.scripts.push(item);
              this.info = item;
              this.editor.setValue(item.script);
              this.editor.setScrollTop(item.ext.scrollTop);
              bus.$emit('opened', item);
              this.resetRecentOpenedTab();
            })
            .end(() => {
              item.ext.loading = false;
            });
        }
      } else if (item.temps.type === 'sync-data') {
        if (!item.config) {
          item.config = {
            type: 'off-line',
            cron: '',
            enable: true,
            targetDatasource: {
              datasourceId: '',
              table: '',
              tableDDL: '',
              columns: [],
              preSql: '',
              writeMode: '',
            },
            datasource: {
              datasourceId: '',
              table: '',
              tableDDL: '',
              columns: [],
              splitPk: '',
              where: '',
            },
          };
        } else {
          if (typeof item.config === 'string') {
            item.config = JSON.parse(item.config);
          }
        }

        this.syncInfo = item;
        // 视图模板预览
        if (!item.ext) {
          this.$set(item, 'ext', {
            type: 'sync-data',
            logs: [],
            debuging: false,
            sessionId: '',
            variables: [],
            decorations: [],
            debugDecorations: [],
            debugDecoration: null,
            save: true,
            loading: false,
            scrollTop: 0,
            tmpScript: item.config, // 缓存一个未修改前的脚本
            tabDraggable: false, // tab拖拽
          });
        }
        if (
          !this.scripts.filter(
            (it) => it.temps && it.temps.id && it.temps.id === item.temps.id,
          )[0]
        ) {
          this.scripts.push(item);
        }

        bus.$emit('opened', item);
      } else if (item.temps.type === 'assembly') {
        if (!item.ext) {
          this.$set(item, 'ext', {
            logs: [],
            debuging: false,
            sessionId: '',
            variables: [],
            decorations: [],
            debugDecorations: [],
            debugDecoration: null,
            save: true,
            loading: false,
            scrollTop: 0,
            tmpScript: item.templateJson, // 缓存一个未修改前的脚本
            tabDraggable: false, // tab拖拽
          });
        }
        if (
          !this.scripts.filter(
            (it) => it.temps && it.temps.id && it.temps.id === item.temps.id,
          )[0]
        ) {
          this.scripts.push(item);
          this.assemblyEditor.setValue(item.templateJson);
          this.assemblyEditor.setScrollTop(item.ext.scrollTop);
        }
        bus.$emit('opened', item);
      }
      this.assemblyFlag = false;
      this.$nextTick(() => {
        this.assemblyFlag = true;
      });
      this.layout();
    },
    resetRecentOpenedTab() {
      store.set(
        contants.RECENT_OPENED_TAB,
        this.scripts.filter((it) => it.id).map((it) => it.id),
      );
    },
    deleteWrapperProperties(obj) {
      delete obj.ext;
      delete obj.groupName;
      delete obj.groupPath;
      delete obj._type;
      delete obj.level;
      delete obj.tmp_id;
    },
    doSaveApi() {
      this.info.headers = this.info.headers || [];
      let thisInfo = this.info;
      let saveObj = { ...this.info };
      this.deleteWrapperProperties(saveObj);
      delete saveObj.optionMap;
      delete saveObj.responseHeader;
      delete saveObj.running;
      // saveObj.responseHeader = JSON.stringify(saveObj.responseHeader)
      saveObj.parameters = saveObj.parameters
        .filter((it) => it.name)
        .map((it) => {
          if (it.value instanceof FileList) {
            let temp = { ...it };
            delete temp.value;
            return temp;
          }
          return it;
        });
      saveObj.paths = saveObj.paths.filter((it) => it.name);
      saveObj.headers = saveObj.headers.filter((it) => it.name);
      saveObj.options = saveObj.option;
      saveObj.option = JSON.stringify(saveObj.option);
      // saveObj.requestHeader = JSON.stringify(saveObj.requestHeader.filter(it => it.name))
      if (contants.config.persistenceResponseBody === false) {
        delete saveObj.responseBody;
        delete saveObj.responseBodyDefinition;
      }
      if (!saveObj.id) delete saveObj.id;
      return request
        .send('/resource/file/api/save?auto=0', JSON.stringify(saveObj), {
          method: 'post',
          headers: {
            'Content-Type': 'application/json',
          },
          transformRequest: [],
        })
        .success((id) => {
          if (saveObj.id) {
            bus.$emit('report', 'script_save');
          } else {
            bus.$emit('report', 'script_add');
          }
          let fullName = utils.replaceURL(
            `${thisInfo.groupName}/${thisInfo.name}(${thisInfo.groupPath}/${thisInfo.path})`,
          );
          bus.$emit('status', `接口「${fullName}」已保存`);
          thisInfo.id = id;
          this.info.ext.tmpScript = saveObj.script;
        });
    },
    doSaveFunction() {
      let thisInfo = this.info;
      let saveObj = { ...this.info };
      this.deleteWrapperProperties(saveObj);
      saveObj.parameters = saveObj.parameters.filter((it) => it.name);
      saveObj.folder = false;
      saveObj.lock = null;
      saveObj.opened = false;
      saveObj.updateTime = null;
      delete saveObj.temps;
      return request
        .send('/resource/file/api/save?auto=0', JSON.stringify(saveObj), {
          method: 'post',
          headers: {
            'Content-Type': 'application/json',
          },
          transformRequest: [],
        })
        .success((id) => {
          if (saveObj.id) {
            bus.$emit('report', 'function_save');
          } else {
            bus.$emit('report', 'function_add');
          }
          let fullName = utils.replaceURL(
            `${thisInfo.groupName}/${thisInfo.name}(${thisInfo.groupPath}/${thisInfo.path})`,
          );
          bus.$emit('status', `函数「${fullName}」已保存`);
          thisInfo.id = id;
          this.info.ext.tmpScript = saveObj.script;
        });
    },
    doSaveSync() {
      saveMagicSyncData({
        taskId: this.syncInfo.taskId,
        taskName: this.syncInfo.taskName,
        config: JSON.stringify(this.syncInfo.config),
      }).then((res) => {
        bus.$emit('status', `数据同步「${this.syncInfo.taskName}」已保存`);
        bus.$emit('getSyncList');
      });
    },
    doSave() {
      if (this.info.temps.type === 'timer') {
        let data = JSON.parse(store.get('timer-parameter'));
        saveMagicTask({
          ...data,
          magicScript: this.editor.getValue(),
        }).then((res) => {
          bus.$emit('status', `定时任务「${data.taskName}」已保存`);
          this.scripts.forEach((item) => {
            if (item.taskId === data.taskId) {
              item.ext.tmpScript = this.editor.getValue();
              item.isChange = false;
            }
          });
        });
      }
      if (this.info.temps.type === 'initTask') {
        let data = JSON.parse(store.get('initTask-parameter'));
        saveInitialization({
          ...data,
          magicScript: this.editor.getValue(),
        }).then((res) => {
          bus.$emit('status', `初始任务「${data.initName}」已保存`);
          this.scripts.forEach((item) => {
            if (item.initId === data.initId) {
              item.ext.tmpScript = this.editor.getValue();
              item.isChange = false;
            }
          });
        });
      }
      if (this.info.temps.type == 'listen') {
        updatePlcTasks(this.info).then((res) => {
          bus.$emit('status', `监听任务「${this.info.taskName}」已保存`);
          this.scripts.forEach((item) => {
            if (item.taskId === this.info.taskId) {
              item.ext.tmpScript = this.editor.getValue();
            }
          });
        });
      }
      if (this.syncInfo) {
        return this.doSaveSync();
      }
      if (this.info._type === 'function') {
        return this.doSaveFunction();
      }
      if (this.selected) {
        if (this.info._type === 'api') {
          return this.doSaveApi();
        }
      }
    },
    doTest() {
      if (this.info.temps.type === 'timer') {
        this.doSave();
        // 执行定时任务
        this.$set(this.info, 'running', true);
        let headers = {};
        let session =
          new Date().getTime() + '' + Math.floor(Math.random() * 100000);
        bus.$emit('message', 'set_session_id', session);
        this.info.ext.sessionId = localStorage.getItem("clientId");
        headers[contants.HEADER_REQUEST_SESSION] = session;
        headers[contants.HEADER_REQUEST_SCRIPT_ID] = this.info.temps.id;
        headers[contants.HEADER_REQUEST_CLIENT_ID] = localStorage.getItem("clientId");
        headers[contants.HEADER_MAGIC_TOKEN] =
          contants.HEADER_MAGIC_TOKEN_VALUE;
        executeTask({ id: this.info.temps.id }, headers)
          .then((res) => {
            this.$set(this.info, 'running', false);
            bus.$emit('setselectedTab', 'log');
          })
          .catch((e) => {
            this.$set(this.info, 'running', false);
            bus.$emit('setselectedTab', 'log');
          });
      } else if (this.info.temps.type === 'initTask') {
        let data = JSON.parse(store.get('initTask-parameter'));
        doTestInitialization({
          ...data,
          magicScript: this.editor.getValue(),
        }).then((res) => {
          if (res.code === 200) {
            this.$magicAlert({
              content: '执行成功',
            });
            this.doSave();
            bus.$emit('status', `初始任务「${data.taskName}」已保存`);
            this.scripts.forEach((item) => {
              if (item.initId === data.initId) {
                item.ext.tmpScript = this.editor.getValue();
              }
            });
          } else {
            this.$magicAlert({
              content: res.msg,
            });
          }
        });
      } else {
        if (!this.selected) {
          this.$magicAlert({
            content: '请打开接口在执行测试',
          });
        } else {
          if (this.info.running || this.info._type !== 'api') {
            return;
          }
          bus.$emit('switch-tab', 'request');
          if (contants.AUTO_SAVE && this.info.lock !== '1') {
            // 自动保存
            let resp = this.doSave();
            resp &&
              resp.end((successed) => {
                if (successed) {
                  this.internalTest();
                }
              });
          } else {
            this.internalTest();
          }
        }
      }
    },
    internalTest() {
      this.editor.deltaDecorations(
        this.editor
          .getModel()
          .getAllDecorations()
          .filter((it) => it.options.inlineClassName === 'squiggly-error')
          .map((it) => it.id),
        [],
      );
      this.$set(this.info, 'running', true);
      let requestConfig = {
        baseURL: contants.SERVER_URL,
        url: utils.replaceURL('/' + this.info.groupPath + '/' + this.info.path),
        method: this.info.method,
        headers: {},
        responseType: 'json',
        withCredentials: true,
      };
      const isToken = (requestConfig.headers || {}).isToken === false;
      // 请求携带来自终端类型
      requestConfig.headers['terminalType'] = 'PC';
      if (getToken() && !isToken) {
        requestConfig.headers['Authorization'] = 'Bearer ' + getToken(); // 让每个请求携带自定义token 请根据实际情况自行修改
      }

      this.info.paths
        .filter((it) => it.value && it.value.trim())
        .forEach((it) => {
          requestConfig.url = requestConfig.url.replace(
            new RegExp(`\{${it.name}\}`, 'g'),
            it.value.trim(),
          );
        });
      // 先处理接口的路径变量，在处理分组的路径变量，顺序递归向上
      const groups = this.$parent.$refs.apiList.getGroupsById(
        this.info.groupId,
      );
      groups
        .filter((group) => group.paths && group.paths.length > 0)
        .forEach((group) => {
          group.paths
            .filter((it) => it.value && it.value.trim())
            .forEach((it) => {
              requestConfig.url = requestConfig.url.replace(
                new RegExp(`\{${it.name}\}`, 'g'),
                it.value.trim(),
              );
            });
        });
      if (requestConfig.url.indexOf('{') > -1) {
        this.$magicAlert({
          content: '请填写路径变量后在测试！',
        });
        this.$set(this.info, 'running', false);
        return;
      }
      this.info.headers
        .filter((it) => it.name)
        .forEach((it) => {
          requestConfig.headers[it.name] = it.value;
        });
      let params = {};
      this.info.parameters
        .filter((it) => it.name)
        .forEach((it) => {
          params[it.name] = it.value;
        });
      if (Object.values(params).some((it) => it instanceof FileList)) {
        requestConfig.headers['Content-Type'] = 'multipart/form-data';
        let formData = new FormData();
        Object.keys(params).forEach((key) => {
          let value = params[key];
          if (value instanceof FileList) {
            value.forEach((file) => formData.append(key, file, file.name));
          } else {
            formData.append(key, value);
          }
        });
        requestConfig.data = formData;
      } else {
        requestConfig.headers['Content-Type'] =
          'application/x-www-form-urlencoded';
        if (requestConfig.method !== 'POST' || this.info.requestBody) {
          requestConfig.params = params;
        } else {
          requestConfig.data = params;
        }
        if (this.info.requestBody) {
          try {
            let requestBody = JSON.parse(this.info.requestBody);
            requestConfig.params = params;
            requestConfig.data = this.info.requestBody;
            requestConfig.headers['Content-Type'] = 'application/json';
            requestConfig.transformRequest = [];
          } catch (e) {
            this.$magicAlert({
              content: 'RequestBody 参数有误，请检查！',
            });
            this.$set(this.info, 'running', false);
            return;
          }
        }
      }
      const info = this.info;
      info.ext.sessionId =
        new Date().getTime() + '' + Math.floor(Math.random() * 100000);
      bus.$emit('message', 'set_session_id', info.ext.sessionId);
      this.sendTestRequest(info, requestConfig, info.ext.sessionId);
    },
    viewHistory() {
      if (!this.selected) {
        return;
      }
      if (!this.info.id) {
        this.$magicAlert({
          content: '当前是新增脚本,无法查看历史记录',
        });
        return;
      }
      let url = `backups?id=${this.info.id}`;
      let isApi = this.info._type === 'api';
      if (!isApi) {
        url = 'function/' + url;
      }
      request.send(url).success((timestampes) => {
        if (timestampes && timestampes.length > 0) {
          this.$refs.history.load(timestampes, this.info, this.editor, isApi);
          this.showHsitoryDialog = true;
        } else {
          this.$magicAlert({
            title: '历史记录',
            content: '当前脚本无历史记录',
          });
        }
      });
    },
    doContinue(step) {
      if (!this.selected) {
        return;
      }
      let target = this.info;
      if (target.ext.debuging) {
        target.ext.debugDecorations &&
          this.editor.deltaDecorations(target.ext.debugDecorations, []);
        target.ext.debuging = false;
        target.ext.variables = [];
        console.log(
          this.editor
            .getModel()
            .getAllDecorations()
            .filter(
              (it) => it.options.linesDecorationsClassName === 'breakpoints',
            )
            .map((it) => it.range.startLineNumber)
            .join('|'),
        );
        bus.$emit(
          'message',
          'resume_breakpoint',
          target.id +
            ',' +
            (step === true ? '1' : '0') +
            ',' +
            this.editor
              .getModel()
              .getAllDecorations()
              .filter(
                (it) => it.options.linesDecorationsClassName === 'breakpoints',
              )
              .map((it) => it.range.startLineNumber)
              .join('|'),
        );
      }
    },
    doStepInto() {
      this.doContinue(true);
    },
    mergeGlobalSettings(requestConfig) {
      let parameters = JSON.parse(store.get('global-parameters') || '[]');
      let headers = JSON.parse(store.get('global-headers') || '[]');
      headers
        .filter((it) => it.name)
        .forEach((item) => {
          requestConfig.headers[item.name] =
            requestConfig.headers[item.name] || item.value;
        });
      parameters
        .filter((it) => it.name)
        .forEach((item) => {
          if (requestConfig.params) {
            requestConfig.params[item.name] =
              requestConfig.params[item.name] || item.value;
          } else {
            requestConfig.data[item.name] =
              requestConfig.data[item.name] || item.value;
          }
        });
    },
    sendTestRequest(target, requestConfig, sessionId) {
      bus.$emit('switch-tab', 'log');
      target.ext.requestConfig = requestConfig;
      target.ext.sessionId = localStorage.getItem("clientId");
      requestConfig.headers[contants.HEADER_REQUEST_SESSION] = sessionId;
      requestConfig.headers[contants.HEADER_REQUEST_SCRIPT_ID] =
        target.temps.id;
      requestConfig.headers[contants.HEADER_REQUEST_CLIENT_ID] =
          localStorage.getItem("clientId");
      requestConfig.headers[contants.HEADER_MAGIC_TOKEN] =
        contants.HEADER_MAGIC_TOKEN_VALUE;
      this.mergeGlobalSettings(requestConfig);
      requestConfig.headers[contants.HEADER_REQUEST_BREAKPOINTS] = this.editor
        .getModel()
        .getAllDecorations()
        .filter((it) => it.options.linesDecorationsClassName === 'breakpoints')
        .map((it) => it.range.startLineNumber)
        .join(',');
      requestConfig.responseType = 'blob';
      requestConfig.validateStatus = () => true;
      let dataLen = 0;
      let fullName = utils.replaceURL(
        `${target.groupName}/${target.name}(${target.groupPath}/${target.path})`,
      );
      requestConfig.transformResponse = [
        function (data, headers) {
          dataLen = data.size;
          if (headers['content-disposition']) {
            return new Promise(function (resolve) {
              resolve(data);
            });
          }
          return new Promise(function (resolve) {
            let reader = new FileReader();
            reader.readAsText(data);
            reader.onload = function () {
              try {
                JSON.parse(this.result);
                resolve(this.result);
              } catch (e) {
                resolve(data);
              }
            };
          });
        },
      ];
      bus.$emit('status', `开始测试「${fullName}」`);
      let start = new Date().getTime();
      request
        .execute(requestConfig)
        .then((res) => {
          res.data.then((data) => {
            let unit = ['B', 'KB', 'MB'];
            let index = 0;
            while (index < unit.length && dataLen >= 1024) {
              dataLen = dataLen / 1024;
              index++;
            }
            dataLen = dataLen.toFixed(2);
            bus.$emit(
              'status',
              `「${fullName}」测试完毕，状态：<em>${
                res.status
              }</em> 大小：<em>${dataLen} ${unit[index]}</em> 耗时：<em>${
                new Date().getTime() - start
              } ms</em>`,
            );
            const contentType = res.headers['content-type'];
            target.ext.debugDecorations &&
              this.editor.deltaDecorations(target.ext.debugDecorations, []);
            target.ext.debugDecorations = target.ext.debugDecoration = null;
            if (!(data instanceof Blob)) {
              target.ext.debuging = target.running = false;
              target.responseBody = utils.formatJson(data);
              bus.$emit('switch-tab', 'result');
              bus.$emit(
                'update-response-body-definition',
                target.responseBodyDefinition,
              );
              bus.$emit(
                'update-response-body',
                target.responseBody,
                res.headers,
              );
            } else {
              // 执行完毕
              target.running = false;
              bus.$emit('switch-tab', 'result');
              bus.$emit('update-response-blob', contentType, data, res.headers);
            }
          });
        })
        .catch((error) => {
          bus.$emit('status', `请求出错：「${fullName}」`);
          target.ext.debuging = target.running = false;
          request.processError(error);
        });
    },
    close(id) {
      this.scripts.forEach((item, index) => {
        if (item.temps.id === id) {
          // 是否修改过
          let hasUpdate = false;
          if (
            (item.temps.type === 'api' || item.temps.type === 'assembly') &&
            item.script !== item.ext.tmpScript
          ) {
            // api 保存
            hasUpdate = true;
          } else if (
            item.temps.type === 'view' &&
            item.templateJson !== item.ext.tmpScript
          ) {
            // 视图模板保存
            hasUpdate = true;
          } else if (
            item.temps.type === 'timer' &&
            item.magicScript !== item.ext.tmpScript
          ) {
            // api 保存
            hasUpdate = true;
          } else if (
            (item.temps.type === 'menu' ||
              item.temps.type === 'initTask' ||
              item.temps.type === 'flow') &&
            item.isChange
          ) {
            // 视图模板保存
            hasUpdate = true;
          } else if (
            item.temps.type === 'sync-data' &&
            item.config !== item.ext.tmpScript
          ) {
            // 视图模板保存
            hasUpdate = true;
          }

          if (hasUpdate) {
            this.$confirm(
              '是否保存' + item.temps.name + '已修改内容?',
              '警告',
              {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
              },
            )
              .then(async () => {
                let result = false;
                if (item.temps.type === 'view') {
                  // 视图模板保存
                  result = this.onSaveView(item);
                } else if (item.temps.type === 'flow') {
                  result = await this.onSaveFlow(item);
                  console.log(result);
                } else {
                  // api 保存
                  result = this.doSave();
                }
                if (result) {
                  bus.$emit('close', item);
                  this.scripts.splice(index, 1);
                  let info;
                  if (index > 0) {
                    info = this.scripts[index - 1];
                  } else if (this.scripts.length > 0) {
                    info = this.scripts[0];
                  } else {
                    this.selected = null;
                    this.currentInfo = null;
                    return;
                  }
                  this.open(info);
                } else {
                  this.$notify.error({ message: '保存失败' });
                }
              })
              .catch((e) => {
                // 点击取消 则是不保存
                bus.$emit('close', item);
                this.scripts.splice(index, 1);
                let info;
                if (index > 0) {
                  info = this.scripts[index - 1];
                } else if (this.scripts.length > 0) {
                  info = this.scripts[0];
                } else {
                  this.selected = null;
                  this.currentInfo = null;
                  return;
                }
                this.open(info);
              });
          } else {
            bus.$emit('close', item);
            this.scripts.splice(index, 1);
            let info;
            if (index > 0) {
              info = this.scripts[index - 1];
            } else if (this.scripts.length > 0) {
              info = this.scripts[0];
            } else {
              this.selected = null;
              this.currentInfo = null;
              return;
            }
            this.open(info);
          }
        }
      });
      if (this.scripts.length === 0) {
        bus.$emit('opened', { empty: true });
      }
      this.scripts.forEach((it) => {
        var equalIndex = 0;
        this.scripts.forEach((item) => {
          if (it.name == item.name) {
            equalIndex++;
          }
        });
        if (equalIndex == 1) {
          it.displayName = it.name;
        }
      });
      this.resetRecentOpenedTab();
    },
    closeAll() {
      let items = [...this.scripts];
      items.forEach((element) => {
        this.close(element.temps.id);
      });
    },
    changed(info) {
      if (info && info === this.selected) {
        let index = -1;
        this.scripts.forEach((item, i) => {
          if (item.id === info.id) {
            index = i;
          }
        });
        if (index > -1) {
          this.scripts[index] = info;
        }
      }
    },
    // tab右键菜单
    tabsContextmenuHandle(event, item, index) {
      this.$magicContextmenu({
        menus: [
          {
            label: '关闭',
            divided: true,
            onClick: () => {
              this.close(item.id || item.tmp_id);
            },
          },
          {
            label: '定位',
            divided: true,
            icon: 'ma-icon-position',
            onClick: () => {
              bus.$emit('position-' + item._type, item.id || item.tmp_id);
            },
          },
          {
            label: '关闭其他',
            divided: true,
            onClick: () => {
              let id = item.id || item.tmp_id || item.temps.id;
              let items = [...this.scripts];
              items.forEach((element) => {
                let oid = element.id || element.tmp_id || element.temps.id;
                if (id !== oid) {
                  this.close(oid);
                }
              });
            },
          },
          {
            label: '关闭左侧',
            onClick: () => {
              let items = [...this.scripts];
              for (let i = 0; i < index; i++) {
                this.close(items[i].id || items[i].tmp_id || items[i].temps.id);
              }
            },
          },
          {
            label: '关闭右侧',
            divided: true,
            onClick: () => {
              let items = [...this.scripts];
              for (let i = index + 1; i < items.length; i++) {
                this.close(items[i].id || items[i].tmp_id || items[i].temps.id);
              }
            },
          },
          {
            label: '全部关闭',
            onClick: () => {
              this.closeAll();
            },
          },
        ],
        event,
        zIndex: 9999,
      });
    },
    // 添加滚动条的监听事件
    addScrollEventListener() {
      // 修改滚轮事件，将y轴变为x轴
      let scrollbar = this.$refs.scrollbar;
      let handler = (e) => {
        if (scrollbar.contains(e.target)) {
          let delta = e.wheelDelta || e.detail;
          scrollbar.scrollLeft += delta > 0 ? -100 : 100;
        }
      };
      document.addEventListener('DOMMouseScroll', handler, false);
      document.addEventListener('mousewheel', handler, false);
      // 监听视图大小变化
      // 观察器的配置（需要观察什么变动）
      const config = {
        attributes: true,
        childList: true,
        characterData: true,
        subtree: true,
      };
      // 创建一个观察器实例并传入回调函数
      this.mutationObserver = new MutationObserver(() => {
        // 控制滚动聚焦选中的tab
        if (this.selected) {
          this.$nextTick(() => {
            const $scrollRect = scrollbar.getBoundingClientRect();
            const $itemDom = document.getElementById(
              'ma-tab-item-' + this.selected.tmp_id,
            );
            if ($itemDom) {
              // const $itemRect = $itemDom.getBoundingClientRect()
              // if ($itemRect.left < $scrollRect.left) {
              //   scrollbar.scrollLeft += $itemRect.left - $scrollRect.left
              // } else if ($scrollRect.left + $scrollRect.width < $itemRect.left + $itemRect.width) {
              //   scrollbar.scrollLeft += $itemRect.left + $itemRect.width - $scrollRect.left - $scrollRect.width
              // }
              $itemDom.scrollIntoView(true);
            }
          });
        }
      });
      // 以上述配置开始观察目标节点
      this.mutationObserver.observe(this.$refs.scrollbar, config);
    },
    tabDraggable(item, event, type) {
      switch (type) {
        // 开始拖拽
        case 'dragstart':
          this.draggableItem = item;
          break;
        // 拖拽到某个元素上
        case 'dragenter':
          if (this.draggableTargetItem.ext) {
            this.draggableTargetItem.ext.tabDraggable = false;
          }
          this.draggableTargetItem = item;
          this.draggableTargetItem.ext.tabDraggable = true;
          break;
        // 结束拖拽
        case 'dragend':
          if (this.draggableItem.tmp_id !== this.draggableTargetItem.tmp_id) {
            const itemIndex = this.scripts.indexOf(this.draggableItem);
            const targetIndex = this.scripts.indexOf(this.draggableTargetItem);
            this.scripts.splice(itemIndex, 1);
            this.scripts.splice(targetIndex, 0, this.draggableItem);
          }
          this.draggableTargetItem.ext.tabDraggable = false;
          break;
      }
    },
  },
};
</script>

<style scoped>
.operation span {
  cursor: pointer;
}

.ma-editor-container {
  flex: 1;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  margin-left: -5px;
  position: relative;
  border-left: 1px solid var(--toolbox-border-color);
  border-right: 1px solid var(--toolbox-border-color);
}

.ma-wrapper {
  height: 23px;
  line-height: 23px;
  width: 100%;
  overflow: hidden;
  flex: none !important;
  border-bottom: 1px solid var(--tab-bar-border-color);
}

.ma-wrapper .ma-tab .ma-icon:first-child {
  font-size: 16px;
  padding-right: 3px;
}

.ma-hot-key {
  position: absolute;
  top: 50%;
  margin-top: -105px;
  text-align: center;
  color: var(--empty-color);
  font-size: 16px;
  width: 100%;
}

.ma-hot-key p {
  display: inline-block;
  text-align: left;
  line-height: 30px;
}

.ma-hot-key p em {
  margin-left: 15px;
  font-style: normal;
  color: var(--empty-key-color);
}

.ma-empty-container {
  flex: none !important;
  position: absolute;
  z-index: 2;
  width: 100%;
  height: 100%;
  background: var(--empty-background);
}

ul {
  width: 100%;
  overflow: hidden;
  flex-wrap: nowrap;
  white-space: nowrap;
  list-style-type: none;
  display: flex;
  align-items: center;
  background: var(--background);
}

ul li {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 22px;
  line-height: 22px;
  background: var(--background);
  padding: 0 10px;
  /* border-bottom: 3px solid transparent; */
  color: var(--color);
}

ul li.selected {
  border-bottom: 2px solid #4083c9;
  color: var(--selected-color);
}

ul li:hover,
ul li.draggableTargetItem {
  background: var(--hover-background);
}

ul li i:not(.ma-icon-lock) {
  color: var(--icon-color);
  margin-left: 5px;
  font-size: 0.5em;
}

.ma-icon-lock {
  margin-left: 5px;
}

.ma-editor-container > div {
  flex: 1;
}
</style>
