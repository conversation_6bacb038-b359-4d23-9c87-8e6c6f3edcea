@font-face {
  font-family: JetBrainsMono;
  src: url(fonts/JetBrainsMono-Regular.8647cf34.woff2) format("woff2");
  font-weight: 100;
  font-style: normal
}

.ma-container {
  font-size: 12px;
  letter-spacing: 0;
  overflow: auto;
  display: flex;
  flex-direction: column;
  position: relative;
  width: 100%;
  height: 100%;
  min-width: 1200px;
  min-height: 600px;
  --color: #000;
  --empty-color: #505050;
  --empty-key-color: #5263a0;
  --background: #f2f2f2;
  --empty-background: #b6b6b6;
  --border-color: #cdcdcd;
  --input-border-color: #bdbdbd;
  --input-border-foucs-color: #0784de;
  --input-background: #fff;
  --select-border-color: grey;
  --select-background: #e3e3e3;
  --select-icon-background: #fff;
  --select-option-background: #fff;
  --select-hover-background: #e3f1fa;
  --select-option-hover-background: #1a7dc4;
  --select-option-hover-color: #fff;
  --select-option-disabled-color: #c0c4cc;
  --select-inputable-background: #fff;
  --select-inputable-border: #bdbdbd;
  --checkbox-background: #fff;
  --checkbox-text-color: #fff;
  --checkbox-border: #b0b0b0;
  --checkbox-selected-border: #4f9ee3;
  --checkbox-selected-background: #4f9ee3;
  --scollbar-color: hsla(0, 0%, 66.7%, 0.7);
  --scollbar-background: hsla(0, 0%, 86.7%, 0.3);
  --scollbar-thumb-background: hsla(0, 0%, 66.7%, 0.4);
  --scollbar-thumb-hover-background: hsla(0, 0%, 66.7%, 0.7);
  --scollbar-scrollbar-corner-background: hsla(0, 0%, 86.7%, 0.3);
  --header-title-color: #000;
  --header-version-color: #333;
  --header-default-color: #6e6e6e;
  --dialog-button-hover-border-color: #99a0a5;
  --dialog-button-hover-background: #e3f1fa;
  --dialog-button-background: #e3e3e3;
  --dialog-button-border: #adadad;
  --dialog-border-color: #707070;
  --dialog-shadow-color: #cfcfcf;
  --middle-background: #f0f0f0;
  --button-run-color: #59a869;
  --button-hover-background: #d9d9d9;
  --button-disabled-background: #bdbdbd;
  --toolbox-border-right-color: silver;
  --toolbox-border-color: #c9c9c9;
  --icon-color: #6e6e6e;
  --icon-debug-color: #59a869;
  --icon-step-color: #389fd6;
  --selected-background: #bdbdbd;
  --hover-background: #d9d9d9;
  --toolbox-list-hover-background: #d4d4d4;
  --toolbox-background: #fff;
  --toolbox-list-selected-background: #d4d4d4;
  --toolbox-list-icon-color: #aeb9c0;
  --toolbox-list-span-color: #999;
  --toolbox-list-label-color: #000;
  --toolbox-list-arrow-color: #b3b3b3;
  --toolbox-list-header-icon-color: #7f7f7f;
  --tab-bar-border-color: #c9c9c9;
  --footer-border-color: #919191;
  --table-col-border-color: #e5e5e5;
  --table-row-border-color: silver;
  --table-even-background: #f2f5f9;
  --table-hover-color: #fff;
  --table-hover-background: #1a7dc4;
  --breakpoints-background: #db5860;
  --debug-line-background: #2154a6;
  --breakpoint-line-background: #faeae6;
  --todo-color: #008dde;
  --history-select-background: #1a7dc4;
  --history-select-color: #fff;
  --text-number-color: #00f;
  --text-string-color: green;
  --text-boolean-color: navy;
  --text-default-color: #000;
  --text-key-color: #660e7a;
  --suggest-hover-background: #d6ebff;
  --suggest-hover-color: #000;
  --statusbar-em-color: #007f31;
  --run-log-background: #fff;
  --log-color-info: #00cd00;
  --log-color-warn: #a66f00;
  --log-color-debug: #0cc;
  --log-color-error: red;
  --log-color-trace: #00e;
  --log-color-cyan: #0cc;
  --log-color-link: #006dcc;
  scrollbar-color: var(--scollbar-color) var(--scollbar-color);
  scrollbar-width: thin;
  outline: 0
}

.ma-container, .ma-container .monaco-editor, .ma-container pre {
}

.ma-container * {
}

.ma-container label {
  font-weight: 400
}

.ma-container .ma-dialog-wrapper .ma-dialog .ma-dialog-header, .ma-container .ma-logo {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAAQCAYAAAAbBi9cAAAACXBIWXMAAA7EAAAOxAGVKw4bAAABE0lEQVQ4T2OcdVXzKcN/Rg4GIPjL8NcvU+fm0d+LAjcz/PttBRLDCphYg6brX77Lwcx2ESbP8v8/gyAjAwMnSID5HyMLWOLfb6DBjEJYDQEJ/v/FxM78jwnIgqsBcagCIC5AM4qRgXHGPwaGbcjCjP//1zMwMvLDxP4wcLxjZWTIhPGxGsSSsHktujN/L/ApBorBDcrWufYFyJ8BU0c1r9HGIBYmLmZ0LxHLR3HRn3/f/hKrEV0dbbzGziLAim4TsXwUF/1k/PCbWI3o6lDS0aa3kc4M2yIkGP4zsAHzCTjgPdVYlm9XVf0JZOKNCBSDbv/QrgWmYKhlwBwIBKxvmbcDqZfAvIfX29QLbIi9UEdQQAEA26I995D9bqEAAAAASUVORK5CYII=)
}

.ma-container input::-webkit-input-placeholder {
  font-size: 12px
}

.ma-container input::-moz-placeholder {
  font-size: 12px
}

.ma-container input:-ms-input-placeholder {
  font-size: 12px
}

.ma-container * {
  scrollbar-color: var(--scollbar-thumb-background) var(--scollbar-thumb-background);
  scrollbar-track-color: var(--scollbar-thumb-background);
  -ms-scrollbar-track-color: var(--scollbar-thumb-background);
  scrollbar-width: thin
}

.ma-container .not-select {
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none
}

.ma-container .monaco-editor .margin-view-overlays .codicon-folding-collapsed, .ma-container .monaco-editor .margin-view-overlays .codicon-folding-expanded {
  margin-left: 12px !important
}

.ma-container ul li {
  list-style: none
}

.ma-container .breakpoints {
  background: var(--breakpoints-background);
  width: 10px !important;
  height: 10px !important;
  right: 0 !important;
  margin-left: 12px;
  top: 5px;
  border-radius: 5px
}

.ma-container .debug-line {
  background: var(--debug-line-background);
  color: #fff !important
}

.ma-container .breakpoint-line {
  background: var(--breakpoint-line-background)
}

.ma-icon.ma-icon-http-api {
  color: #87cee7 !important
}

.ma-icon.ma-icon-function {
  color: #f3c373 !important
}

.ma-container .ma-button {
  height: 22px;
  line-height: 22px;
  background: var(--dialog-button-background);
  text-align: center;
  padding: 0 15px;
  border: 1px solid var(--dialog-button-border);
  outline: 0;
  cursor: pointer;
  color: var(--color)
}

.ma-container .ma-button.active, .ma-container .ma-button:hover {
  background: var(--dialog-button-hover-background);
  border-color: var(--dialog-button-hover-border-color)
}

.ma-request-wrapper {
  background: var(--background);
  height: 100%;
  width: 100%;
  position: relative
}

.ma-api-info {
  padding: 5px;
  border-bottom: 1px solid var(--tab-bar-border-color);
  display: flex
}

.ma-api-info * {
  display: inline-block
}

.ma-api-info label {
  width: 75px;
  text-align: right;
  padding: 0 5px;
  height: 22px;
  line-height: 22px
}

.ma-api-info > .ma-select {
  width: 80px
}

.ma-request-wrapper > div:not(.ma-api-info) {
  position: absolute;
  top: 33px;
  bottom: 0;
  width: 100%;
  overflow: hidden;
  display: inline-block
}

.ma-request-wrapper > div > h3 {
  color: var(--color);
  font-size: 12px;
  font-weight: inherit;
  height: 24px;
  line-height: 24px;
  text-align: center;
  border-bottom: 1px solid var(--tab-bar-border-color)
}

.ma-table-request-row {
  display: flex
}

.ma-container .monaco-list .monaco-list-row.focused {
  background-color: var(--suggest-hover-background) !important;
  color: var(--suggest-hover-color) !important
}

.ma-container .monaco-list-row.focused .monaco-highlighted-label .highlight {
  color: #0097fb !important
}

.ma-container .ma-status-container em, .ma-event .ma-content em {
  color: var(--statusbar-em-color);
  font-style: normal;
  font-weight: 700
}

.ma-log pre span.log-INFO {
  color: var(--log-color-info)
}

.ma-log pre span.log-DEBUG {
  color: var(--log-color-debug)
}

.ma-log pre span.log-ERROR {
  color: var(--log-color-error)
}

.ma-log pre span.log-WARN {
  color: var(--log-color-warn)
}

.ma-log pre span.log-TRACE {
  color: var(--log-color-trace)
}

.ma-log pre span.log-cyan {
  color: var(--log-color-cyan)
}

.ma-log pre a.log-link {
  color: var(--log-color-link)
}

@-webkit-keyframes rotate {
  0% {
    transform: rotate(0deg)
  }
  to {
    transform: rotate(1turn)
  }
}

@keyframes rotate {
  0% {
    transform: rotate(0deg)
  }
  to {
    transform: rotate(1turn)
  }
}

@font-face {
  font-family: magic-iconfont;
  src: url(fonts/iconfont.1d586240.ttf) format("truetype")
}

.ma-icon {
  font-family: magic-iconfont !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale
}

.ma-icon-lock:before {
  content: "\e64f"
}

.ma-icon-unlock:before {
  content: "\e783"
}

.ma-icon-collapse:before {
  content: "\e609"
}

.ma-icon-expand:before {
  content: "\efad"
}

.ma-icon-mysql:before {
  content: "\e61e"
}

.ma-icon-postgresql:before {
  content: "\e603"
}

.ma-icon-oracle:before {
  content: "\e7c8"
}

.ma-icon-event:before {
  content: "\e664"
}

.ma-icon-sql-server:before {
  content: "\e605"
}

.ma-icon-clickhouse:before {
  content: "\ec35"
}

.ma-icon-todo:before {
  content: "\e602"
}

.ma-icon-push:before {
  content: "\e79d"
}

.ma-icon-copy:before {
  content: "\ec7a"
}

.ma-icon-move:before {
  content: "\e727"
}

.ma-icon-update:before {
  content: "\e7e4"
}

.ma-icon-logout:before {
  content: "\e65a"
}

.ma-icon-datasource:before {
  content: "\e615"
}

.ma-icon-table:before {
  content: "\e619"
}

.ma-icon-upload:before {
  content: "\e658"
}

.ma-icon-download:before {
  content: "\e659"
}

.ma-icon-http-api:before {
  content: "\e6e3"
}

.ma-icon-function:before {
  content: "\e604"
}

.ma-icon-search:before {
  content: "\e608"
}

.ma-icon-refresh:before {
  content: "\e747"
}

.ma-icon-ascending:before {
  content: "\e7a7"
}

.ma-icon-descending:before {
  content: "\e7a8"
}

.ma-icon-group-add:before {
  content: "\e6cd"
}

.ma-icon-folding:before {
  content: "\e647"
}

.ma-icon-minus:before {
  content: "\e68a"
}

.ma-icon-plus:before {
  content: "\e621"
}

.ma-icon-api:before {
  content: "\e700"
}

.ma-icon-settings:before {
  content: "\e786"
}

.ma-icon-star:before {
  content: "\e601"
}

.ma-icon-unfold:before {
  content: "\e732"
}

.ma-icon-fold:before {
  content: "\e66b"
}

.ma-icon-step-over:before {
  content: "\e7b2"
}

.ma-icon-history:before {
  content: "\e668"
}

.ma-icon-log:before {
  content: "\efac"
}

.ma-icon-minimize:before {
  content: "\e707"
}

.ma-icon-save:before {
  content: "\e66c"
}

.ma-icon-close:before {
  content: "\e652"
}

.ma-icon-skin:before {
  content: "\e606"
}

.ma-icon-qq:before {
  content: "\e635"
}

.ma-icon-help:before {
  content: "\e60d"
}

.ma-icon-git:before {
  content: "\e64a"
}

.ma-icon-gitee:before {
  content: "\e6d6"
}

.ma-icon-delete:before {
  content: "\e607"
}

.ma-icon-clear:before {
  content: "\e673"
}

.ma-icon-continue:before {
  content: "\e663"
}

.ma-icon-format:before {
  content: "\e6c1"
}

.ma-icon-script:before {
  content: "\e61d"
}

.ma-icon-arrow-right:before {
  content: "\e600"
}

.ma-icon-arrow-bottom:before {
  content: "\efa2"
}

.ma-icon-list:before {
  content: "\e679"
}

.ma-icon-options:before {
  content: "\e60f"
}

.ma-icon-debug-info:before {
  content: "\efa1"
}

.ma-icon-run:before {
  content: "\e626"
}

.ma-icon-parameter:before {
  content: "\e6e9"
}

.ma-icon-position:before {
  content: "\e60b"
}

.ma-dialog-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 999;
  text-align: center
}

.ma-dialog-wrapper.shade {
  background: rgba(0, 0, 0, .6)
}

.ma-dialog-wrapper.moveable .ma-dialog .ma-dialog-header {
  cursor: move
}

.ma-dialog-wrapper:before {
  content: "";
  display: inline-block;
  height: 100%;
  vertical-align: middle;
  margin-right: -.25em
}

.ma-dialog-wrapper .ma-dialog {
  background: var(--background);
  border: 1px solid var(--dialog-border-color);
  display: inline-block;
  vertical-align: middle;
  position: relative;
  min-width: 250px;
  box-shadow: 0 0 8px var(--dialog-shadow-color);
  max-width: 800px;
  color: var(--color)
}

.ma-dialog-wrapper .ma-dialog .ma-dialog-header {
  height: 30px;
  line-height: 30px;
  padding-left: 30px;
  padding-right: 75px;
  background-position: 7px 7px;
  background-repeat: no-repeat;
  text-align: left
}

.ma-dialog-wrapper .ma-dialog .ma-dialog-header span {
  display: inline-block;
  width: 30px;
  position: absolute;
  right: 0;
  text-align: center;
  cursor: pointer;
  font-size: 12px;
  height: 30px;
  line-height: 30px
}

.ma-dialog-wrapper .ma-dialog .ma-dialog-header span:hover:not(.disabled) {
  background: #e81123
}

.ma-dialog-wrapper .ma-dialog .ma-dialog-header span:hover:not(.disabled) i {
  color: var(--select-icon-background)
}

.ma-dialog-wrapper .ma-dialog .ma-dialog-content {
  text-align: left;
  word-break: break-word
}

.ma-dialog-wrapper .ma-dialog .ma-dialog-buttons {
  padding: 5px 0
}

.ma-dialog-wrapper .ma-dialog .ma-dialog-buttons.button-align-right {
  text-align: right;
  margin-right: 10px
}

.ma-dialog-wrapper .ma-dialog .ma-dialog-buttons button:not(:last-child) {
  margin-right: 10px
}

.ma-loading-wrapper[data-v-49da6834] {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 9999999;
  text-align: center;
  background: #fff
}

.ma-loading[data-v-49da6834] {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 500px;
  height: 100px;
  margin-left: -250px;
  margin-top: -50px;
  text-align: center
}

.ma-loading .ma-title[data-v-49da6834] {
  font-size: 0;
  color: #0075ff;
  letter-spacing: 0
}

.ma-loading .ma-title label[data-v-49da6834] {
  font-size: 14px;
  display: inline-block;
  margin-top: 5px;
  vertical-align: top
}

.ma-loading .ma-title span[data-v-49da6834] {
  font-size: 20px;
  display: inline-block;
  padding: 0 3px;
  -webkit-animation: stretch-data-v-49da6834 1s infinite;
  animation: stretch-data-v-49da6834 1s infinite
}

.ma-loading .ma-title span[data-v-49da6834]:first-child {
  -webkit-animation-delay: 0s;
  animation-delay: 0s
}

.ma-loading .ma-title span[data-v-49da6834]:nth-child(2) {
  -webkit-animation-delay: .0625s;
  animation-delay: .0625s
}

.ma-loading .ma-title span[data-v-49da6834]:nth-child(3) {
  -webkit-animation-delay: .125s;
  animation-delay: .125s
}

.ma-loading .ma-title span[data-v-49da6834]:nth-child(4) {
  -webkit-animation-delay: .1875s;
  animation-delay: .1875s
}

.ma-loading .ma-title span[data-v-49da6834]:nth-child(5) {
  -webkit-animation-delay: .25s;
  animation-delay: .25s
}

.ma-loading .ma-title span[data-v-49da6834]:nth-child(6) {
  -webkit-animation-delay: .3125s;
  animation-delay: .3125s
}

.ma-loading .ma-title span[data-v-49da6834]:nth-child(7) {
  -webkit-animation-delay: .375s;
  animation-delay: .375s
}

.ma-loading .ma-title span[data-v-49da6834]:nth-child(8) {
  -webkit-animation-delay: .4375s;
  animation-delay: .4375s
}

.ma-loading .ma-loading-text[data-v-49da6834] {
  text-align: center;
  font-weight: bolder;
  font-style: italic;
  color: #889aa4;
  font-size: 14px;
  margin-top: 5px;
  -webkit-animation: blink-loading-data-v-49da6834 2s ease-in infinite;
  animation: blink-loading-data-v-49da6834 2s ease-in infinite
}

@-webkit-keyframes stretch-data-v-49da6834 {
  0% {
    transform: scale(1)
  }
  25% {
    transform: scale(1.2)
  }
  50% {
    transform: scale(1)
  }
  to {
    transform: scale(1)
  }
}

@keyframes stretch-data-v-49da6834 {
  0% {
    transform: scale(1)
  }
  25% {
    transform: scale(1.2)
  }
  50% {
    transform: scale(1)
  }
  to {
    transform: scale(1)
  }
}

@-webkit-keyframes blink-loading-data-v-49da6834 {
  0% {
    opacity: 1
  }
  50% {
    opacity: .5
  }
  to {
    opacity: 1
  }
}

@keyframes blink-loading-data-v-49da6834 {
  0% {
    opacity: 1
  }
  50% {
    opacity: .5
  }
  to {
    opacity: 1
  }
}

.monaco-editor {
  font-family: -apple-system, BlinkMacSystemFont, Segoe WPC, Segoe UI, HelveticaNeue-Light, system-ui, Ubuntu, Droid Sans, sans-serif;
  --monaco-monospace-font: "SF Mono", Monaco, Menlo, Consolas, "Ubuntu Mono", "Liberation Mono", "DejaVu Sans Mono", "Courier New", monospace
}

.monaco-editor.hc-black .monaco-menu .monaco-action-bar.vertical .action-menu-item:focus .action-label, .monaco-editor.vs-dark .monaco-menu .monaco-action-bar.vertical .action-menu-item:focus .action-label, .monaco-menu .monaco-action-bar.vertical .action-item .action-menu-item:focus .action-label {
  stroke-width: 1.2px
}

.monaco-hover p {
  margin: 0
}

.monaco-aria-container {
  position: absolute !important;
  top: 0;
  height: 1px;
  width: 1px;
  margin: -1px;
  overflow: hidden;
  padding: 0;
  clip: rect(1px, 1px, 1px, 1px);
  -webkit-clip-path: inset(50%);
  clip-path: inset(50%)
}

.monaco-editor.hc-black {
  -ms-high-contrast-adjust: none
}

@media screen and (-ms-high-contrast: active) {
  .monaco-editor.vs-dark .view-overlays .current-line, .monaco-editor.vs .view-overlays .current-line {
    border-color: windowtext !important;
    border-left: 0;
    border-right: 0
  }

  .monaco-editor.vs-dark .cursor, .monaco-editor.vs .cursor {
    background-color: windowtext !important
  }

  .monaco-editor.vs-dark .dnd-target, .monaco-editor.vs .dnd-target {
    border-color: windowtext !important
  }

  .monaco-editor.vs-dark .selected-text, .monaco-editor.vs .selected-text {
    background-color: highlight !important
  }

  .monaco-editor.vs-dark .view-line, .monaco-editor.vs .view-line {
    -ms-high-contrast-adjust: none
  }

  .monaco-editor.vs-dark .view-line span, .monaco-editor.vs .view-line span {
    color: windowtext !important
  }

  .monaco-editor.vs-dark .view-line span.inline-selected-text, .monaco-editor.vs .view-line span.inline-selected-text {
    color: highlighttext !important
  }

  .monaco-editor.vs-dark .view-overlays, .monaco-editor.vs .view-overlays {
    -ms-high-contrast-adjust: none
  }

  .monaco-editor.vs-dark .reference-decoration, .monaco-editor.vs-dark .selectionHighlight, .monaco-editor.vs-dark .wordHighlight, .monaco-editor.vs-dark .wordHighlightStrong, .monaco-editor.vs .reference-decoration, .monaco-editor.vs .selectionHighlight, .monaco-editor.vs .wordHighlight, .monaco-editor.vs .wordHighlightStrong {
    border: 2px dotted highlight !important;
    background: transparent !important;
    box-sizing: border-box
  }

  .monaco-editor.vs-dark .rangeHighlight, .monaco-editor.vs .rangeHighlight {
    background: transparent !important;
    border: 1px dotted activeborder !important;
    box-sizing: border-box
  }

  .monaco-editor.vs-dark .bracket-match, .monaco-editor.vs .bracket-match {
    border-color: windowtext !important;
    background: transparent !important
  }

  .monaco-editor.vs-dark .currentFindMatch, .monaco-editor.vs-dark .findMatch, .monaco-editor.vs .currentFindMatch, .monaco-editor.vs .findMatch {
    border: 2px dotted activeborder !important;
    background: transparent !important;
    box-sizing: border-box
  }

  .monaco-editor.vs-dark .find-widget, .monaco-editor.vs .find-widget {
    border: 1px solid windowtext
  }

  .monaco-editor.vs-dark .monaco-list .monaco-list-row, .monaco-editor.vs .monaco-list .monaco-list-row {
    -ms-high-contrast-adjust: none;
    color: windowtext !important
  }

  .monaco-editor.vs-dark .monaco-list .monaco-list-row.focused, .monaco-editor.vs .monaco-list .monaco-list-row.focused {
    color: highlighttext !important;
    background-color: highlight !important
  }

  .monaco-editor.vs-dark .monaco-list .monaco-list-row:hover, .monaco-editor.vs .monaco-list .monaco-list-row:hover {
    background: transparent !important;
    border: 1px solid highlight;
    box-sizing: border-box
  }

  .monaco-editor.vs-dark .monaco-scrollable-element > .scrollbar, .monaco-editor.vs .monaco-scrollable-element > .scrollbar {
    -ms-high-contrast-adjust: none;
    background: background !important;
    border: 1px solid windowtext;
    box-sizing: border-box
  }

  .monaco-editor.vs-dark .monaco-scrollable-element > .scrollbar > .slider, .monaco-editor.vs .monaco-scrollable-element > .scrollbar > .slider {
    background: windowtext !important
  }

  .monaco-editor.vs-dark .monaco-scrollable-element > .scrollbar > .slider.active, .monaco-editor.vs-dark .monaco-scrollable-element > .scrollbar > .slider:hover, .monaco-editor.vs .monaco-scrollable-element > .scrollbar > .slider.active, .monaco-editor.vs .monaco-scrollable-element > .scrollbar > .slider:hover {
    background: highlight !important
  }

  .monaco-editor.vs-dark .decorationsOverviewRuler, .monaco-editor.vs .decorationsOverviewRuler {
    opacity: 0
  }

  .monaco-editor.vs-dark .minimap, .monaco-editor.vs .minimap {
    display: none
  }

  .monaco-editor.vs-dark .squiggly-d-error, .monaco-editor.vs .squiggly-d-error {
    background: transparent !important;
    border-bottom: 4px double #e47777
  }

  .monaco-editor.vs-dark .squiggly-b-info, .monaco-editor.vs-dark .squiggly-c-warning, .monaco-editor.vs .squiggly-b-info, .monaco-editor.vs .squiggly-c-warning {
    border-bottom: 4px double #71b771
  }

  .monaco-editor.vs-dark .squiggly-a-hint, .monaco-editor.vs .squiggly-a-hint {
    border-bottom: 4px double #6c6c6c
  }

  .monaco-editor.vs-dark .monaco-menu .monaco-action-bar.vertical .action-menu-item:focus .action-label, .monaco-editor.vs .monaco-menu .monaco-action-bar.vertical .action-menu-item:focus .action-label {
    -ms-high-contrast-adjust: none;
    color: highlighttext !important;
    background-color: highlight !important
  }

  .monaco-editor.vs-dark .monaco-menu .monaco-action-bar.vertical .action-menu-item:hover .action-label, .monaco-editor.vs .monaco-menu .monaco-action-bar.vertical .action-menu-item:hover .action-label {
    -ms-high-contrast-adjust: none;
    background: transparent !important;
    border: 1px solid highlight;
    box-sizing: border-box
  }

  .monaco-diff-editor.vs-dark .diffOverviewRuler, .monaco-diff-editor.vs .diffOverviewRuler {
    display: none
  }

  .monaco-editor.vs-dark .line-delete, .monaco-editor.vs-dark .line-insert, .monaco-editor.vs .line-delete, .monaco-editor.vs .line-insert {
    background: transparent !important;
    border: 1px solid highlight !important;
    box-sizing: border-box
  }

  .monaco-editor.vs-dark .char-delete, .monaco-editor.vs-dark .char-insert, .monaco-editor.vs .char-delete, .monaco-editor.vs .char-insert {
    background: transparent !important
  }
}

.monaco-aria-container {
  position: absolute;
  left: -999em
}

::-ms-clear {
  display: none
}

.monaco-editor .editor-widget input {
  color: inherit
}

.monaco-editor {
  position: relative;
  overflow: visible;
  -webkit-text-size-adjust: 100%
}

.monaco-editor .overflow-guard {
  position: relative;
  overflow: hidden
}

.monaco-editor .view-overlays {
  position: absolute;
  top: 0
}

.monaco-editor .inputarea {
  min-width: 0;
  min-height: 0;
  margin: 0;
  padding: 0;
  position: absolute;
  outline: none !important;
  resize: none;
  border: none;
  overflow: hidden;
  color: transparent;
  background-color: transparent
}

.monaco-editor .inputarea.ime-input {
  z-index: 10
}

.monaco-editor .margin-view-overlays .line-numbers {
  font-variant-numeric: tabular-nums;
  position: absolute;
  text-align: right;
  display: inline-block;
  vertical-align: middle;
  box-sizing: border-box;
  cursor: default;
  height: 100%
}

.monaco-editor .relative-current-line-number {
  text-align: left;
  display: inline-block;
  width: 100%
}

.monaco-editor .margin-view-overlays .line-numbers.lh-odd {
  margin-top: 1px
}

.monaco-mouse-cursor-text {
  cursor: text
}

.hc-black.mac .monaco-mouse-cursor-text, .hc-black .mac .monaco-mouse-cursor-text, .vs-dark.mac .monaco-mouse-cursor-text, .vs-dark .mac .monaco-mouse-cursor-text {
  cursor: -webkit-image-set(url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAQAAAC1+jfqAAAAL0lEQVQoz2NgCD3x//9/BhBYBWdhgFVAiVW4JBFKGIa4AqD0//9D3pt4I4tAdAMAHTQ/j5Zom30AAAAASUVORK5CYII=) 1x, url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAQAAADZc7J/AAAAz0lEQVRIx2NgYGBY/R8I/vx5eelX3n82IJ9FxGf6tksvf/8FiTMQAcAGQMDvSwu09abffY8QYSAScNk45G198eX//yev73/4///701eh//kZSARckrNBRvz//+8+6ZohwCzjGNjdgQxkAg7B9WADeBjIBqtJCbhRA0YNoIkBSNmaPEMoNmA0FkYNoFKhapJ6FGyAH3nauaSmPfwI0v/3OukVi0CIZ+F25KrtYcx/CTIy0e+rC7R1Z4KMICVTQQ14feVXIbR695u14+Ir4gwAAD49E54wc1kWAAAAAElFTkSuQmCC) 2x) 5 8, text
}

.monaco-editor .margin-view-overlays .current-line, .monaco-editor .view-overlays .current-line {
  display: block;
  position: absolute;
  left: 0;
  top: 0;
  box-sizing: border-box
}

.monaco-editor .margin-view-overlays .current-line.current-line-margin.current-line-margin-both {
  border-right: 0
}

.monaco-editor .lines-content .cdr {
  position: absolute
}

.monaco-scrollable-element > .scrollbar > .scra {
  cursor: pointer;
  font-size: 11px !important
}

.monaco-scrollable-element > .visible {
  opacity: 1;
  background: transparent;
  transition: opacity .1s linear
}

.monaco-scrollable-element > .invisible {
  opacity: 0;
  pointer-events: none
}

.monaco-scrollable-element > .invisible.fade {
  transition: opacity .8s linear
}

.monaco-scrollable-element > .shadow {
  position: absolute;
  display: none
}

.monaco-scrollable-element > .shadow.top {
  display: block;
  top: 0;
  left: 3px;
  height: 3px;
  width: 100%;
  box-shadow: inset 0 6px 6px -6px #ddd
}

.monaco-scrollable-element > .shadow.left {
  display: block;
  top: 3px;
  left: 0;
  height: 100%;
  width: 3px;
  box-shadow: inset 6px 0 6px -6px #ddd
}

.monaco-scrollable-element > .shadow.top-left-corner {
  display: block;
  top: 0;
  left: 0;
  height: 3px;
  width: 3px
}

.monaco-scrollable-element > .shadow.top.left {
  box-shadow: inset 6px 6px 6px -6px #ddd
}

.vs .monaco-scrollable-element > .scrollbar > .slider {
  background: hsla(0, 0%, 39.2%, .4)
}

.vs-dark .monaco-scrollable-element > .scrollbar > .slider {
  background: hsla(0, 0%, 47.5%, .4)
}

.hc-black .monaco-scrollable-element > .scrollbar > .slider {
  background: rgba(111, 195, 223, .6)
}

.monaco-scrollable-element > .scrollbar > .slider:hover {
  background: hsla(0, 0%, 39.2%, .7)
}

.hc-black .monaco-scrollable-element > .scrollbar > .slider:hover {
  background: rgba(111, 195, 223, .8)
}

.monaco-scrollable-element > .scrollbar > .slider.active {
  background: rgba(0, 0, 0, .6)
}

.vs-dark .monaco-scrollable-element > .scrollbar > .slider.active {
  background: hsla(0, 0%, 74.9%, .4)
}

.hc-black .monaco-scrollable-element > .scrollbar > .slider.active {
  background: #6fc3df
}

.vs-dark .monaco-scrollable-element .shadow.top {
  box-shadow: none
}

.vs-dark .monaco-scrollable-element .shadow.left {
  box-shadow: inset 6px 0 6px -6px #000
}

.vs-dark .monaco-scrollable-element .shadow.top.left {
  box-shadow: inset 6px 6px 6px -6px #000
}

.hc-black .monaco-scrollable-element .shadow.left, .hc-black .monaco-scrollable-element .shadow.top, .hc-black .monaco-scrollable-element .shadow.top.left {
  box-shadow: none
}

.monaco-editor .glyph-margin {
  position: absolute;
  top: 0
}

.monaco-editor .margin-view-overlays .cgmr {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center
}

.monaco-editor .lines-content .core-guide {
  position: absolute
}

.monaco-editor.no-user-select .lines-content, .monaco-editor.no-user-select .view-line, .monaco-editor.no-user-select .view-lines {
  -moz-user-select: none;
  user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none
}

.monaco-editor .view-lines {
  white-space: nowrap
}

.monaco-editor .view-line {
  position: absolute;
  width: 100%
}

.monaco-editor .mtkz {
  display: inline-block
}

.monaco-editor .lines-decorations {
  position: absolute;
  top: 0;
  background: #fff
}

.monaco-editor .margin-view-overlays .cldr {
  position: absolute;
  height: 100%
}

.monaco-editor .margin-view-overlays .cmdr {
  position: absolute;
  left: 0;
  width: 100%;
  height: 100%
}

.monaco-editor .minimap.slider-mouseover .minimap-slider {
  opacity: 0;
  transition: opacity .1s linear
}

.monaco-editor .minimap.slider-mouseover .minimap-slider.active, .monaco-editor .minimap.slider-mouseover:hover .minimap-slider {
  opacity: 1
}

.monaco-editor .minimap-shadow-hidden {
  position: absolute;
  width: 0
}

.monaco-editor .minimap-shadow-visible {
  position: absolute;
  left: -6px;
  width: 6px
}

.monaco-editor.no-minimap-shadow .minimap-shadow-visible {
  position: absolute;
  left: -1px;
  width: 1px
}

.monaco-editor .overlayWidgets {
  position: absolute;
  top: 0;
  left: 0
}

.monaco-editor .view-ruler {
  position: absolute;
  top: 0
}

.monaco-editor .scroll-decoration {
  position: absolute;
  top: 0;
  left: 0;
  height: 6px
}

.monaco-editor .lines-content .cslr {
  position: absolute
}

.monaco-editor .top-left-radius {
  border-top-left-radius: 3px
}

.monaco-editor .bottom-left-radius {
  border-bottom-left-radius: 3px
}

.monaco-editor .top-right-radius {
  border-top-right-radius: 3px
}

.monaco-editor .bottom-right-radius {
  border-bottom-right-radius: 3px
}

.monaco-editor.hc-black .top-left-radius {
  border-top-left-radius: 0
}

.monaco-editor.hc-black .bottom-left-radius {
  border-bottom-left-radius: 0
}

.monaco-editor.hc-black .top-right-radius {
  border-top-right-radius: 0
}

.monaco-editor.hc-black .bottom-right-radius {
  border-bottom-right-radius: 0
}

.monaco-editor .cursors-layer {
  position: absolute;
  top: 0
}

.monaco-editor .cursors-layer > .cursor {
  position: absolute;
  overflow: hidden
}

.monaco-editor .cursors-layer.cursor-smooth-caret-animation > .cursor {
  transition: all 80ms
}

.monaco-editor .cursors-layer.cursor-block-outline-style > .cursor {
  box-sizing: border-box;
  background: transparent !important;
  border-style: solid;
  border-width: 1px
}

.monaco-editor .cursors-layer.cursor-underline-style > .cursor {
  border-bottom-width: 2px;
  border-bottom-style: solid;
  background: transparent !important;
  box-sizing: border-box
}

.monaco-editor .cursors-layer.cursor-underline-thin-style > .cursor {
  border-bottom-width: 1px;
  border-bottom-style: solid;
  background: transparent !important;
  box-sizing: border-box
}

@-webkit-keyframes monaco-cursor-smooth {
  0%, 20% {
    opacity: 1
  }
  60%, to {
    opacity: 0
  }
}

@keyframes monaco-cursor-smooth {
  0%, 20% {
    opacity: 1
  }
  60%, to {
    opacity: 0
  }
}

@-webkit-keyframes monaco-cursor-phase {
  0%, 20% {
    opacity: 1
  }
  90%, to {
    opacity: 0
  }
}

@keyframes monaco-cursor-phase {
  0%, 20% {
    opacity: 1
  }
  90%, to {
    opacity: 0
  }
}

@-webkit-keyframes monaco-cursor-expand {
  0%, 20% {
    transform: scaleY(1)
  }
  80%, to {
    transform: scaleY(0)
  }
}

@keyframes monaco-cursor-expand {
  0%, 20% {
    transform: scaleY(1)
  }
  80%, to {
    transform: scaleY(0)
  }
}

.cursor-smooth {
  -webkit-animation: monaco-cursor-smooth .5s ease-in-out 0s 20 alternate;
  animation: monaco-cursor-smooth .5s ease-in-out 0s 20 alternate
}

.cursor-phase {
  -webkit-animation: monaco-cursor-phase .5s ease-in-out 0s 20 alternate;
  animation: monaco-cursor-phase .5s ease-in-out 0s 20 alternate
}

.cursor-expand > .cursor {
  -webkit-animation: monaco-cursor-expand .5s ease-in-out 0s 20 alternate;
  animation: monaco-cursor-expand .5s ease-in-out 0s 20 alternate
}

.monaco-diff-editor .diffOverview {
  z-index: 9
}

.monaco-diff-editor .diffOverview .diffViewport {
  z-index: 10
}

.monaco-diff-editor.vs .diffOverview {
  background: rgba(0, 0, 0, .03)
}

.monaco-diff-editor.vs-dark .diffOverview {
  background: hsla(0, 0%, 100%, .01)
}

.monaco-scrollable-element.modified-in-monaco-diff-editor.vs-dark .scrollbar, .monaco-scrollable-element.modified-in-monaco-diff-editor.vs .scrollbar {
  background: transparent
}

.monaco-scrollable-element.modified-in-monaco-diff-editor.hc-black .scrollbar {
  background: none
}

.monaco-scrollable-element.modified-in-monaco-diff-editor .slider {
  z-index: 10
}

.modified-in-monaco-diff-editor .slider.active {
  background: hsla(0, 0%, 67.1%, .4)
}

.modified-in-monaco-diff-editor.hc-black .slider.active {
  background: none
}

.monaco-diff-editor .delete-sign, .monaco-diff-editor .insert-sign, .monaco-editor .delete-sign, .monaco-editor .insert-sign {
  font-size: 11px !important;
  opacity: .7 !important;
  display: flex !important;
  align-items: center
}

.monaco-diff-editor.hc-black .delete-sign, .monaco-diff-editor.hc-black .insert-sign, .monaco-editor.hc-black .delete-sign, .monaco-editor.hc-black .insert-sign {
  opacity: 1
}

.monaco-editor .inline-added-margin-view-zone, .monaco-editor .inline-deleted-margin-view-zone {
  text-align: right
}

.monaco-editor .view-zones .view-lines .view-line span {
  display: inline-block
}

.monaco-editor .margin-view-zones .lightbulb-glyph:hover {
  cursor: pointer
}

:root {
  --sash-size: 4px
}

.monaco-sash {
  position: absolute;
  z-index: 35;
  touch-action: none
}

.monaco-sash.disabled {
  pointer-events: none
}

.monaco-sash.mac.vertical {
  cursor: col-resize
}

.monaco-sash.vertical.minimum {
  cursor: e-resize
}

.monaco-sash.vertical.maximum {
  cursor: w-resize
}

.monaco-sash.mac.horizontal {
  cursor: row-resize
}

.monaco-sash.horizontal.minimum {
  cursor: s-resize
}

.monaco-sash.horizontal.maximum {
  cursor: n-resize
}

.monaco-sash.disabled {
  cursor: default !important;
  pointer-events: none !important
}

.monaco-sash.vertical {
  cursor: ew-resize;
  top: 0;
  width: var(--sash-size);
  height: 100%
}

.monaco-sash.horizontal {
  cursor: ns-resize;
  left: 0;
  width: 100%;
  height: var(--sash-size)
}

.monaco-sash:not(.disabled) > .orthogonal-drag-handle {
  content: " ";
  height: calc(var(--sash-size) * 2);
  width: calc(var(--sash-size) * 2);
  z-index: 100;
  display: block;
  cursor: all-scroll;
  position: absolute
}

.monaco-sash.horizontal.orthogonal-edge-north:not(.disabled) > .orthogonal-drag-handle.start, .monaco-sash.horizontal.orthogonal-edge-south:not(.disabled) > .orthogonal-drag-handle.end {
  cursor: nwse-resize
}

.monaco-sash.horizontal.orthogonal-edge-north:not(.disabled) > .orthogonal-drag-handle.end, .monaco-sash.horizontal.orthogonal-edge-south:not(.disabled) > .orthogonal-drag-handle.start {
  cursor: nesw-resize
}

.monaco-sash.vertical > .orthogonal-drag-handle.start {
  left: calc(var(--sash-size) * -0.5);
  top: calc(var(--sash-size) * -1)
}

.monaco-sash.vertical > .orthogonal-drag-handle.end {
  left: calc(var(--sash-size) * -0.5);
  bottom: calc(var(--sash-size) * -1)
}

.monaco-sash.horizontal > .orthogonal-drag-handle.start {
  top: calc(var(--sash-size) * -0.5);
  left: calc(var(--sash-size) * -1)
}

.monaco-sash.horizontal > .orthogonal-drag-handle.end {
  top: calc(var(--sash-size) * -0.5);
  right: calc(var(--sash-size) * -1)
}

.monaco-sash:before {
  content: "";
  pointer-events: none;
  position: absolute;
  width: 100%;
  height: 100%;
  transition: background-color .1s ease-out;
  background: transparent
}

.monaco-sash.vertical:before {
  width: var(--sash-hover-size);
  left: calc(50% - var(--sash-hover-size) / 2)
}

.monaco-sash.horizontal:before {
  height: var(--sash-hover-size);
  top: calc(50% - var(--sash-hover-size) / 2)
}

.monaco-sash.debug {
  background: #0ff
}

.monaco-sash.debug.disabled {
  background: rgba(0, 255, 255, .2)
}

.monaco-sash.debug:not(.disabled) > .orthogonal-drag-handle {
  background: red
}

.monaco-diff-editor .diff-review-line-number {
  text-align: right;
  display: inline-block
}

.monaco-diff-editor .diff-review {
  position: absolute;
  -moz-user-select: none;
  user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none
}

.monaco-diff-editor .diff-review-summary {
  padding-left: 10px
}

.monaco-diff-editor .diff-review-shadow {
  position: absolute
}

.monaco-diff-editor .diff-review-row {
  white-space: pre
}

.monaco-diff-editor .diff-review-table {
  display: table;
  min-width: 100%
}

.monaco-diff-editor .diff-review-row {
  display: table-row;
  width: 100%
}

.monaco-diff-editor .diff-review-spacer {
  display: inline-block;
  width: 10px;
  vertical-align: middle
}

.monaco-diff-editor .diff-review-spacer > .codicon {
  font-size: 9px !important
}

.monaco-diff-editor .diff-review-actions {
  display: inline-block;
  position: absolute;
  right: 10px;
  top: 2px
}

.monaco-diff-editor .diff-review-actions .action-label {
  width: 16px;
  height: 16px;
  margin: 2px 0
}

.monaco-action-bar {
  white-space: nowrap;
  height: 100%
}

.monaco-action-bar .actions-container {
  display: flex;
  margin: 0 auto;
  padding: 0;
  height: 100%;
  width: 100%;
  align-items: center
}

.monaco-action-bar.vertical .actions-container {
  display: inline-block
}

.monaco-action-bar .action-item {
  display: block;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  position: relative
}

.monaco-action-bar .action-item.disabled {
  cursor: default
}

.monaco-action-bar .action-item .codicon, .monaco-action-bar .action-item .icon {
  display: block
}

.monaco-action-bar .action-item .codicon {
  display: flex;
  align-items: center;
  width: 16px;
  height: 16px
}

.monaco-action-bar .action-label {
  font-size: 11px;
  padding: 3px;
  border-radius: 5px
}

.monaco-action-bar .action-item.disabled .action-label, .monaco-action-bar .action-item.disabled .action-label:before, .monaco-action-bar .action-item.disabled .action-label:hover {
  opacity: .4
}

.monaco-action-bar.vertical {
  text-align: left
}

.monaco-action-bar.vertical .action-item {
  display: block
}

.monaco-action-bar.vertical .action-label.separator {
  display: block;
  border-bottom: 1px solid #bbb;
  padding-top: 1px;
  margin-left: .8em;
  margin-right: .8em
}

.monaco-action-bar .action-item .action-label.separator {
  width: 1px;
  height: 16px;
  margin: 5px 4px !important;
  cursor: default;
  min-width: 1px;
  padding: 0;
  background-color: #bbb
}

.secondary-actions .monaco-action-bar .action-label {
  margin-left: 6px
}

.monaco-action-bar .action-item.select-container {
  overflow: hidden;
  flex: 1;
  max-width: 170px;
  min-width: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px
}

.monaco-action-bar .action-item.action-dropdown-item {
  display: flex
}

.monaco-action-bar .action-item.action-dropdown-item > .action-label {
  margin-right: 1px
}

@font-face {
  font-family: codicon;
  font-display: block;
  src: url(fonts/codicon.888f9cc0.ttf) format("truetype")
}

.codicon[class*=codicon-] {
  font: normal normal normal 16px/1 codicon;
  display: inline-block;
  text-decoration: none;
  text-rendering: auto;
  text-align: center;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -moz-user-select: none;
  user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none
}

.codicon-wrench-subaction {
  opacity: .5
}

@-webkit-keyframes codicon-spin {
  to {
    transform: rotate(1turn)
  }
}

@keyframes codicon-spin {
  to {
    transform: rotate(1turn)
  }
}

.codicon-gear.codicon-modifier-spin, .codicon-loading.codicon-modifier-spin, .codicon-notebook-state-executing.codicon-modifier-spin, .codicon-sync.codicon-modifier-spin {
  -webkit-animation: codicon-spin 1.5s steps(30) infinite;
  animation: codicon-spin 1.5s steps(30) infinite
}

.codicon-modifier-disabled {
  opacity: .4
}

.codicon-loading, .codicon-tree-item-loading:before {
  -webkit-animation-duration: 1s !important;
  animation-duration: 1s !important;
  -webkit-animation-timing-function: cubic-bezier(.53, .21, .29, .67) !important;
  animation-timing-function: cubic-bezier(.53, .21, .29, .67) !important
}

.context-view {
  position: absolute;
  z-index: 2500
}

.context-view.fixed {
  all: initial;
  font-family: inherit;
  font-size: 13px;
  position: fixed;
  z-index: 2500;
  color: inherit
}

.context-view .monaco-menu {
  min-width: 130px
}

.monaco-list {
  position: relative;
  height: 100%;
  width: 100%;
  white-space: nowrap
}

.monaco-list.mouse-support {
  -moz-user-select: none;
  user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none
}

.monaco-list > .monaco-scrollable-element {
  height: 100%
}

.monaco-list-rows {
  position: relative;
  width: 100%;
  height: 100%
}

.monaco-list.horizontal-scrolling .monaco-list-rows {
  width: auto;
  min-width: 100%
}

.monaco-list-row {
  position: absolute;
  box-sizing: border-box;
  overflow: hidden;
  width: 100%
}

.monaco-list.mouse-support .monaco-list-row {
  cursor: pointer;
  touch-action: none
}

.monaco-list-row.scrolling {
  display: none !important
}

.monaco-list.element-focused, .monaco-list.selection-multiple, .monaco-list.selection-single {
  outline: 0 !important
}

.monaco-drag-image {
  display: inline-block;
  padding: 1px 7px;
  border-radius: 10px;
  font-size: 12px;
  position: absolute;
  z-index: 1000
}

.monaco-list-type-filter {
  display: flex;
  align-items: center;
  position: absolute;
  border-radius: 2px;
  padding: 0 3px;
  max-width: calc(100% - 10px);
  text-overflow: ellipsis;
  overflow: hidden;
  text-align: right;
  box-sizing: border-box;
  cursor: all-scroll;
  font-size: 13px;
  line-height: 18px;
  height: 20px;
  z-index: 1;
  top: 4px
}

.monaco-list-type-filter.dragging {
  transition: top .2s, left .2s
}

.monaco-list-type-filter.ne {
  right: 4px
}

.monaco-list-type-filter.nw {
  left: 4px
}

.monaco-list-type-filter > .controls {
  display: flex;
  align-items: center;
  box-sizing: border-box;
  transition: width .2s;
  width: 0
}

.monaco-list-type-filter.dragging > .controls, .monaco-list-type-filter:hover > .controls {
  width: 36px
}

.monaco-list-type-filter > .controls > * {
  border: none;
  box-sizing: border-box;
  -webkit-appearance: none;
  -moz-appearance: none;
  background: none;
  width: 16px;
  height: 16px;
  flex-shrink: 0;
  margin: 0;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer
}

.monaco-list-type-filter > .controls > .filter {
  margin-left: 4px
}

.monaco-list-type-filter-message {
  position: absolute;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  padding: 40px 1em 1em 1em;
  text-align: center;
  white-space: normal;
  opacity: .7;
  pointer-events: none
}

.monaco-list-type-filter-message:empty {
  display: none
}

.monaco-list-type-filter {
  cursor: -webkit-grab;
  cursor: grab
}

.monaco-list-type-filter.dragging {
  cursor: -webkit-grabbing;
  cursor: grabbing
}

.monaco-split-view2 {
  position: relative;
  width: 100%;
  height: 100%
}

.monaco-split-view2 > .sash-container {
  position: absolute;
  width: 100%;
  height: 100%;
  pointer-events: none
}

.monaco-split-view2 > .sash-container > .monaco-sash {
  pointer-events: auto
}

.monaco-split-view2 > .monaco-scrollable-element {
  width: 100%;
  height: 100%
}

.monaco-split-view2 > .monaco-scrollable-element > .split-view-container {
  width: 100%;
  height: 100%;
  white-space: nowrap;
  position: relative
}

.monaco-split-view2 > .monaco-scrollable-element > .split-view-container > .split-view-view {
  white-space: normal;
  position: absolute
}

.monaco-split-view2 > .monaco-scrollable-element > .split-view-container > .split-view-view:not(.visible) {
  display: none
}

.monaco-split-view2.vertical > .monaco-scrollable-element > .split-view-container > .split-view-view {
  width: 100%
}

.monaco-split-view2.horizontal > .monaco-scrollable-element > .split-view-container > .split-view-view {
  height: 100%
}

.monaco-split-view2.separator-border > .monaco-scrollable-element > .split-view-container > .split-view-view:not(:first-child):before {
  content: " ";
  position: absolute;
  top: 0;
  left: 0;
  z-index: 5;
  pointer-events: none;
  background-color: var(--separator-border)
}

.monaco-split-view2.separator-border.horizontal > .monaco-scrollable-element > .split-view-container > .split-view-view:not(:first-child):before {
  height: 100%;
  width: 1px
}

.monaco-split-view2.separator-border.vertical > .monaco-scrollable-element > .split-view-container > .split-view-view:not(:first-child):before {
  height: 1px;
  width: 100%
}

.monaco-table {
  display: flex;
  flex-direction: column;
  position: relative;
  height: 100%;
  width: 100%;
  white-space: nowrap
}

.monaco-table > .monaco-split-view2 {
  border-bottom: 1px solid transparent
}

.monaco-table > .monaco-list {
  flex: 1
}

.monaco-table-tr {
  display: flex;
  height: 100%
}

.monaco-table-th {
  width: 100%;
  height: 100%;
  font-weight: 700;
  overflow: hidden;
  text-overflow: ellipsis
}

.monaco-table-td, .monaco-table-th {
  box-sizing: border-box;
  flex-shrink: 0;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis
}

.monaco-table > .monaco-split-view2 .monaco-sash.vertical:before {
  content: "";
  position: absolute;
  left: calc(var(--sash-size) / 2);
  width: 0;
  border-left: 1px solid transparent
}

.monaco-table > .monaco-split-view2, .monaco-table > .monaco-split-view2 .monaco-sash.vertical:before {
  transition: border-color .2s ease-out
}

.monaco-tl-row {
  display: flex;
  height: 100%;
  align-items: center;
  position: relative
}

.monaco-tl-indent {
  height: 100%;
  position: absolute;
  top: 0;
  left: 16px;
  pointer-events: none
}

.hide-arrows .monaco-tl-indent {
  left: 12px
}

.monaco-tl-indent > .indent-guide {
  display: inline-block;
  box-sizing: border-box;
  height: 100%;
  border-left: 1px solid transparent;
  transition: border-color .1s linear
}

.monaco-tl-contents, .monaco-tl-twistie {
  height: 100%
}

.monaco-tl-twistie {
  font-size: 10px;
  text-align: right;
  padding-right: 6px;
  flex-shrink: 0;
  width: 16px;
  display: flex !important;
  align-items: center;
  justify-content: center;
  transform: translateX(3px)
}

.monaco-tl-contents {
  flex: 1;
  overflow: hidden
}

.monaco-tl-twistie:before {
  border-radius: 20px
}

.monaco-tl-twistie.collapsed:before {
  transform: rotate(-90deg)
}

.monaco-tl-twistie.codicon-tree-item-loading:before {
  -webkit-animation: codicon-spin 1.25s steps(30) infinite;
  animation: codicon-spin 1.25s steps(30) infinite
}

.quick-input-widget {
  font-size: 13px
}

.quick-input-widget .monaco-highlighted-label .highlight {
  color: #0066bf
}

.vs .quick-input-widget .monaco-list-row.focused .monaco-highlighted-label .highlight {
  color: #9dddff
}

.vs-dark .quick-input-widget .monaco-highlighted-label .highlight {
  color: #0097fb
}

.hc-black .quick-input-widget .monaco-highlighted-label .highlight {
  color: #f38518
}

.monaco-keybinding > .monaco-keybinding-key {
  background-color: hsla(0, 0%, 86.7%, .4);
  border: 1px solid hsla(0, 0%, 80%, .4);
  border-bottom-color: hsla(0, 0%, 73.3%, .4);
  box-shadow: inset 0 -1px 0 hsla(0, 0%, 73.3%, .4);
  color: #555
}

.hc-black .monaco-keybinding > .monaco-keybinding-key {
  background-color: transparent;
  border: 1px solid #6fc3df;
  box-shadow: none;
  color: #fff
}

.vs-dark .monaco-keybinding > .monaco-keybinding-key {
  background-color: hsla(0, 0%, 50.2%, .17);
  border: 1px solid rgba(51, 51, 51, .6);
  border-bottom-color: rgba(68, 68, 68, .6);
  box-shadow: inset 0 -1px 0 rgba(68, 68, 68, .6);
  color: #ccc
}

.monaco-text-button {
  box-sizing: border-box;
  display: flex;
  width: 100%;
  padding: 4px;
  text-align: center;
  cursor: pointer;
  justify-content: center;
  align-items: center
}

.monaco-text-button:focus {
  outline-offset: 2px !important
}

.monaco-text-button:hover {
  text-decoration: none !important
}

.monaco-button.disabled, .monaco-button.disabled:focus {
  opacity: .4 !important;
  cursor: default
}

.monaco-text-button > .codicon {
  margin: 0 .2em;
  color: inherit !important
}

.monaco-button-dropdown {
  display: flex;
  cursor: pointer
}

.monaco-button-dropdown > .monaco-dropdown-button {
  margin-left: 1px
}

.monaco-description-button {
  flex-direction: column
}

.monaco-description-button .monaco-button-label {
  font-weight: 500
}

.monaco-description-button .monaco-button-description {
  font-style: italic
}

.monaco-count-badge {
  padding: 3px 6px;
  border-radius: 11px;
  font-size: 11px;
  min-width: 18px;
  min-height: 18px;
  line-height: 11px;
  font-weight: 400;
  text-align: center;
  display: inline-block;
  box-sizing: border-box
}

.monaco-count-badge.long {
  padding: 2px 3px;
  border-radius: 2px;
  min-height: auto;
  line-height: normal
}

.monaco-progress-container {
  width: 100%;
  height: 5px;
  overflow: hidden
}

.monaco-progress-container .progress-bit {
  width: 2%;
  height: 5px;
  position: absolute;
  left: 0;
  display: none
}

.monaco-progress-container.active .progress-bit {
  display: inherit
}

.monaco-progress-container.discrete .progress-bit {
  left: 0;
  transition: width .1s linear
}

.monaco-progress-container.discrete.done .progress-bit {
  width: 100%
}

.monaco-progress-container.infinite .progress-bit {
  -webkit-animation-name: progress;
  animation-name: progress;
  -webkit-animation-duration: 4s;
  animation-duration: 4s;
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
  -webkit-animation-timing-function: linear;
  animation-timing-function: linear;
  transform: translateZ(0)
}

@-webkit-keyframes progress {
  0% {
    transform: translateX(0) scaleX(1)
  }
  50% {
    transform: translateX(2500%) scaleX(3)
  }
  to {
    transform: translateX(4900%) scaleX(1)
  }
}

@keyframes progress {
  0% {
    transform: translateX(0) scaleX(1)
  }
  50% {
    transform: translateX(2500%) scaleX(3)
  }
  to {
    transform: translateX(4900%) scaleX(1)
  }
}

.quick-input-widget {
  position: absolute;
  width: 600px;
  z-index: 2000;
  padding: 0 1px 1px 1px;
  left: 50%;
  margin-left: -300px
}

.quick-input-titlebar {
  display: flex;
  align-items: center
}

.quick-input-left-action-bar {
  display: flex;
  margin-left: 4px;
  flex: 1
}

.quick-input-title {
  padding: 3px 0;
  text-align: center;
  text-overflow: ellipsis;
  overflow: hidden
}

.quick-input-right-action-bar {
  display: flex;
  margin-right: 4px;
  flex: 1
}

.quick-input-right-action-bar > .actions-container {
  justify-content: flex-end
}

.quick-input-titlebar .monaco-action-bar .action-label.codicon {
  background-position: 50%;
  background-repeat: no-repeat;
  padding: 2px
}

.quick-input-description {
  margin: 6px
}

.quick-input-header .quick-input-description {
  margin: 4px 2px
}

.quick-input-header {
  display: flex;
  padding: 6px 6px 0 6px;
  margin-bottom: -2px
}

.quick-input-widget.hidden-input .quick-input-header {
  padding: 0;
  margin-bottom: 0
}

.quick-input-and-message {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  min-width: 0;
  position: relative
}

.quick-input-check-all {
  align-self: center;
  margin: 0
}

.quick-input-filter {
  flex-grow: 1;
  display: flex;
  position: relative
}

.quick-input-box {
  flex-grow: 1
}

.quick-input-widget.show-checkboxes .quick-input-box, .quick-input-widget.show-checkboxes .quick-input-message {
  margin-left: 5px
}

.quick-input-visible-count {
  position: absolute;
  left: -10000px
}

.quick-input-count {
  align-self: center;
  position: absolute;
  right: 4px;
  display: flex;
  align-items: center
}

.quick-input-count .monaco-count-badge {
  vertical-align: middle;
  padding: 2px 4px;
  border-radius: 2px;
  min-height: auto;
  line-height: normal
}

.quick-input-action {
  margin-left: 6px
}

.quick-input-action .monaco-text-button {
  font-size: 11px;
  padding: 0 6px;
  display: flex;
  height: 27.5px;
  align-items: center
}

.quick-input-message {
  margin-top: -1px;
  padding: 5px 5px 2px 5px;
  overflow-wrap: break-word
}

.quick-input-message > .codicon {
  margin: 0 .2em;
  vertical-align: text-bottom
}

.quick-input-progress.monaco-progress-container {
  position: relative
}

.quick-input-progress.monaco-progress-container, .quick-input-progress.monaco-progress-container .progress-bit {
  height: 2px
}

.quick-input-list {
  line-height: 22px;
  margin-top: 6px
}

.quick-input-widget.hidden-input .quick-input-list {
  margin-top: 0
}

.quick-input-list .monaco-list {
  overflow: hidden;
  max-height: 440px
}

.quick-input-list .quick-input-list-entry {
  box-sizing: border-box;
  overflow: hidden;
  display: flex;
  height: 100%;
  padding: 0 6px
}

.quick-input-list .quick-input-list-entry.quick-input-list-separator-border {
  border-top-width: 1px;
  border-top-style: solid
}

.quick-input-list .monaco-list-row[data-index="0"] .quick-input-list-entry.quick-input-list-separator-border {
  border-top-style: none
}

.quick-input-list .quick-input-list-label {
  overflow: hidden;
  display: flex;
  height: 100%;
  flex: 1
}

.quick-input-list .quick-input-list-checkbox {
  align-self: center;
  margin: 0
}

.quick-input-list .quick-input-list-rows {
  overflow: hidden;
  text-overflow: ellipsis;
  display: flex;
  flex-direction: column;
  height: 100%;
  flex: 1;
  margin-left: 5px
}

.quick-input-widget.show-checkboxes .quick-input-list .quick-input-list-rows {
  margin-left: 10px
}

.quick-input-widget .quick-input-list .quick-input-list-checkbox {
  display: none
}

.quick-input-widget.show-checkboxes .quick-input-list .quick-input-list-checkbox {
  display: inline
}

.quick-input-list .quick-input-list-rows > .quick-input-list-row {
  display: flex;
  align-items: center
}

.quick-input-list .quick-input-list-rows > .quick-input-list-row .monaco-icon-label, .quick-input-list .quick-input-list-rows > .quick-input-list-row .monaco-icon-label .monaco-icon-label-container > .monaco-icon-name-container {
  flex: 1
}

.quick-input-list .quick-input-list-rows > .quick-input-list-row .codicon[class*=codicon-] {
  vertical-align: text-bottom
}

.quick-input-list .quick-input-list-rows .monaco-highlighted-label span {
  opacity: 1
}

.quick-input-list .quick-input-list-entry .quick-input-list-entry-keybinding {
  margin-right: 8px
}

.quick-input-list .quick-input-list-label-meta {
  opacity: .7;
  line-height: normal;
  text-overflow: ellipsis;
  overflow: hidden
}

.quick-input-list .monaco-highlighted-label .highlight {
  font-weight: 700
}

.quick-input-list .quick-input-list-entry .quick-input-list-separator {
  margin-right: 8px
}

.quick-input-list .quick-input-list-entry-action-bar {
  display: flex;
  flex: 0;
  overflow: visible
}

.quick-input-list .quick-input-list-entry-action-bar .action-label {
  display: none
}

.quick-input-list .quick-input-list-entry-action-bar .action-label.codicon {
  margin-right: 4px;
  padding: 0 2px 2px 2px
}

.quick-input-list .quick-input-list-entry-action-bar {
  margin-top: 1px;
  margin-right: 4px
}

.quick-input-list .monaco-list-row.focused .quick-input-list-entry-action-bar .action-label, .quick-input-list .quick-input-list-entry .quick-input-list-entry-action-bar .action-label.always-visible, .quick-input-list .quick-input-list-entry:hover .quick-input-list-entry-action-bar .action-label {
  display: flex
}

.quick-input-list .monaco-list-row.focused .monaco-keybinding-key, .quick-input-list .monaco-list-row.focused .quick-input-list-entry .quick-input-list-separator {
  color: inherit
}

.quick-input-list .monaco-list-row.focused .monaco-keybinding-key {
  background: none
}

.monaco-inputbox {
  position: relative;
  display: block;
  padding: 0;
  box-sizing: border-box;
  font-size: inherit
}

.monaco-inputbox.idle {
  border: 1px solid transparent
}

.monaco-inputbox > .ibwrapper > .input, .monaco-inputbox > .ibwrapper > .mirror {
  padding: 4px
}

.monaco-inputbox > .ibwrapper {
  position: relative;
  width: 100%;
  height: 100%
}

.monaco-inputbox > .ibwrapper > .input {
  display: inline-block;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  line-height: inherit;
  border: none;
  font-family: inherit;
  font-size: inherit;
  resize: none;
  color: inherit
}

.monaco-inputbox > .ibwrapper > input {
  text-overflow: ellipsis
}

.monaco-inputbox > .ibwrapper > textarea.input {
  display: block;
  -ms-overflow-style: none;
  scrollbar-width: none;
  outline: none
}

.monaco-inputbox > .ibwrapper > textarea.input::-webkit-scrollbar {
  display: none
}

.monaco-inputbox > .ibwrapper > textarea.input.empty {
  white-space: nowrap
}

.monaco-inputbox > .ibwrapper > .mirror {
  position: absolute;
  display: inline-block;
  width: 100%;
  top: 0;
  left: 0;
  box-sizing: border-box;
  white-space: pre-wrap;
  visibility: hidden;
  word-wrap: break-word
}

.monaco-inputbox-container {
  text-align: right
}

.monaco-inputbox-container .monaco-inputbox-message {
  display: inline-block;
  overflow: hidden;
  text-align: left;
  width: 100%;
  box-sizing: border-box;
  padding: .4em;
  font-size: 12px;
  line-height: 17px;
  margin-top: -1px;
  word-wrap: break-word
}

.monaco-inputbox .monaco-action-bar {
  position: absolute;
  right: 2px;
  top: 4px
}

.monaco-inputbox .monaco-action-bar .action-item {
  margin-left: 2px
}

.monaco-inputbox .monaco-action-bar .action-item .codicon {
  background-repeat: no-repeat;
  width: 16px;
  height: 16px
}

.monaco-icon-label {
  display: flex;
  overflow: hidden;
  text-overflow: ellipsis
}

.monaco-icon-label:before {
  background-size: 16px;
  background-position: 0;
  background-repeat: no-repeat;
  padding-right: 6px;
  width: 16px;
  height: 22px;
  line-height: inherit !important;
  display: inline-block;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  vertical-align: top;
  flex-shrink: 0
}

.monaco-icon-label > .monaco-icon-label-container {
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1
}

.monaco-icon-label > .monaco-icon-label-container > .monaco-icon-name-container > .label-name {
  color: inherit;
  white-space: pre
}

.monaco-icon-label > .monaco-icon-label-container > .monaco-icon-name-container > .label-name > .label-separator {
  margin: 0 2px;
  opacity: .5
}

.monaco-icon-label > .monaco-icon-label-container > .monaco-icon-description-container > .label-description {
  opacity: .7;
  margin-left: .5em;
  font-size: .9em;
  white-space: pre
}

.monaco-icon-label.nowrap > .monaco-icon-label-container > .monaco-icon-description-container > .label-description {
  white-space: nowrap
}

.vs .monaco-icon-label > .monaco-icon-label-container > .monaco-icon-description-container > .label-description {
  opacity: .95
}

.monaco-icon-label.italic > .monaco-icon-label-container > .monaco-icon-description-container > .label-description, .monaco-icon-label.italic > .monaco-icon-label-container > .monaco-icon-name-container > .label-name {
  font-style: italic
}

.monaco-icon-label.deprecated {
  text-decoration: line-through;
  opacity: .66
}

.monaco-icon-label.italic:after {
  font-style: italic
}

.monaco-icon-label.strikethrough > .monaco-icon-label-container > .monaco-icon-description-container > .label-description, .monaco-icon-label.strikethrough > .monaco-icon-label-container > .monaco-icon-name-container > .label-name {
  text-decoration: line-through
}

.monaco-icon-label:after {
  opacity: .75;
  font-size: 90%;
  font-weight: 600;
  margin: auto 16px 0 5px;
  text-align: center
}

.monaco-list:focus .selected .monaco-icon-label, .monaco-list:focus .selected .monaco-icon-label:after {
  color: inherit !important
}

.monaco-list-row.focused.selected .label-description, .monaco-list-row.selected .label-description {
  opacity: .8
}

.monaco-keybinding {
  display: flex;
  align-items: center;
  line-height: 10px
}

.monaco-keybinding > .monaco-keybinding-key {
  display: inline-block;
  border-style: solid;
  border-width: 1px;
  border-radius: 3px;
  vertical-align: middle;
  font-size: 11px;
  padding: 3px 5px;
  margin: 0 2px
}

.monaco-keybinding > .monaco-keybinding-key:first-child {
  margin-left: 0
}

.monaco-keybinding > .monaco-keybinding-key:last-child {
  margin-right: 0
}

.monaco-keybinding > .monaco-keybinding-key-separator {
  display: inline-block
}

.monaco-keybinding > .monaco-keybinding-key-chord-separator {
  width: 6px
}

.monaco-editor .selection-anchor {
  background-color: #007acc;
  width: 2px !important
}

.monaco-editor .bracket-match {
  box-sizing: border-box
}

.monaco-editor .monaco-editor-overlaymessage {
  padding-bottom: 8px;
  z-index: 10000
}

.monaco-editor .monaco-editor-overlaymessage.below {
  padding-bottom: 0;
  padding-top: 8px;
  z-index: 10000
}

@-webkit-keyframes fadeIn {
  0% {
    opacity: 0
  }
  to {
    opacity: 1
  }
}

@keyframes fadeIn {
  0% {
    opacity: 0
  }
  to {
    opacity: 1
  }
}

.monaco-editor .monaco-editor-overlaymessage.fadeIn {
  -webkit-animation: fadeIn .15s ease-out;
  animation: fadeIn .15s ease-out
}

@-webkit-keyframes fadeOut {
  0% {
    opacity: 1
  }
  to {
    opacity: 0
  }
}

@keyframes fadeOut {
  0% {
    opacity: 1
  }
  to {
    opacity: 0
  }
}

.monaco-editor .monaco-editor-overlaymessage.fadeOut {
  -webkit-animation: fadeOut .1s ease-out;
  animation: fadeOut .1s ease-out
}

.monaco-editor .monaco-editor-overlaymessage .message {
  padding: 1px 4px
}

.monaco-editor .monaco-editor-overlaymessage .anchor {
  width: 0 !important;
  height: 0 !important;
  border-color: transparent;
  border-style: solid;
  z-index: 1000;
  border-width: 8px;
  position: absolute
}

.monaco-editor .monaco-editor-overlaymessage.below .anchor.below, .monaco-editor .monaco-editor-overlaymessage:not(.below) .anchor.top {
  display: none
}

.monaco-editor .monaco-editor-overlaymessage.below .anchor.top {
  display: inherit;
  top: -8px
}

.monaco-editor .contentWidgets .codicon-light-bulb, .monaco-editor .contentWidgets .codicon-lightbulb-autofix {
  display: flex;
  align-items: center;
  justify-content: center
}

.monaco-editor .contentWidgets .codicon-light-bulb:hover, .monaco-editor .contentWidgets .codicon-lightbulb-autofix:hover {
  cursor: pointer
}

.monaco-editor .codelens-decoration {
  overflow: hidden;
  display: inline-block;
  text-overflow: ellipsis;
  white-space: nowrap
}

.monaco-editor .codelens-decoration > a, .monaco-editor .codelens-decoration > span {
  -moz-user-select: none;
  user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  white-space: nowrap;
  vertical-align: sub
}

.monaco-editor .codelens-decoration > a {
  text-decoration: none
}

.monaco-editor .codelens-decoration > a:hover {
  cursor: pointer
}

.monaco-editor .codelens-decoration .codicon {
  vertical-align: middle;
  color: currentColor !important
}

.monaco-editor .codelens-decoration > a:hover .codicon:before {
  cursor: pointer
}

@-webkit-keyframes fadein {
  0% {
    opacity: 0;
    visibility: visible
  }
  to {
    opacity: 1
  }
}

@keyframes fadein {
  0% {
    opacity: 0;
    visibility: visible
  }
  to {
    opacity: 1
  }
}

.monaco-editor .codelens-decoration.fadein {
  -webkit-animation: fadein .1s linear;
  animation: fadein .1s linear
}

.monaco-editor .goto-definition-link {
  text-decoration: underline;
  cursor: pointer
}

.monaco-editor .peekview-widget .head {
  box-sizing: border-box;
  display: flex
}

.monaco-editor .peekview-widget .head .peekview-title {
  display: flex;
  align-items: center;
  font-size: 13px;
  margin-left: 20px;
  min-width: 0
}

.monaco-editor .peekview-widget .head .peekview-title.clickable {
  cursor: pointer
}

.monaco-editor .peekview-widget .head .peekview-title .dirname:not(:empty) {
  font-size: .9em;
  margin-left: .5em
}

.monaco-editor .peekview-widget .head .peekview-title .dirname, .monaco-editor .peekview-widget .head .peekview-title .meta {
  white-space: nowrap
}

.monaco-editor .peekview-widget .head .peekview-title .filename {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap
}

.monaco-editor .peekview-widget .head .peekview-title .meta:not(:empty):before {
  content: "-";
  padding: 0 .3em
}

.monaco-editor .peekview-widget .head .peekview-actions {
  flex: 1;
  text-align: right;
  padding-right: 2px
}

.monaco-editor .peekview-widget .head .peekview-actions > .monaco-action-bar {
  display: inline-block
}

.monaco-editor .peekview-widget .head .peekview-actions > .monaco-action-bar, .monaco-editor .peekview-widget .head .peekview-actions > .monaco-action-bar > .actions-container {
  height: 100%
}

.monaco-editor .peekview-widget > .body {
  border-top: 1px solid;
  position: relative
}

.monaco-editor .peekview-widget .head .peekview-title .codicon {
  margin-right: 4px
}

.monaco-editor .peekview-widget .monaco-list .monaco-list-row.focused .codicon {
  color: inherit !important
}

.monaco-editor .zone-widget {
  position: absolute;
  z-index: 10
}

.monaco-editor .zone-widget .zone-widget-container {
  border-top-style: solid;
  border-bottom-style: solid;
  border-top-width: 0;
  border-bottom-width: 0;
  position: relative
}

.monaco-dropdown {
  height: 100%;
  padding: 0
}

.monaco-dropdown > .dropdown-label {
  cursor: pointer;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center
}

.monaco-dropdown > .dropdown-label > .action-label.disabled {
  cursor: default
}

.monaco-dropdown-with-primary {
  display: flex !important;
  flex-direction: row;
  border-radius: 5px
}

.monaco-dropdown-with-primary > .action-container > .action-label {
  margin-right: 0
}

.monaco-dropdown-with-primary > .dropdown-action-container > .monaco-dropdown > .dropdown-label .codicon[class*=codicon-] {
  font-size: 12px;
  padding-left: 0;
  padding-right: 0;
  line-height: 16px;
  margin-left: -3px
}

.monaco-dropdown-with-primary > .dropdown-action-container > .monaco-dropdown > .dropdown-label > .action-label {
  display: block;
  background-size: 16px;
  background-position: 50%;
  background-repeat: no-repeat
}

.monaco-action-bar .action-item.menu-entry .action-label.icon {
  width: 16px;
  height: 16px;
  background-repeat: no-repeat;
  background-position: 50%;
  background-size: 16px
}

.monaco-action-bar .action-item.menu-entry .action-label {
  background-image: var(--menu-entry-icon-light)
}

.hc-black .monaco-action-bar .action-item.menu-entry .action-label, .vs-dark .monaco-action-bar .action-item.menu-entry .action-label {
  background-image: var(--menu-entry-icon-dark)
}

.monaco-dropdown-with-default {
  display: flex !important;
  flex-direction: row;
  border-radius: 5px
}

.monaco-dropdown-with-default > .action-container > .action-label {
  margin-right: 0
}

.monaco-dropdown-with-default > .action-container.menu-entry > .action-label.icon {
  width: 16px;
  height: 16px;
  background-repeat: no-repeat;
  background-position: 50%;
  background-size: 16px
}

.monaco-dropdown-with-default > .action-container.menu-entry > .action-label {
  background-image: var(--menu-entry-icon-light)
}

.hc-black .monaco-dropdown-with-default > .action-container.menu-entry > .action-label, .vs-dark .monaco-dropdown-with-default > .action-container.menu-entry > .action-label {
  background-image: var(--menu-entry-icon-dark)
}

.monaco-dropdown-with-default > .dropdown-action-container > .monaco-dropdown > .dropdown-label .codicon[class*=codicon-] {
  font-size: 12px;
  padding-left: 0;
  padding-right: 0;
  line-height: 16px;
  margin-left: -3px
}

.monaco-dropdown-with-default > .dropdown-action-container > .monaco-dropdown > .dropdown-label > .action-label {
  display: block;
  background-size: 16px;
  background-position: 50%;
  background-repeat: no-repeat
}

.monaco-editor .zone-widget .zone-widget-container.reference-zone-widget {
  border-top-width: 1px;
  border-bottom-width: 1px
}

.monaco-editor .reference-zone-widget .inline {
  display: inline-block;
  vertical-align: top
}

.monaco-editor .reference-zone-widget .messages {
  height: 100%;
  width: 100%;
  text-align: center;
  padding: 3em 0
}

.monaco-editor .reference-zone-widget .ref-tree {
  line-height: 23px
}

.monaco-editor .reference-zone-widget .ref-tree .reference {
  text-overflow: ellipsis;
  overflow: hidden
}

.monaco-editor .reference-zone-widget .ref-tree .reference-file {
  display: inline-flex;
  width: 100%;
  height: 100%
}

.monaco-editor .reference-zone-widget .ref-tree .monaco-list:focus .selected .reference-file {
  color: inherit !important
}

.monaco-editor .reference-zone-widget .ref-tree .reference-file .count {
  margin-right: 12px;
  margin-left: auto
}

.monaco-editor.hc-black .reference-zone-widget .ref-tree .reference-file {
  font-weight: 700
}

.monaco-hover {
  cursor: default;
  position: absolute;
  overflow: hidden;
  z-index: 50;
  -moz-user-select: text;
  user-select: text;
  -webkit-user-select: text;
  -ms-user-select: text;
  box-sizing: initial;
  -webkit-animation: fadein .1s linear;
  animation: fadein .1s linear;
  line-height: 1.5em
}

.monaco-hover.hidden {
  display: none
}

.monaco-hover .hover-contents:not(.html-hover-contents) {
  padding: 4px 8px
}

.monaco-hover .markdown-hover > .hover-contents:not(.code-hover-contents) {
  max-width: 500px;
  word-wrap: break-word
}

.monaco-hover .markdown-hover > .hover-contents:not(.code-hover-contents) hr {
  min-width: 100%
}

.monaco-hover .code, .monaco-hover p, .monaco-hover ul {
  margin: 8px 0
}

.monaco-hover code {
  font-family: var(--monaco-monospace-font)
}

.monaco-hover hr {
  box-sizing: border-box;
  border-left: 0;
  border-right: 0;
  margin-top: 4px;
  margin-bottom: -4px;
  margin-left: -8px;
  margin-right: -8px;
  height: 1px
}

.monaco-hover .code:first-child, .monaco-hover p:first-child, .monaco-hover ul:first-child {
  margin-top: 0
}

.monaco-hover .code:last-child, .monaco-hover p:last-child, .monaco-hover ul:last-child {
  margin-bottom: 0
}

.monaco-hover ol, .monaco-hover ul {
  padding-left: 20px
}

.monaco-hover li > p {
  margin-bottom: 0
}

.monaco-hover li > ul {
  margin-top: 0
}

.monaco-hover code {
  border-radius: 3px;
  padding: 0 .4em
}

.monaco-hover .monaco-tokenized-source {
  white-space: pre-wrap
}

.monaco-hover .hover-row.status-bar {
  font-size: 12px;
  line-height: 22px
}

.monaco-hover .hover-row.status-bar .actions {
  display: flex;
  padding: 0 8px
}

.monaco-hover .hover-row.status-bar .actions .action-container {
  margin-right: 16px;
  cursor: pointer
}

.monaco-hover .hover-row.status-bar .actions .action-container .action .icon {
  padding-right: 4px
}

.monaco-hover .markdown-hover .hover-contents .codicon {
  color: inherit;
  font-size: inherit;
  vertical-align: middle
}

.monaco-hover .hover-contents a.code-link, .monaco-hover .hover-contents a.code-link:hover {
  color: inherit
}

.monaco-hover .hover-contents a.code-link:before {
  content: "("
}

.monaco-hover .hover-contents a.code-link:after {
  content: ")"
}

.monaco-hover .hover-contents a.code-link > span {
  text-decoration: underline;
  border-bottom: 1px solid transparent;
  text-underline-position: under
}

.monaco-hover .markdown-hover .hover-contents:not(.code-hover-contents):not(.html-hover-contents) span {
  margin-bottom: 4px;
  display: inline-block
}

.monaco-hover-content .action-container a {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none
}

.monaco-hover-content .action-container.disabled {
  pointer-events: none;
  opacity: .4;
  cursor: default
}

.colorpicker-widget {
  height: 190px;
  -moz-user-select: none;
  user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none
}

.monaco-editor .colorpicker-hover:focus {
  outline: none
}

.colorpicker-header {
  display: flex;
  height: 24px;
  position: relative;
  background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAQAAAAECAYAAACp8Z5+AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAAZdEVYdFNvZnR3YXJlAHBhaW50Lm5ldCA0LjAuMTZEaa/1AAAAHUlEQVQYV2PYvXu3JAi7uLiAMaYAjAGTQBPYLQkAa/0Zef3qRswAAAAASUVORK5CYII=");
  background-size: 9px 9px;
  -ms-interpolation-mode: nearest-neighbor;
  image-rendering: -moz-crisp-edges;
  image-rendering: pixelated
}

.colorpicker-header .picked-color {
  width: 216px;
  line-height: 24px;
  cursor: pointer;
  color: #fff;
  flex: 1;
  text-align: center
}

.colorpicker-header .picked-color.light {
  color: #000
}

.colorpicker-header .original-color {
  width: 74px;
  z-index: inherit;
  cursor: pointer
}

.colorpicker-body {
  display: flex;
  padding: 8px;
  position: relative
}

.colorpicker-body .saturation-wrap {
  overflow: hidden;
  height: 150px;
  position: relative;
  min-width: 220px;
  flex: 1
}

.colorpicker-body .saturation-box {
  height: 150px;
  position: absolute
}

.colorpicker-body .saturation-selection {
  width: 9px;
  height: 9px;
  margin: -5px 0 0 -5px;
  border: 1px solid #fff;
  border-radius: 100%;
  box-shadow: 0 0 2px rgba(0, 0, 0, .8);
  position: absolute
}

.colorpicker-body .strip {
  width: 25px;
  height: 150px
}

.colorpicker-body .hue-strip {
  background: linear-gradient(180deg, red 0, #ff0 17%, #0f0 33%, #0ff 50%, #00f 67%, #f0f 83%, red)
}

.colorpicker-body .hue-strip, .colorpicker-body .opacity-strip {
  position: relative;
  margin-left: 8px;
  cursor: -webkit-grab;
  cursor: grab
}

.colorpicker-body .opacity-strip {
  background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAQAAAAECAYAAACp8Z5+AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAAZdEVYdFNvZnR3YXJlAHBhaW50Lm5ldCA0LjAuMTZEaa/1AAAAHUlEQVQYV2PYvXu3JAi7uLiAMaYAjAGTQBPYLQkAa/0Zef3qRswAAAAASUVORK5CYII=");
  background-size: 9px 9px;
  -ms-interpolation-mode: nearest-neighbor;
  image-rendering: -moz-crisp-edges;
  image-rendering: pixelated
}

.colorpicker-body .strip.grabbing {
  cursor: -webkit-grabbing;
  cursor: grabbing
}

.colorpicker-body .slider {
  position: absolute;
  top: 0;
  left: -2px;
  width: calc(100% + 4px);
  height: 4px;
  box-sizing: border-box;
  border: 1px solid hsla(0, 0%, 100%, .71);
  box-shadow: 0 0 1px rgba(0, 0, 0, .85)
}

.colorpicker-body .strip .overlay {
  height: 150px;
  pointer-events: none
}

.monaco-editor .peekview-widget .head .peekview-title .severity-icon {
  display: inline-block;
  vertical-align: text-top;
  margin-right: 4px
}

.monaco-editor .marker-widget {
  text-overflow: ellipsis;
  white-space: nowrap
}

.monaco-editor .marker-widget > .stale {
  opacity: .6;
  font-style: italic
}

.monaco-editor .marker-widget .title {
  display: inline-block;
  padding-right: 5px
}

.monaco-editor .marker-widget .descriptioncontainer {
  position: absolute;
  white-space: pre;
  -moz-user-select: text;
  user-select: text;
  -webkit-user-select: text;
  -ms-user-select: text;
  padding: 8px 12px 0 20px
}

.monaco-editor .marker-widget .descriptioncontainer .message {
  display: flex;
  flex-direction: column
}

.monaco-editor .marker-widget .descriptioncontainer .message .details {
  padding-left: 6px
}

.monaco-editor .marker-widget .descriptioncontainer .message .source, .monaco-editor .marker-widget .descriptioncontainer .message span.code {
  opacity: .6
}

.monaco-editor .marker-widget .descriptioncontainer .message a.code-link {
  opacity: .6;
  color: inherit
}

.monaco-editor .marker-widget .descriptioncontainer .message a.code-link:before {
  content: "("
}

.monaco-editor .marker-widget .descriptioncontainer .message a.code-link:after {
  content: ")"
}

.monaco-editor .marker-widget .descriptioncontainer .message a.code-link > span {
  text-decoration: underline;
  border-bottom: 1px solid transparent;
  text-underline-position: under
}

.monaco-editor .marker-widget .descriptioncontainer .filename {
  cursor: pointer
}

.monaco-editor .snippet-placeholder {
  min-width: 2px
}

.monaco-editor .finish-snippet-placeholder, .monaco-editor .snippet-placeholder {
  outline-style: solid;
  outline-width: 1px
}

.monaco-editor .suggest-widget {
  width: 430px;
  z-index: 40;
  display: flex;
  flex-direction: column
}

.monaco-editor .suggest-widget.message {
  flex-direction: row;
  align-items: center
}

.monaco-editor .suggest-details, .monaco-editor .suggest-widget {
  flex: 0 1 auto;
  width: 100%;
  border-style: solid;
  border-width: 1px
}

.monaco-editor.hc-black .suggest-details, .monaco-editor.hc-black .suggest-widget {
  border-width: 2px
}

.monaco-editor .suggest-widget .suggest-status-bar {
  box-sizing: border-box;
  display: none;
  flex-flow: row nowrap;
  justify-content: space-between;
  width: 100%;
  font-size: 80%;
  padding: 0 4px 0 4px;
  border-top: 1px solid transparent;
  overflow: hidden
}

.monaco-editor .suggest-widget.with-status-bar .suggest-status-bar {
  display: flex
}

.monaco-editor .suggest-widget .suggest-status-bar .left {
  padding-right: 8px
}

.monaco-editor .suggest-widget.with-status-bar .suggest-status-bar .action-label {
  opacity: .5;
  color: inherit
}

.monaco-editor .suggest-widget.with-status-bar .suggest-status-bar .action-item:not(:last-of-type) .action-label {
  margin-right: 0
}

.monaco-editor .suggest-widget.with-status-bar .suggest-status-bar .action-item:not(:last-of-type) .action-label:after {
  content: ", ";
  margin-right: .3em
}

.monaco-editor .suggest-widget.with-status-bar .monaco-list .monaco-list-row.focused.string-label > .contents > .main > .right > .readMore, .monaco-editor .suggest-widget.with-status-bar .monaco-list .monaco-list-row > .contents > .main > .right > .readMore {
  display: none
}

.monaco-editor .suggest-widget.with-status-bar:not(.docs-side) .monaco-list .monaco-list-row:hover > .contents > .main > .right.can-expand-details > .details-label {
  width: 100%
}

.monaco-editor .suggest-widget > .message {
  padding-left: 22px
}

.monaco-editor .suggest-widget > .tree {
  height: 100%;
  width: 100%
}

.monaco-editor .suggest-widget .monaco-list {
  -moz-user-select: none;
  user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none
}

.monaco-editor .suggest-widget .monaco-list .monaco-list-row {
  display: flex;
  -mox-box-sizing: border-box;
  box-sizing: border-box;
  padding-right: 10px;
  background-repeat: no-repeat;
  background-position: 2px 2px;
  white-space: nowrap;
  cursor: pointer;
  touch-action: none
}

.monaco-editor .suggest-widget .monaco-list .monaco-list-row > .contents {
  flex: 1;
  height: 100%;
  overflow: hidden;
  padding-left: 2px
}

.monaco-editor .suggest-widget .monaco-list .monaco-list-row > .contents > .main {
  display: flex;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: pre;
  justify-content: space-between
}

.monaco-editor .suggest-widget .monaco-list .monaco-list-row > .contents > .main > .left, .monaco-editor .suggest-widget .monaco-list .monaco-list-row > .contents > .main > .right {
  display: flex
}

.monaco-editor .suggest-widget:not(.frozen) .monaco-highlighted-label .highlight {
  font-weight: 700
}

.monaco-editor .suggest-details > .monaco-scrollable-element > .body > .header > .codicon-close, .monaco-editor .suggest-widget .monaco-list .monaco-list-row > .contents > .main > .right > .readMore:before {
  color: inherit;
  opacity: 1;
  font-size: 14px;
  cursor: pointer
}

.monaco-editor .suggest-details > .monaco-scrollable-element > .body > .header > .codicon-close {
  position: absolute;
  top: 6px;
  right: 2px
}

.monaco-editor .suggest-details > .monaco-scrollable-element > .body > .header > .codicon-close:hover, .monaco-editor .suggest-widget .monaco-list .monaco-list-row > .contents > .main > .right > .readMore:hover {
  opacity: 1
}

.monaco-editor .suggest-widget .monaco-list .monaco-list-row > .contents > .main > .right > .details-label {
  opacity: .7
}

.monaco-editor .suggest-widget .monaco-list .monaco-list-row > .contents > .main > .left > .signature-label {
  overflow: hidden;
  text-overflow: ellipsis;
  opacity: .6
}

.monaco-editor .suggest-widget .monaco-list .monaco-list-row > .contents > .main > .left > .qualifier-label {
  margin-left: 12px;
  opacity: .4;
  font-size: 85%;
  line-height: normal;
  text-overflow: ellipsis;
  overflow: hidden;
  align-self: center
}

.monaco-editor .suggest-widget .monaco-list .monaco-list-row > .contents > .main > .right > .details-label {
  font-size: 85%;
  margin-left: 1.1em;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap
}

.monaco-editor .suggest-widget .monaco-list .monaco-list-row > .contents > .main > .right > .details-label > .monaco-tokenized-source {
  display: inline
}

.monaco-editor .suggest-widget .monaco-list .monaco-list-row > .contents > .main > .right > .details-label {
  display: none
}

.monaco-editor .suggest-widget.docs-side .monaco-list .monaco-list-row.focused:not(.string-label) > .contents > .main > .right > .details-label, .monaco-editor .suggest-widget .monaco-list .monaco-list-row:not(.string-label) > .contents > .main > .right > .details-label, .monaco-editor .suggest-widget:not(.shows-details) .monaco-list .monaco-list-row.focused > .contents > .main > .right > .details-label {
  display: inline
}

.monaco-editor .suggest-widget:not(.docs-side) .monaco-list .monaco-list-row:hover > .contents > .main > .right.can-expand-details > .details-label {
  width: calc(100% - 26px)
}

.monaco-editor .suggest-widget .monaco-list .monaco-list-row > .contents > .main > .left {
  flex-shrink: 1;
  flex-grow: 1;
  overflow: hidden
}

.monaco-editor .suggest-widget .monaco-list .monaco-list-row > .contents > .main > .left > .monaco-icon-label {
  flex-shrink: 0
}

.monaco-editor .suggest-widget .monaco-list .monaco-list-row:not(.string-label) > .contents > .main > .left > .monaco-icon-label {
  max-width: 100%
}

.monaco-editor .suggest-widget .monaco-list .monaco-list-row.string-label > .contents > .main > .left > .monaco-icon-label {
  flex-shrink: 1
}

.monaco-editor .suggest-widget .monaco-list .monaco-list-row > .contents > .main > .right {
  overflow: hidden;
  flex-shrink: 4;
  max-width: 70%
}

.monaco-editor .suggest-widget .monaco-list .monaco-list-row > .contents > .main > .right > .readMore {
  display: inline-block;
  position: absolute;
  right: 10px;
  width: 18px;
  height: 18px;
  visibility: hidden
}

.monaco-editor .suggest-widget.docs-below .monaco-list .monaco-list-row > .contents > .main > .right > .readMore, .monaco-editor .suggest-widget.docs-side .monaco-list .monaco-list-row > .contents > .main > .right > .readMore {
  display: none !important
}

.monaco-editor .suggest-widget .monaco-list .monaco-list-row.string-label > .contents > .main > .right > .readMore {
  display: none
}

.monaco-editor .suggest-widget .monaco-list .monaco-list-row.focused.string-label > .contents > .main > .right > .readMore {
  display: inline-block
}

.monaco-editor .suggest-widget.docs-below .monaco-list .monaco-list-row > .contents > .main > .right > .readMore, .monaco-editor .suggest-widget.docs-side .monaco-list .monaco-list-row > .contents > .main > .right > .readMore {
  display: none
}

.monaco-editor .suggest-widget .monaco-list .monaco-list-row:hover > .contents > .main > .right > .readMore {
  visibility: visible
}

.monaco-editor .suggest-widget .monaco-list .monaco-list-row .monaco-icon-label.deprecated {
  opacity: .66;
  text-decoration: unset
}

.monaco-editor .suggest-widget .monaco-list .monaco-list-row .monaco-icon-label.deprecated > .monaco-icon-label-container > .monaco-icon-name-container {
  text-decoration: line-through
}

.monaco-editor .suggest-widget .monaco-list .monaco-list-row .monaco-icon-label:before {
  height: 100%
}

.monaco-editor .suggest-widget .monaco-list .monaco-list-row .icon {
  display: block;
  height: 16px;
  width: 16px;
  margin-left: 2px;
  background-repeat: no-repeat;
  background-size: 80%;
  background-position: 50%
}

.monaco-editor .suggest-widget .monaco-list .monaco-list-row .icon.hide {
  display: none
}

.monaco-editor .suggest-widget .monaco-list .monaco-list-row .suggest-icon {
  display: flex;
  align-items: center;
  margin-right: 4px
}

.monaco-editor .suggest-widget.no-icons .monaco-list .monaco-list-row .icon, .monaco-editor .suggest-widget.no-icons .monaco-list .monaco-list-row .suggest-icon:before {
  display: none
}

.monaco-editor .suggest-widget .monaco-list .monaco-list-row .icon.customcolor .colorspan {
  margin: 0 0 0 .3em;
  border: .1em solid #000;
  width: .7em;
  height: .7em;
  display: inline-block
}

.monaco-editor .suggest-details-container {
  z-index: 41
}

.monaco-editor .suggest-details {
  display: flex;
  flex-direction: column;
  cursor: default
}

.monaco-editor .suggest-details.no-docs {
  display: none
}

.monaco-editor .suggest-details > .monaco-scrollable-element {
  flex: 1
}

.monaco-editor .suggest-details > .monaco-scrollable-element > .body {
  box-sizing: border-box;
  height: 100%;
  width: 100%
}

.monaco-editor .suggest-details > .monaco-scrollable-element > .body > .header > .type {
  flex: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  opacity: .7;
  white-space: pre;
  margin: 0 24px 0 0;
  padding: 4px 0 12px 5px
}

.monaco-editor .suggest-details > .monaco-scrollable-element > .body > .header > .type.auto-wrap {
  white-space: normal;
  word-break: break-all
}

.monaco-editor .suggest-details > .monaco-scrollable-element > .body > .docs {
  margin: 0;
  padding: 4px 5px;
  white-space: pre-wrap
}

.monaco-editor .suggest-details.no-type > .monaco-scrollable-element > .body > .docs {
  margin-right: 24px
}

.monaco-editor .suggest-details > .monaco-scrollable-element > .body > .docs.markdown-docs {
  padding: 0;
  white-space: normal;
  min-height: calc(1rem + 8px)
}

.monaco-editor .suggest-details > .monaco-scrollable-element > .body > .docs.markdown-docs > div, .monaco-editor .suggest-details > .monaco-scrollable-element > .body > .docs.markdown-docs > span:not(:empty) {
  padding: 4px 5px
}

.monaco-editor .suggest-details > .monaco-scrollable-element > .body > .docs.markdown-docs > div > p:first-child {
  margin-top: 0
}

.monaco-editor .suggest-details > .monaco-scrollable-element > .body > .docs.markdown-docs > div > p:last-child {
  margin-bottom: 0
}

.monaco-editor .suggest-details > .monaco-scrollable-element > .body > .docs .code {
  white-space: pre-wrap;
  word-wrap: break-word
}

.monaco-editor .suggest-details > .monaco-scrollable-element > .body > .docs.markdown-docs .codicon {
  vertical-align: sub
}

.monaco-editor .suggest-details > .monaco-scrollable-element > .body > p:empty {
  display: none
}

.monaco-editor .suggest-details code {
  border-radius: 3px;
  padding: 0 .4em
}

.monaco-editor .suggest-details ol, .monaco-editor .suggest-details ul {
  padding-left: 20px
}

.monaco-editor .suggest-details p code {
  font-family: var(--monaco-monospace-font)
}

.monaco-editor .suggest-preview-additional-widget {
  white-space: nowrap
}

.monaco-editor .suggest-preview-additional-widget .content-spacer {
  color: transparent;
  white-space: pre
}

.monaco-editor .suggest-preview-additional-widget .button {
  display: inline-block;
  cursor: pointer;
  text-decoration: underline;
  text-underline-position: under
}

.monaco-editor .ghost-text-hidden {
  opacity: 0;
  font-size: 0
}

.monaco-editor .ghost-text-decoration, .monaco-editor .suggest-preview-text {
  font-style: italic
}

.monaco-editor.vs .dnd-target {
  border-right: 2px dotted #000;
  color: #fff
}

.monaco-editor.vs-dark .dnd-target {
  border-right: 2px dotted #aeafad;
  color: #51504f
}

.monaco-editor.hc-black .dnd-target {
  border-right: 2px dotted #fff;
  color: #000
}

.monaco-editor.hc-black.mac.mouse-default .view-lines, .monaco-editor.mouse-default .view-lines, .monaco-editor.vs-dark.mac.mouse-default .view-lines {
  cursor: default
}

.monaco-editor.hc-black.mac.mouse-copy .view-lines, .monaco-editor.mouse-copy .view-lines, .monaco-editor.vs-dark.mac.mouse-copy .view-lines {
  cursor: copy
}

.monaco-custom-checkbox {
  margin-left: 2px;
  float: left;
  cursor: pointer;
  overflow: hidden;
  opacity: .7;
  width: 20px;
  height: 20px;
  border: 1px solid transparent;
  padding: 1px;
  box-sizing: border-box;
  -moz-user-select: none;
  user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none
}

.monaco-custom-checkbox.checked, .monaco-custom-checkbox:hover {
  opacity: 1
}

.hc-black .monaco-custom-checkbox, .hc-black .monaco-custom-checkbox:hover {
  background: none
}

.monaco-custom-checkbox.monaco-simple-checkbox {
  height: 18px;
  width: 18px;
  border: 1px solid transparent;
  border-radius: 3px;
  margin-right: 9px;
  margin-left: 0;
  padding: 0;
  opacity: 1;
  background-size: 16px !important
}

.monaco-custom-checkbox.monaco-simple-checkbox:not(.checked):before {
  visibility: hidden
}

.monaco-editor .find-widget {
  position: absolute;
  z-index: 35;
  height: 33px;
  overflow: hidden;
  line-height: 19px;
  transition: transform .2s linear;
  padding: 0 4px;
  box-sizing: border-box;
  transform: translateY(calc(-100% - 10px))
}

.monaco-editor .find-widget textarea {
  margin: 0
}

.monaco-editor .find-widget.hiddenEditor {
  display: none
}

.monaco-editor .find-widget.replaceToggled > .replace-part {
  display: flex
}

.monaco-editor .find-widget.visible {
  transform: translateY(0)
}

.monaco-editor .find-widget .monaco-inputbox.synthetic-focus {
  outline: 1px solid -webkit-focus-ring-color;
  outline-offset: -1px
}

.monaco-editor .find-widget .monaco-inputbox .input {
  background-color: transparent;
  min-height: 0
}

.monaco-editor .find-widget .monaco-findInput .input {
  font-size: 13px
}

.monaco-editor .find-widget > .find-part, .monaco-editor .find-widget > .replace-part {
  margin: 4px 0 0 17px;
  font-size: 12px;
  display: flex
}

.monaco-editor .find-widget > .find-part .monaco-inputbox, .monaco-editor .find-widget > .replace-part .monaco-inputbox {
  min-height: 25px
}

.monaco-editor .find-widget > .replace-part .monaco-inputbox > .ibwrapper > .mirror {
  padding-right: 22px
}

.monaco-editor .find-widget > .find-part .monaco-inputbox > .ibwrapper > .input, .monaco-editor .find-widget > .find-part .monaco-inputbox > .ibwrapper > .mirror, .monaco-editor .find-widget > .replace-part .monaco-inputbox > .ibwrapper > .input, .monaco-editor .find-widget > .replace-part .monaco-inputbox > .ibwrapper > .mirror {
  padding-top: 2px;
  padding-bottom: 2px
}

.monaco-editor .find-widget > .find-part .find-actions, .monaco-editor .find-widget > .replace-part .replace-actions {
  height: 25px;
  display: flex;
  align-items: center
}

.monaco-editor .find-widget .monaco-findInput {
  vertical-align: middle;
  display: flex;
  flex: 1
}

.monaco-editor .find-widget .monaco-findInput .monaco-scrollable-element {
  width: 100%
}

.monaco-editor .find-widget .monaco-findInput .monaco-scrollable-element .scrollbar.vertical {
  opacity: 0
}

.monaco-editor .find-widget .matchesCount {
  display: flex;
  flex: initial;
  margin: 0 0 0 3px;
  padding: 2px 0 0 2px;
  height: 25px;
  vertical-align: middle;
  box-sizing: border-box;
  text-align: center;
  line-height: 23px
}

.monaco-editor .find-widget .button {
  width: 16px;
  height: 16px;
  padding: 3px;
  border-radius: 5px;
  flex: initial;
  margin-left: 3px;
  background-position: 50%;
  background-repeat: no-repeat;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center
}

.monaco-editor .find-widget .codicon-find-selection {
  width: 22px;
  height: 22px;
  padding: 3px;
  border-radius: 5px
}

.monaco-editor .find-widget .button.left {
  margin-left: 0;
  margin-right: 3px
}

.monaco-editor .find-widget .button.wide {
  width: auto;
  padding: 1px 6px;
  top: -1px
}

.monaco-editor .find-widget .button.toggle {
  position: absolute;
  top: 0;
  left: 3px;
  width: 18px;
  height: 100%;
  border-radius: 0;
  box-sizing: border-box
}

.monaco-editor .find-widget .button.toggle.disabled {
  display: none
}

.monaco-editor .find-widget .disabled {
  opacity: .3;
  cursor: default
}

.monaco-editor .find-widget > .replace-part {
  display: none
}

.monaco-editor .find-widget > .replace-part > .monaco-findInput {
  position: relative;
  display: flex;
  vertical-align: middle;
  flex: auto;
  flex-grow: 0;
  flex-shrink: 0
}

.monaco-editor .find-widget > .replace-part > .monaco-findInput > .controls {
  position: absolute;
  top: 3px;
  right: 2px
}

.monaco-editor .find-widget.reduced-find-widget .matchesCount {
  display: none
}

.monaco-editor .find-widget.narrow-find-widget {
  max-width: 257px !important
}

.monaco-editor .find-widget.collapsed-find-widget {
  max-width: 170px !important
}

.monaco-editor .find-widget.collapsed-find-widget .button.next, .monaco-editor .find-widget.collapsed-find-widget .button.previous, .monaco-editor .find-widget.collapsed-find-widget .button.replace, .monaco-editor .find-widget.collapsed-find-widget .button.replace-all, .monaco-editor .find-widget.collapsed-find-widget > .find-part .monaco-findInput .controls {
  display: none
}

.monaco-editor .findMatch {
  -webkit-animation-duration: 0;
  animation-duration: 0;
  -webkit-animation-name: inherit !important;
  animation-name: inherit !important
}

.monaco-editor .find-widget .monaco-sash {
  left: 0 !important
}

.monaco-editor.hc-black .find-widget .button:before {
  position: relative;
  top: 1px;
  left: 2px
}

.monaco-findInput {
  position: relative
}

.monaco-findInput .monaco-inputbox {
  font-size: 13px;
  width: 100%
}

.monaco-findInput > .controls {
  position: absolute;
  top: 3px;
  right: 2px
}

.vs .monaco-findInput.disabled {
  background-color: #e1e1e1
}

.vs-dark .monaco-findInput.disabled {
  background-color: #333
}

.monaco-findInput.highlight-0 .controls {
  -webkit-animation: monaco-findInput-highlight-0 .1s linear 0s;
  animation: monaco-findInput-highlight-0 .1s linear 0s
}

.monaco-findInput.highlight-1 .controls {
  -webkit-animation: monaco-findInput-highlight-1 .1s linear 0s;
  animation: monaco-findInput-highlight-1 .1s linear 0s
}

.hc-black .monaco-findInput.highlight-0 .controls, .vs-dark .monaco-findInput.highlight-0 .controls {
  -webkit-animation: monaco-findInput-highlight-dark-0 .1s linear 0s;
  animation: monaco-findInput-highlight-dark-0 .1s linear 0s
}

.hc-black .monaco-findInput.highlight-1 .controls, .vs-dark .monaco-findInput.highlight-1 .controls {
  -webkit-animation: monaco-findInput-highlight-dark-1 .1s linear 0s;
  animation: monaco-findInput-highlight-dark-1 .1s linear 0s
}

@-webkit-keyframes monaco-findInput-highlight-0 {
  0% {
    background: rgba(253, 255, 0, .8)
  }
  to {
    background: transparent
  }
}

@keyframes monaco-findInput-highlight-0 {
  0% {
    background: rgba(253, 255, 0, .8)
  }
  to {
    background: transparent
  }
}

@-webkit-keyframes monaco-findInput-highlight-1 {
  0% {
    background: rgba(253, 255, 0, .8)
  }
  99% {
    background: transparent
  }
}

@keyframes monaco-findInput-highlight-1 {
  0% {
    background: rgba(253, 255, 0, .8)
  }
  99% {
    background: transparent
  }
}

@-webkit-keyframes monaco-findInput-highlight-dark-0 {
  0% {
    background: hsla(0, 0%, 100%, .44)
  }
  to {
    background: transparent
  }
}

@keyframes monaco-findInput-highlight-dark-0 {
  0% {
    background: hsla(0, 0%, 100%, .44)
  }
  to {
    background: transparent
  }
}

@-webkit-keyframes monaco-findInput-highlight-dark-1 {
  0% {
    background: hsla(0, 0%, 100%, .44)
  }
  99% {
    background: transparent
  }
}

@keyframes monaco-findInput-highlight-dark-1 {
  0% {
    background: hsla(0, 0%, 100%, .44)
  }
  99% {
    background: transparent
  }
}

.monaco-editor .margin-view-overlays .codicon-folding-collapsed, .monaco-editor .margin-view-overlays .codicon-folding-expanded {
  cursor: pointer;
  opacity: 0;
  transition: opacity .5s;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 140%;
  margin-left: 2px
}

.monaco-editor .margin-view-overlays .codicon.alwaysShowFoldIcons, .monaco-editor .margin-view-overlays .codicon.codicon-folding-collapsed, .monaco-editor .margin-view-overlays:hover .codicon {
  opacity: 1
}

.monaco-editor .inline-folded:after {
  color: grey;
  margin: .1em .2em 0 .2em;
  content: "⋯";
  display: inline;
  line-height: 1em;
  cursor: pointer
}

.monaco-editor .detected-link, .monaco-editor .detected-link-active {
  text-decoration: underline;
  text-underline-position: under
}

.monaco-editor .detected-link-active {
  cursor: pointer
}

.monaco-editor .parameter-hints-widget {
  z-index: 10;
  display: flex;
  flex-direction: column;
  line-height: 1.5em
}

.monaco-editor .parameter-hints-widget > .phwrapper {
  max-width: 440px;
  display: flex;
  flex-direction: row
}

.monaco-editor .parameter-hints-widget.multiple {
  min-height: 3.3em;
  padding: 0
}

.monaco-editor .parameter-hints-widget.visible {
  transition: left .05s ease-in-out
}

.monaco-editor .parameter-hints-widget p, .monaco-editor .parameter-hints-widget ul {
  margin: 8px 0
}

.monaco-editor .parameter-hints-widget .body, .monaco-editor .parameter-hints-widget .monaco-scrollable-element {
  display: flex;
  flex: 1;
  flex-direction: column;
  min-height: 100%
}

.monaco-editor .parameter-hints-widget .signature {
  padding: 4px 5px
}

.monaco-editor .parameter-hints-widget .docs {
  padding: 0 10px 0 5px;
  white-space: pre-wrap
}

.monaco-editor .parameter-hints-widget .docs.empty {
  display: none
}

.monaco-editor .parameter-hints-widget .docs .markdown-docs {
  white-space: normal
}

.monaco-editor .parameter-hints-widget .docs .markdown-docs code {
  font-family: var(--monaco-monospace-font)
}

.monaco-editor .parameter-hints-widget .docs .code {
  white-space: pre-wrap
}

.monaco-editor .parameter-hints-widget .docs code {
  border-radius: 3px;
  padding: 0 .4em
}

.monaco-editor .parameter-hints-widget .controls {
  display: none;
  flex-direction: column;
  align-items: center;
  min-width: 22px;
  justify-content: flex-end
}

.monaco-editor .parameter-hints-widget.multiple .controls {
  display: flex;
  padding: 0 2px
}

.monaco-editor .parameter-hints-widget.multiple .button {
  width: 16px;
  height: 16px;
  background-repeat: no-repeat;
  cursor: pointer
}

.monaco-editor .parameter-hints-widget .button.previous {
  bottom: 24px
}

.monaco-editor .parameter-hints-widget .overloads {
  text-align: center;
  height: 12px;
  line-height: 12px;
  opacity: .5;
  font-family: var(--monaco-monospace-font)
}

.monaco-editor .parameter-hints-widget .signature .parameter.active {
  font-weight: 700;
  text-decoration: underline
}

.monaco-editor .parameter-hints-widget .documentation-parameter > .parameter {
  font-weight: 700;
  margin-right: .5em
}

.monaco-editor .rename-box {
  z-index: 100;
  color: inherit
}

.monaco-editor .rename-box.preview {
  padding: 3px 3px 0 3px
}

.monaco-editor .rename-box .rename-input {
  padding: 3px;
  width: calc(100% - 6px)
}

.monaco-editor .rename-box .rename-label {
  display: none;
  opacity: .8
}

.monaco-editor .rename-box.preview .rename-label {
  display: inherit
}

.monaco-editor .accessibilityHelpWidget {
  padding: 10px;
  vertical-align: middle;
  overflow: scroll
}

.monaco-editor .iPadShowKeyboard {
  width: 58px;
  min-width: 0;
  height: 36px;
  min-height: 0;
  margin: 0;
  padding: 0;
  position: absolute;
  resize: none;
  overflow: hidden;
  background: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTMiIGhlaWdodD0iMzYiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGcgY2xpcC1wYXRoPSJ1cmwoI2NsaXAwKSI+PHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik00OC4wMzYgNC4wMUg0LjAwOFYzMi4wM2g0NC4wMjhWNC4wMXpNNC4wMDguMDA4QTQuMDAzIDQuMDAzIDAgMDAuMDA1IDQuMDFWMzIuMDNhNC4wMDMgNC4wMDMgMCAwMDQuMDAzIDQuMDAyaDQ0LjAyOGE0LjAwMyA0LjAwMyAwIDAwNC4wMDMtNC4wMDJWNC4wMUE0LjAwMyA0LjAwMyAwIDAwNDguMDM2LjAwOEg0LjAwOHpNOC4wMSA4LjAxM2g0LjAwM3Y0LjAwM0g4LjAxVjguMDEzem0xMi4wMDggMGgtNC4wMDJ2NC4wMDNoNC4wMDJWOC4wMTN6bTQuMDAzIDBoNC4wMDJ2NC4wMDNoLTQuMDAyVjguMDEzem0xMi4wMDggMGgtNC4wMDN2NC4wMDNoNC4wMDNWOC4wMTN6bTQuMDAyIDBoNC4wMDN2NC4wMDNINDAuMDNWOC4wMTN6bS0yNC4wMTUgOC4wMDVIOC4wMXY0LjAwM2g4LjAwNnYtNC4wMDN6bTQuMDAyIDBoNC4wMDN2NC4wMDNoLTQuMDAzdi00LjAwM3ptMTIuMDA4IDBoLTQuMDAzdjQuMDAzaDQuMDAzdi00LjAwM3ptMTIuMDA4IDB2NC4wMDNoLTguMDA1di00LjAwM2g4LjAwNXptLTMyLjAyMSA4LjAwNUg4LjAxdjQuMDAzaDQuMDAzdi00LjAwM3ptNC4wMDMgMGgyMC4wMTN2NC4wMDNIMTYuMDE2di00LjAwM3ptMjguMDE4IDBINDAuMDN2NC4wMDNoNC4wMDN2LTQuMDAzeiIgZmlsbD0iIzQyNDI0MiIvPjwvZz48ZGVmcz48Y2xpcFBhdGggaWQ9ImNsaXAwIj48cGF0aCBmaWxsPSIjZmZmIiBkPSJNMCAwaDUzdjM2SDB6Ii8+PC9jbGlwUGF0aD48L2RlZnM+PC9zdmc+") 50% no-repeat;
  border: 4px solid #f6f6f6;
  border-radius: 4px
}

.monaco-editor.vs-dark .iPadShowKeyboard {
  background: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTMiIGhlaWdodD0iMzYiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGcgY2xpcC1wYXRoPSJ1cmwoI2NsaXAwKSI+PHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik00OC4wMzYgNC4wMUg0LjAwOFYzMi4wM2g0NC4wMjhWNC4wMXpNNC4wMDguMDA4QTQuMDAzIDQuMDAzIDAgMDAuMDA1IDQuMDFWMzIuMDNhNC4wMDMgNC4wMDMgMCAwMDQuMDAzIDQuMDAyaDQ0LjAyOGE0LjAwMyA0LjAwMyAwIDAwNC4wMDMtNC4wMDJWNC4wMUE0LjAwMyA0LjAwMyAwIDAwNDguMDM2LjAwOEg0LjAwOHpNOC4wMSA4LjAxM2g0LjAwM3Y0LjAwM0g4LjAxVjguMDEzem0xMi4wMDggMGgtNC4wMDJ2NC4wMDNoNC4wMDJWOC4wMTN6bTQuMDAzIDBoNC4wMDJ2NC4wMDNoLTQuMDAyVjguMDEzem0xMi4wMDggMGgtNC4wMDN2NC4wMDNoNC4wMDNWOC4wMTN6bTQuMDAyIDBoNC4wMDN2NC4wMDNINDAuMDNWOC4wMTN6bS0yNC4wMTUgOC4wMDVIOC4wMXY0LjAwM2g4LjAwNnYtNC4wMDN6bTQuMDAyIDBoNC4wMDN2NC4wMDNoLTQuMDAzdi00LjAwM3ptMTIuMDA4IDBoLTQuMDAzdjQuMDAzaDQuMDAzdi00LjAwM3ptMTIuMDA4IDB2NC4wMDNoLTguMDA1di00LjAwM2g4LjAwNXptLTMyLjAyMSA4LjAwNUg4LjAxdjQuMDAzaDQuMDAzdi00LjAwM3ptNC4wMDMgMGgyMC4wMTN2NC4wMDNIMTYuMDE2di00LjAwM3ptMjguMDE4IDBINDAuMDN2NC4wMDNoNC4wMDN2LTQuMDAzeiIgZmlsbD0iI0M1QzVDNSIvPjwvZz48ZGVmcz48Y2xpcFBhdGggaWQ9ImNsaXAwIj48cGF0aCBmaWxsPSIjZmZmIiBkPSJNMCAwaDUzdjM2SDB6Ii8+PC9jbGlwUGF0aD48L2RlZnM+PC9zdmc+") 50% no-repeat;
  border: 4px solid #252526
}

.monaco-editor .tokens-inspect-widget {
  z-index: 50;
  -moz-user-select: text;
  user-select: text;
  -webkit-user-select: text;
  -ms-user-select: text;
  padding: 10px
}

.tokens-inspect-separator {
  height: 1px;
  border: 0
}

.monaco-editor .tokens-inspect-widget .tm-token {
  font-family: var(--monaco-monospace-font)
}

.monaco-editor .tokens-inspect-widget .tm-token-length {
  font-weight: 400;
  font-size: 60%;
  float: right
}

.monaco-editor .tokens-inspect-widget .tm-metadata-table {
  width: 100%
}

.monaco-editor .tokens-inspect-widget .tm-metadata-value {
  font-family: var(--monaco-monospace-font);
  text-align: right
}

.monaco-editor .tokens-inspect-widget .tm-token-type {
  font-family: var(--monaco-monospace-font)
}

input[data-v-0540f40c] {
  height: 22px;
  line-height: 22px;
  border-radius: 0;
  outline: 0;
  border: 1px solid var(--input-border-color);
  padding-left: 5px;
  background: var(--input-background);
  color: var(--color);
  width: 100%
}

input[data-v-0540f40c]:focus {
  border-color: var(--input-border-foucs-color)
}

div[data-v-0540f40c] {
  position: relative;
  display: inline-block
}

span[data-v-0540f40c] {
  position: absolute;
  right: 5px;
  color: var(--icon-color);
  cursor: pointer
}

.ma-tree-container {
  position: relative
}

.ma-tree-container .loading i {
  color: var(--color);
  font-size: 20px
}

.ma-tree-container .loading .icon .ma-icon {
  padding: 0
}

.ma-tree-container .loading .icon {
  width: 20px;
  margin: 0 auto;
  line-height: normal;
  -webkit-animation: rotate 1s linear infinite;
  animation: rotate 1s linear infinite
}

.ma-tree-container .loading {
  color: var(--color);
  position: absolute;
  text-align: center;
  width: 100%;
  top: 50%;
  margin-top: -20px
}

.ma-tree-container .no-data {
  color: var(--color);
  position: absolute;
  top: 50%;
  left: 50%;
  margin-left: -20px
}

.ma-checkbox[data-v-33f38957] {
  width: 100%;
  height: 100%;
  text-align: center
}

.ma-checkbox input[data-v-33f38957] {
  display: none
}

.ma-checkbox input + label[data-v-33f38957] {
  position: relative;
  color: #c9c9c9;
  font-size: 12px;
  height: 24px;
  line-height: 24px;
  width: 24px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  display: inline-block
}

.ma-checkbox input + label[data-v-33f38957]:after {
  display: inline-block;
  background-color: var(--checkbox-background);
  border: 1px solid var(--checkbox-border);
  content: "";
  width: 16px;
  height: 16px;
  line-height: 16px;
  position: absolute;
  top: 2px;
  left: 3px;
  text-align: center;
  font-size: 12px;
  color: var(--checkbox-text-color)
}

.ma-checkbox input:checked + label[data-v-33f38957]:after {
  content: "\2714";
  background-color: var(--checkbox-selected-background);
  border-color: var(--checkbox-selected-border)
}

.ma-checkbox input + label.checkedHalf[data-v-33f38957]:after {
  content: "\2501"
}

.ma-tree-wrapper:not(.ma-dialog-wrapper) {
  text-align: left;
  float: left;
  border-right: 1px solid var(--toolbox-border-right-color);
  overflow: auto;
  /* width: 250px; */
  min-width: 250px;
  background: var(--toolbox-background);
  position: relative
}

.ma-tree-wrapper .ma-tree-item .ma-tree-item-header {
  font-weight: 700;
  height: 20px;
  line-height: 20px
}

.ma-tree-wrapper .ma-tree-item .ma-tree-sub-items > * {
  line-height: 20px;
  white-space: nowrap
}

.ma-tree-wrapper .ma-tree-item .ma-tree-item-header[dragtarget] label {
  border-color: red
}

.ma-tree-wrapper .ma-tree-hover:hover, .ma-tree-wrapper .ma-tree-select {
  background: var(--toolbox-list-hover-background)
}

.ma-tree-wrapper .ma-tree-item .ma-tree-sub-items > .selected {
  background: var(--toolbox-list-selected-background)
}

.ma-tree-wrapper .ma-icon {
  color: var(--toolbox-list-icon-color);
  padding-right: 2px;
  font-size: 14px
}

.ma-tree-wrapper .ma-icon-arrow-bottom {
  color: var(--toolbox-list-arrow-color)
}


.ma-tree-wrapper .ma-icon-lock {
  color: var(--toolbox-list-label-color);
  margin-left: 5px
}

.ma-tree-wrapper label {
  color: var(--toolbox-list-label-color);
  display: inline-block;
  border: 1px solid transparent
}

.ma-tree-wrapper .ma-tree-toolbar-search {
  flex: 1
}

.ma-tree-wrapper .ma-tree-toolbar-search input {
  border: none;
  background: none;
  height: 100%;
  color: var(--input-color)
}

.ma-tree-wrapper .ma-tree-toolbar-search input:focus {
  outline: none
}

.ma-tree-wrapper .ma-tree-toolbar {
  background: var(--background);
  color: var(--toolbox-list-label-color);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  padding: 1px
}

.ma-tree-wrapper .ma-tree-toolbar-btn {
  padding: 2px;
  align-self: flex-end;
  display: inline-block
}

.ma-tree-wrapper .ma-tree-toolbar-btn.hover, .ma-tree-wrapper .ma-tree-toolbar-btn:hover {
  background: var(--toolbox-list-hover-background)
}

.ma-tree-wrapper .ma-tree-toolbar i {
  color: var(--toolbox-list-header-icon-color)
}

.ma-tree-wrapper .ma-tree-container {
  height: calc(100% - 25px);
  overflow: auto
}

.ma-tree-wrapper .ma-icon-datasource {
  color: #089910
}

.ma-checkbox[data-v-9baa9c8a] {
  display: inline-block;
  width: 20px;
  height: 12px
}

.ma-tree-wrapper .ma-tree-container[data-v-9baa9c8a] {
  height: 100%
}

.ma-search-container {
  overflow: hidden
}

.ma-search-container .ma-search-result-container {
  overflow: auto;
  height: 200px
}

.ma-search-result-container {
  background: var(--toolbox-background);
  margin-top: -4px
}

.ma-search-container .ma-search-result-container .ma-search-result-item {
  display: flex;
  padding: 0 5px;
  line-height: 20px
}

.ma-search-container .ma-search-result-container .ma-search-result-item.selected, .ma-search-container .ma-search-result-container .ma-search-result-item:hover {
  background: var(--toolbox-list-hover-background)
}

.ma-search-container .ma-search-result-container .ma-search-result-item .label {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap
}

.ma-search-container .ma-search-result-container .ma-search-result-item .label .keyword {
  background: #ffde7b;
  color: #000
}

.ma-search-result-item .line, .ma-search-result-item .name {
  color: var(--toolbox-list-span-color)
}

.ma-search-container .ma-search-result-container .ma-search-result-item .line {
  padding-left: 5px
}

.ma-search-container .display-text {
  padding: 0 10px;
  height: 30px;
  border-top: 1px solid var(--border-color)
}

.ma-search-container .no-data-tip {
  line-height: 530px;
  text-align: center
}

.ma-header[data-v-60bad85e] {
  height: 30px;
  line-height: 30px;
  text-align: right;
  color: var(--header-default-color);
  background: var(--background);
  display: flex;
  border-bottom: 1px solid var(--border-color);
  padding-right: 15px
}

.ma-header > .ma-logo[data-v-60bad85e] {
  float: left;
  color: var(--header-title-color);
  font-weight: 700;
  font-size: 0;
  letter-spacing: 0;
  background-repeat: no-repeat;
  background-position: 4px 7px;
  padding-left: 25px
}

.ma-header > div[data-v-60bad85e]:not(.ma-logo):not(.ma-skin-selector) {
  flex: 1;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap
}

.ma-header img[data-v-60bad85e] {
  height: 24px;
  width: 22px;
  vertical-align: middle;
  margin-top: -6px
}

.ma-header > div > label[data-v-60bad85e] {
  font-size: 16px;
  display: inline-block;
  margin-right: 5px;
  text-align: left
}

.ma-header > div > label.version[data-v-60bad85e] {
  font-size: 12px;
  color: var(--header-version-color)
}

.ma-header > span[data-v-60bad85e] {
  cursor: pointer;
  padding: 0 4px;
  height: 30px;
  line-height: 30px;
  display: inline-block;
  vertical-align: middle;
  border-radius: 2px;
  text-align: center
}

.ma-header > span[data-v-60bad85e]:last-child {
  margin-right: 15px
}

.ma-header .ma-button-run[data-v-60bad85e] {
  color: var(--button-run-color)
}

.ma-header .ma-icon-push[data-v-60bad85e] {
  color: var(--button-run-color);
  font-weight: 700
}

.ma-header > span[data-v-60bad85e]:hover:not(.disabled) {
  background: var(--button-hover-background)
}

.ma-header > span.disabled[data-v-60bad85e] {
  color: var(--button-disabled-background)
}

.ma-skin-selector[data-v-60bad85e] {
  position: absolute;
  top: 30px;
  right: 100px;
  z-index: 20;
  background: var(--background);
  border: 1px solid var(--border-color);
  border-top: none
}

.ma-skin-selector ul li[data-v-60bad85e]:not(:last-child) {
  border-bottom: 1px solid var(--border-color);
  padding: 2px 5px
}

.ma-remote-push-container label[data-v-60bad85e] {
  width: 80px;
  text-align: right;
  display: inline-block
}

ul li[data-v-60bad85e] {
  height: 24px;
  line-height: 24px;
  text-align: center;
  cursor: pointer
}

label[data-v-32ef90d3] {
  width: 80px;
  text-align: right;
  display: inline-block
}

.ma-status-container[data-v-2b13953c] {
  position: absolute;
  bottom: 0;
  height: 24px;
  width: 100%;
  border-top: 1px solid var(--footer-border-color);
  padding-left: 20px;
  line-height: 24px;
  background: var(--background);
  flex: none;
  text-align: left;
  color: var(--color);
  display: flex
}

.ma-status-container > div[data-v-2b13953c] {
  flex: 1
}

.ma-status-container .ma-icons[data-v-2b13953c] {
  flex: none;
  color: var(--header-default-color)
}

.ma-status-container .ma-icons span[data-v-2b13953c] {
  cursor: pointer;
  padding: 0 4px;
  height: 20px;
  line-height: 20px;
  display: inline-block;
  vertical-align: middle;
  border-radius: 2px;
  text-align: center
}

.ma-status-container .ma-icons span[data-v-2b13953c]:hover {
  background: var(--button-hover-background)
}

.ma-bottom-content-item[data-v-4193fef0] {
  background: #fff;
  overflow: auto;
  height: 100%;
  position: relative;
  color: var(--color)
}

.ma-bottom-content-item > div[data-v-4193fef0]:first-child {
  background: var(--background);
  border-bottom: 1px solid var(--tab-bar-border-color);
  height: 24px;
  line-height: 24px;
  padding-left: 10px
}

.ma-bottom-content-item > div:first-child > span[data-v-4193fef0] {
  color: var(--icon-color);
  cursor: pointer;
  padding: 0 3px;
  display: inline-block;
  height: 24px;
  line-height: 24px
}

.ma-bottom-content-item > div:first-child > span[data-v-4193fef0]:last-child {
  float: right;
  margin-right: 12px
}

.ma-bottom-content-item .ma-item-body[data-v-4193fef0] {
  position: absolute;
  top: 24px;
  bottom: 0;
  width: 100%;
  overflow: auto
}

.ma-select[data-v-55a605a0] {
  position: relative;
  display: inline-block;
  background: var(--select-background);
  height: 22px;
  line-height: 22px;
  width: 80px;
  font-size: 12px
}

.ma-select.inputable[data-v-55a605a0] {
  background: var(--select-inputable-background);
  border-color: var(--select-inputable-border)
}

.ma-select.border[data-v-55a605a0] {
  border: 1px solid var(--input-border-color)
}

.ma-select input[data-v-55a605a0] {
  background: none;
  border: none;
  height: 22px;
  line-height: 22px;
  border-radius: 0;
  outline: 0;
  padding-left: 5px;
  width: 100%;
  color: var(--color)
}

.ma-select span[data-v-55a605a0] {
  height: 22px;
  line-height: 22px;
  border-radius: 0;
  outline: 0;
  padding-left: 5px
}

.ma-select[data-v-55a605a0]:hover:not(.inputable) {
  background: var(--select-hover-background)
}

.ma-select[data-v-55a605a0]:after {
  content: "\efa2";
  font-family: magic-iconfont;
  position: absolute;
  right: 5px;
  top: 0
}

ul[data-v-55a605a0] {
  display: block;
  position: fixed;
  z-index: 10;
  background: var(--select-option-background);
  border: 1px solid var(--select-border-color);
  margin-top: -2px;
  padding: 0;
  box-sizing: content-box;
  margin-left: -1px
}

ul li[data-v-55a605a0] {
  padding: 0 5px;
  text-align: left;
  width: 100% !important;
  height: 22px;
  text-overflow: ellipsis;
  word-break: keep-all
}

ul li[data-v-55a605a0]:hover {
  background: var(--select-option-hover-background);
  color: var(--select-option-hover-color)
}

textarea[data-v-1bfec594] {
  line-height: 22px;
  border-radius: 0;
  outline: 0;
  border: 1px solid var(--input-border-color);
  resize: none;
  background: var(--input-background);
  color: var(--input-color)
}

textarea[data-v-1bfec594]:focus {
  border-color: var(--input-border-foucs-color)
}

.ma-tree {
  font-size: 14px;
  line-height: 14px
}

.ma-tree .tree-item {
  cursor: pointer
}

.ma-tree .item-selected {
  background-color: var(--toolbox-list-hover-background)
}

.item-inline {
  display: flex;
  flex-direction: row;
  align-items: center;
  white-space: nowrap
}

.ma-json-container[data-v-444eb439] {
  display: flex;
  flex-direction: row;
  height: 100%
}

.f_c[data-v-444eb439] {
  display: flex;
  flex-direction: column
}

.json-view[data-v-444eb439] {
  width: 50%;
  margin: 0 10px;
  border: 1px solid var(--border-color);
  border-top: none
}

.json-view .view-box[data-v-444eb439] {
  padding: 5px 0;
  height: 100%;
  overflow: auto
}

.json-panel[data-v-444eb439] {
  width: 50%;
  margin: 0 10px;
  border: 1px solid var(--border-color);
  border-top: none;
  overflow: auto
}

.json-panel .panel-box[data-v-444eb439] {
  padding: 5px
}

.json-panel .panel-box .box-item[data-v-444eb439] {
  min-height: 35px;
  display: flex;
  flex-direction: row;
  align-items: center;
  border-bottom: 1px solid var(--border-color)
}

.json-panel .panel-box .box-item .item-title[data-v-444eb439] {
  width: 125px
}

.json-panel .panel-box .box-item .item-content[data-v-444eb439] {
  flex: 1;
  word-break: break-all
}

.header[data-v-444eb439] {
  height: 30px;
  line-height: 30px;
  font-size: 14px;
  text-align: left;
  padding-left: 20px;
  background-color: var(--background);
  border-bottom: 1px solid var(--border-color);
  width: 100%
}

.ma-body-editor[data-v-7dba015a] {
  width: 100%;
  height: 100%
}

.ma-options[data-v-5c9a6bc0], div.ma-run[data-v-cf302046] {
  background: var(--background);
  height: 100%;
  width: 100%;
  position: relative;
  overflow: hidden
}

div.ma-run[data-v-cf302046] {
  display: flex;
  flex-direction: column
}

div.ma-run > [data-v-cf302046]:not(ul) {
  width: 100%;
  height: 100%;
  flex: 1
}

.ma-body-editor[data-v-cf302046] {
  width: 100%;
  height: 100%
}

.ma-response-body-container[data-v-cf302046] {
  width: 100%;
  height: 100%;
  border: none
}

.ma-layout-container .ma-content .ma-table-row div[data-v-cf302046], .ma-layout-container .ma-header div[data-v-cf302046] {
  width: 50%;
  padding-left: 5px;
  background: none
}

.ma-run .ma-layout .ma-content .content-bg[data-v-cf302046] {
  cursor: pointer
}

.ma-run .ma-layout .ma-content .content-bg[data-v-cf302046]:hover {
  background: var(--toolbox-list-hover-background)
}

.ma-structure-a > div[data-v-56f2b73e] {
  display: inline-block
}

.ma-structure-a .expand-a[data-v-56f2b73e] {
  display: block;
  font-style: normal
}

.expand-a label[data-v-56f2b73e] {
  margin-left: -6px
}

.ma-structure-a > div > span > span[data-v-56f2b73e]:last-child {
  padding-left: 5px
}

.ma-structure-a > div > span:not(:first-child):not(:last-child):not(:nth-child(2)) > span[data-v-56f2b73e]:first-child {
  padding-right: 5px
}

.colon[data-v-56f2b73e] {
  margin-right: 5px
}

.ma-structure-o > div[data-v-417e1a7e] {
  display: inline-block
}

.ma-structure-o .expand-o[data-v-417e1a7e] {
  display: block;
  font-style: normal
}

.expand-o label[data-v-417e1a7e] {
  margin-left: -6px
}

.ma-structure-o > div > span > span[data-v-417e1a7e]:last-child {
  padding-left: 5px
}

.ma-structure-o > div > span:not(:first-child):not(:last-child):not(:nth-child(2)) > span[data-v-417e1a7e]:first-child {
  padding-right: 5px
}

.colon[data-v-417e1a7e] {
  margin-right: 5px
}

.ma-structure-container {
  display: flex;
  flex-direction: row;
  height: 100%;
  font-size: 14px
}

.ma-structure-container label {
  padding-right: 2px
}

.ma-structure-container span {
  display: inline-block;
  color: var(--color)
}

.ma-structure-container .number {
  color: var(--text-number-color);
  font-weight: 700
}

.ma-structure-container .string {
  color: var(--text-string-color);
  font-weight: 700
}

.ma-structure-container .boolean {
  color: var(--text-boolean-color);
  font-weight: 700
}

.ma-structure-container .class, .ma-structure-container .property {
  color: var(--text-key-color);
  font-weight: 700
}

.ma-structure-container .ma-icon {
  font-size: 12px;
  color: var(--color)
}

.ma-debug-container[data-v-6d24a74e] {
  height: 100%;
  width: 100%;
  position: relative;
  background: var(--background)
}

ul[data-v-6d24a74e] {
  position: absolute;
  top: 0;
  left: 0;
  width: 24px;
  bottom: 0;
  border-right: 1px solid var(--tab-bar-border-color);
  background: var(--background)
}

ul li[data-v-6d24a74e] {
  width: 24px;
  height: 24px;
  line-height: 24px;
  text-align: center;
  cursor: pointer
}

ul li[data-v-6d24a74e]:not(.disabled):hover {
  background: var(--hover-background)
}

ul li i[data-v-6d24a74e] {
  color: var(--button-disabled-background)
}

ul li:first-child:not(.disabled) i[data-v-6d24a74e] {
  color: var(--icon-debug-color)
}

ul li:last-child:not(.disabled) i[data-v-6d24a74e] {
  color: var(--icon-step-color)
}

.ma-debug-container > div[data-v-6d24a74e] {
  position: absolute;
  left: 24px;
  top: 0;
  right: 0;
  bottom: 0;
  overflow: auto
}

table[data-v-6d24a74e] {
  width: 100%;
  border-collapse: collapse
}

table tr td[data-v-6d24a74e]:not(:last-child), table tr th[data-v-6d24a74e]:not(:last-child) {
  border-right: 1px solid var(--table-col-border-color)
}

table tr td[data-v-6d24a74e], table tr th[data-v-6d24a74e] {
  border-bottom: 1px solid var(--table-row-border-color);
  height: 24px;
  line-height: 24px;
  padding-left: 5px
}

table tr th[data-v-6d24a74e] {
  text-align: left
}

table tbody tr[data-v-6d24a74e]:nth-child(2n) {
  background: var(--table-even-background)
}

.ma-log-wrapper[data-v-70f852e4] {
  position: relative;
  overflow: auto;
  width: 100%;
  height: 100%;
  background: var(--run-log-background);
  padding: 5px
}

.ma-log[data-v-70f852e4] {
  position: absolute;
  font-size: 13.5px;
  height: 100%
}

.ma-log > div pre[data-v-70f852e4] {
  line-height: 20px
}

.ma-log > div.multiple pre[data-v-70f852e4] {
  max-height: 60px;
  overflow: hidden
}

.ma-log > div.multiple.more pre[data-v-70f852e4] {
  max-height: none
}

.ma-log span.multiple[data-v-70f852e4] {
  opacity: .5;
  font-size: 13px;
  text-decoration: underline;
  cursor: pointer
}

.ma-settings[data-v-5fac80a3] {
  background: var(--background);
  height: 100%;
  width: 100%;
  position: relative;
  outline: 0
}

.ma-settings .ma-layout[data-v-5fac80a3] {
  height: calc(100% - 25px)
}

.ma-request-wrapper[data-v-98eddd34] {
  background: var(--background);
  height: 100%;
  width: 100%;
  position: relative
}

.ma-api-info[data-v-98eddd34] {
  padding: 5px;
  border-bottom: 1px solid var(--tab-bar-border-color)
}

.ma-api-info [data-v-98eddd34] {
  display: inline-block
}

.ma-api-info label[data-v-98eddd34] {
  width: 75px;
  text-align: right;
  padding: 0 5px
}

.ma-api-info input[data-v-98eddd34]:last-child {
  width: calc(100% - 570px)
}

.ma-request-wrapper > div[data-v-98eddd34]:not(.ma-api-info) {
  position: absolute;
  top: 33px;
  bottom: 0;
  width: 100%;
  overflow: hidden;
  display: inline-block
}

.ma-request-wrapper > div > h3[data-v-98eddd34] {
  color: var(--color);
  font-size: 12px;
  font-weight: inherit;
  height: 24px;
  line-height: 24px;
  text-align: center;
  border-bottom: 1px solid var(--tab-bar-border-color)
}

.ma-layout .ma-table-row > [data-v-98eddd34] {
  width: 20%
}

.ma-layout .ma-table-row > [data-v-98eddd34]:last-child {
  width: 60%
}

.ma-todo[data-v-4f8952a4] {
  background: var(--background);
  height: 100%;
  width: 100%;
  position: relative;
  outline: 0
}

.ma-todo .ma-layout[data-v-4f8952a4] {
  height: 100%
}

.ma-todo .ma-layout .ma-content .content-bg[data-v-4f8952a4] {
  cursor: pointer
}

.ma-todo .ma-layout .ma-content .content-bg span[data-v-4f8952a4] {
  color: var(--toolbox-list-span-color)
}

.ma-todo .ma-layout .ma-content .content-bg[data-v-4f8952a4]:nth-child(2n) {
  background: var(--table-even-background)
}

.ma-todo .ma-layout .ma-content .content-bg .todo-item[data-v-4f8952a4] {
  font-style: italic;
  color: var(--todo-color)
}

.ma-todo .ma-layout .ma-content .content-bg[data-v-4f8952a4]:hover {
  background: var(--toolbox-list-hover-background)
}

.ma-layout .ma-table-row > [data-v-4f8952a4] {
  width: 30% !important;
  background: none
}

.ma-layout .ma-table-row > [data-v-4f8952a4]:last-child {
  width: 70% !important
}

.ma-todo .loading i[data-v-4f8952a4] {
  color: var(--color);
  font-size: 20px
}

.ma-todo .loading .icon[data-v-4f8952a4] {
  width: 20px;
  margin: 0 auto;
  -webkit-animation: rotate 1s linear infinite;
  animation: rotate 1s linear infinite
}

.ma-todo .loading[data-v-4f8952a4] {
  color: var(--color);
  position: absolute;
  text-align: center;
  width: 100%;
  top: 50%;
  margin-top: -20px
}

.ma-todo .no-data[data-v-4f8952a4] {
  color: var(--color);
  position: absolute;
  top: 50%;
  left: 50%;
  margin-left: -20px
}

.ma-event[data-v-131af95c] {
  background: var(--background);
  height: 100%;
  width: 100%;
  position: relative;
  outline: 0
}

.ma-event .ma-layout[data-v-131af95c] {
  height: 100%
}

.ma-event .ma-layout .ma-content .content-bg span[data-v-131af95c] {
  color: var(--toolbox-list-span-color)
}

.ma-event .ma-layout .ma-content .content-bg[data-v-131af95c]:nth-child(2n) {
  background: var(--table-even-background)
}

.ma-event .ma-layout .ma-content .content-bg[data-v-131af95c]:hover {
  background: var(--toolbox-list-hover-background)
}

.ma-layout .ma-table-row[data-v-131af95c] {
  display: flex
}

.ma-layout .ma-table-row > [data-v-131af95c] {
  width: 150px !important;
  background: none;
  padding: 0 2px
}

.ma-layout .ma-table-row > [data-v-131af95c]:last-child {
  flex: 1;
  width: auto
}

.ma-bottom-container {
  background: var(--background)
}

.ma-bottom-container .ma-bottom-content-container {
  border-bottom: 1px solid var(--tab-bar-border-color);
  height: 300px
}

.ma-bottom-container .ma-bottom-content-container > div {
  display: none
}

.ma-bottom-container .ma-bottom-content-container > .visible {
  display: block
}

.ma-bottom-tab li {
  float: left;
  cursor: pointer;
  padding: 0 8px;
  height: 24px;
  line-height: 24px;
  color: var(--color)
}

.ma-bottom-tab li.float-right {
  float: right
}

.ma-bottom-tab li i {
  color: var(--icon-color);
  padding: 0 2px;
  display: inline-block;
  vertical-align: top
}

.ma-bottom-tab li:hover {
  background: var(--hover-background)
}

.ma-bottom-tab li.selected {
  background: var(--selected-background)
}

.ma-resizer-y {
  position: absolute;
  width: 100%;
  height: 10px;
  margin-top: -5px;
  background: none;
  cursor: n-resize
}

.ma-nav {
  border-bottom: 1px solid var(--tab-bar-border-color);
  height: 24px
}

.ma-nav li {
  display: inline-block;
  height: 24px;
  line-height: 24px;
  padding: 0 10px;
  cursor: pointer
}

.ma-nav li.selected {
  background: var(--selected-background)
}

.ma-nav li:hover:not(.selected) {
  background: var(--hover-background)
}

.ma-layout {
  display: flex;
  flex: auto;
  flex-direction: row;
  height: 100%
}

.ma-layout .ma-layout-container {
  flex: auto;
  height: 100%;
  width: 100%
}

.ma-layout .ma-header > * {
  padding: 0 2px;
  border-right: none !important
}

.ma-layout .ma-table-row > * {
  display: inline-block;
  width: 60%;
  height: 23px;
  line-height: 23px;
  border-bottom: 1px solid var(--input-border-color);
  border-right: 1px solid var(--input-border-color);
  background: var(--background)
}

.ma-layout .ma-table-row input:focus {
  border-color: var(--input-border-foucs-color)
}

.ma-layout .ma-table-row input {
  border-color: transparent;
  border-top-color: transparent;
  border-right-color: transparent;
  border-bottom-color: transparent;
  border-left-color: transparent
}

.ma-layout .ma-table-row > :first-child, .ma-layout .ma-table-row > :last-child {
  width: 20%
}

.ma-layout .ma-content {
  flex: auto;
  overflow-x: hidden;
  height: calc(100% - 50px)
}

.ma-layout .ma-sider {
  border-right: 1px solid var(--tab-bar-border-color)
}

.ma-layout .ma-sider > * {
  width: 18px;
  height: 18px;
  line-height: 18px;
  margin: 3px;
  text-align: center;
  padding: 0;
  color: var(--icon-color);
  border-radius: 2px
}

.ma-layout .ma-sider > :hover {
  background: var(--hover-background)
}

.ma-nav-tab li {
  display: inline-block;
  height: 24px;
  line-height: 24px;
  padding: 0 10px;
  cursor: pointer
}

.ma-nav-tab li.selected {
  background: var(--selected-background)
}

.ma-nav-tab li:hover {
  background: var(--hover-background)
}

.ma-nav-tab {
  border-bottom: 1px solid var(--tab-bar-border-color);
  height: 24px
}

.ma-tree-wrapper .ma-checkbox input + label {
  width: 12px !important;
  height: 12px !important
}

.ma-tree-wrapper .ma-checkbox input + label:after {
  width: 12px !important;
  height: 12px !important;
  line-height: 12px !important;
  top: 0 !important;
  left: 0 !important
}

.ma-checkbox[data-v-3961dfda] {
  display: inline-block;
  width: 20px;
  height: 12px
}

.ma-tree-wrapper .ma-tree-container[data-v-3961dfda] {
  height: 100%
}

ul li[data-v-e868cf0a] {
  line-height: 20px;
  padding-left: 10px
}

ul li[data-v-e868cf0a]:hover {
  background: var(--toolbox-list-hover-background)
}

.ds-form[data-v-e868cf0a] {
  margin-bottom: 5px;
  display: flex
}

.ds-form label[data-v-e868cf0a] {
  margin-right: 5px;
  display: inline-block;
  width: 60px;
  text-align: right;
  height: 22px;
  line-height: 22px
}

.ds-form > div[data-v-e868cf0a] {
  flex: 1
}

.ds-form label[data-v-e868cf0a]:nth-of-type(2) {
  margin: 0 5px
}

.ma-editor span[data-v-e868cf0a] {
  color: unset
}

.ma-tree-wrapper[data-v-e868cf0a] {
  width: 100%;
  height: 100%
}

.ma-tree-wrapper .loading i[data-v-e868cf0a] {
  color: var(--color);
  font-size: 20px
}

.ma-tree-wrapper .loading .icon[data-v-e868cf0a] {
  width: 20px;
  margin: 0 auto;
  -webkit-animation: rotate 1s linear infinite;
  animation: rotate 1s linear infinite
}

.ma-tree-wrapper .loading[data-v-e868cf0a] {
  color: var(--color);
  position: absolute;
  text-align: center;
  width: 100%;
  top: 50%;
  margin-top: -20px
}

.ma-tree-wrapper .no-data[data-v-e868cf0a] {
  color: var(--color);
  position: absolute;
  top: 50%;
  left: 50%;
  margin-left: -20px
}

.ma-history[data-v-0ab6125a] {
  overflow: auto;
  position: relative;
  width: 100%;
  height: 485px;
  border-top: 1px solid var(--border-color)
}

.ma-history ul[data-v-0ab6125a] {
  position: absolute;
  left: 0;
  width: 210px;
  bottom: 5px;
  top: 0;
  color: var(--color);
  overflow: auto;
  background: var(--toolbox-background)
}

.ma-history ul li[data-v-0ab6125a] {
  border-bottom: 1px solid var(--border-color);
  height: 20px;
  line-height: 20px;
  padding-left: 5px;
  white-space: nowrap
}

.ma-history ul li.selected[data-v-0ab6125a], .ma-history ul li[data-v-0ab6125a]:hover {
  background: var(--history-select-background);
  color: var(--history-select-color)
}

.ma-history .version[data-v-0ab6125a] {
  position: absolute;
  left: 210px;
  right: 0;
  line-height: 24px;
  height: 24px
}

.ma-history .version span[data-v-0ab6125a] {
  float: left;
  display: block;
  padding: 0 10px
}

.ma-history .version span.current[data-v-0ab6125a] {
  float: right
}

.ma-history .diff-editor[data-v-0ab6125a] {
  position: absolute;
  left: 210px;
  right: 0;
  top: 24px;
  bottom: 5px
}

.ma-editor-container[data-v-c04ca732] {
  flex: 1;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  margin-left: -5px;
  position: relative
}

.ma-wrapper[data-v-c04ca732] {
  height: 23px;
  line-height: 23px;
  width: 100%;
  overflow: hidden;
  flex: none !important;
  border-bottom: 1px solid var(--tab-bar-border-color)
}

.ma-wrapper .ma-tab .ma-icon[data-v-c04ca732]:first-child {
  font-size: 16px;
  padding-right: 3px
}

.ma-hot-key[data-v-c04ca732] {
  position: absolute;
  top: 50%;
  margin-top: -105px;
  text-align: center;
  color: var(--empty-color);
  font-size: 16px;
  width: 100%
}

.ma-hot-key p[data-v-c04ca732] {
  display: inline-block;
  text-align: left;
  line-height: 30px
}

.ma-hot-key p em[data-v-c04ca732] {
  margin-left: 15px;
  font-style: normal;
  color: var(--empty-key-color)
}

.ma-empty-container[data-v-c04ca732] {
  flex: none !important;
  position: absolute;
  z-index: 2;
  width: 100%;
  height: 100%;
  background: var(--empty-background)
}

ul[data-v-c04ca732] {
  width: 100%;
  overflow: hidden;
  flex-wrap: nowrap;
  white-space: nowrap;
  list-style-type: none
}

ul[data-v-c04ca732], ul li[data-v-c04ca732] {
  display: flex;
  align-items: center;
  background: var(--background)
}

ul li[data-v-c04ca732] {
  justify-content: center;
  height: 30px;
  line-height: 33px;
  padding: 0 10px;
  border-bottom: 3px solid transparent;
  color: var(--color)
}

ul li.selected[data-v-c04ca732] {
  border-bottom: 3px solid #4083c9;
  color: var(--selected-color)
}

ul li.draggableTargetItem[data-v-c04ca732], ul li[data-v-c04ca732]:hover {
  background: var(--hover-background)
}

ul li i[data-v-c04ca732]:not(.ma-icon-lock) {
  color: var(--icon-color);
  margin-left: 5px;
  font-size: .5em
}

.ma-icon-lock[data-v-c04ca732] {
  margin-left: 5px
}

.ma-editor-container > div[data-v-c04ca732] {
  flex: 1
}

.ma-dialog-content .no-data-tip[data-v-c6cee81e] {
  line-height: 380px;
  text-align: center
}

.ma-tree-item[data-v-c6cee81e] {
  white-space: nowrap
}

[data-v-7369a04e] {
  margin: 0;
  padding: 0;
  box-sizing: border-box
}

.ma-main-container[data-v-7369a04e] {
  position: absolute;
  top: 30px;
  left: 22px;
  bottom: 24px;
  right: 0;
  display: flex;
  flex-direction: column
}

.ma-middle-container[data-v-7369a04e] {
  flex: 1;
  display: flex;
  overflow: auto;
  background: var(--middle-background);
  border-bottom: 1px solid var(--border-color)
}

.ma-toolbar-container[data-v-7369a04e] {
  background: var(--background);
  border-right: 1px solid var(--toolbox-border-color);
  width: 22px;
  position: absolute;
  left: 0;
  bottom: 24px;
  top: 30px
}

.ma-toolbar-container > li[data-v-7369a04e] {
  padding: 6px 3px;
  cursor: pointer;
  letter-spacing: 2px;
  text-align: center;
  color: var(--color);
  border-bottom: 1px solid var(--toolbox-border-color)
}

.ma-toolbar-container > li > i[data-v-7369a04e] {
  color: var(--icon-color);
  font-size: 14px;
  padding-top: 3px;
  display: inline-block
}

.ma-toolbar-container > li[data-v-7369a04e]:hover {
  background: var(--hover-background)
}

.ma-toolbar-container > li.selected[data-v-7369a04e] {
  background: var(--selected-background);
  color: var(--selected-color)
}

.ma-resizer-x[data-v-7369a04e] {
  float: left;
  width: 10px;
  height: 100%;
  margin-left: -5px;
  background: none;
  cursor: e-resize;
  z-index: 1000
}

.Contextmenu_menu_11GS6, .Contextmenu_menu_item_3Og8t, .Contextmenu_menu_item__clickable_1z-eR, .Contextmenu_menu_item__unclickable_3D5i6 {
  box-sizing: border-box
}

.magic-contextmenu {
  position: fixed;
  border: 1px solid var(--border-color);
  background: var(--background)
}

.magic-contextmenu-body {
  display: block
}

.magic-contextmenu-item {
  transition: .2s;
  height: 24px;
  line-height: 24px;
  padding: 0 10px;
  display: flex
}

.magic-contextmenu-item-divided {
  border-bottom: 1px solid var(--border-color)
}

.magic-contextmenu-item .magic-contextmenu-item-icon {
  margin-right: 5px;
  width: 13px
}

.magic-contextmenu-item .magic-contextmenu-item-icon i {
  font-size: 12px;
  color: var(--icon-color)
}

.magic-contextmenu-item .magic-contextmenu-item-label {
  flex: 1
}

.magic-contextmenu-item .magic-contextmenu-item-expand-icon {
  margin-left: 10px;
  font-size: 6px;
  width: 10px
}

.magic-contextmenu-item-available {
  color: var(--color);
  cursor: pointer
}

.magic-contextmenu-item-available:hover {
  background: var(--select-option-hover-background);
  color: var(--select-option-hover-color)
}

.magic-contextmenu-item-disabled {
  color: var(--select-option-disabled-color);
  cursor: not-allowed
}

.magic-contextmenu-item-expand {
  background: var(--select-option-hover-background);
  color: var(--select-option-hover-color)
}
