import Vue from 'vue';
import Router from 'vue-router';
// 解决登录时路由跳转时控制台的报错---博客上说这是路由版本升级导致的，不影响功能
const originalPush = Router.prototype.push
Router.prototype.push = function push(location, onResolve, onReject) {
  if (onResolve || onReject) return originalPush.call(this, location, onResolve, onReject)
  return originalPush.call(this, location).catch((err) => err)
}
Vue.use(Router);

/* Layout */
import Layout from '@/layout';

/**
 * Note: 路由配置项
 *
 * hidden: true                   // 当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1
 * alwaysShow: true               // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面
 *                                // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面
 *                                // 若你想不管路由下面的 children 声明的个数都显示你的根路由
 *                                // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由
 * redirect: noRedirect           // 当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'             // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * meta : {
    noCache: true                // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)
    title: 'title'               // 设置该路由在侧边栏和面包屑中展示的名字
    icon: 'svg-name'             // 设置该路由的图标，对应路径src/assets/icons/svg
    breadcrumb: false            // 如果设置为false，则不会在breadcrumb面包屑中显示
    activeMenu: '/system/user'   // 当路由设置了该属性，则会高亮相对应的侧边栏。
  }
 */

// 公共路由
export const constantRoutes = [
  {
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/redirect/:path(.*)',
        component: () => import('@/views/redirect'),
      },
    ],
  },
  {
    path: '/login',
    component: () => import('@/views/login'),
    hidden: true,
  },
  {
    path: '/applicationPreview',
    name: 'applicationPreview',
    component: () => import('@/views/admin/preview'),
    hidden: true,
  },
  {
    path: '/busFromDesigner',
    component: () => import('@/views/tool/variantform/BusFromDesigner.vue'),
    hidden: true,
  },
  {
    path: '/index',
    component: () => import('@/views/index'),
    hidden: true,
  },
  {
    path: '/markdown',
    component: () => import('@/views/system/markdown'),
    hidden: true,
  },
  {
    path: '/admin/design',
    name: 'design',
    component: () => import('@/views/admin/FormProcessDesign.vue'),
    meta: { title: '表单流程设计' },
    hidden: true,
  },
  {
    path: '/barcode/designBarcode',
    component: () => import('@/views/admin/barcode/designBarcode'),
    hidden: true,
  },
  {
    path: '/report/preview',
    component: () => import('@/views/tool/excelreport/viewer'),
    hidden: true,
  },
  {
    path: '/codeView',
    component: () => import('@/views/tool/variantform/codeView'),
    hidden: true,
  },
  {
    path: '/eqPowerByDays/:id',
    component: () => import('@/views/tool/variantform/commonIndex'),
    hidden: true,
  },
  {
    path: '/commonPage/:id',
    component: () => import('@/views/tool/variantform/commonIndex'),
    hidden: true,
  },
  {
    path: '/404',
    component: () => import('@/views/error/404'),
    hidden: true,
  },
  {
    path: '/401',
    component: () => import('@/views/error/401'),
    hidden: true,
  },
  {
    path: '/formDesign',
    component: Layout,
    hidden: true,
    meta: { title: '系统工具' },
    children: [
      {
        path: '/formDesign/:formId',
        meta: { title: '视图模板' },
        name: 'formDesign',
        component: () => import('@/views/tool/variantform/index.vue'),
      },
    ],
  },
  // {
  //   path: '/tool/table/:key',
  //   component: () => import('@/views/tool/variantform/flowBusList.vue'),
  //   hidden: true,
  // },
  {
    path: '/flow',
    component: Layout,
    hidden: true,
    meta: { title: '设备管理' },
    children: [
      {
        path: '/flow/:definitionId',
        component: () =>
          import('@/views/tool/variantform/operationFlowFrom.vue'),
        name: 'OperationFlowFrom',
        meta: { title: {} },
      },
    ],
  },
  {
    path: '/user',
    component: Layout,
    hidden: true,
    redirect: 'noredirect',
    children: [
      {
        path: 'profile',
        name: 'Profile',
        component: () => import('@/views/system/user/profile/index'),
        meta: { title: '个人中心', icon: 'user' },
      },
    ],
  },
  {
    path: '/system/user-auth',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'role/:userId(\\d+)',
        component: () => import('@/views/system/user/authRole'),
        name: 'AuthRole',
        meta: { title: '分配角色', activeMenu: '/system/user' },
      },
    ],
  },
  {
    path: '/system/role-auth',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'user/:roleId(\\d+)',
        component: () => import('@/views/system/role/authUser'),
        name: 'AuthUser',
        meta: { title: '分配用户', activeMenu: '/system/role' },
      },
    ],
  },
  {
    path: '/system/dict-data',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'index/:dictId(\\d+)',
        component: () => import('@/views/system/dict/data'),
        name: 'Data',
        meta: { title: '字典数据', activeMenu: '/system/dict' },
      },
    ],
  },
  {
    path: '/monitor/job-log',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'index',
        component: () => import('@/views/monitor/job/log'),
        name: 'JobLog',
        meta: { title: '调度日志', activeMenu: '/monitor/job' },
      },
    ],
  },
  {
    path: '/tool/gen-edit',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'index',
        component: () => import('@/views/tool/gen/editTable'),
        name: 'GenEdit',
        meta: { title: '修改模型配置', activeMenu: '/tool/gen' },
      },
    ],
  },
  {
    path: '/excelreport',
    component: Layout,
    hidden: true,
    meta: { title: '系统工具' },
  },
  {
    path: '/excelreport/designer',
    component: () => import('@/views/admin/layout/ReportDesign'),
    hidden: true,
  },
  {
    path: '/template',
    component: Layout,
    hidden: true,
    meta: { title: '系统工具' },
    children: [
      {
        path: 'designer',
        meta: { title: '在线组件' },
        component: () =>
          import('@/views/tool/variantform/templateDesigner.vue'),
      },
      {
        path: 'dot',
        meta: { title: '新页面' },
        component: () => import('@/views/new/dot.vue'),
      },
      {
        path: 'device',
        meta: { title: '新页面' },
        component: () => import('@/views/new/device.vue'),
      },
      {
        path: 'energy',
        meta: { title: '新页面' },
        component: () => import('@/views/new/energy.vue'),
      },
      {
        path: 'warning',
        meta: { title: '新页面' },
        component: () => import('@/views/new/warning.vue'),
      },
      {
        path: 'iot',
        meta: { title: '新页面' },
        component: () => import('@/views/iot/index.vue'),
      },
      {
        path: 'productDetails',
        meta: { title: '产品详情' },
        component: () => import('@/views/iot/productDetails/index.vue'),
      },
      {
        path: 'collector',
        meta: { title: '采集器' },
        component: () => import('@/views/iot/collector/index.vue'),
      },
      {
        path: 'iotDevice',
        meta: { title: '设备' },
        component: () => import('@/views/iot/device/index.vue'),
      },
      {
        path: 'deviceDetails',
        meta: { title: '设备详情' },
        component: () => import('@/views/iot/device/details/index.vue'),
      },
      {
        path: 'alarmLog',
        meta: { title: '告警记录' },
        component: () => import('@/views/iot/alarm/log/index.vue'),
      },
    ],
  },
  {
    path: '/code',
    component: Layout,
    hidden: true,
    meta: { title: '系统工具' },
    children: [
      {
        path: 'designer',
        name: 'codeDesigner',
        meta: { title: '条码配置' },
        component: () => import('@/directive/dragPage/index.vue'),
      },
    ],
  },
  {
    path: '/workstationDesign',
    component: () => import('@/views/workstation/design'),
    hidden: true,
  },
  {
    path: '/workstationRender',
    component: () => import('@/views/workstation/render'),
    hidden: true,
  },
  {
    path: '/modelOperationOnly',
    component: () => import('@/views/tool/variantform/modelOperationOnly'),
    hidden: true,
  },
  {
    path: '/processConfig',
    component: () => import('@/views/processConfig/index'),
    hidden: true,
  },
  {
    path: '/tool/knowledgeBase',
    hidden: true,
    meta: { title: '知识库' },
    component: () => import('@/views/tool/knowledgeBase/index.vue'),
  },
  {
    path: '/agent/config',
    hidden: true,
    meta: { title: 'AI助手配置' },
    component: () => import('@/views/admin/aiAgent/config/index.vue'),
  },
  {
    path: '/agent/application',
    hidden: true,
    meta: { title: 'AI助手应用' },
    component: () => import('@/views/admin/aiAgent/application/index.vue'),
  },
  {
    path: '/agent/chat',
    hidden: true,
    meta: { title: 'AI助手对话' },
    component: () => import('@/views/admin/aiAgent/Chat/index.vue'),
  },
  {
    path: '/agent/debug',
    hidden: true,
    meta: { title: 'AI助手Debug' },
    component: () => import('@/views/admin/aiAgent/debug/index.vue'),
  },
];

export default new Router({
  mode: 'history', // 去掉url中的#
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRoutes,
});
