'use strict';
const path = require('path');
function resolve(dir) {
  return path.join(__dirname, dir);
}
const webpack = require('webpack');

// const name = process.env.VUE_APP_TITLE || '设备管理系统' // 网页标题
// 设置环境变量，可以在全局使用
process.env.VUE_APP_MA_VERSION = require('./package.json').version;

const port = process.env.port || process.env.npm_config_port || 80; // 端口

const MonacoWebpackPlugin = require('monaco-editor-webpack-plugin');
const Timestamp = new Date().getTime();
// vue.config.js 配置说明
// 官方vue.config.js 参考文档 https://cli.vuejs.org/zh/config/#css-loaderoptions
// 这里只列一部分，具体配置参考文档
module.exports = {
  // 部署生产环境和开发环境下的URL。
  // 默认情况下，Vue CLI 会假设你的应用是被部署在一个域名的根路径上
  // 例如 https://www.ruoyi.vip/。如果应用被部署在一个子路径上，你就需要用这个选项指定这个子路径。例如，如果你的应用被部署在 https://www.ruoyi.vip/admin/，则设置 baseUrl 为 /admin/。
  publicPath: process.env.NODE_ENV === 'production' ? '/' : '/',
  // 在npm run build 或 yarn build 时 ，生成文件的目录名称（要和baseUrl的生产环境路径一致）（默认dist）
  outputDir: 'dist',
  // 用于放置生成的静态资源 (js、css、img、fonts) 的；（项目打包之后，静态资源会放在这个文件夹下）
  assetsDir: 'static',
  // 是否开启eslint保存检测，有效值：ture | false | 'error'
  lintOnSave: process.env.NODE_ENV === 'development',
  // 如果你不需要生产环境的 source map，可以将其设置为 false 以加速生产环境构建。
  productionSourceMap: process.env.NODE_ENV === 'development',
  runtimeCompiler: true,
  // webpack-dev-server 相关配置
  devServer: {
    host: '0.0.0.0',
    port: port,
    open: true,
    proxy: {
      // detail: https://cli.vuejs.org/config/#devserver-proxy
      [process.env.VUE_APP_BASE_API]: {
        // target: 'http://***************:21017', // 新仁测试
        // target: 'http://mom.xrmaterial.com:1080/stage-api/', // 新仁正式
        // target: 'http://*************:8081', // 华联正式
        // target: 'http://************:8080',
        // target: 'http://logictrue.cn:20851/stage-api',
        // target: 'http://***********:8081',
        // target: 'http://*************:8080',
        target: 'http://127.0.0.1:8080',
        changeOrigin: true,
        pathRewrite: { ['^' + process.env.VUE_APP_BASE_API]: '' },
      },
      '/baiduApi': {
        target: 'https://fanyi-api.baidu.com/api/trans/vip/translate',
        changeOrigin: true,
        pathRewrite: { ['^/baiduApi']: '' },
      },
    },
    disableHostCheck: true,
    hot: true,
  },
  // css: { loaderOptions: { sass: { data: `@import "@/assets/styles/variables.scss";` }}},
  configureWebpack: {
    // name: name,
    resolve: {
      alias: {
        '@': resolve('src'),
        vue$: 'vue/dist/vue.common',
      },
    },
    plugins: [
      new webpack.ProvidePlugin({
        $: 'jquery',
        jQuery: 'jquery',
        'windows.jQuery': 'jquery',
      }),
    ],
    output: {
      // 把应用打包成umd库格式
      library: 'myLibrary', // 输出重构  打包编译后的文件名称  【模块名称.时间戳】
      filename: `[name].${Timestamp}.js`,
      libraryTarget: 'umd',
      globalObject: 'this',
    },
    optimization: process.env.NODE_ENV === 'production' ? {
      minimize: true,  // 启用代码压缩
      minimizer: [
        new (require('terser-webpack-plugin'))({
          terserOptions: {
            compress: {
              drop_console: true,  // 移除 console.log
            },
          },
        }),
      ],
    } : undefined,
  },
  chainWebpack(config) {
    config.plugin('monaco').use(new MonacoWebpackPlugin());
    config.plugins.delete('preload'); // TODO: need test
    config.plugins.delete('prefetch'); // TODO: need test
    config.resolve.alias.set('vue$', 'vue/dist/vue.esm.js');
    if (process.env.NODE_ENV !== 'development') {
      const timestamp = new Date().getTime();

      // 修改主文件和 chunk 文件的输出名，添加时间戳
      config.output
        .filename(`js/[name].[contenthash:8].${timestamp}.js`)
        .chunkFilename(`js/[name].[contenthash:8].${timestamp}.js`)
        .end();

      const CompressionPlugin = require('compression-webpack-plugin');
      config.plugin('CompressionPlugin').use(
        new CompressionPlugin({
          algorithm: 'gzip', // 使用gzip压缩
          test: /\.js$|\.html$|\.css$/, // 匹配文件名
          // filename: '[path].gz[query]', // 压缩后的文件名(保持原文件名，后缀加.gz)
          filename: '[path][base].gz',
          minRatio: 0.8, // 压缩率小于0.8才会压缩
          threshold: 10240, // 对超过10k的数据压缩
          deleteOriginalAssets: false, // 是否删除未压缩的源文件，谨慎设置，如果希望提供非gzip的资源，可不设置或者设置为false（比如删除打包后的gz后还可以加载到原始资源文件）
        }),
      );
    }

    // set svg-sprite-loader
    config.module.rule('svg').exclude.add(resolve('src/assets/icons')).end();
    config.module
      .rule('icons')
      .test(/\.svg$/)
      .include.add(resolve('src/assets/icons'))
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({ symbolId: 'icon-[name]' })
      .end();

    config.when(process.env.NODE_ENV !== 'development', (config) => {
      config
        .plugin('ScriptExtHtmlWebpackPlugin')
        .after('html')
        .use('script-ext-html-webpack-plugin', [
          {
            // `runtime` must same as runtimeChunk name. default is `runtime`
            inline: /runtime\..*\.js$/,
          },
        ])
        .end();
      config.optimization.splitChunks({
        chunks: 'all',
        cacheGroups: {
          libs: {
            name: 'chunk-libs',
            test: /[\\/]node_modules[\\/]/,
            priority: 10,
            chunks: 'initial', // only package third parties that are initially dependent
          },
          elementUI: {
            name: 'chunk-elementUI', // split elementUI into a single package
            priority: 20, // the weight needs to be larger than libs and app or it will be packaged into libs or app
            test: /[\\/]node_modules[\\/]_?element-ui(.*)/, // in order to adapt to cnpm
          },
          commons: {
            name: 'chunk-commons',
            test: resolve('src/components'), // can customize your rules
            minChunks: 3, //  minimum common number
            priority: 5,
            reuseExistingChunk: true,
          },
        },
      });
      config.optimization.runtimeChunk('single'),
        {
          from: path.resolve(__dirname, './public/robots.txt'), // 防爬虫文件
          to: './', // 到根目录下
        };
    });
  },
};
